# Updated semantic mask generation with adaptive erosion

def generate_adaptive_semantic_mask(patch_msk, erosion_config='auto'):
    """
    Generate semantic mask with adaptive erosion based on spot sizes.
    
    Args:
        patch_msk: Instance mask with spot IDs
        erosion_config: 'auto', 'light', 'medium', 'heavy', or (kernel_size, iterations)
    """
    import cv2
    from skimage.measure import regionprops
    
    if erosion_config == 'auto':
        # Analyze spot sizes to choose erosion
        valid_ids = np.unique(patch_msk)[1:]
        if len(valid_ids) == 0:
            return (patch_msk > 0).astype(np.float32)
        
        diameters = []
        for iid in valid_ids:
            spot_mask = (patch_msk == iid).astype(np.uint8)
            props = regionprops(spot_mask)
            if props:
                diameters.append(props[0].equivalent_diameter)
        
        if not diameters:
            return (patch_msk > 0).astype(np.float32)
        
        avg_diameter = np.mean(diameters)
        
        # Choose erosion based on average spot size
        if avg_diameter < 4:
            kernel_size, iterations = 1, 1  # Light erosion for small spots
        elif avg_diameter < 8:
            kernel_size, iterations = 2, 1  # Medium erosion
        else:
            kernel_size, iterations = 2, 2  # Heavy erosion for large spots
            
    elif erosion_config == 'light':
        kernel_size, iterations = 1, 1
    elif erosion_config == 'medium':
        kernel_size, iterations = 2, 1
    elif erosion_config == 'heavy':
        kernel_size, iterations = 2, 2
    else:
        kernel_size, iterations = erosion_config
    
    # Apply erosion to each spot individually
    kernel = np.ones((kernel_size, kernel_size), np.uint8)
    eroded_mask = np.zeros_like(patch_msk)
    
    for iid in np.unique(patch_msk):
        if iid == 0:
            continue
        spot_binary = (patch_msk == iid).astype(np.uint8)
        eroded_spot = cv2.erode(spot_binary, kernel, iterations=iterations)
        eroded_mask[eroded_spot > 0] = iid
    
    return (eroded_mask > 0).astype(np.float32)

# Updated __getitem__ method for your dataset
def updated_getitem_with_erosion(self, idx, erosion_config='auto'):
    """
    Updated __getitem__ method with adaptive erosion.
    Replace the semantic mask generation in your dataset with this.
    """
    # ... existing code until semantic mask generation ...
    
    # UPDATED: Use adaptive erosion instead of direct conversion
    sem = generate_adaptive_semantic_mask(patch_msk, erosion_config)
    
    # ... rest of the existing code remains the same ...
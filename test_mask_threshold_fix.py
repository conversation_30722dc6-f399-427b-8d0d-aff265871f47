# Test the mask threshold fix

import matplotlib.pyplot as plt
import numpy as np

def test_mask_threshold_fix():
    """Test the original vs fixed mask threshold behavior"""
    
    # Test parameters
    test_params = {
        'image_size': (128, 128),
        'min_spots': 10,
        'max_spots': 20,
        'min_radius': 3,
        'max_radius': 8,
        'density_factor': 1.0,
        'mask_threshold': 0.35,  # Test with this threshold
        'allow_touching': False,
        'shape_variation': 0.1,
        'add_gradients': False,
        'realistic_noise': False
    }
    
    # Create both generators
    from enhanced_synthetic_data_generator import EnhancedSyntheticSpotGenerator as OriginalGenerator
    from fixed_enhanced_synthetic_generator import EnhancedSyntheticSpotGenerator as FixedGenerator
    
    original_gen = OriginalGenerator(**test_params)
    fixed_gen = FixedGenerator(**test_params)
    
    # Generate samples
    orig_img, orig_mask, orig_data = original_gen.generate_sample()
    fixed_img, fixed_mask, fixed_data = fixed_gen.generate_sample()
    
    # Create comparison plot
    fig, axes = plt.subplots(2, 4, figsize=(16, 8))
    
    # Row 1: Original generator
    axes[0, 0].imshow(orig_img, cmap='gray')
    axes[0, 0].set_title('Original: Image')
    axes[0, 0].axis('off')
    
    axes[0, 1].imshow(orig_mask, cmap='nipy_spectral')
    axes[0, 1].set_title(f'Original: Mask\n{len(orig_data)} spots')
    axes[0, 1].axis('off')
    
    axes[0, 2].imshow(orig_img, cmap='gray')
    axes[0, 2].imshow(orig_mask > 0, cmap='Reds', alpha=0.5)
    axes[0, 2].set_title('Original: Overlay')
    axes[0, 2].axis('off')
    
    # Calculate mask statistics for original
    orig_mask_sizes = [spot['mask_pixels'] if 'mask_pixels' in spot else 0 for spot in orig_data]
    orig_intensities = [spot['intensity'] for spot in orig_data]
    
    axes[0, 3].scatter(orig_intensities, orig_mask_sizes, alpha=0.7)
    axes[0, 3].set_xlabel('Spot Intensity')
    axes[0, 3].set_ylabel('Mask Size (pixels)')
    axes[0, 3].set_title('Original: Intensity vs Mask Size')
    
    # Row 2: Fixed generator
    axes[1, 0].imshow(fixed_img, cmap='gray')
    axes[1, 0].set_title('Fixed: Image')
    axes[1, 0].axis('off')
    
    axes[1, 1].imshow(fixed_mask, cmap='nipy_spectral')
    axes[1, 1].set_title(f'Fixed: Mask\n{len(fixed_data)} spots')
    axes[1, 1].axis('off')
    
    axes[1, 2].imshow(fixed_img, cmap='gray')
    axes[1, 2].imshow(fixed_mask > 0, cmap='Reds', alpha=0.5)
    axes[1, 2].set_title('Fixed: Overlay')
    axes[1, 2].axis('off')
    
    # Calculate mask statistics for fixed
    fixed_mask_sizes = [spot['mask_pixels'] for spot in fixed_data]
    fixed_intensities = [spot['intensity'] for spot in fixed_data]
    
    axes[1, 3].scatter(fixed_intensities, fixed_mask_sizes, alpha=0.7, color='orange')
    axes[1, 3].set_xlabel('Spot Intensity')
    axes[1, 3].set_ylabel('Mask Size (pixels)')
    axes[1, 3].set_title('Fixed: Intensity vs Mask Size')
    
    plt.tight_layout()
    plt.show()
    
    # Print statistics
    print("=== Mask Threshold Fix Comparison ===")
    print(f"Mask threshold: {test_params['mask_threshold']}")
    print()
    
    print("Original Generator:")
    if orig_mask_sizes:
        print(f"  Mask sizes: {min(orig_mask_sizes)}-{max(orig_mask_sizes)} pixels")
        print(f"  Average mask size: {np.mean(orig_mask_sizes):.1f} pixels")
        print(f"  Intensity range: {min(orig_intensities):.3f}-{max(orig_intensities):.3f}")
        
        # Check correlation between intensity and mask size (should be low for fixed version)
        if len(orig_intensities) > 1:
            correlation = np.corrcoef(orig_intensities, orig_mask_sizes)[0, 1]
            print(f"  Intensity-MaskSize correlation: {correlation:.3f}")
    
    print()
    print("Fixed Generator:")
    if fixed_mask_sizes:
        print(f"  Mask sizes: {min(fixed_mask_sizes)}-{max(fixed_mask_sizes)} pixels")
        print(f"  Average mask size: {np.mean(fixed_mask_sizes):.1f} pixels")
        print(f"  Intensity range: {min(fixed_intensities):.3f}-{max(fixed_intensities):.3f}")
        
        if len(fixed_intensities) > 1:
            correlation = np.corrcoef(fixed_intensities, fixed_mask_sizes)[0, 1]
            print(f"  Intensity-MaskSize correlation: {correlation:.3f}")
    
    print()
    print("Expected behavior:")
    print("- Original: High correlation between intensity and mask size (BAD)")
    print("- Fixed: Low correlation between intensity and mask size (GOOD)")
    print("- Fixed masks should be more consistent in size relative to spot radius")

def test_different_thresholds():
    """Test different mask threshold values with the fixed generator"""
    
    thresholds = [0.1, 0.2, 0.35, 0.5, 0.7]
    
    fig, axes = plt.subplots(2, len(thresholds), figsize=(4*len(thresholds), 8))
    
    for i, threshold in enumerate(thresholds):
        test_params = {
            'image_size': (128, 128),
            'min_spots': 15,
            'max_spots': 25,
            'min_radius': 3,
            'max_radius': 6,
            'mask_threshold': threshold,
            'add_gradients': False,
            'realistic_noise': False
        }
        
        from fixed_enhanced_synthetic_generator import EnhancedSyntheticSpotGenerator
        gen = EnhancedSyntheticSpotGenerator(**test_params)
        img, mask, data = gen.generate_sample()
        
        # Show image
        axes[0, i].imshow(img, cmap='gray')
        axes[0, i].set_title(f'Threshold: {threshold}')
        axes[0, i].axis('off')
        
        # Show mask overlay
        axes[1, i].imshow(img, cmap='gray')
        axes[1, i].imshow(mask > 0, cmap='Reds', alpha=0.6)
        axes[1, i].set_title(f'Mask pixels: {np.sum(mask > 0):.0f}')
        axes[1, i].axis('off')
    
    plt.tight_layout()
    plt.show()
    
    print("Threshold recommendations:")
    print("- 0.1-0.2: Large masks (may include spot halos)")
    print("- 0.3-0.4: Medium masks (good balance)")
    print("- 0.5-0.7: Small masks (tight around spot centers)")

# Run the tests
if __name__ == "__main__":
    test_mask_threshold_fix()
    test_different_thresholds()
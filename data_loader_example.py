import os
import numpy as np
import matplotlib.pyplot as plt
import torch
from torch.utils.data import DataLoader
from data_utils import SpotDataset, create_data_loaders
from sparse_data_utils import SparseSpotDataset, create_sparse_data_loaders
from synthetic_data_generator import Synthetic<PERSON>potGenerator, AdvancedSyntheticSpotGenerator

def visualize_samples(dataset, num_samples=5, title="Dataset Samples"):
    """Visualize samples from the dataset"""
    fig, axes = plt.subplots(num_samples, 3, figsize=(15, 3*num_samples))
    
    for i in range(num_samples):
        # Get a random sample
        idx = np.random.randint(0, len(dataset))
        sample = dataset[idx]
        
        # Extract image, mask, and confidence
        image = sample['image'].squeeze().cpu().numpy()
        mask = sample['mask'].squeeze().cpu().numpy()
        confidence = sample['confidence'].squeeze().cpu().numpy() if 'confidence' in sample else None
        
        # Plot image
        axes[i, 0].imshow(image, cmap='gray')
        axes[i, 0].set_title(f"Image {idx}")
        axes[i, 0].axis('off')
        
        # Plot mask
        axes[i, 1].imshow(mask, cmap='hot')
        axes[i, 1].set_title(f"Mask {idx}")
        axes[i, 1].axis('off')
        
        # Plot confidence if available
        if confidence is not None:
            axes[i, 2].imshow(confidence, cmap='viridis')
            axes[i, 2].set_title(f"Confidence {idx}")
        else:
            axes[i, 2].imshow(np.ones_like(mask), cmap='viridis')
            axes[i, 2].set_title("Full Confidence")
        axes[i, 2].axis('off')
    
    plt.suptitle(title)
    plt.tight_layout()
    plt.show()

def load_and_visualize_data(data_dir=None, use_synthetic=True, synthetic_size=100):
    """
    Load data from disk and/or generate synthetic data, then visualize samples
    
    Args:
        data_dir: Directory containing images and masks
        use_synthetic: Whether to generate synthetic data
        synthetic_size: Number of synthetic samples to generate
    """
    # Configuration
    image_size = (256, 256)
    batch_size = 8
    
    # Create data loaders
    train_loader, val_loader, train_dataset, val_dataset = create_sparse_data_loaders(
        data_dir=data_dir,
        batch_size=batch_size,
        image_size=image_size,
        train_val_split=0.8,
        synthetic=use_synthetic,
        synthetic_size=synthetic_size,
        augmentation_level='strong',
        num_workers=0  # Use 0 for debugging
    )
    
    print(f"Training samples: {len(train_dataset)}")
    print(f"Validation samples: {len(val_dataset)}")
    
    # Visualize training samples
    visualize_samples(train_dataset, title="Training Samples")
    
    # Visualize validation samples
    visualize_samples(val_dataset, title="Validation Samples")
    
    return train_loader, val_loader, train_dataset, val_dataset

def generate_and_visualize_synthetic_data(num_samples=5):
    """
    Generate and visualize synthetic data with variable spot sizes and densities
    
    Args:
        num_samples: Number of samples to generate and visualize
    """
    # Create synthetic data generator
    generator = SyntheticSpotGenerator(
        image_size=(256, 256),
        min_spots=5,
        max_spots=50,
        min_radius=2,
        max_radius=10
    )
    
    # Generate samples
    images, masks = generator.generate_batch(num_samples)
    
    # Visualize samples
    fig, axes = plt.subplots(num_samples, 2, figsize=(10, 3*num_samples))
    
    for i in range(num_samples):
        # Plot image
        axes[i, 0].imshow(images[i], cmap='gray')
        axes[i, 0].set_title(f"Synthetic Image {i+1}")
        axes[i, 0].axis('off')
        
        # Plot mask with unique IDs
        mask_rgb = np.zeros((*masks[i].shape, 3))
        unique_ids = np.unique(masks[i])
        unique_ids = unique_ids[unique_ids > 0]  # Skip background (0)
        
        for spot_id in unique_ids:
            # Generate random color for this spot ID
            color = np.random.rand(3)
            mask_rgb[masks[i] == spot_id] = color
        
        axes[i, 1].imshow(mask_rgb)
        axes[i, 1].set_title(f"Mask with {len(unique_ids)} unique spots")
        axes[i, 1].axis('off')
    
    plt.suptitle("Synthetic Data with Variable Spot Sizes and Densities")
    plt.tight_layout()
    plt.show()
    
    # Create advanced synthetic data generator
    advanced_generator = AdvancedSyntheticSpotGenerator(
        image_size=(256, 256),
        min_spots=5,
        max_spots=50,
        min_radius=2,
        max_radius=10,
        allow_overlapping=True,
        shape_variation=0.3,
        add_gradients=True,
        realistic_noise=True
    )
    
    # Generate advanced samples
    adv_images, adv_masks = advanced_generator.generate_batch(num_samples)
    
    # Visualize advanced samples
    fig, axes = plt.subplots(num_samples, 2, figsize=(10, 3*num_samples))
    
    for i in range(num_samples):
        # Plot image
        axes[i, 0].imshow(adv_images[i], cmap='gray')
        axes[i, 0].set_title(f"Advanced Synthetic Image {i+1}")
        axes[i, 0].axis('off')
        
        # Plot mask with unique IDs
        mask_rgb = np.zeros((*adv_masks[i].shape, 3))
        unique_ids = np.unique(adv_masks[i])
        unique_ids = unique_ids[unique_ids > 0]  # Skip background (0)
        
        for spot_id in unique_ids:
            # Generate random color for this spot ID
            color = np.random.rand(3)
            mask_rgb[adv_masks[i] == spot_id] = color
        
        axes[i, 1].imshow(mask_rgb)
        axes[i, 1].set_title(f"Mask with {len(unique_ids)} unique spots")
        axes[i, 1].axis('off')
    
    plt.suptitle("Advanced Synthetic Data with Variable Spot Sizes and Densities")
    plt.tight_layout()
    plt.show()

if __name__ == "__main__":
    # Example usage
    print("Generating and visualizing synthetic data...")
    generate_and_visualize_synthetic_data(5)
    
    print("\nLoading and visualizing data...")
    # Replace with your data directory or set to None to use only synthetic data
    data_dir = None  # "/path/to/your/data"
    train_loader, val_loader, train_dataset, val_dataset = load_and_visualize_data(
        data_dir=data_dir,
        use_synthetic=True,
        synthetic_size=10
    )
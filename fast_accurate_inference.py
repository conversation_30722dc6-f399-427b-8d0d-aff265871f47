import torch
import numpy as np
from skimage.feature import peak_local_max
from skimage.segmentation import watershed
from skimage.measure import regionprops
import cv2

def fast_accurate_inference(model, image, device='cuda', patch_size=256, overlap=64, 
                           nms_threshold=0.3, use_flow=True, centroid_threshold=0.3):
    """
    Fast and accurate spot detection with instance segmentation
    
    Args:
        model: Trained model
        image: Input image (any size)
        device: 'cuda' or 'cpu'
        patch_size: Patch size for tiling (256)
        overlap: Overlap between patches
        nms_threshold: NMS threshold (optional)
        use_flow: Use flow field for refinement
        centroid_threshold: Threshold for centroid detection
    
    Returns:
        dict with instance_masks, instance_labels, spots (with coordinates, scores, sizes, intensities)
    """
    
    model.eval()
    model = model.to(device)
    
    # Store original image for intensity calculation
    original_image = image.copy()
    
    # Normalize image like dataloader
    image = image.astype(np.float32)
    image /= (image.max() + 1e-8)
    image = np.clip(image, 0, 1)
    
    H, W = image.shape
    
    # Initialize output arrays
    full_centroid = np.zeros((H, W), dtype=np.float32)
    full_distance = np.zeros((H, W), dtype=np.float32)
    full_boundary = np.zeros((H, W), dtype=np.float32)
    full_semantic = np.zeros((H, W), dtype=np.float32)
    full_flow = np.zeros((2, H, W), dtype=np.float32)
    
    # Tile processing
    stride = patch_size - overlap
    
    with torch.no_grad():
        for y in range(0, H, stride):
            for x in range(0, W, stride):
                # Extract patch
                y_end = min(y + patch_size, H)
                x_end = min(x + patch_size, W)
                patch = image[y:y_end, x:x_end]
                
                # Pad if needed
                ph = patch_size - patch.shape[0]
                pw = patch_size - patch.shape[1]
                if ph > 0 or pw > 0:
                    patch = np.pad(patch, ((0, ph), (0, pw)), 'reflect')
                
                # Convert to tensor
                patch_tensor = torch.from_numpy(patch).unsqueeze(0).unsqueeze(0).to(device)
                
                # Model inference
                outputs = model(patch_tensor)
                
                # Get predictions
                sem = torch.sigmoid(outputs['semantic'])[0, 0].cpu().numpy()
                centroid = torch.sigmoid(outputs['heatmaps'])[0, 1].cpu().numpy()
                distance = torch.sigmoid(outputs['distance'])[0, 0].cpu().numpy()
                boundary = torch.sigmoid(outputs['boundary'])[0, 0].cpu().numpy()
                flow = outputs['flow'][0].cpu().numpy()
                
                # Remove padding
                if ph > 0 or pw > 0:
                    sem = sem[:patch_size-ph, :patch_size-pw]
                    centroid = centroid[:patch_size-ph, :patch_size-pw]
                    distance = distance[:patch_size-ph, :patch_size-pw]
                    boundary = boundary[:patch_size-ph, :patch_size-pw]
                    flow = flow[:, :patch_size-ph, :patch_size-pw]
                
                # Place in full image (average overlapping regions)
                patch_h, patch_w = sem.shape
                full_semantic[y:y+patch_h, x:x+patch_w] = np.maximum(
                    full_semantic[y:y+patch_h, x:x+patch_w], sem)
                full_centroid[y:y+patch_h, x:x+patch_w] = np.maximum(
                    full_centroid[y:y+patch_h, x:x+patch_w], centroid)
                full_distance[y:y+patch_h, x:x+patch_w] = np.maximum(
                    full_distance[y:y+patch_h, x:x+patch_w], distance)
                full_boundary[y:y+patch_h, x:x+patch_w] = np.maximum(
                    full_boundary[y:y+patch_h, x:x+patch_w], boundary)
                full_flow[:, y:y+patch_h, x:x+patch_w] = flow
    
    # Extract spot coordinates from centroid map
    coordinates = peak_local_max(
        full_centroid, 
        min_distance=3, 
        threshold_abs=centroid_threshold,
        exclude_border=False
    )
    
    if len(coordinates) == 0:
        return {
            'instance_masks': [],
            'instance_labels': np.zeros_like(image, dtype=np.uint16),
            'spots': []
        }
    
    # Refine coordinates with flow if enabled
    if use_flow:
        refined_coords = []
        for y, x in coordinates:
            # Follow flow for 3 iterations
            cy, cx = float(y), float(x)
            for _ in range(3):
                iy, ix = int(round(cy)), int(round(cx))
                iy = np.clip(iy, 0, H-1)
                ix = np.clip(ix, 0, W-1)
                dy, dx = full_flow[0, iy, ix], full_flow[1, iy, ix]
                cy += dy * 0.5
                cx += dx * 0.5
                cy = np.clip(cy, 0, H-1)
                cx = np.clip(cx, 0, W-1)
            refined_coords.append([int(round(cy)), int(round(cx))])
        coordinates = np.array(refined_coords)
    
    # NMS if enabled
    if nms_threshold > 0:
        scores = [full_centroid[y, x] for y, x in coordinates]
        order = np.argsort(scores)[::-1]
        keep = []
        
        while len(order) > 0:
            i = order[0]
            keep.append(i)
            if len(order) == 1:
                break
            
            current = coordinates[i]
            others = coordinates[order[1:]]
            dists = np.linalg.norm(others - current, axis=1)
            order = order[1:][dists > nms_threshold]
        
        coordinates = coordinates[keep]
    
    # Create markers for watershed
    markers = np.zeros_like(full_semantic, dtype=np.int32)
    for i, (y, x) in enumerate(coordinates):
        markers[y, x] = i + 1
    
    # Use boundary to create better mask for watershed
    semantic_mask = (full_semantic > 0.5) & (full_boundary < 0.5)
    
    # Watershed segmentation using distance transform
    instance_labels = watershed(
        -full_distance, 
        markers, 
        mask=semantic_mask,
        watershed_line=True
    )
    
    # Extract individual instances and calculate properties
    instance_masks = []
    spots = []
    
    for i, (y, x) in enumerate(coordinates):
        label_id = i + 1
        instance_mask = (instance_labels == label_id).astype(np.uint8)
        
        if instance_mask.sum() > 0:
            # Calculate properties
            props = regionprops(instance_mask)[0]
            centroid_score = full_centroid[y, x]
            
            # Calculate mean intensity from original image
            masked_original = original_image * instance_mask
            mean_intensity = masked_original.sum() / instance_mask.sum()
            
            spots.append({
                'y': y,
                'x': x,
                'score': float(centroid_score),
                'size': int(props.area),
                'intensity': float(mean_intensity),
                'equivalent_diameter': float(props.equivalent_diameter)
            })
            
            instance_masks.append(instance_mask)
    
    return {
        'instance_masks': instance_masks,      # List of individual binary masks
        'instance_labels': instance_labels,    # Full labeled image
        'spots': spots,                        # List of dicts with coordinates, scores, sizes, intensities
        'debug': {
            'semantic': full_semantic,
            'centroid': full_centroid,
            'distance': full_distance,
            'boundary': full_boundary
        }
    }
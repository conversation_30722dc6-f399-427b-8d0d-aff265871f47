import os
import glob

def get_next_index(output_dir="training_dataset_fixed"):
    """
    Get the next available index for dataset generation
    
    Args:
        output_dir: Directory containing the dataset
        
    Returns:
        int: Next available index
    """
    img_dir = os.path.join(output_dir, "images")
    
    if not os.path.exists(img_dir):
        print(f"📁 Directory {img_dir} doesn't exist - starting from index 0")
        return 0
    
    # Find all synthetic files
    pattern = os.path.join(img_dir, "synthetic_*.tif")
    existing_files = glob.glob(pattern)
    
    if not existing_files:
        print(f"📁 No existing files found - starting from index 0")
        return 0
    
    # Extract indices from filenames
    indices = []
    for filepath in existing_files:
        filename = os.path.basename(filepath)
        # Extract number from synthetic_XXXXXX.tif
        try:
            index_str = filename.split('_')[1].split('.')[0]
            indices.append(int(index_str))
        except:
            continue
    
    if indices:
        max_index = max(indices)
        next_index = max_index + 1
        print(f"📁 Found {len(indices)} existing files (max index: {max_index})")
        print(f"🔢 Next available index: {next_index}")
        return next_index
    else:
        return 0

def check_dataset_status(output_dir="training_dataset_fixed"):
    """
    Check the current status of the dataset
    
    Args:
        output_dir: Directory containing the dataset
    """
    print(f"📊 Dataset Status: {output_dir}")
    print("="*50)
    
    # Check images
    img_dir = os.path.join(output_dir, "images")
    if os.path.exists(img_dir):
        img_files = glob.glob(os.path.join(img_dir, "synthetic_*.tif"))
        print(f"🖼️  Images: {len(img_files)} files")
        if img_files:
            indices = []
            for f in img_files:
                try:
                    idx = int(os.path.basename(f).split('_')[1].split('.')[0])
                    indices.append(idx)
                except:
                    continue
            if indices:
                print(f"   Range: {min(indices)} - {max(indices)}")
    else:
        print(f"🖼️  Images: Directory doesn't exist")
    
    # Check masks
    mask_dir = os.path.join(output_dir, "masks")
    if os.path.exists(mask_dir):
        mask_files = glob.glob(os.path.join(mask_dir, "synthetic_*.tif"))
        print(f"🎭 Masks: {len(mask_files)} files")
    else:
        print(f"🎭 Masks: Directory doesn't exist")
    
    # Check visualizations
    vis_dir = os.path.join(output_dir, "visualizations")
    if os.path.exists(vis_dir):
        vis_files = glob.glob(os.path.join(vis_dir, "synthetic_*_vis.png"))
        print(f"👁️  Visualizations: {len(vis_files)} files")
    else:
        print(f"👁️  Visualizations: Directory doesn't exist")
    
    print("="*50)

# Quick generation function
def generate_more_samples(num_samples, 
                         output_dir="training_dataset_fixed",
                         mask_threshold=0.35,
                         shape_variation=0.2):
    """
    Generate more samples automatically starting from the next available index
    
    Args:
        num_samples: Number of new samples to generate
        output_dir: Output directory
        mask_threshold: Mask threshold (0.1=large, 0.5=small)
        shape_variation: Shape variation (0=circles, 0.5=very elliptical)
    """
    from enhanced_dataset_generator import generate_training_dataset_enhanced
    
    # Check current status
    check_dataset_status(output_dir)
    
    # Get next available index
    start_index = get_next_index(output_dir)
    
    # Generate samples
    print(f"\n🚀 Generating {num_samples} new samples...")
    stats = generate_training_dataset_enhanced(
        num_samples=num_samples,
        output_dir=output_dir,
        start_index=start_index,
        mask_threshold=mask_threshold,
        shape_variation=shape_variation
    )
    
    # Show updated status
    print(f"\n📊 Updated Dataset Status:")
    check_dataset_status(output_dir)
    
    return stats

# Example usage functions
def example_usage():
    print("📝 Example Usage:")
    print()
    print("# Check current dataset status:")
    print("check_dataset_status('training_dataset_fixed')")
    print()
    print("# Get next available index:")
    print("next_idx = get_next_index('training_dataset_fixed')")
    print()
    print("# Generate 500 more samples automatically:")
    print("stats = generate_more_samples(500)")
    print()
    print("# Generate with specific parameters:")
    print("stats = generate_more_samples(")
    print("    num_samples=300,")
    print("    mask_threshold=0.4,  # Smaller masks")
    print("    shape_variation=0.3  # More elliptical spots")
    print(")")

if __name__ == "__main__":
    example_usage()
# Fix for missing functions - add these to your notebook

def initialize_model_refined(resume_from_checkpoint=None):
    """Initialize model with resume capability"""
    from OptimizedSpotDetection_model import SkeletonAwareSpotDetector  # Replace with your model
    
    model = SkeletonAwareSpotDetector(in_ch=1, base_ch=48)
    start_epoch = 0
    best_val_loss = float('inf')
    optimizer_state = None
    
    if resume_from_checkpoint and os.path.exists(resume_from_checkpoint):
        checkpoint = torch.load(resume_from_checkpoint, map_location='cpu')
        model.load_state_dict(checkpoint['model_state_dict'])
        
        if 'epoch' in checkpoint:
            start_epoch = checkpoint['epoch'] + 1
        if 'best_val_loss' in checkpoint:
            best_val_loss = checkpoint['best_val_loss']
        if 'optimizer_state_dict' in checkpoint:
            optimizer_state = checkpoint['optimizer_state_dict']
            
        print(f"Resumed from epoch {start_epoch}, best val loss: {best_val_loss:.4f}")
    else:
        print("Starting training from scratch")
    
    return model, start_epoch, best_val_loss, optimizer_state

def train_skeleton_aware_model_ultra_refined(model, train_loader, val_loader, num_epochs, device, 
                                           start_epoch=0, best_val_loss=float('inf'), optimizer_state=None):
    """Ultra-refined training function"""
    from torch.amp import GradScaler, autocast
    from torch.optim.lr_scheduler import CosineAnnealingWarmRestarts
    from tqdm import tqdm
    
    # Compile model
    if hasattr(torch, 'compile'):
        model = torch.compile(model, mode='reduce-overhead')
    
    # Your loss function here
    criterion = SkeletonAwareLoss()  # Replace with your loss
    scaler = GradScaler('cuda')
    
    optimizer = torch.optim.AdamW(
        model.parameters(), 
        lr=1e-4,
        weight_decay=1e-2,
        betas=(0.9, 0.999),
        fused=True
    )
    
    if optimizer_state:
        optimizer.load_state_dict(optimizer_state)
    
    scheduler = CosineAnnealingWarmRestarts(optimizer, T_0=20, T_mult=2, eta_min=1e-6)
    
    model.to(device)
    train_losses, val_losses = [], []
    patience = 25
    early_stop_counter = 0
    
    for epoch in range(start_epoch, num_epochs):
        model.train()
        epoch_losses = []
        
        pbar = tqdm(train_loader, desc=f"Epoch {epoch+1}/{num_epochs}")
        for images, targets in pbar:
            images = images.to(device, non_blocking=True)
            targets = targets.to(device, non_blocking=True)
            
            optimizer.zero_grad(set_to_none=True)
            
            with autocast('cuda'):
                outputs = model(images)
                loss, loss_dict = criterion(outputs, targets)
            
            if torch.isnan(loss):
                continue
            
            scaler.scale(loss).backward()
            scaler.unscale_(optimizer)
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            scaler.step(optimizer)
            scaler.update()
            scheduler.step()
            
            epoch_losses.append(loss.item())
            pbar.set_postfix({'Loss': f'{loss.item():.4f}', 'LR': f'{scheduler.get_last_lr()[0]:.2e}'})
        
        train_losses.append(np.mean(epoch_losses))
        
        # Validation
        model.eval()
        val_epoch_losses = []
        
        with torch.no_grad():
            for images, targets in val_loader:
                images = images.to(device, non_blocking=True)
                targets = targets.to(device, non_blocking=True)
                
                with autocast('cuda'):
                    outputs = model(images)
                    loss, _ = criterion(outputs, targets)
                
                if not torch.isnan(loss):
                    val_epoch_losses.append(loss.item())
        
        val_losses.append(np.mean(val_epoch_losses))
        
        print(f"Epoch {epoch+1}: Train={train_losses[-1]:.4f}, Val={val_losses[-1]:.4f}")
        
        # Save best model
        if val_losses[-1] < best_val_loss:
            best_val_loss = val_losses[-1]
            torch.save({
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'scheduler_state_dict': scheduler.state_dict(),
                'epoch': epoch,
                'best_val_loss': best_val_loss,
                'train_losses': train_losses,
                'val_losses': val_losses
            }, 'best_model_ultra_refined.pth')
            
            print(f"✅ Best model saved (val_loss={best_val_loss:.4f})")
            early_stop_counter = 0
        else:
            early_stop_counter += 1
            if early_stop_counter >= patience:
                print(f"⚠️ Early stopping after {patience} epochs")
                break
    
    return model, train_losses, val_losses

# Quick fix - add this to your notebook cell:
import os
import torch
import numpy as np

# Set your checkpoint path
resume_checkpoint = '/mnt/d/Users/<USER>/Documents/spot detector git amazon 2025/skeleton_aware_model_improved/best_model.pth'

# Initialize model
model, start_epoch, best_val_loss, optimizer_state = initialize_model_refined(resume_checkpoint)

print(f"Model initialized successfully!")
print(f"Starting from epoch: {start_epoch}")
print(f"Best validation loss: {best_val_loss:.4f}")
# %% [markdown]
# 3D Spot Detection — Enhanced Refactor (Jupyter-friendly Python script)
# Updated for separate 'images' and 'masks' folders, with instance mask annotations.
# %%
# -----------------------------
# CELL 0 — USER PARAMETERS (Updated Paths and Tolerance)
# -----------------------------
import os
from pathlib import Path # Using pathlib for more robust path handling

# --- Data Paths ---
DATA_ROOT_DIR = Path("/mnt/c/Users/<USER>/Desktop/Spot_Detector_Stardist/Data/Antho") # Parent folder
# Using pathlib for robust path joining
IMAGE_DIR = DATA_ROOT_DIR / "X spot detector only"
MASK_DIR = DATA_ROOT_DIR / "Y spot detector only"
# Assuming masks have the same base name as images
ANNOT_EXT = ".tif" # Instance masks are .tif files

# --- Device & Training Parameters ---
DEVICE = None # 'cuda:0' or None to auto-select
BATCH_SIZE = 2 # training batch (patches)
PATCH_SIZE = (32, 128, 128) # (Z, Y, X)
OVERLAP = (8, 32, 32)
NUM_EPOCHS = 300
NUM_WORKERS = 6
PIN_MEMORY = True
USE_AMP = True # Automatic Mixed Precision
LEARNING_RATE = 1e-4

# --- Model Parameters ---
BASE_CH = 16
USE_RESIDUAL = True
USE_SE = True

# --- Loss Weights ---
W_SEM = 1.0
W_CENTS = [1.0, 0.8, 0.6] # per-scale weights
W_FLOW = 1.0

# --- Spot Characteristics & Matching ---
SIGMA_Z = 2.0 # Gaussian sigma for spots in Z (voxels)
SIGMA_XY = 1.0 # Gaussian sigma for spots in X/Y (voxels)
# Physical dimensions (for accurate matching and scaling)
PHYSICAL_PIXEL_SIZE_XY_MICROMETERS = 0.1030866 # Physical size of a pixel in X/Y (micrometers)
Z_SCALE = 2.5 # Anisotropy (Physical Z step size / Physical X/Y pixel size)
# Matching Tolerance
VAL_TOLERANCE = 3.0 # Matching tolerance in physical units (micrometers)
# VAL_TOLERANCE_VOXELS_XY is calculated based on the representative PHYSICAL_PIXEL_SIZE_XY_MICROMETERS.
# For images with different pixel sizes, recalculate tolerance_vox_xy = VAL_TOLERANCE / actual_pixel_size_xy_um
VAL_TOLERANCE_VOXELS_XY = VAL_TOLERANCE / PHYSICAL_PIXEL_SIZE_XY_MICROMETERS
print(f"Validation Tolerance: {VAL_TOLERANCE} µm -> {VAL_TOLERANCE_VOXELS_XY:.2f} XY voxels "
      f"(using pixel size {PHYSICAL_PIXEL_SIZE_XY_MICROMETERS} µm/px)")

# --- Post-processing Parameters ---
MIN_DISTANCE = (5, 7, 7) # Minimum distance between detections (Z, Y, X) for peak finding
SEMANTIC_THRESHOLD = 0.5 # Threshold for semantic mask binarization
FLOW_MAX_STEPS = 100     # Max steps for flow following
FLOW_STEP_SIZE = 1.0     # Step size for flow following
DBSCAN_EPS = 2.0         # DBSCAN eps for clustering flow endpoints
DBSCAN_MIN_SAMPLES = 3   # DBSCAN min_samples for clustering
TTA = True               # Use Test-Time Augmentation

# --- Data Sampling ---
POSITIVE_PATCH_PROB = 0.7 # Probability of sampling a patch centered on a spot

# --- Multi-scale Heads ---
CENTROID_SIGMAS = [0.8, 1.6, 3.2] # Sigmas for multi-scale centroid heatmaps (voxels)

# --- Output & Checkpointing ---
SAVE_OUTPUT_DIR = "./results_spot3D"
PROGRESS_SAVE_DIR = os.path.join(SAVE_OUTPUT_DIR, "progress")
SAVE_PROGRESS_EVERY_N_EPOCHS = 10
SAVE_BEST_MODEL_BY_LOSS = True

# --- Misc ---
SEED = 42

# --- Ensure Output Directories Exist ---
os.makedirs(SAVE_OUTPUT_DIR, exist_ok=True)
os.makedirs(PROGRESS_SAVE_DIR, exist_ok=True)

# %%
# -----------------------------
# CELL 1 — IMPORTS & GLOBAL OPTS
# -----------------------------
import os
import time
from pathlib import Path
from typing import List, Tuple, Optional, Dict, Any
import numpy as np
import tifffile
from scipy.ndimage import gaussian_filter, maximum_filter, distance_transform_edt, map_coordinates
from scipy.spatial import cKDTree
from scipy.optimize import linear_sum_assignment # For matching
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
from torch.cuda.amp import autocast, GradScaler
from sklearn.cluster import DBSCAN
from skimage.feature import peak_local_max
from skimage.segmentation import clear_border,watershed
from skimage.measure import label, regionprops
from tqdm.auto import tqdm
import random

# Speed optimizations
torch.backends.cudnn.benchmark = True
if DEVICE is None:
    DEVICE = torch.device("cuda" if torch.cuda.is_available() else "cpu")
else:
    DEVICE = torch.device(DEVICE)
print(f"Using device: {DEVICE}")

# %%
# -----------------------------
# CELL 2 — UTILITIES
# -----------------------------
def set_seed(seed: int = 42):
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed_all(seed)

def normalize_volume(vol: np.ndarray) -> np.ndarray:
    vol = vol.astype(np.float32)
    m = vol.mean()
    s = vol.std()
    return (vol - m) / (s + 1e-8) if s > 1e-8 else vol - m

def pad_to_block(vol: np.ndarray, block: Tuple[int, int, int]) -> Tuple[np.ndarray, Tuple[Tuple[int, int], ...]]:
    z, y, x = vol.shape
    bz, by, bx = block
    pad_z = (bz - (z % bz)) % bz
    pad_y = (by - (y % by)) % by
    pad_x = (bx - (x % bx)) % bx
    pads = ((0, pad_z), (0, pad_y), (0, pad_x))
    vol_p = np.pad(vol, pads, mode='constant', constant_values=0)
    return vol_p, pads

def to_device(t: torch.Tensor, device: torch.device) -> torch.Tensor:
    return t.to(device, non_blocking=True)

def match_detections(gt_coords: np.ndarray, pred_coords: np.ndarray, tol_vox: float, z_scale: float = 1.0):
    """Match predicted and ground truth coordinates using Hungarian algorithm with tolerance."""
    # Handle empty cases
    if gt_coords.size == 0:
        return 0, len(pred_coords), 0, np.empty((0, 2), dtype=int), {}
    if pred_coords.size == 0:
        return 0, 0, len(gt_coords), np.empty((0, 2), dtype=int), {}

    # --- DO NOT PRE-SCALE COORDINATES FOR THE TREE ---
    # Keep coordinates in original voxel units for building the tree
    gt_coords_float = np.asarray(gt_coords, dtype=np.float64)
    pred_coords_float = np.asarray(pred_coords, dtype=np.float64)

    # --- Compute pairwise distances using an ANISOTROPIC APPROACH ---
    # Build cKDTree using UNSCALED ground truth coordinates
    tree_gt = cKDTree(gt_coords_float)

    # --- Find candidates within an enlarged isotropic radius ---
    # Use tol_vox * max(1.0, z_scale) as a heuristic upper bound for the isotropic query.
    isotropic_upper_bound = tol_vox * max(1.0, z_scale)

    # Query using the ISOTROPIC upper bound to get candidate neighbors
    dists_iso, indices = tree_gt.query(pred_coords_float, k=1, distance_upper_bound=isotropic_upper_bound)

    # --- Prepare cost matrix for Hungarian algorithm ---
    cost_matrix = np.full((len(gt_coords), len(pred_coords)), np.inf, dtype=np.float64)

    # --- Find valid matches using ANISOTROPIC distance ---
    # Check if isotropic distance is finite (found a neighbor within the loose bound)
    valid_by_iso_distance = ~np.isinf(dists_iso)
    # Check if the returned index is valid for the gt_coords array
    valid_by_index = (indices >= 0) & (indices < len(gt_coords))
    # Preliminary candidates based on isotropic check
    candidate_mask = valid_by_iso_distance & valid_by_index # <-- This is the correct variable name

    if np.any(candidate_mask):
        # --- Calculate precise ANISOTROPIC distances for candidates ---
        # Iterate only over the candidate pairs identified by candidate_mask
        candidate_pred_indices = np.where(candidate_mask)[0] # <-- Use candidate_mask here
        candidate_gt_indices = indices[candidate_mask]

        for pred_idx, gt_idx in zip(candidate_pred_indices, candidate_gt_indices):
            delta = pred_coords_float[pred_idx] - gt_coords_float[gt_idx]
            # Apply anisotropic scaling for distance calculation
            # Z difference is scaled, X/Y differences are not (implicitly scaled by 1.0)
            # delta_scaled represents the vector in physical units if z_scale is correctly calculated
            delta_scaled = np.array([delta[0] * z_scale, delta[1], delta[2]])
            # Calculate anisotropic Euclidean distance
            aniso_dist = np.linalg.norm(delta_scaled)

            # If within the specified tolerance, add to cost matrix
            if aniso_dist <= tol_vox:
                 cost_matrix[gt_idx, pred_idx] = aniso_dist


    # --- Check if any matches were found before assignment ---
    # If no finite costs exist, linear_sum_assignment might fail or produce empty assignment
    if not np.any(np.isfinite(cost_matrix)):
        # print(f"Debug: No valid matches found for matching. Cost matrix is all inf.")
        # Handle gracefully: no matches possible
        TP, FP, FN = 0, len(pred_coords), len(gt_coords)
        matches_array = np.empty((0, 2), dtype=int)
        details_dict = {
            'status': 'no_valid_matches',
            'cost_matrix_shape': cost_matrix.shape,
            'num_gt': len(gt_coords),
            'num_pred': len(pred_coords)
        }
        return TP, FP, FN, matches_array, details_dict

    # --- Solve assignment problem ---
    try:
        gt_indices_for_assignment, pred_indices_for_assignment = linear_sum_assignment(cost_matrix)

        # --- Determine final matches based on finite cost ---
        assigned_costs = cost_matrix[gt_indices_for_assignment, pred_indices_for_assignment]
        valid_assignments = np.isfinite(assigned_costs) # Cost was not inf

        final_gt_matches = gt_indices_for_assignment[valid_assignments]
        final_pred_matches = pred_indices_for_assignment[valid_assignments]

        matches_array = np.array(list(zip(final_gt_matches, final_pred_matches)), dtype=int)

        # --- Count TP, FP, FN ---
        TP = len(matches_array)
        FP = len(pred_coords) - TP
        FN = len(gt_coords) - TP

        details_dict = {
            'status': 'success',
            'cost_matrix_shape': cost_matrix.shape,
            'assigned_gt_indices': gt_indices_for_assignment,
            'assigned_pred_indices': pred_indices_for_assignment,
            'assigned_costs': assigned_costs,
            'valid_assignments_mask': valid_assignments
        }

    except ValueError as e:
        if "cost matrix is infeasible" in str(e).lower(): # Case insensitive check
            # print(f"Warning: linear_sum_assignment reported infeasible matrix: {e}")
            # This should ideally not happen if we checked for np.any(np.isfinite(cost_matrix))
            # But handle defensively
            TP, FP, FN = 0, len(pred_coords), len(gt_coords)
            matches_array = np.empty((0, 2), dtype=int)
            details_dict = {'status': 'infeasible_matrix_error', 'error': str(e)}
        else:
            # Re-raise unexpected errors
            raise e

    return TP, FP, FN, matches_array, details_dict


# %%
# -----------------------------
# CELL 3 — AUGMENTATIONS & SAMPLING
# -----------------------------
def simple_3d_augment(vol: np.ndarray, heatmaps: List[np.ndarray], flow: np.ndarray):
    """Perform in-place cheap augmentations valid for 3D volumes."""
    # Random flips (axes 1, 2, 3 correspond to Z, Y, X)
    if random.random() > 0.5:
        vol = np.flip(vol, axis=1).copy() # Z flip
        flow[0] = np.flip(flow[0], axis=0).copy() * -1 # Invert dz
        for i in range(len(heatmaps)):
            heatmaps[i] = np.flip(heatmaps[i], axis=0).copy()
    if random.random() > 0.5:
        vol = np.flip(vol, axis=2).copy() # Y flip
        flow[1] = np.flip(flow[1], axis=1).copy() * -1 # Invert dy
        for i in range(len(heatmaps)):
            heatmaps[i] = np.flip(heatmaps[i], axis=1).copy()
    if random.random() > 0.5:
        vol = np.flip(vol, axis=3).copy() # X flip
        flow[2] = np.flip(flow[2], axis=2).copy() * -1 # Invert dx
        for i in range(len(heatmaps)):
            heatmaps[i] = np.flip(heatmaps[i], axis=2).copy()
    # Additive Gaussian noise (small)
    if random.random() > 0.5:
        noise = np.random.normal(0, 0.01, vol.shape).astype(vol.dtype)
        vol += noise
    return vol, heatmaps, flow

class PatchSampler:
    """Balanced patch sampler: with probability p sample centered on a random centroid, else random crop."""
    def __init__(self, patch_size: Tuple[int, int, int], positive_prob: float = 0.7):
        self.patch_size = patch_size
        self.positive_prob = positive_prob

    def sample(self, vol_shape: Tuple[int, int, int], centroids: Optional[np.ndarray] = None) -> Tuple[int, int, int]:
        bz, by, bx = self.patch_size
        vz, vy, vx = vol_shape

        if centroids is not None and len(centroids) > 0 and random.random() < self.positive_prob:
            # Sample a centroid
            idx = random.randint(0, len(centroids) - 1)
            cz, cy, cx = centroids[idx] # These are float centroids
            # Calculate start positions, ensuring they are within bounds and are integers
            # Using int() truncates towards zero, which is usually fine for start indices.
            # Alternatively, you could use round() for rounding to nearest integer.
            start_z = int(max(0, min(cz - bz // 2, vz - bz)))
            start_y = int(max(0, min(cy - by // 2, vy - by)))
            start_x = int(max(0, min(cx - bx // 2, vx - bx)))
        else:
            # Random crop - random.randint already returns integers
            start_z = random.randint(0, max(0, vz - bz))
            start_y = random.randint(0, max(0, vy - by))
            start_x = random.randint(0, max(0, vx - bx))

        # Ensure the returned values are definitely integers (good practice)
        return int(start_z), int(start_y), int(start_x)


# %%
# -----------------------------
# CELL 4 — DATASET WITH MULTI-SCALE HEADS (Updated for separate mask folder)
# -----------------------------
# (Assumes necessary imports like Path, np, tifffile, regionprops, label, etc. are available from previous cells)

class Spots3DDataset(Dataset):
    def __init__(self, image_files: List[Path], mask_dir: str, patch_size=(32,128,128), sigma_z=2.0, sigma_xy=1.0,
                 sigmas=[0.8,1.6,3.2], augment=True, sampler=None, max_flow_radius=30.0):
        self.image_files = image_files
        self.mask_dir = mask_dir
        self.patch_size = tuple(patch_size)
        self.sigma_z = float(sigma_z)
        self.sigma_xy = float(sigma_xy)
        self.sigmas = sigmas
        self.augment = augment
        # Ensure sampler is initialized, using the global POSITIVE_PATCH_PROB if sampler is None
        # Make sure POSITIVE_PATCH_PROB is defined in the parameters cell (e.g., Cell 0)
        self.sampler = sampler if sampler is not None else PatchSampler(self.patch_size, POSITIVE_PATCH_PROB)
        self.max_flow_radius = max_flow_radius

    def __len__(self):
        # Arbitrary scaling for epochs based on the number of unique image files
        return len(self.image_files) * 10

    def _create_heatmap(self, coords: np.ndarray, shape: Tuple[int, ...], sigma: float) -> np.ndarray:
        """Creates an isotropic/anisotropic Gaussian heatmap for given coordinates."""
        heatmap = np.zeros(shape, dtype=np.float32)
        if coords.size == 0:
             return heatmap
        # Pre-calculate grid limits for efficiency
        sz_max = int(3 * self.sigma_z)
        sy_max = int(3 * self.sigma_xy)
        sx_max = int(3 * self.sigma_xy)

        for coord in coords:
            z, y, x = coord
            # Define local grid bounds, clamped to image dimensions
            gz_start = max(0, int(np.round(z)) - sz_max)
            gz_end = min(shape[0], int(np.round(z)) + sz_max + 1)
            gy_start = max(0, int(np.round(y)) - sy_max)
            gy_end = min(shape[1], int(np.round(y)) + sy_max + 1)
            gx_start = max(0, int(np.round(x)) - sx_max)
            gx_end = min(shape[2], int(np.round(x)) + sx_max + 1)

            if gz_end > gz_start and gy_end > gy_start and gx_end > gx_start:
                # Create relative coordinate grids for the local region
                zz, yy, xx = np.mgrid[gz_start:gz_end, gy_start:gy_end, gx_start:gx_end]
                zz_rel = zz - z
                yy_rel = yy - y
                xx_rel = xx - x
                # Calculate Gaussian value using anisotropic sigma
                val = np.exp(-(zz_rel**2 / (2 * self.sigma_z**2) +
                               yy_rel**2 / (2 * self.sigma_xy**2) +
                               xx_rel**2 / (2 * self.sigma_xy**2)))
                # Use maximum projection to handle overlapping Gaussians from nearby points
                heatmap[gz_start:gz_end, gy_start:gy_end, gx_start:gx_end] = np.maximum(
                    heatmap[gz_start:gz_end, gy_start:gy_end, gx_start:gx_end], val
                )
        return heatmap

    def _create_flow(self, coords: np.ndarray, shape: Tuple[int, ...]) -> np.ndarray:
        """Creates a flow field pointing towards given coordinates."""
        flow = np.zeros((3,) + shape, dtype=np.float32) # (dz, dy, dx)
        if coords.size == 0:
            return flow

        # Create a coordinate grid for the entire volume
        coords_grid = np.mgrid[0:shape[0], 0:shape[1], 0:shape[2]].astype(np.float32)

        for coord in coords:
            z, y, x = coord
            # Calculate displacement vectors from every point to the current coord
            dz = z - coords_grid[0]
            dy = y - coords_grid[1]
            dx = x - coords_grid[2]
            dist_sq = dz**2 + dy**2 + dx**2

            # Create a mask to avoid division by zero and limit influence range
            mask = (dist_sq > 0) & (dist_sq < self.max_flow_radius**2)
            # Accumulate normalized displacement vectors where mask is True
            # np.where is used to apply the calculation only where mask is True
            flow[0] += np.where(mask, dz / (dist_sq + 1e-8), 0)
            flow[1] += np.where(mask, dy / (dist_sq + 1e-8), 0)
            flow[2] += np.where(mask, dx / (dist_sq + 1e-8), 0)
        # Note: Flow vectors are accumulated. No further magnitude capping here as it's done during creation.
        return flow

    def __getitem__(self, idx):
        """Loads and processes a patch from an image file and its corresponding mask."""
        # Determine which image file to use for this index
        file_idx = idx % len(self.image_files)
        image_path = self.image_files[file_idx]

        # --- Load Image Volume ---
        vol = tifffile.imread(str(image_path)).astype(np.float32)
        if vol.ndim == 4:
            vol = vol[0] # Assume first channel is the image of interest

        # --- Load Instance Segmentation Mask and Derive Centroids ---
        # Initialize centroids as an empty array. Crucial for PatchSampler and logic below.
        centroids = np.zeros((0, 3), dtype=np.float32)

        # Derive the corresponding mask path
        mask_filename = image_path.name # Assumes mask has the same base name
        mask_path = Path(self.mask_dir) / mask_filename

        # Attempt to load the mask and calculate centroids if it exists
        if mask_path.exists():
            try:
                inst_map = tifffile.imread(str(mask_path))
                # Analyze connected components in the instance mask
                props = regionprops(label(inst_map))
                # Extract centroids if any objects are found
                if props:
                    centroids = np.array([p.centroid for p in props], dtype=np.float32)
                    # Optional: Filter based on area or other properties
                    # areas = np.array([p.area for p in props])
                    # valid_mask = areas > some_min_area_threshold
                    # centroids = centroids[valid_mask]
                # else: centroids remains the empty (0,3) array
            except Exception as e:
                print(f"Warning: Could not load/process mask {mask_path}: {e}")
                # centroids remains the empty (0,3) array if loading/processing fails

        # --- Sample Patch ---
        # Use the full-volume centroids for sampling. The sampler handles empty centroids.
        start_z, start_y, start_x = self.sampler.sample(vol.shape, centroids)
        # Ensure start coordinates are integers (defensive programming)
        start_z, start_y, start_x = int(start_z), int(start_y), int(start_x)

        bz, by, bx = self.patch_size
        end_z, end_y, end_x = start_z + bz, start_y + by, start_x + bx

        # Extract the image patch
        patch_vol = vol[start_z:end_z, start_y:end_y, start_x:end_x]

        # --- Adjust Coordinates and Create Targets ---
        # Adjust centroids to be relative to the sampled patch coordinates
        if centroids.size > 0:
            patch_centroids = centroids - np.array([start_z, start_y, start_x], dtype=np.float32)
            # Filter out centroids that fall outside the boundaries of the patch
            mask_in_patch = (
                (patch_centroids[:, 0] >= 0) & (patch_centroids[:, 0] < bz) &
                (patch_centroids[:, 1] >= 0) & (patch_centroids[:, 1] < by) &
                (patch_centroids[:, 2] >= 0) & (patch_centroids[:, 2] < bx)
            )
            patch_centroids = patch_centroids[mask_in_patch]
        else:
            # If no centroids in the full volume, the patch also has none
            patch_centroids = centroids # Shape (0, 3)

        # --- Create Ground Truth Target Maps ---
        # Semantic target: 1.0 if patch contains any spots, 0.0 otherwise (scalar per patch)
        semantic_target = float(patch_centroids.size > 0)

        # Multi-scale centroid heatmaps
        centroid_heatmaps = [self._create_heatmap(patch_centroids, (bz, by, bx), s) for s in self.sigmas]

        # Flow target field
        flow_target = self._create_flow(patch_centroids, (bz, by, bx))

        # --- Preprocessing ---
        # Normalize the image patch
        patch_vol = normalize_volume(patch_vol)

        # Apply data augmentations if enabled
        if self.augment:
            # Ensure simple_3d_augment is defined in an earlier cell (e.g., Cell 3)
            # It should handle the list of heatmaps and the flow field correctly
            patch_vol, centroid_heatmaps, flow_target = simple_3d_augment(
                patch_vol[None, ...], centroid_heatmaps, flow_target # Add temporary batch dim
            )
            patch_vol = patch_vol[0] # Remove the temporary batch dimension

        # --- Convert to PyTorch Tensors ---
        # Image patch: Add channel dimension -> (1, Z, Y, X)
        patch_vol_t = torch.from_numpy(patch_vol).unsqueeze(0)

        # Semantic target: Scalar value -> (1,) tensor
        semantic_target_t = torch.tensor(semantic_target, dtype=torch.float32)

        # Centroid heatmaps: List of arrays (Z, Y, X) -> List of tensors (1, Z, Y, X)
        centroid_heatmaps_t = [torch.from_numpy(ch).unsqueeze(0) for ch in centroid_heatmaps]

        # Flow target: Array (3, Z, Y, X) -> Tensor (3, Z, Y, X)
        flow_target_t = torch.from_numpy(flow_target)

        # --- Return ---
        # Return the patch tensor and a dictionary of target tensors
        return patch_vol_t, {
            'semantic': semantic_target_t,
            'centroid_multi': centroid_heatmaps_t,
            'flow': flow_target_t
        }


# %%
# -----------------------------
# CELL 5 — MODEL: Optimized 3D U-Net with Residuals and SE
# -----------------------------
class SEBlock3D(nn.Module):
    """Squeeze-and-Excitation Block for 3D"""
    def __init__(self, channel, reduction=16):
        super(SEBlock3D, self).__init__()
        self.avg_pool = nn.AdaptiveAvgPool3d(1)
        self.fc = nn.Sequential(
            nn.Linear(channel, channel // reduction, bias=False),
            nn.ReLU(inplace=True),
            nn.Linear(channel // reduction, channel, bias=False),
            nn.Sigmoid()
        )

    def forward(self, x):
        b, c, _, _, _ = x.size()
        y = self.avg_pool(x).view(b, c)
        y = self.fc(y).view(b, c, 1, 1, 1)
        return x * y.expand_as(x)

class ResBlock3D(nn.Module):
    def __init__(self, in_c, out_c, ks=3, padding=1, use_se=False):
        super().__init__()
        self.conv1 = nn.Conv3d(in_c, out_c, kernel_size=ks, padding=padding, bias=False)
        self.gn1 = nn.GroupNorm(min(8, out_c), out_c) # GroupNorm is good for small batches
        self.relu = nn.ReLU(inplace=True)
        self.conv2 = nn.Conv3d(out_c, out_c, kernel_size=ks, padding=padding, bias=False)
        self.gn2 = nn.GroupNorm(min(8, out_c), out_c)

        # Shortcut connection
        self.shortcut = nn.Identity()
        if in_c != out_c:
            self.shortcut = nn.Conv3d(in_c, out_c, kernel_size=1, bias=False)
            
        self.use_se = use_se
        if self.use_se:
            self.se = SEBlock3D(out_c)

    def forward(self, x):
        residual = self.shortcut(x)
        out = self.conv1(x)
        out = self.gn1(out)
        out = self.relu(out)
        out = self.conv2(out)
        out = self.gn2(out)
        if self.use_se:
            out = self.se(out)
        out += residual
        out = self.relu(out)
        return out

class UNet3D_MultiCent(nn.Module):
    def __init__(self, in_ch=1, base_ch=16, num_cent_heads=3, use_residual=True, use_se=False):
        super().__init__()
        self.use_residual = use_residual
        self.base_ch = base_ch
        chs = [base_ch, base_ch * 2, base_ch * 4, base_ch * 8] # Define channel sizes

        # Encoder
        Block = ResBlock3D if use_residual else ConvBlock3D # Fallback to ConvBlock if not residual
        self.enc1 = Block(in_ch, chs[0], use_se=use_se)
        self.enc2 = Block(chs[0], chs[1], use_se=use_se)
        self.enc3 = Block(chs[1], chs[2], use_se=use_se)
        self.pool = nn.MaxPool3d(2)

        # Bottleneck
        self.bottleneck = Block(chs[2], chs[3], use_se=use_se)

        # Decoder (with correct padding for skip connections)
        self.up3 = nn.ConvTranspose3d(chs[3], chs[2], kernel_size=2, stride=2)
        self.dec3 = Block(chs[2] * 2, chs[2], use_se=use_se) # *2 for skip connection
        self.up2 = nn.ConvTranspose3d(chs[2], chs[1], kernel_size=2, stride=2)
        self.dec2 = Block(chs[1] * 2, chs[1], use_se=use_se)
        self.up1 = nn.ConvTranspose3d(chs[1], chs[0], kernel_size=2, stride=2)
        self.dec1 = Block(chs[0] * 2, chs[0], use_se=use_se)

        # Heads
        self.head_sem = nn.Conv3d(chs[0], 1, kernel_size=1)
        self.head_cent = nn.ModuleList([nn.Conv3d(chs[0], 1, kernel_size=1) for _ in range(num_cent_heads)])
        self.head_flow = nn.Conv3d(chs[0], 3, kernel_size=1) # dz, dy, dx

    def forward(self, x):
        # Encoder
        e1 = self.enc1(x)
        e2 = self.enc2(self.pool(e1))
        e3 = self.enc3(self.pool(e2))

        # Bottleneck
        b = self.bottleneck(self.pool(e3))

        # Decoder with explicit padding for skip connections
        def crop_and_concat(down_layer, up_layer):
            # Calculate padding needed
            diffZ = down_layer.size()[2] - up_layer.size()[2]
            diffY = down_layer.size()[3] - up_layer.size()[3]
            diffX = down_layer.size()[4] - up_layer.size()[4]
            # Pad up_layer to match down_layer size
            up_layer = F.pad(up_layer, [
                diffX // 2, diffX - diffX // 2,
                diffY // 2, diffY - diffY // 2,
                diffZ // 2, diffZ - diffZ // 2
            ])
            return torch.cat([down_layer, up_layer], dim=1)

        d3 = self.up3(b)
        d3 = crop_and_concat(e3, d3)
        d3 = self.dec3(d3)

        d2 = self.up2(d3)
        d2 = crop_and_concat(e2, d2)
        d2 = self.dec2(d2)

        d1 = self.up1(d2)
        d1 = crop_and_concat(e1, d1)
        d1 = self.dec1(d1)

        # Heads
        sem_logits = self.head_sem(d1)
        cent_logits = [head(d1) for head in self.head_cent]
        flow_pred = self.head_flow(d1)

        return sem_logits, cent_logits, flow_pred

# --- Define the ConvBlock3D if not using residual as fallback ---
class ConvBlock3D(nn.Module):
    def __init__(self, in_c, out_c, ks=3, padding=1):
        super().__init__()
        self.net = nn.Sequential(
            nn.Conv3d(in_c, out_c, kernel_size=ks, padding=padding, bias=False),
            nn.GroupNorm(min(8, out_c), out_c),
            nn.ReLU(inplace=True),
            nn.Conv3d(out_c, out_c, kernel_size=ks, padding=padding, bias=False),
            nn.GroupNorm(min(8, out_c), out_c),
            nn.ReLU(inplace=True),
        )
    def forward(self, x):
        return self.net(x)


# %%
# -----------------------------
# CELL 6 — LOSS & TRAINING HELPERS (focal, dice, flow-weighting)
# -----------------------------
import torch.nn.functional as F # Make sure F is imported if not already

class FocalLoss(nn.Module):
    def __init__(self, alpha=0.25, gamma=2.0, reduction='mean'):
        super().__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.reduction = reduction

    def forward(self, inputs, targets):
        BCE_loss = F.binary_cross_entropy_with_logits(inputs, targets, reduction='none')
        pt = torch.exp(-BCE_loss)
        F_loss = self.alpha * (1-pt)**self.gamma * BCE_loss

        if self.reduction == 'mean':
            return torch.mean(F_loss)
        elif self.reduction == 'sum':
            return torch.sum(F_loss)
        else:
            return F_loss

def dice_loss_logits(logits, targets, eps=1e-6):
    """Calculate Dice loss for binary segmentation from logits."""
    probs = torch.sigmoid(logits)
    # Flatten for batch-wise calculation: (B, 1, D, H, W) -> (B, D*H*W)
    probs_flat = probs.view(probs.size(0), -1)
    targets_flat = targets.view(targets.size(0), -1)
    intersection = (probs_flat * targets_flat).sum(dim=1)
    dice_numerator = 2 * intersection
    dice_denominator = probs_flat.sum(dim=1) + targets_flat.sum(dim=1)
    dice_score = (dice_numerator + eps) / (dice_denominator + eps)
    return 1 - dice_score.mean()

class MultiHeadLossV2(nn.Module):
    """
    Loss function for the multi-head 3D U-Net.
    Expects:
    - predictions: (sem_logits, [cent_logits_1, ...], flow_logits)
    - targets: {'semantic': tensor, 'centroid_multi': [tensor_1, ...], 'flow': tensor}
    Based on the knowledge base:
    - y_sem is a scalar (B,) indicating if the patch has any spots.
    - y_cents are spatial maps (B, 1, D, H, W).
    - y_flow is a spatial map (B, 3, D, H, W).
    - sem_p from model is likely (B, 1, D, H, W) - spatial map.
    """
    def __init__(self, w_sem=1.0, w_cents=[1.0, 0.8, 0.6], w_flow=1.0,
                 focal_alpha=0.25, focal_gamma=2.0, use_dice=False):
        super().__init__()
        self.w_sem = w_sem
        self.w_cents = w_cents
        self.w_flow = w_flow
        self.focal = FocalLoss(alpha=focal_alpha, gamma=focal_gamma)
        self.mse = nn.MSELoss(reduction='none') # Per-element for weighting
        self.use_dice = use_dice
        # Add a pooling layer to convert spatial semantic map to scalar if needed
        self.sem_pool = nn.AdaptiveAvgPool3d((1, 1, 1)) # Pools (B, 1, D, H, W) -> (B, 1, 1, 1, 1)

    def forward(self, predictions, targets):
        sem_p, cents_p, flow_p = predictions
        y_sem = targets['semantic'] # Expected shape: (B,) - scalar per patch
        y_cents = targets['centroid_multi'] # List of tensors, each (B, 1, D, H, W)
        y_flow = targets['flow']   # Expected shape: (B, 3, D, H, W)

        # --- Semantic Loss ---
        # The model outputs sem_p likely as (B, 1, D, H, W) - a spatial map.
        # The target y_sem is (B,) - a scalar per patch.
        # We need to reduce sem_p to (B,) for the Focal Loss.

        # Ensure sem_p is the right shape for pooling (B, 1, D, H, W)
        if sem_p.dim() == 5 and sem_p.shape[1] == 1:
            # Correct shape for pooling: (B, 1, D, H, W)
            sem_p_scalar = self.sem_pool(sem_p).view(-1) # (B, 1, 1, 1, 1) -> (B,)
        elif sem_p.dim() == 5 and sem_p.shape[1] != 1:
            # Unexpected channel dim, perhaps sum across channels first?
            # This is less likely but handle defensively
            print(f"Warning: Unexpected semantic pred channel dim {sem_p.shape}. Averaging channels.")
            sem_p_pooled = F.adaptive_avg_pool3d(sem_p, (1, 1, 1)) # (B, C, 1, 1, 1)
            sem_p_scalar = torch.mean(sem_p_pooled, dim=1).view(-1) # (B, 1, 1, 1) -> (B,)
        elif sem_p.dim() == 1:
            # Already (B,) - unlikely from KB model, but handle
            sem_p_scalar = sem_p
        else:
            # Unexpected shape
            raise ValueError(f"Unexpected semantic prediction shape for loss: {sem_p.shape}")

        # Focal Loss for patch-level semantic classification (scalar per patch)
        sem_loss_bce = self.focal(sem_p_scalar, y_sem.float()) # Inputs: (B,), Targets: (B,)

        if self.use_dice:
            # Dice loss for spatial semantic map.
            # y_sem needs to be a spatial map too. KB uses scalar.
            # If y_sem was spatial (B, 1, D, H, W), we could use:
            # sem_loss_dice = dice_loss_logits(sem_p, y_sem)
            # sem_loss = sem_loss_bce + sem_loss_dice
            # But with scalar y_sem, Dice is not directly applicable here without modification.
            print("Warning: Dice loss requested but target is scalar. Using Focal/BCE only for semantic.")
            sem_loss = sem_loss_bce
        else:
            sem_loss = sem_loss_bce

        # --- Centroid Loss (Multi-scale MSE) ---
        cent_loss = 0.0
        # Ensure self.w_cents length matches the number of centroid heads
        if len(self.w_cents) != len(cents_p):
             print(f"Warning: Mismatch in centroid head weights ({len(self.w_cents)}) and predictions ({len(cents_p)}). Using equal weights.")
             weights = [1.0 / len(cents_p) for _ in cents_p] if len(cents_p) > 0 else []
        else:
             weights = self.w_cents

        for i, (cp, yc) in enumerate(zip(cents_p, y_cents)):
             # Ensure shapes match (they should be (B, 1, D, H, W))
             # The KB model produces (B, 1, D, H, W), Dataset should provide targets of same shape.
             w = weights[i] if i < len(weights) else 1.0 / len(cents_p) if len(cents_p) > 0 else 0
             cent_loss += w * F.mse_loss(cp, yc)

        # --- Flow Loss (Weighted MSE) ---
        # Weight the flow loss by the ground truth centroid heatmap.
        # This focuses the flow learning on areas where spots are present.
        # Use the first (likely finest scale) ground truth centroid map as the weight.
        # y_cents is a list of target heatmaps [(B, 1, D, H, W), ...]
        if len(y_cents) > 0:
            # Use the first ground truth centroid heatmap as spatial weight
            weight_map = y_cents[0] # Shape: (B, 1, D, H, W)
        else:
            # Fallback if no centroids (shouldn't happen if loss is computed)
            weight_map = torch.ones_like(y_flow[:, 0:1, :, :, :], device=y_flow.device) # Shape: (B, 1, D, H, W)

        # Ensure weight_map has the same spatial dims as flow_p and y_flow
        # flow_p and y_flow are (B, 3, D, H, W)
        # weight_map is (B, 1, D, H, W) - needs to be broadcastable or expanded
        # MSE loss is computed per element
        flow_loss_per_element = self.mse(flow_p, y_flow) # Shape: (B, 3, D, H, W)

        # Expand weight_map to match flow channels for weighting
        # weight_map: (B, 1, D, H, W) -> (B, 3, D, H, W) by expanding
        if weight_map.shape[1] == 1 and flow_loss_per_element.shape[1] == 3:
             # Use expand_as for clarity and safety
             weight_map = weight_map.expand_as(flow_loss_per_element) # (B, 1, D, H, W) -> (B, 3, D, H, W)

        # Apply weight and calculate mean
        weighted_flow_loss = (flow_loss_per_element * weight_map).mean()

        # --- Total Loss ---
        total_loss = self.w_sem * sem_loss + cent_loss + self.w_flow * weighted_flow_loss
        return total_loss

def train_epoch(model, loader, optimizer, device, scaler: Optional[GradScaler], loss_fn):
    """
    Training loop for one epoch.
    Handles AMP, loss calculation, and optimizer step.
    """
    model.train()
    total_loss = 0.0
    n = 0
    progress_bar = tqdm(loader, desc='Train', leave=False)
    for x, y in progress_bar:
        x = to_device(x, device)
        # y['semantic'] is (B,) - scalar
        # y['centroid_multi'] is List[(B, 1, D, H, W)]
        # y['flow'] is (B, 3, D, H, W)
        y_sem = to_device(y['semantic'], device)
        y_cents = [to_device(h, device) for h in y['centroid_multi']]
        y_flow = to_device(y['flow'], device)

        optimizer.zero_grad(set_to_none=True) # More efficient than zero_grad()
        
        # Determine device type string for torch.amp.autocast
        device_type = 'cuda' if 'cuda' in str(device) else 'cpu'
        with torch.amp.autocast(device_type=device_type, enabled=USE_AMP): # Corrected line
            sem_p, cents_p, flow_p = model(x)
            # Predictions from KB model:
            # sem_p: (B, 1, D, H, W) - spatial map (this was causing the issue)
            # cents_p: List[(B, 1, D, H, W)]
            # flow_p: (B, 3, D, H, W)

            # The corrected loss_fn now correctly handles sem_p being (B, 1, D, H, W)
            loss = loss_fn((sem_p, cents_p, flow_p), {'semantic': y_sem, 'centroid_multi': y_cents, 'flow': y_flow})

        if scaler is not None:
            scaler.scale(loss).backward()
            scaler.step(optimizer)
            scaler.update()
        else:
            loss.backward()
            optimizer.step()

        total_loss += float(loss.detach().cpu())
        n += 1
        progress_bar.set_postfix({'loss': f"{total_loss / max(n, 1):.4f}"})

    avg_loss = total_loss / max(n, 1)
    return avg_loss

# %%
# -----------------------------
# CELL 7 — SLIDING WINDOW INFERENCE + TTA (Optimized)
# -----------------------------
def sliding_window_patches(vol: np.ndarray, patch_size: Tuple[int,int,int], overlap: Tuple[int,int,int]):
    bz, by, bx = patch_size
    ovz, ovy, ovx = overlap
    vol_p, pads = pad_to_block(vol, (bz, by, bx))
    Zp, Yp, Xp = vol_p.shape
    
    # Pre-calculate step sizes to avoid repeated calculation
    step_z = bz - ovz
    step_y = by - ovy
    step_x = bx - ovx
    
    # Ensure step sizes are positive
    step_z = max(1, step_z)
    step_y = max(1, step_y)
    step_x = max(1, step_x)
    
    z_steps = list(range(0, Zp - bz + 1, step_z))
    y_steps = list(range(0, Yp - by + 1, step_y))
    x_steps = list(range(0, Xp - bx + 1, step_x))
    
    # Handle potential edge case where volume size is exactly divisible
    if z_steps and z_steps[-1] + bz < Zp:
        z_steps.append(Zp - bz)
    if y_steps and y_steps[-1] + by < Yp:
        y_steps.append(Yp - by)
    if x_steps and x_steps[-1] + bx < Xp:
        x_steps.append(Xp - bx)
        
    for z in z_steps:
        for y in y_steps:
            for x in x_steps:
                yield (z, y, x, bz, by, bx, pads)

def predict_volume_sliding(model: nn.Module, vol: np.ndarray, device: torch.device,
                           patch_size=(32,128,128), overlap=(8,32,32), batch_size=4, tta=False):
    model.eval()
    # TTA: build list of transforms (identity + flips)
    transforms = [lambda x: x]
    inv_transforms = [lambda x: x]
    if tta:
        # Flips along Z, Y, X axes
        transforms += [
            lambda x: np.flip(x, axis=0), # Z
            lambda x: np.flip(x, axis=1), # Y
            lambda x: np.flip(x, axis=2), # X
        ]
        inv_transforms += [
            lambda x: np.flip(x, axis=0), # Z
            lambda x: np.flip(x, axis=1), # Y
            lambda x: np.flip(x, axis=2), # X
        ]

    accum_sem = None
    accum_cent = None
    accum_flow = None
    n_tta = len(transforms)

    with torch.no_grad():
        for i, (tr, inv_tr) in enumerate(zip(transforms, inv_transforms)):
            vol_t = tr(vol.copy())
            vol_p, pads = pad_to_block(vol_t, patch_size)
            Zp, Yp, Xp = vol_p.shape

            out_sem = np.zeros((Zp, Yp, Xp), dtype=np.float32)
            out_cent = np.zeros_like(out_sem)
            out_flow = np.zeros((3, Zp, Yp, Xp), dtype=np.float32)
            counts = np.zeros_like(out_sem)

            patches = list(sliding_window_patches(vol_t, patch_size, overlap))

            # --- Batch Processing Loop ---
            for idx in range(0, len(patches), batch_size):
                batch_patches = patches[idx:idx+batch_size]
                imgs = []
                coords_list = []
                for z,y,x,bz,by,bx,_ in batch_patches:
                    patch_img = vol_p[z:z+bz, y:y+by, x:x+bx]
                    imgs.append(patch_img)
                    coords_list.append((z,y,x,bz,by,bx))

                inp_np = np.stack(imgs, axis=0)[:, None, ...] # Add channel dim
                inp_t = torch.from_numpy(inp_np.astype(np.float32)).to(device, non_blocking=True)

                # Determine device type string for torch.amp.autocast
                device_type = 'cuda' if 'cuda' in str(device) else 'cpu'
                with torch.amp.autocast(device_type=device_type, enabled=USE_AMP): # Corrected line
                    sem_p, cents_p, flow_p = model(inp_t)
                    # Process outputs
                    sem_np = torch.sigmoid(sem_p).detach().cpu().numpy()[:, 0] # Sigmoid + squeeze channel
                    # Average multi-scale centroid predictions
                    cent_np = np.mean([torch.sigmoid(cp).detach().cpu().numpy()[:, 0] for cp in cents_p], axis=0)
                    flow_np = flow_p.detach().cpu().numpy() # (B, 3, D, H, W)

                # Accumulate results
                for j, (z,y,x,bz,by,bx) in enumerate(coords_list):
                    s = sem_np[j]
                    c = cent_np[j]
                    f = flow_np[j]

                    # Inverse transform predictions
                    s = inv_tr(s)
                    c = inv_tr(c)
                    f = inv_tr(f) # Note: Flow components might need sign flip on flip, but simple flip works for magnitude accumulation

                    out_sem[z:z+bz, y:y+by, x:x+bx] += s
                    out_cent[z:z+bz, y:y+by, x:x+bx] += c
                    out_flow[:, z:z+bz, y:y+by, x:x+bx] += f
                    counts[z:z+bz, y:y+by, x:x+bx] += 1

            # Normalize by counts
            counts_safe = np.maximum(counts, 1.0)
            out_sem /= counts_safe
            out_cent /= counts_safe
            out_flow /= counts_safe

            # Crop to original unpadded size
            out_sem = out_sem[:vol_t.shape[0], :vol_t.shape[1], :vol_t.shape[2]]
            out_cent = out_cent[:vol_t.shape[0], :vol_t.shape[1], :vol_t.shape[2]]
            out_flow = out_flow[:, :vol_t.shape[0], :vol_t.shape[1], :vol_t.shape[2]]

            # Inverse transform (undo initial TTA transform)
            out_sem = inv_tr(out_sem)
            out_cent = inv_tr(out_cent)
            out_flow = inv_tr(out_flow) # Check flow component signs if issues

            if accum_sem is None:
                accum_sem = out_sem
                accum_cent = out_cent
                accum_flow = out_flow
            else:
                accum_sem += out_sem
                accum_cent += out_cent
                accum_flow += out_flow

    # Average TTA predictions
    accum_sem /= n_tta
    accum_cent /= n_tta
    accum_flow /= n_tta

    return accum_sem, accum_cent, accum_flow


# %%
# -----------------------------
# CELL 8 — POSTPROCESSING: peaks, improved flow clustering, seeded watershed
# -----------------------------
def detect_peaks_3d_maxfilter(heatmap: np.ndarray, min_distance=(5,7,7), threshold_abs=0.5):
    mz, my, mx = min_distance
    footprint = np.ones((2*mz+1, 2*my+1, 2*mx+1), dtype=bool)
    local_max = (maximum_filter(heatmap, footprint=footprint) == heatmap) & (heatmap >= threshold_abs)
    coords = np.array(np.nonzero(local_max)).T # (N, 3) - (z, y, x)
    return coords

# --- Optimized flow_follow_endpoints (Reduced steps, potential vectorization idea) ---
def flow_follow_endpoints(flow: np.ndarray, semantic: np.ndarray, threshold: float = 0.5,
                          max_steps:int=50, step_size: float=1.0): # Use optimized parameters
    """Follow flow vectors from seed points to endpoints."""
    mask = semantic >= threshold
    coords = np.array(np.nonzero(mask)).astype(np.float32) # (3, N) -> (N, 3) might be easier
    if coords.size == 0:
        return np.zeros((0,3), dtype=np.float32)
    
    # Transpose for easier handling (N, 3)
    pts = coords.T.copy() # (3, N) -> (N, 3)
    coords_shape = semantic.shape
    
    for _ in range(max_steps):
        # Vectorized interpolation
        # map_coordinates expects (ndim, nsamples) -> pts.T is (3, N)
        dz = map_coordinates(flow[0], pts.T, order=1, mode='nearest', prefilter=False) # prefilter=False can speed up
        dy = map_coordinates(flow[1], pts.T, order=1, mode='nearest', prefilter=False)
        dx = map_coordinates(flow[2], pts.T, order=1, mode='nearest', prefilter=False)
        
        # Update points
        pts[:, 0] += dz * step_size # Z
        pts[:, 1] += dy * step_size # Y
        pts[:, 2] += dx * step_size # X
        
        # Optional: Add simple boundary checks to prevent runaway points
        # This is a basic check, more sophisticated clamping might be needed
        pts[:, 0] = np.clip(pts[:, 0], 0, coords_shape[0] - 1.01) # Z
        pts[:, 1] = np.clip(pts[:, 1], 0, coords_shape[1] - 1.01) # Y
        pts[:, 2] = np.clip(pts[:, 2], 0, coords_shape[2] - 1.01) # X

    return pts # Return (N, 3)

def nms_points(coords: np.ndarray, scores: np.ndarray, min_distance_phys: Tuple[float, float, float], z_scale: float):
    """
    Applies Non-Maximum Suppression to 3D points based on physical distance.

    This function is designed for speed and accuracy in 3D spot detection.
    It uses a KDTree for efficient neighbor finding.

    Args:
        coords (np.ndarray): Array of point coordinates, shape (N, 3). Order is (Z, Y, X).
        scores (np.ndarray): Array of confidence scores for each point, shape (N,).
                         Higher scores are kept.
        min_distance_phys (Tuple[float, float, float]): Minimum allowed distance
            in physical units (Z, Y, X). Points closer than this in physical space
            will be suppressed, keeping the one with the higher score.
        z_scale (float): The Z anisotropy factor (Physical Z pixel size / Physical X/Y pixel size).
                     Used to scale Z coordinates for physical distance calculation.

    Returns:
        np.ndarray: Boolean array of shape (N,), indicating which points are kept (True) or suppressed (False).
    """
    if coords.size == 0:
        return np.array([], dtype=bool)

    N = coords.shape[0]
    if N == 1:
        return np.array([True])

    # 1. Sort indices by descending score (highest first)
    sorted_indices = np.argsort(-scores) # Negative for descending order
    sorted_coords = coords[sorted_indices]
    
    # 2. Scale coordinates to physical space for distance calculation
    # Apply z_scale only to the Z dimension
    scaled_coords = sorted_coords.copy().astype(np.float64) # Use float64 for precision in distance calc
    scaled_coords[:, 0] *= z_scale # Scale Z coordinate
    
    # 3. Define the physical suppression radius (half of min_distance)
    # We use half because we check if distance < radius for suppression
    radius_z, radius_y, radius_x = min_distance_phys[0] / 2.0, min_distance_phys[1] / 2.0, min_distance_phys[2] / 2.0
    
    # 4. Build KDTree for efficient neighbor search in physical space
    tree = cKDTree(scaled_coords)
    
    # 5. Suppress points
    keep = np.ones(N, dtype=bool) # Initially, keep all points
    
    for i in range(N):
        if not keep[i]: # If already suppressed, skip
            continue
            
        # Current point's physical coordinates
        current_point = scaled_coords[i]
        
        # Define anisotropic search bounds for potential neighbors
        # We search within a box that encompasses the ellipsoidal suppression region
        # This is an approximation to speed up the initial query.
        # A more precise check is done with the actual distance.
        z_min = current_point[0] - radius_z
        z_max = current_point[0] + radius_z
        y_min = current_point[1] - radius_y
        y_max = current_point[1] + radius_y
        x_min = current_point[2] - radius_x
        x_max = current_point[2] + radius_x
        
        # Query the tree for neighbors within this box
        # cKDTree doesn't directly support anisotropic queries, so we query a cube
        # that encloses our ellipsoid and then filter.
        max_radius = max(radius_z, radius_y, radius_x)
        neighbor_indices = tree.query_ball_point(current_point, max_radius)
        
        # Iterate through potential neighbors (must come after 'i' in sorted list to be suppressed)
        for j in neighbor_indices:
            if j <= i: # Only suppress points that come later in the sorted list (lower scores)
                continue
            if not keep[j]: # If neighbor already suppressed, skip
                continue

            # 6. Precise Anisotropic Distance Check
            # Calculate the actual anisotropic distance between point i and j
            delta = scaled_coords[i] - scaled_coords[j]
            # Normalize by the radius components (this is equivalent to checking if point is inside ellipsoid)
            dist_norm = np.sqrt( (delta[0] / radius_z)**2 + (delta[1] / radius_y)**2 + (delta[2] / radius_x)**2 )
            
            # If normalized distance is less than 1, they are within the ellipsoidal suppression zone
            if dist_norm < 1.0:
                keep[j] = False # Suppress the lower-scoring point

    # 7. Map the keep mask back to the original order
    # Create a result array for the original indices
    original_keep = np.zeros(N, dtype=bool)
    original_keep[sorted_indices] = keep
    
    return original_keep

def cluster_instances_from_offsets(offset_vectors: np.ndarray, coords: np.ndarray,
                                   z_scale: float = 1.0, eps: float = 2.0, min_samples: int = 3):
    """Cluster instance IDs based on predicted offset vectors."""
    if coords.size == 0:
        return np.zeros((0,3), dtype=float), np.array([], dtype=int)

    # Predicted instance center for each point: coord + offset
    predicted_centers = coords + offset_vectors

    # Scale Z for physical distance clustering
    predicted_centers_scaled = predicted_centers.copy()
    predicted_centers_scaled[:, 0] *= z_scale

    # Cluster predicted centers
    clustering = DBSCAN(eps=eps, min_samples=min_samples).fit(predicted_centers_scaled)
    labels = clustering.labels_

    # Calculate final centroids for each cluster
    unique_labels = np.unique(labels[labels >= 0])
    final_centroids = []
    if len(unique_labels) > 0:
        for lab in unique_labels:
            member_centers = predicted_centers[labels == lab]
            final_centroids.append(np.mean(member_centers, axis=0))

    return np.array(final_centroids, dtype=np.float32), labels

def flow_displacement_and_cluster_3d(semantic: np.ndarray, centroid_heatmap: np.ndarray,
                                    flow: np.ndarray, z_scale: float = 1.0,
                                    sem_thresh: float = 0.5, max_steps:int=50, # Use optimized param
                                    step_size: float = 1.0, eps: float = 2.0,
                                    min_samples:int = 3):
    """Perform instance segmentation using flow."""
    # Mask for semantic foreground points
    mask = semantic >= sem_thresh
    coords = np.array(np.nonzero(mask)).astype(np.float32) # (3, N) -> (N, 3)
    
    if coords.size == 0:
        return np.zeros_like(semantic, dtype=np.int32), np.zeros((0, 3), dtype=np.float32)

    # Sample flow vectors at the coordinates
    offset_vectors = np.zeros_like(coords, dtype=np.float32) # (N, 3)
    offset_vectors[:, 0] = map_coordinates(flow[0], coords.T, order=1, mode='nearest')
    offset_vectors[:, 1] = map_coordinates(flow[1], coords.T, order=1, mode='nearest')
    offset_vectors[:, 2] = map_coordinates(flow[2], coords.T, order=1, mode='nearest')

    # Cluster based on predicted offsets
    centroids, labels = cluster_instances_from_offsets(offset_vectors, coords, z_scale=z_scale, eps=eps, min_samples=min_samples)

    # Create instance map
    inst_map = np.zeros_like(semantic, dtype=np.int32)
    if coords.shape[0] == 0 or labels.size == 0 or len(centroids) == 0:
        return inst_map, np.zeros((0, 3), dtype=np.float32) # Return empty centroids if none found

    # Map labels to instance IDs
    unique_valid_labels = np.unique(labels[labels >= 0])
    if len(unique_valid_labels) > 0:
        label_map = {old_label: new_id + 1 for new_id, old_label in enumerate(unique_valid_labels)}
        for (z, y, x), lab in zip(coords, labels):
            if lab >= 0:
                inst_map[z, y, x] = label_map[lab]

    return inst_map, centroids

def seeded_watershed_instances(semantic_prob: np.ndarray, seeds: np.ndarray, threshold: float = 0.2):
    # semantic_prob: (Z,Y,X) in [0,1]; seeds: (K,3) centroids in voxel coords
    # produce voxel markers
    markers = np.zeros_like(semantic_prob, dtype=np.int32)
    for i, (z,y,x) in enumerate(seeds):
        zi, yi, xi = int(round(z)), int(round(y)), int(round(x))
        if 0 <= zi < markers.shape[0] and 0 <= yi < markers.shape[1] and 0 <= xi < markers.shape[2]:
            markers[zi, yi, xi] = i + 1 # 1-indexed labels

    # mask for watershed
    mask = semantic_prob > threshold
    if not np.any(mask):
        return markers # Return empty map if no foreground

    # distance transform on mask
    dist = distance_transform_edt(mask)
    # Avoid watershed if no markers
    if markers.max() > 0:
        labels_ws = watershed(-dist, markers=markers, mask=mask)
    else:
        labels_ws = np.zeros_like(markers)
    return labels_ws


# %%
# -----------------------------
# CELL 9 — I/O helpers (save results AS .tif)
# -----------------------------
def save_results(volume_path: str, sem_logits: np.ndarray, cent_logits: np.ndarray,
                 flow: np.ndarray, instance_map: np.ndarray, centroids: np.ndarray,
                 out_dir: str = SAVE_OUTPUT_DIR):
    """
    Saves results. Main output as OME-TIFF, centroids as a separate point cloud TIFF.
    """
    p = Path(volume_path)
    stem = p.stem
    out_path_main = Path(out_dir) / f"{stem}_results.ome.tif"
    out_path_centroids = Path(out_dir) / f"{stem}_centroids.tif" # Changed extension

    # Stack main results: semantic prob, centroid prob, flow dz, dy, dx, instance map
    imgs = [
        sem_logits.astype(np.float32),
        cent_logits.astype(np.float32),
        flow[0].astype(np.float32),
        flow[1].astype(np.float32),
        flow[2].astype(np.float32),
        instance_map.astype(np.uint32)
    ]
    arr_main = np.stack(imgs, axis=0) # (C, Z, Y, X)
    # Save main results
    tifffile.imwrite(str(out_path_main), arr_main, imagej=False, bigtiff=True, compression='zlib')
    print(f"Saved main results (6 channels: sem, cent, flow_z, flow_y, flow_x, instances) to {out_path_main}")

    # Save centroids as a point cloud TIFF
    if centroids.size > 0 and centroids.ndim == 2 and centroids.shape[1] == 3:
        tifffile.imwrite(str(out_path_centroids), centroids.astype(np.float32), bigtiff=False)
        print(f"Saved centroids (N x 3 array) to {out_path_centroids}")
    elif centroids.size == 0:
        empty_centroids = np.empty((0, 3), dtype=np.float32)
        tifffile.imwrite(str(out_path_centroids), empty_centroids, bigtiff=False)
        print(f"Saved empty centroids placeholder to {out_path_centroids}")
    else:
        print(f"Warning: Centroids format unexpected for saving as .tif. Shape: {centroids.shape}. Skipping centroid .tif save.")




# %%
# -----------------------------
# CELL 9b — PROGRESS SAVING HELPER (Fixed for float16 error)
# -----------------------------
import numpy as np # Make sure np is imported if not already in the cell
import torch
import os
from pathlib import Path
from typing import Union # Add if needed
# Assuming tifffile, watershed, distance_transform_edt, detect_peaks_3d_maxfilter are imported elsewhere

# Ensure these constants are accessible (e.g., from Cell 0 or global scope)
# MIN_DISTANCE, USE_AMP should be defined

def save_training_progress(epoch: int, model: nn.Module, loader: DataLoader, device: torch.device,
                           save_dir: str, num_samples: int = 2):
    """
    Saves example predictions (images, outputs, segmentations) during training for QC.
    Addresses potential float16 dtype issues when saving with tifffile.
    """
    model.eval()
    # Create epoch-specific directory
    epoch_dir = os.path.join(save_dir, f"epoch_{epoch:04d}")
    os.makedirs(epoch_dir, exist_ok=True)

    # Get a small batch of data
    data_iter = iter(loader)
    try:
        x_sample, y_sample = next(data_iter)
    except StopIteration:
        print(f"Warning: Could not get sample data for progress save at epoch {epoch}.")
        model.train() # Ensure model is set back to train mode
        return

    # Move input data to device
    x_sample = x_sample[:num_samples].to(device, non_blocking=True)

    with torch.no_grad():
        # Use torch.amp.autocast correctly (addressing previous warnings)
        # Determine device type string for torch.amp.autocast
        device_type = 'cuda' if 'cuda' in str(device) else 'cpu'
        with torch.amp.autocast(device_type=device_type, enabled=USE_AMP): # Corrected line
            sem_p, cents_p, flow_p = model(x_sample)

        # --- Process and Convert Tensors to NumPy (ensuring float32) ---
        # Ensure tensors are moved to CPU and converted to numpy float32
        input_img_np = x_sample.cpu().to(dtype=torch.float32).numpy() # (N, 1, Z, Y, X)

        # Target Outputs
        y_sem_np = y_sample['semantic'][:num_samples].cpu().to(dtype=torch.float32).numpy() # Ensure float32
        # Take first centroid target map for visualization
        y_cent_np = y_sample['centroid_multi'][0][:num_samples].cpu().to(dtype=torch.float32).numpy() # Ensure float32
        y_flow_np = y_sample['flow'][:num_samples].cpu().to(dtype=torch.float32).numpy() # Ensure float32

        # Predicted Outputs (convert to float32 numpy)
        pred_sem_np = torch.sigmoid(sem_p).cpu().to(dtype=torch.float32).numpy() # (N, 1, Z, Y, X)
        
        # Centroids: Average multi-scale predictions
        if isinstance(cents_p, list) and len(cents_p) > 0:
            # Average predictions from all heads, ensure float32 at each step
            pred_cent_np_list = [torch.sigmoid(cp).cpu().to(dtype=torch.float32).numpy() for cp in cents_p]
            pred_cent_np = np.mean(pred_cent_np_list, axis=0, dtype=np.float64) # Use float64 for mean calculation
            pred_cent_np = pred_cent_np.astype(np.float32) # Cast final result to float32
        else:
            pred_cent_np = torch.sigmoid(cents_p).cpu().to(dtype=torch.float32).numpy()
            
        pred_flow_np = flow_p.cpu().to(dtype=torch.float32).numpy() # (N, 3, Z, Y, X)

        # --- Save each sample in the batch ---
        for i in range(x_sample.shape[0]): # Iterate over batch dimension N
            sample_dir = os.path.join(epoch_dir, f"sample_{i:02d}")
            os.makedirs(sample_dir, exist_ok=True)

            # Determine slice index
            mid_z_input = input_img_np.shape[2] // 2 # Z is dim 2 in (N, C, Z, H, W)
            mid_z_target = y_cent_np.shape[2] // 2
            mid_z_pred = pred_cent_np.shape[2] // 2
            # Use the minimum mid_z to avoid index errors if shapes differ slightly
            mid_z = min(mid_z_input, mid_z_target, mid_z_pred)

            # 1. Input Image (save a central Z slice)
            input_vol = input_img_np[i, 0] # (Z, Y, X)
            # Ensure slice is float32 before saving
            tifffile.imwrite(os.path.join(sample_dir, "input_image_slice.tif"), input_vol[mid_z].astype(np.float32))

            # 2. Target Outputs (save a central Z slice)
            # Semantic target
            if y_sem_np.ndim > 1 and y_sem_np.shape[1] == 1: # Assume (N, 1, ...) -> spatial
                 y_sem_vol = y_sem_np[i, 0] # (Z, Y, X)
                 tifffile.imwrite(os.path.join(sample_dir, "target_semantic_slice.tif"), y_sem_vol[mid_z].astype(np.float32))
            else: # Assume scalar (N,) or (N, 1) with size 1
                np.savetxt(os.path.join(sample_dir, "target_semantic_scalar.txt"), [y_sem_np[i]], fmt='%.6f')

            y_cent_vol = y_cent_np[i, 0] # (Z, Y, X)
            tifffile.imwrite(os.path.join(sample_dir, "target_centroid_slice.tif"), y_cent_vol[mid_z].astype(np.float32))

            # Flow target (save magnitude of 2D slice for visualization)
            y_flow_vol = y_flow_np[i] # (3, Z, Y, X)
            # Ensure input to np.linalg.norm is float32 to prevent float16 output
            y_flow_vol_f32 = y_flow_vol.astype(np.float32)
            flow_mag_slice = np.linalg.norm(y_flow_vol_f32[:, mid_z, :, :], axis=0) # Result should be f32
            # Explicitly ensure the result is float32 before saving
            tifffile.imwrite(os.path.join(sample_dir, "target_flow_magnitude_slice.tif"), flow_mag_slice.astype(np.float32))

            # 3. Predicted Outputs (save a central Z slice)
            pred_sem_vol = pred_sem_np[i, 0] # (Z, Y, X)
            tifffile.imwrite(os.path.join(sample_dir, "pred_semantic_slice.tif"), pred_sem_vol[mid_z].astype(np.float32))

            pred_cent_vol = pred_cent_np[i, 0] # (Z, Y, X)
            tifffile.imwrite(os.path.join(sample_dir, "pred_centroid_slice.tif"), pred_cent_vol[mid_z].astype(np.float32))

            # Flow prediction (save magnitude of 2D slice for visualization)
            pred_flow_vol = pred_flow_np[i] # (3, Z, Y, X)
            # Ensure input to np.linalg.norm is float32 to prevent float16 output
            pred_flow_vol_f32 = pred_flow_vol.astype(np.float32)
            pred_flow_mag_slice = np.linalg.norm(pred_flow_vol_f32[:, mid_z, :, :], axis=0) # Result should be f32
            # Explicitly ensure the result is float32 before saving
            tifffile.imwrite(os.path.join(sample_dir, "pred_flow_magnitude_slice.tif"), pred_flow_mag_slice.astype(np.float32))

            # Saving predicted centroids
            tifffile.imwrite(os.path.join(sample_dir, "pred_centroid_heatmap.tif"), pred_cent_np[i].astype(np.float32))
            
            # 4. Simple Instance Segmentation Example (Optional, basic)
            try:
                # Ensure pred_cent_vol is float32 for peak detection
                peaks = detect_peaks_3d_maxfilter(pred_cent_vol.astype(np.float32), min_distance=MIN_DISTANCE, threshold_abs=0.5)
                if peaks.size > 0:
                    markers = np.zeros_like(pred_sem_vol[mid_z], dtype=np.int32)
                    for j, (pz, py, px) in enumerate(peaks):
                        if abs(pz - mid_z) <= 2:
                            py_i, px_i = int(round(py)), int(round(px))
                            if 0 <= py_i < markers.shape[0] and 0 <= px_i < markers.shape[1]:
                                markers[py_i, px_i] = j + 1
                    
                    mask_ws = pred_sem_vol[mid_z] > 0.5
                    if np.any(mask_ws) and np.any(markers > 0):
                        # Ensure mask and markers are standard dtypes
                        mask_ws = mask_ws.astype(bool)
                        dist_ws = distance_transform_edt(mask_ws.astype(np.float32)) # Ensure float32 input
                        labels_ws = watershed(-dist_ws.astype(np.float32), markers=markers, mask=mask_ws) # Ensure float32
                        tifffile.imwrite(os.path.join(sample_dir, "pred_instance_seg_slice.tif"), labels_ws.astype(np.int32))
                    else:
                        tifffile.imwrite(os.path.join(sample_dir, "pred_instance_seg_slice.tif"), np.zeros_like(pred_sem_vol[mid_z], dtype=np.int32))
                else:
                    tifffile.imwrite(os.path.join(sample_dir, "pred_instance_seg_slice.tif"), np.zeros_like(pred_sem_vol[mid_z], dtype=np.int32))
            except Exception as e:
                print(f"Warning: Could not generate instance seg example for sample {i} at epoch {epoch}: {e}")
                tifffile.imwrite(os.path.join(sample_dir, "pred_instance_seg_slice.tif"), np.zeros_like(pred_sem_vol[mid_z], dtype=np.int32))

    print(f"Saved training progress for epoch {epoch} to {epoch_dir}")
    model.train() # Set model back to train mode


# %%
# -----------------------------
# CELL 10 — VALIDATION METRICS & LOOP (with Debugging)
# -----------------------------
def detections_from_heatmap(centroid_heatmap: np.ndarray, min_distance=(5,7,7), threshold_abs=0.4, z_scale_for_nms=Z_SCALE):
    """
    Detects peaks and applies Non-Maximum Suppression.
    Assumes min_distance is in physical units (voxels * physical pixel size).
    """
    # 1. Detect initial peaks using max filter
    coords = detect_peaks_3d_maxfilter(centroid_heatmap, min_distance=min_distance, threshold_abs=threshold_abs)
    
    if coords.size == 0:
        return coords # Return empty array if no peaks

    # 2. Get scores for each peak (value of the heatmap at that point)
    # Ensure coords are integer indices for accessing the heatmap array
    coords_int = np.round(coords).astype(int)
    # Clip coordinates to be within array bounds to prevent index errors
    coords_int[:, 0] = np.clip(coords_int[:, 0], 0, centroid_heatmap.shape[0] - 1)
    coords_int[:, 1] = np.clip(coords_int[:, 1], 0, centroid_heatmap.shape[1] - 1)
    coords_int[:, 2] = np.clip(coords_int[:, 2], 0, centroid_heatmap.shape[2] - 1)
    
    scores = centroid_heatmap[coords_int[:, 0], coords_int[:, 1], coords_int[:, 2]]

    # 3. Apply NMS
    # min_distance is already in physical units (voxels), so pass it directly
    # z_scale_for_nms should be the Z_SCALE for the current image
    keep_mask = nms_points(coords, scores, min_distance, z_scale_for_nms)
    
    # 4. Return filtered coordinates
    return coords[keep_mask]

def evaluate_detection_on_volume(gt_centroids: np.ndarray, pred_centroids: np.ndarray, tol_physical: float, z_scale: float=1.0):
    """Evaluate detection performance for one volume."""
    # --- Debugging: Print shapes and parameters ---
    print(f"Debug (Match): gt_centroids shape: {gt_centroids.shape}, pred_centroids shape: {pred_centroids.shape}")
    print(f"Debug (Match): tol_physical (should be VAL_TOLERANCE_VOXELS_XY): {tol_physical:.2f}, z_scale: {z_scale:.4f}")
    # --- End Debugging ---

    # The tol_physical passed in should already be in voxel units (VAL_TOLERANCE_VOXELS_XY)
    # No need to convert again if VAL_TOLERANCE_VOXELS_XY is passed correctly.
    tol_vox_xy = tol_physical # Assuming tol_physical is already in voxels

    TP, FP, FN, matches, matdict = match_detections(gt_centroids, pred_centroids, tol_vox=tol_vox_xy, z_scale=z_scale)
    
    # --- Debugging: Print match results ---
    print(f"Debug (Match): TP={TP}, FP={FP}, FN={FN}")
    # --- End Debugging ---

    prec = TP / (TP + FP) if (TP + FP) > 0 else 0.0
    rec = TP / (TP + FN) if (TP + FN) > 0 else 0.0
    f1 = 2 * prec * rec / (prec + rec) if (prec + rec) > 0 else 0.0
    return {'TP': TP, 'FP': FP, 'FN': FN, 'precision': prec, 'recall': rec, 'f1': f1}

def validate_epoch(model, val_image_files: List[Path], mask_dir: str, device: torch.device, patch_size, overlap, z_scale, tol_physical, tta=False):
    """Perform validation on a list of images."""
    model.eval()
    results = []
    # Use a simple range for tqdm to easily index if needed, or stick with files
    progress_bar = tqdm(enumerate(val_image_files), desc='Validation', total=len(val_image_files))
    
    for idx, image_path in progress_bar: # Iterate over image paths with index
        try:
            # --- Load and Preprocess Volume ---
            vol = tifffile.imread(str(image_path)).astype(np.float32)
            if vol.ndim == 4:
                vol = vol[0] # Assume first channel
            vol = normalize_volume(vol)

            # --- Predict ---
            sem_logits, cent_logits, flow = predict_volume_sliding(
                model, vol, device, patch_size=patch_size, overlap=overlap, 
                batch_size=BATCH_SIZE, tta=tta
            )
            
            # --- Detect Centroids with NMS ---
            # Use the average of multi-scale centroid maps for detection
            if isinstance(cent_logits, list):
                 avg_cent_logits = np.mean([ch for ch in cent_logits], axis=0)
            else:
                 avg_cent_logits = cent_logits

            # Pass the image-specific z_scale to NMS
            preds = detections_from_heatmap(
                avg_cent_logits, 
                min_distance=MIN_DISTANCE, 
                threshold_abs=0.5, # Use the higher threshold based on previous analysis
                z_scale_for_nms=z_scale # Pass z_scale for accurate NMS
            )
            
            # --- Debugging: Print prediction info ---
            print(f"Debug (Val {idx:02d}): Processing {image_path.name}")
            print(f"Debug (Val {idx:02d}): Predicted centroids shape (after NMS): {preds.shape}")
            # --- End Debugging ---

            # --- Load Ground Truth from Masks folder ---
            gt_centroids = np.zeros((0,3), dtype=np.float32) # Initialize empty
            mask_filename = image_path.name # Get image filename
            mask_path = Path(mask_dir) / mask_filename # Derive mask path
            
            if mask_path.exists():
                try:
                    gt_inst_map = tifffile.imread(str(mask_path))
                    props = regionprops(label(gt_inst_map))
                    gt_centroids = np.array([pp.centroid for pp in props], dtype=np.float32)
                    print(f"Debug (Val {idx:02d}): Loaded GT centroids from .tif mask: {gt_centroids.shape}")
                except Exception as e:
                    print(f"Warning: Could not load GT mask {mask_path} during validation: {e}")
            else:
                 print(f"Debug (Val {idx:02d}): GT mask file not found at {mask_path}")

            # --- Sanity Check Coordinate Loading (Debugging) ---
            # Print shapes and first few coordinates for the first validation image
            if idx == 0: # Print for the first validation image
                print(f"Debug (Val {idx:02d}): Sanity Check:")
                print(f"Debug (Val {idx:02d}):   Image shape (approx): {vol.shape}") # Approximate shape
                print(f"Debug (Val {idx:02d}):   First few GT coords (Z,Y,X): {gt_centroids[:3] if len(gt_centroids) > 0 else 'None'}")
                print(f"Debug (Val {idx:02d}):   First few Pred coords (Z,Y,X): {preds[:3] if len(preds) > 0 else 'None'}")
                # Check if coordinates seem within plausible bounds
                if len(gt_centroids) > 0:
                    print(f"Debug (Val {idx:02d}):   GT Coord Ranges - Z: [{gt_centroids[:, 0].min():.1f}, {gt_centroids[:, 0].max():.1f}], "
                          f"Y: [{gt_centroids[:, 1].min():.1f}, {gt_centroids[:, 1].max():.1f}], "
                          f"X: [{gt_centroids[:, 2].min():.1f}, {gt_centroids[:, 2].max():.1f}]")
                if len(preds) > 0:
                    print(f"Debug (Val {idx:02d}):   Pred Coord Ranges - Z: [{preds[:, 0].min():.1f}, {preds[:, 0].max():.1f}], "
                          f"Y: [{preds[:, 1].min():.1f}, {preds[:, 1].max():.1f}], "
                          f"X: [{preds[:, 2].min():.1f}, {preds[:, 2].max():.1f}]")
            # --- End Sanity Check Debugging ---

            # --- Check for empty cases before matching (Debugging) ---
            if preds.size == 0:
                print(f"Debug (Val {idx:02d}): No predictions for {image_path.name}. TP=0, FP=0, FN={len(gt_centroids)}.")
            if gt_centroids.size == 0:
                print(f"Debug (Val {idx:02d}): No ground truth for {image_path.name}. TP=0, FP={len(preds)}, FN=0.")
            # --- End Debugging ---

            # --- Evaluate ---
            # tol_physical passed should be VAL_TOLERANCE_VOXELS_XY
            m = evaluate_detection_on_volume(gt_centroids, preds, tol_physical=tol_physical, z_scale=z_scale)
            results.append(m)
            
            # Update progress bar
            current_f1 = np.mean([r['f1'] for r in results]) if results else 0.0
            progress_bar.set_postfix({'F1': f"{current_f1:.4f}"})
            
        except Exception as e:
            print(f"Error processing {image_path}: {e}")
            results.append({'TP': 0, 'FP': 0, 'FN': 0, 'precision': 0.0, 'recall': 0.0, 'f1': 0.0})

    # --- Aggregate Results ---
    if results:
        precision = np.mean([r['precision'] for r in results])
        recall = np.mean([r['recall'] for r in results])
        f1 = np.mean([r['f1'] for r in results])
        print(f"\nValidation Summary: Precision: {precision:.4f}, Recall: {recall:.4f}, F1: {f1:.4f}")
    else:
        precision, recall, f1 = 0.0, 0.0, 0.0
        print("\nValidation Summary: No results to aggregate.")

    return {'precision': precision, 'recall': recall, 'f1': f1, 'per_volume': results}

# %%
# -----------------------------
# CELL 11 — TRAIN/VALIDATE DRIVER (Updated for Resume Training)
# -----------------------------
# ... (previous setup code: imports, set_seed, finding files) ...

if __name__ == '__main__':
    set_seed(SEED)
    # --- Find image files (Updated for separate image/mask folders) ---
    image_files = sorted(list(Path(IMAGE_DIR).glob('*.tif')))
    if len(image_files) == 0:
        print('No volumes found in', IMAGE_DIR)
        exit()

    # split image files
    n_val = max(1, int(0.1 * len(image_files)))
    train_image_files = image_files[n_val:]
    val_image_files = image_files[:n_val]
    print(f"Train image files: {len(train_image_files)}, Val image files: {len(val_image_files)}")

    # --- Data (Updated to pass mask_dir) ---
    sampler_train = PatchSampler(PATCH_SIZE, POSITIVE_PATCH_PROB)
    sampler_val = PatchSampler(PATCH_SIZE, POSITIVE_PATCH_PROB) # Or 1.0 if you want to always sample positives for val?
    train_dataset = Spots3DDataset(
        train_image_files,
        mask_dir=MASK_DIR,
        patch_size=PATCH_SIZE,
        sigma_z=SIGMA_Z,
        sigma_xy=SIGMA_XY,
        sigmas=CENTROID_SIGMAS,
        augment=True,
        sampler=sampler_train
    )
    val_dataset = Spots3DDataset(
        val_image_files,
        mask_dir=MASK_DIR,
        patch_size=PATCH_SIZE,
        sigma_z=SIGMA_Z,
        sigma_xy=SIGMA_XY,
        sigmas=CENTROID_SIGMAS,
        augment=False,
        sampler=sampler_val
    )

    train_loader = DataLoader(train_dataset, batch_size=BATCH_SIZE, shuffle=True, num_workers=NUM_WORKERS, pin_memory=PIN_MEMORY, drop_last=True)
    val_loader = DataLoader(val_dataset, batch_size=max(1, BATCH_SIZE // 2), shuffle=False, num_workers=NUM_WORKERS, pin_memory=PIN_MEMORY)

    # --- Model ---
    model = UNet3D_MultiCent(in_ch=1, base_ch=BASE_CH, num_cent_heads=len(CENTROID_SIGMAS)).to(DEVICE)

    # Attempt to compile model for speed
    try:
        model = torch.compile(model)
        print("Model compiled successfully.")
    except Exception as e:
        print(f"Model compilation skipped or failed: {e}. Proceeding with standard model.")

    # --- Loss & Optimizer ---
    loss_fn = MultiHeadLossV2(w_sem=W_SEM, w_cents=W_CENTS, w_flow=W_FLOW, focal_alpha=0.25, focal_gamma=2.0)
    optimizer = torch.optim.AdamW(model.parameters(), lr=LEARNING_RATE, weight_decay=1e-4)

    # --- Scaler for AMP ---
    scaler = GradScaler() if USE_AMP else None # Using standard GradScaler, warnings are usually informational

    # --- Checkpointing Variables ---
    best_train_loss = float('inf')
    best_val_f1 = 0.0
    checkpoint_path_best_loss = Path(SAVE_OUTPUT_DIR) / "best_model_by_loss.pth"
    checkpoint_path_best_f1 = Path(SAVE_OUTPUT_DIR) / "best_model_by_f1.pth"

    # --- Resume Training Logic ---
    start_epoch = 0
    initial_train_loss = float('inf') # For printing initial best loss

    if checkpoint_path_best_loss.exists():
        print(f"Loading checkpoint from {checkpoint_path_best_loss} to resume training...")
        try:
            # Load checkpoint
            checkpoint = torch.load(checkpoint_path_best_loss, map_location=DEVICE,weights_only=False)
            
            # Load model state
            model.load_state_dict(checkpoint['model_state_dict'])
            print("Model state loaded.")
            
            # Load optimizer state
            if 'optimizer_state_dict' in checkpoint and checkpoint['optimizer_state_dict'] is not None:
                optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
                print("Optimizer state loaded.")
            else:
                print("Warning: Optimizer state not found in checkpoint.")
            
            # Load scaler state (for AMP)
            if USE_AMP and 'scaler_state_dict' in checkpoint and checkpoint['scaler_state_dict'] is not None:
                scaler.load_state_dict(checkpoint['scaler_state_dict'])
                print("Scaler (AMP) state loaded.")
            elif USE_AMP:
                print("Warning: Scaler state not found in checkpoint or was None.")
            
            # Get starting epoch and previous best loss
            start_epoch = checkpoint.get('epoch', -1) + 1 # Start from the next epoch
            # Retrieve the training loss from the checkpoint if available, otherwise use the saved one
            loaded_train_loss = checkpoint.get('loss', checkpoint.get('train_loss', float('inf'))) # Try different keys
            if loaded_train_loss != float('inf'):
                best_train_loss = loaded_train_loss
                initial_train_loss = best_train_loss
            # Also try to load the saved best_val_f1 if it was saved
            if 'val_f1' in checkpoint:
                best_val_f1 = checkpoint['val_f1']
            elif 'val_metrics' in checkpoint and 'f1' in checkpoint['val_metrics']:
                 best_val_f1 = checkpoint['val_metrics']['f1']
            
            print(f"Resumed from epoch {start_epoch}, previous best train loss: {initial_train_loss:.4f}, previous best val F1: {best_val_f1:.4f}")
        except Exception as e:
            print(f"Error loading checkpoint: {e}. Starting training from scratch.")
            start_epoch = 0
            best_train_loss = float('inf')
            best_val_f1 = 0.0
            initial_train_loss = float('inf')
    else:
        print(f"No checkpoint found at {checkpoint_path_best_loss}. Starting training from scratch.")
        initial_train_loss = float('inf')


    # --- Training Loop ---
    num_epochs_to_run = NUM_EPOCHS # Total epochs to run in this session
    print(f"Starting/resuming training for {num_epochs_to_run} epochs from epoch {start_epoch + 1}...")
    print(f"Initial best train loss: {initial_train_loss:.4f}")
    
    for epoch in range(start_epoch, start_epoch + num_epochs_to_run):
        current_epoch_number = epoch + 1 # For display (1-based)
        print(f"\nEpoch {current_epoch_number}/{start_epoch + num_epochs_to_run}")
        
        train_loss = train_epoch(model, train_loader, optimizer, DEVICE, scaler, loss_fn)
        print(f"Train Loss: {train_loss:.4f}")

        # --- Validation ---
        val_metrics = validate_epoch(
            model, val_image_files, MASK_DIR, DEVICE, PATCH_SIZE, OVERLAP, Z_SCALE, VAL_TOLERANCE_VOXELS_XY, tta=False
        )
        print(f"Val Precision: {val_metrics['precision']:.4f}, Recall: {val_metrics['recall']:.4f}, F1: {val_metrics['f1']:.4f}")

        # --- Save Best Model by Training Loss ---
        # Checkpoint based on the training loss of *this* epoch
        if SAVE_BEST_MODEL_BY_LOSS and train_loss < best_train_loss:
            best_train_loss = train_loss
            torch.save({
                'epoch': epoch, # Save the 0-based epoch index
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'scaler_state_dict': scaler.state_dict() if scaler else None,
                'loss': train_loss, # This is the training loss of the current best epoch
                'val_metrics': val_metrics,
                'val_f1': val_metrics['f1'], # Also save val F1 for convenience
            }, checkpoint_path_best_loss)
            print(f"New best model by TRAINING LOSS saved with loss: {best_train_loss:.4f}")

        # --- Save Best Model by Validation F1 ---
        if val_metrics['f1'] > best_val_f1:
            best_val_f1 = val_metrics['f1']
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'scaler_state_dict': scaler.state_dict() if scaler else None,
                'loss': train_loss, # Save current train loss for reference
                'val_f1': val_metrics['f1'],
                'val_metrics': val_metrics,
            }, checkpoint_path_best_f1)
            print(f"New best model by VALIDATION F1 saved with F1: {best_val_f1:.4f}")

        # --- Save Training Progress Images ---
        if (current_epoch_number) % SAVE_PROGRESS_EVERY_N_EPOCHS == 0 or epoch == start_epoch: # Save on first resumed epoch and every N epochs
            try:
                print(f"Saving training progress for epoch {current_epoch_number}...")
                save_training_progress(current_epoch_number, model, val_loader, DEVICE, PROGRESS_SAVE_DIR, num_samples=2)
            except Exception as e:
                print(f"Warning: Failed to save training progress for epoch {current_epoch_number}: {e}")

    print("Training finished.")




# %%
# -----------------------------
# CELL 11 — TRAIN/VALIDATE DRIVER (Corrected)
# -----------------------------
def main():
    set_seed(SEED)
    
    # --- Find image files (CORRECTED) ---
    image_files = sorted(list(Path(IMAGE_DIR).glob('*.tif'))) # Define image_files
    if len(image_files) == 0:
        print('No volumes found in', IMAGE_DIR) # Check IMAGE_DIR
        return

    # --- Split image files (CORRECTED variable names) ---
    n_val = max(1, int(0.1 * len(image_files)))
    train_image_files = image_files[n_val:] # Use image_files
    val_image_files = image_files[:n_val]   # Use image_files
    print(f"Train image files: {len(train_image_files)}, Val image files: {len(val_image_files)}")

    # --- Data (CORRECTED: Pass image_files lists and MASK_DIR) ---
    train_dataset = Spots3DDataset(train_image_files, mask_dir=MASK_DIR, patch_size=PATCH_SIZE, sigma_z=SIGMA_Z, sigma_xy=SIGMA_XY, sigmas=CENTROID_SIGMAS, augment=True)
    val_dataset = Spots3DDataset(val_image_files, mask_dir=MASK_DIR, patch_size=PATCH_SIZE, sigma_z=SIGMA_Z, sigma_xy=SIGMA_XY, sigmas=CENTROID_SIGMAS, augment=False)

    train_loader = DataLoader(train_dataset, batch_size=BATCH_SIZE, shuffle=True, num_workers=NUM_WORKERS, pin_memory=PIN_MEMORY, drop_last=True)
    # Note: val_loader not used as validate_epoch processes full volumes directly.
    # If used, it would be: val_loader = DataLoader(val_dataset, batch_size=1, shuffle=False, num_workers=NUM_WORKERS, pin_memory=PIN_MEMORY)

    # Model - Use the optimized version
    model = UNet3D_MultiCent(
        in_ch=1, 
        base_ch=BASE_CH, 
        num_cent_heads=len(CENTROID_SIGMAS),
        use_residual=USE_RESIDUAL, # Enable/disable residuals
        use_se=USE_SE # Enable/disable SE blocks
    ).to(DEVICE)
    
    # Attempt to compile model for speed (PyTorch 2.0+)
    try:
        model = torch.compile(model)
        print("Model compiled successfully for potential speedup.")
    except Exception as e:
        print(f"Model compilation skipped or failed: {e}. Proceeding with standard model.")

    # --- Loss & Optimizer ---
    loss_fn = MultiHeadLossV2(w_sem=W_SEM, w_cents=W_CENTS, w_flow=W_FLOW, use_dice=False)
    optimizer = torch.optim.AdamW(model.parameters(), lr=LEARNING_RATE, weight_decay=1e-4)

    # --- Scaler for AMP ---
    scaler = GradScaler() if USE_AMP else None   # New syntax if USE_AMP else None

    # --- Checkpointing ---
    best_f1 = 0.0
    checkpoint_path = Path(SAVE_OUTPUT_DIR) / "best_model.pth"

    # --- Training Loop ---
    for epoch in range(NUM_EPOCHS):
        print(f"\nEpoch {epoch+1}/{NUM_EPOCHS}")
        train_loss = train_epoch(model, train_loader, optimizer, DEVICE, scaler, loss_fn)
        print(f"Train Loss: {train_loss:.4f}")

        # --- Validation (CORRECTED: Pass val_image_files and MASK_DIR) ---
        # Assuming validate_epoch signature is updated to accept mask_dir
        val_metrics = validate_epoch(
            model, val_image_files, MASK_DIR, DEVICE, PATCH_SIZE, OVERLAP, Z_SCALE, VAL_TOLERANCE, tta=False
        )
        print(f"Val Precision: {val_metrics['precision']:.4f}, Recall: {val_metrics['recall']:.4f}, F1: {val_metrics['f1']:.4f}")

        # --- Checkpointing ---
        if val_metrics['f1'] > best_f1:
            best_f1 = val_metrics['f1']
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'scaler_state_dict': scaler.state_dict() if scaler else None,
                'loss': train_loss,
                'val_f1': val_metrics['f1'],
            }, checkpoint_path)
            print(f"New best model saved with F1: {best_f1:.4f}")

    print("Training finished.")

if __name__ == '__main__':
    main()


# %%
# -----------------------------
# CELL 12 — QUICK INFERENCE EXAMPLE & SAVE
# -----------------------------
# Usage example (run after training or load a pre-trained model)
def run_inference_example(volume_path: str, model_checkpoint_path: str):
    print(f"Running inference on {volume_path}")
    # Load model
    model = UNet3D_MultiCent(in_ch=1, base_ch=BASE_CH, num_cent_heads=len(CENTROID_SIGMAS)).to(DEVICE)
    checkpoint = torch.load(model_checkpoint_path, map_location=DEVICE)
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()
    print("Model loaded.")

    # Load and preprocess volume
    vol = tifffile.imread(volume_path)
    vol = vol[0] if vol.ndim == 4 else vol
    vol = normalize_volume(vol)
    print(f"Volume shape: {vol.shape}")

    # Predict
    print("Running prediction (sliding window + TTA)...")
    sem_logits, cent_logits, flow = predict_volume_sliding(
        model, vol, DEVICE, patch_size=PATCH_SIZE, overlap=OVERLAP,
        batch_size=BATCH_SIZE, tta=TTA
    )
    print("Prediction done.")

    # Detect centroids
    print("Detecting centroids...")
    peaks = detect_peaks_3d_maxfilter(cent_logits, min_distance=MIN_DISTANCE, threshold_abs=0.5)
    print(f"Detected {len(peaks)} centroids (before clustering).")

    # Instance segmentation (Primary method)
    print("Performing instance segmentation (flow clustering)...")
    inst_map, centroids = flow_displacement_and_cluster_3d_v2(
        sem_logits, cent_logits, flow, z_scale=Z_SCALE,
        sem_thresh=SEMANTIC_THRESHOLD, eps=DBSCAN_EPS, min_samples=DBSCAN_MIN_SAMPLES
    )
    print(f"Found {len(centroids)} instances via flow clustering.")

    # Fallback: Seeded Watershed (if flow clustering seems insufficient or as alternative)
    # print("Performing instance segmentation (seeded watershed)...")
    # ws_map = seeded_watershed_instances(sem_logits, peaks, threshold=0.2) # Use detected peaks as seeds
    # print(f"Generated instance map via watershed.")

    # Save results
    print("Saving results...")
    save_results(volume_path, sem_logits, cent_logits, flow, inst_map, centroids, out_dir=SAVE_OUTPUT_DIR)
    print("Inference example complete.")

# --- Run Inference ---
# Replace 'path/to/your/volume.tif' and 'path/to/best_model.pth' with actual paths
# run_inference_example('path/to/your/volume.tif', './results/best_model.pth')




# %%
# -----------------------------
# CELL 13 — SYNTHETIC 3D SPOT GENERATOR (REALISTIC)
# -----------------------------
"""
This cell implements a configurable 3D synthetic spot generator that produces
realistic fluorescence-like volumes and matching instance masks. Features:
 - variable number of images
 - spot size distributions (small/medium/large)
 - anisotropic voxels (sigma_z vs sigma_xy)
 - controllable overlap/touching via min distance or explicit overlap probability
 - realistic background texture (filtered noise), optional "cells/nuclei" blobs
 - PSF blur simulated with an anisotropic Gaussian (fast) or external PSF if available
 - mixed noise model: Poisson (photon shot) + Gaussian read noise
 - adjustable intensity distribution for spots and background
 - outputs saved to disk in two folders (images/, masks/) with matching names

Usage example:
 gen = Synthetic3DGenerator(out_dir='synthetic', n=100, shape=(40,256,256))
 gen.generate_and_save()

Parameters you can tune in constructor and generate call are documented below.
"""
import math
from scipy import fftpack
from scipy.ndimage import gaussian_filter

class Synthetic3DGenerator:
    """Generates synthetic 3D fluorescence volumes and instance masks.

    Main parameters (set at init):
      out_dir: base folder; will create out_dir/images and out_dir/masks
      shape: (Z,Y,X) volume shape in voxels
      voxel_size: (z, y, x) physical spacing in microns (used for PSF scaling/DBSCAN tuning)
      n_images: number of volumes to generate
      spots_per_image: (min,max) range of number of spots
      size_modes: dict for 'small','medium','large' specifying (sigma_z_pixels, sigma_xy_pixels)
      size_mix: relative probabilities for selecting modes
      intensity_range: (min,max) spot peak intensity before PSF
      background_level: baseline background intensity
      background_texture_scale: controls coarse-grained background variations
      noise_poisson_scale: scales intensities before Poisson draw (photon counts)
      noise_gauss_sigma: gaussian read noise std (after Poisson)
      psf_sigma_multiplier: multiplies size sigmas to produce final PSF (or provide psf_kernel)
      overlap: dict controlling allowed overlap: {'mode':'min_distance', 'min_dist':5} or {'mode':'allow', 'prob':0.3}
      max_attempts: attempts per spot placement
    """

    def __init__(self,
                 out_dir: str = './synthetic_dataset',
                 n_images: int = 100,
                 shape: Tuple[int,int,int] = (40,256,256),
                 voxel_size: Tuple[float,float,float] = (1.0, 0.2, 0.2),
                 spots_per_image: Tuple[int,int] = (50, 200),
                 size_modes: dict = None,
                 size_mix: dict = None,
                 intensity_range: Tuple[float,float] = (100.0, 2000.0),
                 background_level: float = 10.0,
                 background_texture_scale: float = 8.0,
                 noise_poisson_scale: float = 1.0,
                 noise_gauss_sigma: float = 2.0,
                 psf_sigma_multiplier: float = 1.0,
                 overlap: dict = None,
                 max_attempts: int = 500):
        self.out_dir = Path(out_dir)
        self.images_dir = self.out_dir / 'images'
        self.masks_dir = self.out_dir / 'masks'
        self.images_dir.mkdir(parents=True, exist_ok=True)
        self.masks_dir.mkdir(parents=True, exist_ok=True)
        self.n_images = n_images
        self.shape = tuple(shape)
        self.voxel_size = voxel_size
        self.spots_per_image = spots_per_image
        self.size_modes = size_modes or {
            'small': (1.0, 0.6),   # sigma_z (voxels), sigma_xy (voxels)
            'medium': (2.0, 1.0),
            'large': (3.5, 1.8)
        }
        self.size_mix = size_mix or {'small':0.4, 'medium':0.4, 'large':0.2}
        self.intensity_range = intensity_range
        self.background_level = background_level
        self.background_texture_scale = background_texture_scale
        self.noise_poisson_scale = noise_poisson_scale
        self.noise_gauss_sigma = noise_gauss_sigma
        self.psf_sigma_multiplier = psf_sigma_multiplier
        self.overlap = overlap or {'mode':'min_distance', 'min_dist': 3}  # voxels
        self.max_attempts = max_attempts
        # precompute mode choice list
        self.mode_names = list(self.size_mix.keys())
        self.mode_probs = np.array([self.size_mix[k] for k in self.mode_names], dtype=float)
        self.mode_probs /= self.mode_probs.sum()

    def _sample_num_spots(self):
        return int(np.random.randint(self.spots_per_image[0], self.spots_per_image[1]+1))

    def _choose_size(self):
        mode = np.random.choice(self.mode_names, p=self.mode_probs)
        return self.size_modes[mode]

    def _make_background(self):
        """Create a textured background using filtered Gaussian noise + optional cell-like blobs."""
        Z,Y,X = self.shape
        # coarse texture: generate Gaussian white noise and smooth at scale
        bg = np.random.randn(Z, Y, X).astype(np.float32)
        # apply anisotropic smoothing so background has larger XY correlation than Z
        sigma = (max(0.5, self.background_texture_scale/4.0), self.background_texture_scale, self.background_texture_scale)
        bg = gaussian_filter(bg, sigma=sigma)
        # normalize and scale to background_level
        bg = (bg - bg.min())
        if bg.max() > 0:
            bg = bg / bg.max()
        bg = bg * (self.background_level * np.random.uniform(0.8, 1.2))
        # optional nucleus/cell-like blobs: a few larger blobs of higher baseline
        n_cells = max(1, int((self.shape[1]*self.shape[2]) / (256*256)))  # heuristic
        for _ in range(np.random.randint(2, 1 + n_cells)):
            cz = np.random.uniform(0, Z)
            cy = np.random.uniform(0, Y)
            cx = np.random.uniform(0, X)
            rz = np.random.uniform(0.8*Z*0.05, 0.8*Z*0.15)
            ry = np.random.uniform(0.8*Y*0.05, 0.8*Y*0.15)
            rx = np.random.uniform(0.8*X*0.05, 0.8*X*0.15)
            zz = np.arange(Z)[:,None,None]
            yy = np.arange(Y)[None,:,None]
            xx = np.arange(X)[None,None,:]
            mask = np.exp(-0.5*(((zz-cz)/rz)**2 + ((yy-cy)/ry)**2 + ((xx-cx)/rx)**2))
            bg += mask * (self.background_level * np.random.uniform(0.5, 2.0))
        return bg

    def _place_spots(self, n_spots):
        """Sample spot centers and attributes with overlap control.

        Returns list of dicts: [{'center':(z,y,x),'sigma_z':..,'sigma_xy':..,'intensity':.., 'id':int}, ...]
        """
        Z,Y,X = self.shape
        placed = []
        attempts = 0
        while len(placed) < n_spots and attempts < self.max_attempts:
            attempts += 1
            cz = np.random.uniform(0, Z)
            cy = np.random.uniform(0, Y)
            cx = np.random.uniform(0, X)
            sigma_z, sigma_xy = self._choose_size()
            sigma_z *= self.psf_sigma_multiplier
            sigma_xy *= self.psf_sigma_multiplier
            intensity = float(np.random.uniform(self.intensity_range[0], self.intensity_range[1]))
            # overlap rules
            ok = True
            if self.overlap['mode'] == 'min_distance' and len(placed) > 0:
                mind = self.overlap.get('min_dist', 3.0)
                for p in placed:
                    dz = (p['center'][0] - cz)
                    dy = (p['center'][1] - cy)
                    dx = (p['center'][2] - cx)
                    if np.sqrt(dz*dz + dy*dy + dx*dx) < mind:
                        ok = False
                        break
            elif self.overlap['mode'] == 'probabilistic' and len(placed) > 0:
                # allow overlaps with a specified probability
                if np.random.rand() > self.overlap.get('prob', 0.3):
                    # enforce a small min distance
                    for p in placed:
                        dz = (p['center'][0] - cz)
                        dy = (p['center'][1] - cy)
                        dx = (p['center'][2] - cx)
                        if np.sqrt(dz*dz + dy*dy + dx*dx) < 1.0:
                            ok = False
                            break
            # else mode 'allow' accepts everything
            if ok:
                placed.append({'center':(cz,cy,cx),'sigma_z':sigma_z,'sigma_xy':sigma_xy,'intensity':intensity,'id':len(placed)+1})
        return placed

    def _render_instance_masks(self, spots):
        """Create a crisp instance mask by assigning each voxel to the nearest spot center (Voronoi partition).

        This yields single-instanced masks even when PSFs overlap in the intensity image.
        """
        Z,Y,X = self.shape
        mask = np.zeros(self.shape, dtype=np.uint32)
        # build grid of coordinates (float) to compute nearest center (in voxels)
        zz = np.arange(Z)[:,None,None]
        yy = np.arange(Y)[None,:,None]
        xx = np.arange(X)[None,None,:]
        centers = np.array([s['center'] for s in spots])
        if centers.size == 0:
            return mask
        # to save memory, compute distance to each center incrementally and assign
        dist_min = np.full(self.shape, np.inf, dtype=np.float32)
        for s in spots:
            cz, cy, cx = s['center']
            # squared distance
            d2 = (zz - cz)**2 + (yy - cy)**2 + (xx - cx)**2
            better = d2 < dist_min
            if np.any(better):
                mask[better] = s['id']
                dist_min[better] = d2[better]
        return mask

    def _render_intensity_image(self, spots, background):
        """Render intensity image by placing impulses at spot centers and convolving with anisotropic Gaussian PSF.

        Fast approach: create a zero volume, set voxel at nearest integer center to spot intensity, then
        convolve with anisotropic gaussian whose sigma matches spot's sigma (we use per-image average PSF).
        """
        Z,Y,X = self.shape
        img = np.array(background, dtype=np.float32)
        # impulses
        impulses = np.zeros(self.shape, dtype=np.float32)
        for s in spots:
            cz, cy, cx = s['center']
            iz, iy, ix = int(round(cz)), int(round(cy)), int(round(cx))
            if 0 <= iz < Z and 0 <= iy < Y and 0 <= ix < X:
                impulses[iz, iy, ix] += s['intensity']
        # compute average PSF sigma across spots (could be refined per-spot but expensive)
        if len(spots) > 0:
            avg_sigma_z = float(np.mean([s['sigma_z'] for s in spots]))
            avg_sigma_xy = float(np.mean([s['sigma_xy'] for s in spots]))
        else:
            avg_sigma_z = self.size_modes['medium'][0] * self.psf_sigma_multiplier
            avg_sigma_xy = self.size_modes['medium'][1] * self.psf_sigma_multiplier
        # blur impulses -> intensity image
        img += gaussian_filter(impulses, sigma=(avg_sigma_z, avg_sigma_xy, avg_sigma_xy))
        return img

    def _add_noise_and_camera(self, img: np.ndarray):
        """Simulate photon shot noise (Poisson) and camera read noise (Gaussian),
        plus small illumination gradient.
        """
        # illumination gradient: gentle vignetting / field inhomogeneity
        Z,Y,X = img.shape
        zy = np.linspace(-1,1,Y)[None,:]
        xv = np.linspace(-1,1,X)[None,:]
        # gradient across XY
        grad = (1.0 + 0.2 * (np.sin(np.linspace(0, math.pi, Y))[None,:] * np.cos(np.linspace(0, math.pi, X)[:,None]).T))
        # apply same gradient to all z slices with mild z-dependent scaling
        ig = img * (1.0 + 0.1 * np.linspace(-0.5,0.5,Z)[:,None,None])
        ig = ig * (1.0 + 0.15 * (np.linspace(-1,1,X)[None,None,:] * 0.0) )  # reserved for advanced patterns
        # photon scaling then Poisson
        scaled = ig * self.noise_poisson_scale
        noisy = np.random.poisson(np.clip(scaled, 0, None)).astype(np.float32) / max(1.0, self.noise_poisson_scale)
        # add gaussian read noise
        noisy += np.random.normal(0, self.noise_gauss_sigma, size=img.shape).astype(np.float32)
        # clamp negatives
        noisy = np.clip(noisy, 0, None)
        return noisy

    def generate_single(self, idx: int):
        """Generate one synthetic image and mask pair and return them (image, mask, metadata)."""
        n_spots = self._sample_num_spots()
        background = self._make_background()
        spots = self._place_spots(n_spots)
        mask = self._render_instance_masks(spots)
        img = self._render_intensity_image(spots, background)
        img = self._add_noise_and_camera(img)
        meta = {'n_spots': len(spots), 'spots': spots}
        return img.astype(np.float32), mask.astype(np.uint32), meta

    def generate_and_save(self, prefix: str = 'synthetic_3d', zero_pad: int = 3):
        """Generate the requested number of images and save to disk with matching names.

        Files are saved as:
         out_dir/images/<prefix>_001.tif
         out_dir/masks/<prefix>_001.tif
        Also save per-image metadata as JSON (.npy for simplicity).
        """
        for i in range(1, self.n_images+1):
            img, mask, meta = self.generate_single(i)
            name = f"{prefix}_{str(i).zfill(zero_pad)}.tif"
            img_path = self.images_dir / name
            mask_path = self.masks_dir / name
            # save as float32 for image, uint32 for mask
            tifffile.imwrite(str(img_path), img.astype(np.float32), imagej=False, bigtiff=True)
            tifffile.imwrite(str(mask_path), mask.astype(np.uint32), imagej=False, bigtiff=True)
            # save meta
            np.save(self.images_dir / (name.replace('.tif','.npy')), meta)
            if i % 10 == 0 or i==self.n_images:
                print(f"Saved {i}/{self.n_images}: {img_path} & {mask_path}")

# add a small quick demo function for notebook users
def demo_synthetic_generation(out_dir='./synthetic_demo', n=5, shape=(40,128,128)):
    gen = Synthetic3DGenerator(out_dir=out_dir, n_images=n, shape=shape,
                               spots_per_image=(30,80), intensity_range=(300,2000),
                               background_level=10.0, background_texture_scale=6.0,
                               noise_poisson_scale=1.0, noise_gauss_sigma=2.0,
                               overlap={'mode':'probabilistic', 'prob':0.4})
    gen.generate_and_save(prefix='synthetic_3d')

# Note: This generator uses an anisotropic Gaussian PSF approximation (fast). If you
# want physically accurate PSFs (Gibson-Lanni or vectorial models), consider using
# libraries like `MicroscPSF-Py`, `psf-generator` or `psf` (installable via pip). The
# generator is modular so you can replace `_render_intensity_image` to convolve
# impulses with a custom PSF kernel.

# End synthetic generator cell

# End of enhanced refactor


# Cell 1: Imports and Utilities
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import tifffile
import random
import cv2
from scipy.ndimage import distance_transform_edt
from skimage.measure import regionprops
from skimage.morphology import skeletonize
from scipy.spatial.distance import cdist
from torch.utils.data import Dataset, DataLoader
from sklearn.model_selection import train_test_split
import albumentations as A
import os
import matplotlib.pyplot as plt
from tqdm import tqdm
from skimage.color import label2rgb
import glob as glob
# Compatibility for AMP
try:
    from torch.amp import GradScaler, autocast
except ImportError:
    from torch.cuda.amp import GradScaler, autocast

# --- Utility Functions ---
# Improved augmentation strategy
def get_optimized_transforms():
    """Improved augmentation pipeline for microscopy images."""
    return A.Compose([
        A.HorizontalFlip(p=0.5),
        A.VerticalFlip(p=0.5),
        A.Rotate(limit=180, p=0.7),  # Allow full rotation
        A.RandomRotate90(p=0.5),     # Add 90-degree rotations
        
        # More conservative blur to preserve small structures
        A.OneOf([
            A.GaussianBlur(blur_limit=(1, 3), sigma_limit=(0.1, 0.5), p=1.0),
            A.MotionBlur(blur_limit=3, p=1.0),
        ], p=0.2),
        
        # Reduced intensity changes
        A.RandomBrightnessContrast(brightness_limit=0.1, contrast_limit=0.1, p=0.3),
        
        # Add elastic transform for more realistic deformations
        A.ElasticTransform(alpha=1, sigma=50, alpha_affine=50, p=0.2),
        
        # Grid distortion for additional variety
        A.GridDistortion(p=0.1),
        
        # Final normalization
        A.Normalize(mean=0.0, std=1.0, max_pixel_value=1.0, p=1.0)
    ], p=1.0)


import torch
import numpy as np
import tifffile
import random
import cv2
from torch.utils.data import Dataset
from scipy.ndimage import distance_transform_edt, gaussian_filter
from skimage.measure import regionprops, label
from skimage.morphology import skeletonize
from scipy.spatial.distance import cdist

class SkeletonAwareSpotDataset(Dataset):
    def __init__(self, image_paths, mask_paths, transform=None, patch_size=256):
        assert len(image_paths) == len(mask_paths), "Image and mask lists must be the same length"
        self.image_paths = image_paths
        self.mask_paths = mask_paths
        self.transform = transform
        self.patch_size = patch_size

    def _get_valid_spots(self, mask):
        valid_ids = np.unique(mask)
        return valid_ids[valid_ids > 0]

    def _extract_spot_info(self, mask, valid_ids):
        centroids = []
        spot_masks = []
        for spot_id in valid_ids:
            spot_mask = (mask == spot_id).astype(np.uint8)
            props = regionprops(spot_mask)
            if props:
                centroids.append(props[0].centroid)
                spot_masks.append(spot_mask)
        return centroids, spot_masks

    def _generate_semantic_mask(self, spot_masks, shape):
        semantic = np.zeros(shape, dtype=np.float32)
        for mask in spot_masks:
            semantic = np.maximum(semantic, mask.astype(np.float32))
        return semantic

    def _place_centroid_skeleton_point(self, skeleton_map, spot_region, centroid_y, centroid_x):
        H, W = skeleton_map.shape
        cy_int, cx_int = int(round(centroid_y)), int(round(centroid_x))
        if 0 <= cy_int < H and 0 <= cx_int < W:
            y_min, y_max = max(0, cy_int - 1), min(H, cy_int + 2)
            x_min, x_max = max(0, cx_int - 1), min(W, cx_int + 2)
            Y, X = np.ogrid[y_min:y_max, x_min:x_max]
            dist_from_center = np.sqrt((Y - centroid_y)**2 + (X - centroid_x)**2)
            skel_peak = np.clip(1.0 - dist_from_center / 2.0, 0, 1)
            skeleton_map[y_min:y_max, x_min:x_max] = np.maximum(
                skeleton_map[y_min:y_max, x_min:x_max], skel_peak
            )

    def generate_skeleton_aware_distance_transform(self, spot_mask):
        if not np.any(spot_mask):
            return (np.zeros_like(spot_mask, dtype=np.float32),
                    np.zeros_like(spot_mask, dtype=np.float32),
                    np.zeros_like(spot_mask, dtype=np.float32))

        H, W = spot_mask.shape
        spot_area = np.sum(spot_mask)
        spot_region = spot_mask > 0

        # Skeleton - Fixed to ensure proper values
        skeleton = np.zeros_like(spot_mask, dtype=np.float32)
        if spot_area <= 10:
            props = regionprops(spot_mask.astype(np.uint8))
            if props:
                cy, cx = props[0].centroid
                # Create a small gaussian around centroid
                Y, X = np.ogrid[:H, :W]
                dist_from_center = np.sqrt((Y - cy)**2 + (X - cx)**2)
                gaussian_skel = np.exp(-dist_from_center**2 / (2 * 1.0**2))
                skeleton[spot_mask > 0] = gaussian_skel[spot_mask > 0]
        else:
            try:
                skel_temp = skeletonize(spot_mask > 0)
                # Dilate skeleton slightly for better training
                kernel = np.ones((3, 3), np.uint8)
                skel_dilated = cv2.dilate(skel_temp.astype(np.uint8), kernel, iterations=1)
                skeleton = skel_dilated.astype(np.float32)
                
                if np.sum(skeleton) == 0:
                    props = regionprops(spot_mask.astype(np.uint8))
                    if props:
                        cy, cx = props[0].centroid
                        self._place_centroid_skeleton_point(skeleton, spot_region, cy, cx)
            except Exception:
                props = regionprops(spot_mask.astype(np.uint8))
                if props:
                    cy, cx = props[0].centroid
                    self._place_centroid_skeleton_point(skeleton, spot_region, cy, cx)

        # Boundary - Fixed to be more robust
        kernel = np.ones((3, 3), np.uint8)
        dilated = cv2.dilate(spot_mask.astype(np.uint8), kernel, iterations=1)
        eroded = cv2.erode(spot_mask.astype(np.uint8), kernel, iterations=1)
        boundary = (dilated - eroded).astype(np.float32)
        # Ensure boundary is only on the actual spot pixels
        boundary = boundary * spot_mask.astype(np.float32)

        # Distance to boundary - Fixed normalization
        dist_to_boundary = distance_transform_edt(spot_mask)

        # Internal distance to skeleton - More robust implementation
        skeleton_binary = skeleton > 0.1  # Lower threshold
        dist_to_skeleton_internal = np.zeros((H, W), dtype=np.float32)
        
        if np.any(spot_region) and np.any(skeleton_binary):
            # Use distance transform instead of cdist for better performance
            inverted_skeleton = np.logical_and(spot_region, ~skeleton_binary)
            if np.any(inverted_skeleton):
                dist_to_skeleton_internal = distance_transform_edt(inverted_skeleton)
                dist_to_skeleton_internal[~spot_region] = 0.0
            else:
                dist_to_skeleton_internal[spot_region] = 0.0
        else:
            dist_to_skeleton_internal[spot_region] = 0.0

        # Better normalization
        max_skel_dist = np.max(dist_to_skeleton_internal[spot_region]) if np.any(spot_region) else 1.0
        if max_skel_dist < 1e-6:
            max_skel_dist = 1.0
            
        skeleton_norm = dist_to_skeleton_internal / max_skel_dist
        skeleton_norm[~spot_region] = 0.0
        skeleton_norm = np.clip(skeleton_norm, 0.0, 1.0)

        # Improved SDT calculation
        if np.any(spot_region):
            max_boundary_dist = np.max(dist_to_boundary[spot_region])
            if max_boundary_dist < 1e-6:
                max_boundary_dist = 1.0
                
            boundary_norm = dist_to_boundary / max_boundary_dist
            boundary_norm[~spot_region] = 0
            
            # More balanced combination
            alpha = 1.2 if spot_area <= 15 else 1.0
            sdt = boundary_norm * np.power(np.clip(1.0 - skeleton_norm, 0, 1), alpha)
            sdt = np.clip(sdt, 0, 1)
        else:
            sdt = np.zeros_like(spot_mask, dtype=np.float32)

        return sdt, skeleton, boundary

    def generate_centroid_map(self, shape, centroids):
        centroid_map = np.zeros(shape, dtype=np.float32)
        # Increased sigma for better training signal
        sigma = 2.0
        for cy, cx in centroids:
            cx_int, cy_int = int(round(cx)), int(round(cy))
            # Larger radius for centroid influence
            radius = int(3 * sigma)
            x_min, x_max = max(0, cx_int - radius), min(shape[1], cx_int + radius + 1)
            y_min, y_max = max(0, cy_int - radius), min(shape[0], cy_int + radius + 1)
            
            if x_max > x_min and y_max > y_min:
                Y, X = np.ogrid[y_min:y_max, x_min:x_max]
                g = np.exp(-((X - cx)**2 + (Y - cy)**2) / (2 * sigma**2))
                existing = centroid_map[y_min:y_max, x_min:x_max]
                centroid_map[y_min:y_max, x_min:x_max] = np.maximum(existing, g)
        return centroid_map

    def generate_flow_field_from_centroids(self, semantic_mask, centroids):
        h, w = semantic_mask.shape
        if len(centroids) == 0:
            return np.zeros((2, h, w), dtype=np.float32)
            
        flow_field = np.zeros((2, h, w), dtype=np.float32)
        
        # Get all foreground pixels
        y_coords, x_coords = np.where(semantic_mask > 0.5)
        if len(y_coords) == 0:
            return flow_field
            
        pixel_coords = np.column_stack((y_coords, x_coords))
        centroids_arr = np.array(centroids)
        
        if len(centroids_arr) == 0:
            return flow_field

        # Find nearest centroid for each pixel
        distances = cdist(pixel_coords, centroids_arr)
        nearest_idx = np.argmin(distances, axis=1)
        nearest_centroids = centroids_arr[nearest_idx]
        
        # Calculate flow vectors
        delta = nearest_centroids - pixel_coords
        
        # Normalize flow vectors
        norms = np.linalg.norm(delta, axis=1)
        valid_mask = norms > 1e-6
        
        unit_vectors = np.zeros_like(delta)
        unit_vectors[valid_mask] = delta[valid_mask] / norms[valid_mask, None]
        
        # Apply flow field
        flow_field[0, y_coords, x_coords] = unit_vectors[:, 0]
        flow_field[1, y_coords, x_coords] = unit_vectors[:, 1]
        
        return flow_field

    def __len__(self):
        return len(self.image_paths)

    def __getitem__(self, idx):
        image = tifffile.imread(self.image_paths[idx]).astype(np.float32)
        mask = tifffile.imread(self.mask_paths[idx]).astype(np.int32)
        
        # Improved normalization
        vmin, vmax = np.percentile(image, (0.5, 99.5))
        image = np.clip((image - vmin) / (vmax - vmin + 1e-8), 0, 1)
        H, W = image.shape

        valid_ids = self._get_valid_spots(mask)
        
        # Better patch sampling strategy
        if len(valid_ids) > 0 and random.random() < 0.8:  # Increased probability
            spot_id = random.choice(valid_ids)
            coords = np.argwhere(mask == spot_id)
            cy, cx = coords.mean(axis=0).astype(int)
            
            # Add some randomness to avoid always centering
            offset_y = random.randint(-self.patch_size//4, self.patch_size//4)
            offset_x = random.randint(-self.patch_size//4, self.patch_size//4)
            
            y0 = np.clip(cy - self.patch_size // 2 + offset_y, 0, H - self.patch_size)
            x0 = np.clip(cx - self.patch_size // 2 + offset_x, 0, W - self.patch_size)
        else:
            y0 = random.randint(0, max(0, H - self.patch_size))
            x0 = random.randint(0, max(0, W - self.patch_size))

        patch_img = image[y0:y0+self.patch_size, x0:x0+self.patch_size]
        patch_mask = mask[y0:y0+self.patch_size, x0:x0+self.patch_size]

        # Better padding strategy
        if patch_img.shape[0] < self.patch_size or patch_img.shape[1] < self.patch_size:
            ph = max(0, self.patch_size - patch_img.shape[0])
            pw = max(0, self.patch_size - patch_img.shape[1])
            patch_img = np.pad(patch_img, ((0, ph), (0, pw)), 'reflect')
            patch_mask = np.pad(patch_mask, ((0, ph), (0, pw)), 'constant', constant_values=0)

        valid_ids = self._get_valid_spots(patch_mask)
        
        # Don't skip patches without spots - generate empty targets instead
        if len(valid_ids) == 0:
            # Generate all-zero targets but keep the image
            targets = np.zeros((7, self.patch_size, self.patch_size), dtype=np.float32)
            img_tensor = torch.from_numpy(patch_img.astype(np.float32)).unsqueeze(0)
            targets_tensor = torch.from_numpy(targets)
            return img_tensor, targets_tensor

        centroids, spot_masks = self._extract_spot_info(patch_mask, valid_ids)
        semantic = self._generate_semantic_mask(spot_masks, patch_mask.shape)

        combined_sdt = np.zeros_like(semantic, dtype=np.float32)
        combined_skeleton = np.zeros_like(semantic, dtype=np.float32)
        combined_boundary = np.zeros_like(semantic, dtype=np.float32)

        for spot_mask in spot_masks:
            sdt, skeleton, boundary = self.generate_skeleton_aware_distance_transform(spot_mask)
            combined_sdt = np.maximum(combined_sdt, sdt)
            combined_skeleton = np.maximum(combined_skeleton, skeleton)
            combined_boundary = np.maximum(combined_boundary, boundary)

        centroid_map = self.generate_centroid_map(patch_mask.shape, centroids)
        flow_field = self.generate_flow_field_from_centroids(semantic, centroids)

        targets = np.stack([
            semantic,           # 0: semantic segmentation
            combined_sdt,       # 1: signed distance transform
            combined_skeleton,  # 2: skeleton
            centroid_map,       # 3: centroid heatmap
            flow_field[0],      # 4: flow field Y
            flow_field[1],      # 5: flow field X
            combined_boundary   # 6: boundary
        ], axis=0)

        # Apply augmentations if provided
        if self.transform is not None:
            # Move channels to last dimension for albumentation
            targets_hwc = np.moveaxis(targets, 0, -1)
            augmented = self.transform(image=patch_img, mask=targets_hwc)
            patch_img = augmented['image']
            targets = np.moveaxis(augmented['mask'], -1, 0)
            
            # Fix augmented targets
            targets[0] = (targets[0] > 0.5).astype(np.float32)  # semantic (binary)
            targets[1] = np.clip(targets[1], 0, 1)              # SDT
            targets[2] = np.clip(targets[2], 0, 1)              # skeleton
            targets[3] = np.clip(targets[3], 0, 1)              # centroid
            targets[4] = np.clip(targets[4], -1, 1)             # flow Y
            targets[5] = np.clip(targets[5], -1, 1)             # flow X
            targets[6] = (targets[6] > 0.5).astype(np.float32)  # boundary (binary)

        img_tensor = torch.from_numpy(patch_img.astype(np.float32)).unsqueeze(0)
        targets_tensor = torch.from_numpy(targets.astype(np.float32))

        return img_tensor, targets_tensor

print("Fixed SkeletonAwareSpotDataset class is ready.")



import os
import matplotlib.pyplot as plt
import numpy as np
import tifffile
from scipy.ndimage import distance_transform_edt, maximum_filter
from scipy.spatial.distance import cdist
from skimage.measure import regionprops
from skimage.morphology import skeletonize
from skimage.segmentation import watershed
import cv2

def generate_skeleton_aware_distance_transform(spot_mask):
    if not np.any(spot_mask):
        return (np.zeros_like(spot_mask, dtype=np.float32),
                np.zeros_like(spot_mask, dtype=np.float32),
                np.zeros_like(spot_mask, dtype=np.float32))
    H, W = spot_mask.shape
    spot_area = np.sum(spot_mask)
    skeleton = np.zeros_like(spot_mask, dtype=np.float32)
    if spot_area <= 10:
        props = regionprops(spot_mask.astype(np.uint16))
        if props:
            cy, cx = props[0].centroid
            Y, X = np.ogrid[:H, :W]
            dist_from_center = np.sqrt((Y - cy)**2 + (X - cx)**2)
            skeleton[spot_mask > 0] = np.clip(1.0 - dist_from_center[spot_mask > 0] / 2.0, 0, 1)
    else:
        try:
            skel_temp = skeletonize(spot_mask > 0)
            skeleton = skel_temp.astype(np.float32)
            if np.sum(skeleton) == 0:
                props = regionprops(spot_mask.astype(np.uint16))
                if props:
                    cy, cx = props[0].centroid
                    cy, cx = int(round(cy)), int(round(cx))
                    if 0 <= cy < H and 0 <= cx < W:
                        skeleton[cy, cx] = 1.0
        except Exception:
            props = regionprops(spot_mask.astype(np.uint16))
            if props:
                cy, cx = props[0].centroid
                cy, cx = int(round(cy)), int(round(cx))
                if 0 <= cy < H and 0 <= cx < W:
                    skeleton[cy, cx] = 1.0
    kernel = np.ones((3, 3), np.uint8)
    eroded = cv2.erode(spot_mask.astype(np.uint8), kernel, iterations=1)
    boundary = (spot_mask.astype(np.uint8) - eroded).astype(np.float32)
    spot_region = spot_mask > 0
    dist_to_boundary = distance_transform_edt(spot_mask)
    skeleton_binary = skeleton > 0
    if np.any(spot_region) and np.any(skeleton_binary):
        coords_spot = np.argwhere(spot_region)
        coords_skel = np.argwhere(skeleton_binary)
        if len(coords_skel) > 0:
            dists = cdist(coords_spot, coords_skel, 'euclidean')
            min_dists = np.min(dists, axis=1)
            dist_to_skeleton_internal = np.full((H, W), np.inf, dtype=np.float32)
            for (y, x), d in zip(coords_spot, min_dists):
                dist_to_skeleton_internal[y, x] = d
            max_skel_dist = np.max(min_dists) if len(min_dists) > 0 else 1.0
            skeleton_norm = dist_to_skeleton_internal / (max_skel_dist + 1e-6)
            skeleton_norm[~spot_region] = 0
        else:
            skeleton_norm = np.zeros_like(spot_mask, dtype=np.float32)
    else:
        skeleton_norm = np.zeros_like(spot_mask, dtype=np.float32)
    if np.any(spot_region):
        max_boundary_dist = np.max(dist_to_boundary[spot_region]) + 1e-6
        boundary_norm = dist_to_boundary / max_boundary_dist
        boundary_norm[~spot_region] = 0
        alpha = 1.5 if spot_area <= 15 else 1.2
        sdt = boundary_norm * np.power(np.clip(1.0 - skeleton_norm, 0, 1), alpha)
        sdt = np.clip(sdt, 0, 1)
    else:
        sdt = np.zeros_like(spot_mask, dtype=np.float32)
    return sdt, skeleton, boundary

def generate_centroid_map(shape, centroids):
    centroid_map = np.zeros(shape, dtype=np.float32)
    sigma = 1.0
    for cy, cx in centroids:
        cy_int, cx_int = int(round(cy)), int(round(cx))
        y_min = max(0, cy_int - 6)
        y_max = min(shape[0], cy_int + 7)
        x_min = max(0, cx_int - 6)
        x_max = min(shape[1], cx_int + 7)
        if y_min >= y_max or x_min >= x_max:
            continue
        Y, X = np.ogrid[y_min:y_max, x_min:x_max]
        g = np.exp(-((X - cx)**2 + (Y - cy)**2) / (2 * sigma**2))
        existing = centroid_map[y_min:y_max, x_min:x_max]
        centroid_map[y_min:y_max, x_min:x_max] = np.maximum(existing, g)
    return centroid_map

def visualize_full_image_analysis(image_path, mask_path, output_dir="full_image_analysis"):
    os.makedirs(output_dir, exist_ok=True)
    try:
        image = tifffile.imread(image_path).astype(np.float32)
        mask = tifffile.imread(mask_path).astype(np.uint16)
    except Exception as e:
        print(f"Error loading files: {e}")
        return
    vmin, vmax = np.percentile(image, (1, 99))
    image_norm = np.clip((image - vmin) / (vmax - vmin + 1e-8), 0, 1)
    valid_ids = np.unique(mask)
    valid_ids = valid_ids[valid_ids > 0]
    print(f"Found {len(valid_ids)} spots in the mask (16-bit safe).")
    H, W = mask.shape
    combined_semantic = np.zeros((H, W), dtype=np.float32)
    combined_skeleton = np.zeros((H, W), dtype=np.float32)
    combined_boundary = np.zeros((H, W), dtype=np.float32)
    combined_sdt = np.zeros((H, W), dtype=np.float32)
    all_centroids = []
    for spot_id in valid_ids:
        spot_mask = (mask == spot_id).astype(np.uint16)
        if np.sum(spot_mask) == 0:
            continue
        props = regionprops(spot_mask)
        if props:
            cy, cx = props[0].centroid
            all_centroids.append((cy, cx))
        sdt, skeleton, boundary = generate_skeleton_aware_distance_transform(spot_mask)
        combined_semantic = np.maximum(combined_semantic, spot_mask.astype(np.float32))
        combined_skeleton = np.maximum(combined_skeleton, skeleton)
        combined_boundary = np.maximum(combined_boundary, boundary)
        combined_sdt = np.maximum(combined_sdt, sdt)
    centroid_map = generate_centroid_map((H, W), all_centroids)
    print(f"Generated Gaussian centroid map with {len(all_centroids)} peaks.")
    peaks = (centroid_map > 0.3) & (maximum_filter(centroid_map, size=3) == centroid_map)
    seeds = np.zeros((H, W), dtype=np.int32)
    if len(all_centroids) > 0:
        for idx, (y, x) in enumerate(all_centroids, 1):
            y, x = int(round(y)), int(round(x))
            if 0 <= y < H and 0 <= x < W:
                seeds[y, x] = idx
    instance_labels = watershed(-combined_sdt, seeds, mask=(combined_semantic > 0.5))
    fig, axes = plt.subplots(3, 3, figsize=(20, 18))
    fig.suptitle("Full-Image Multi-Channel Analysis", fontsize=18)
    axes[0, 0].imshow(image_norm, cmap='gray')
    axes[0, 0].set_title("Input Image")
    axes[0, 0].axis('off')
    axes[0, 1].imshow(combined_semantic, cmap='viridis')
    axes[0, 1].set_title("Semantic Mask (All Spots)")
    axes[0, 1].axis('off')
    axes[0, 2].imshow(combined_skeleton, cmap='bone')
    axes[0, 2].set_title("Skeleton (All Spots)")
    axes[0, 2].axis('off')
    axes[1, 0].imshow(combined_boundary, cmap='gray')
    axes[1, 0].set_title("Boundary (All Spots)")
    axes[1, 0].axis('off')
    im5 = axes[1, 1].imshow(combined_sdt, cmap='magma')
    axes[1, 1].set_title("SDT (All Spots)")
    plt.colorbar(im5, ax=axes[1, 1], shrink=0.8)
    im6 = axes[1, 2].imshow(centroid_map, cmap='coolwarm')
    axes[1, 2].set_title("Gaussian Centroid Map (All Spots)")
    plt.colorbar(im6, ax=axes[1, 2], shrink=0.8)
    axes[2, 0].imshow(combined_semantic, cmap='gray')
    axes[2, 0].imshow(combined_skeleton, cmap='Reds', alpha=0.6)
    axes[2, 0].set_title("Spot + Skeleton Overlay")
    axes[2, 0].axis('off')
    axes[2, 1].imshow(combined_semantic, cmap='gray')
    axes[2, 1].imshow(centroid_map, cmap='coolwarm', alpha=0.6)
    if all_centroids:
        y_coords, x_coords = zip(*all_centroids)
        axes[2, 1].scatter(x_coords, y_coords, c='red', s=30, marker='x', label='Centroid')
        axes[2, 1].legend()
    axes[2, 1].set_title("Spot + Centroid Overlay")
    axes[2, 1].axis('off')
    axes[2, 2].imshow(instance_labels, cmap='nipy_spectral')
    axes[2, 2].set_title("Instance Labels")
    axes[2, 2].axis('off')
    plt.tight_layout()
    plot_filename = os.path.join(output_dir, "full_image_analysis_grid.png")
    try:
        plt.savefig(plot_filename, dpi=150, bbox_inches='tight')
        plt.close()
        print(f"✅ Full-image analysis saved to: {plot_filename}")
    except Exception as e:
        print(f"Error saving plot: {e}")
    print(f"✅ Total spots processed: {len(valid_ids)}")
    print(f"✅ Centroids aligned: {len(all_centroids)}")

if __name__ == "__main__":
    image_path = "/mnt/d/Users/<USER>/FISH_spots/2d/synthetic/images/synthetic_000003.tif"
    mask_path = "/mnt/d/Users/<USER>/FISH_spots/2d/synthetic/masks/synthetic_000003.tif"
    visualize_full_image_analysis(image_path, mask_path)
    print("\n🎉 Full-image analysis complete.")

# Cell: Updated Data Loader Creation (Fix Pin Memory)
def create_skeleton_aware_data_loaders_fixed(image_paths, mask_paths, batch_size=12, 
                                             patch_size=256, num_workers=4):
    """
    Fixed data loader creation without pin_memory to avoid CUDA OOM errors.
    """
    # Validate dataset
    assert len(image_paths) == len(mask_paths), "Image and mask counts must match"
    assert all(os.path.exists(p) for p in image_paths), "Some image paths are invalid"
    assert all(os.path.exists(p) for p in mask_paths), "Some mask paths are invalid"
    
    # Split data
    train_imgs, val_imgs, train_masks, val_masks = train_test_split(
        image_paths, mask_paths, test_size=0.2, random_state=42, shuffle=True
    )
    
    print(f"Dataset split: {len(train_imgs)} training, {len(val_imgs)} validation samples")
    
    # Datasets
    train_dataset = SkeletonAwareSpotDataset(
        train_imgs, train_masks,
        transform=get_optimized_transforms(),
        patch_size=patch_size
    )
    val_dataset = SkeletonAwareSpotDataset(
        val_imgs, val_masks,
        transform=None,
        patch_size=patch_size
    )
    
    # Worker initialization function for deterministic behavior
    def worker_init_fn(worker_id):
        worker_seed = torch.initial_seed() % 2**32
        np.random.seed(worker_seed)
        random.seed(worker_seed)
    
    # Calculate optimal prefetch factor
    prefetch_factor = max(2, batch_size // 4)
    
    # --- FIX: Disable pin_memory to prevent CUDA OOM ---
    use_pin_memory = False # Set to False to avoid pin memory errors
    
    # DataLoaders with optimized settings
    train_loader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=num_workers,
        pin_memory=use_pin_memory, # Use the fixed value
        drop_last=True,
        prefetch_factor=prefetch_factor,
        persistent_workers=True if num_workers > 0 else False,
        worker_init_fn=worker_init_fn
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=max(0, num_workers // 2),
        pin_memory=use_pin_memory, # Use the fixed value
        drop_last=False,
        prefetch_factor=prefetch_factor,
        persistent_workers=True if num_workers > 0 else False
    )
    
    # Verify first batch loads correctly (without pin_memory)
    try:
        images, targets = next(iter(train_loader))
        print(f"Data loader verified: Batch shape - images {images.shape}, targets {targets.shape}")
        print(f"Data types: images {images.dtype}, targets {targets.dtype}")
    except Exception as e:
        print(f"WARNING: Data loader verification failed: {e}")
    
    return train_loader, val_loader

# Update your main training script to use this function:
# train_loader, val_loader = create_skeleton_aware_data_loaders_fixed(
#     image_paths, mask_paths, 
#     batch_size=12, 
#     patch_size=256, 
#     num_workers=4
# )

import torch
import torch.nn as nn
import torch.nn.functional as F

class EfficientBlock(nn.Module):
    def __init__(self, in_ch, out_ch, stride=1, expand_ratio=4, dropout_prob=0.1):
        super().__init__()
        hidden_ch = in_ch * expand_ratio
        self.stride = stride
        self.expand = nn.Sequential(
            nn.Conv2d(in_ch, hidden_ch, 1, bias=False),
            nn.BatchNorm2d(hidden_ch),
            nn.SiLU(inplace=True)
        ) if expand_ratio != 1 else nn.Identity()
        
        self.depthwise = nn.Sequential(
            nn.Conv2d(hidden_ch, hidden_ch, 3, stride, 1, groups=hidden_ch, bias=False),
            nn.BatchNorm2d(hidden_ch),
            nn.SiLU(inplace=True)
        )
        
        self.se = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(hidden_ch, max(1, hidden_ch//4), 1),
            nn.SiLU(inplace=True),
            nn.Conv2d(max(1, hidden_ch//4), hidden_ch, 1),
            nn.Sigmoid()
        )
        
        self.project = nn.Sequential(
            nn.Conv2d(hidden_ch, out_ch, 1, bias=False),
            nn.BatchNorm2d(out_ch)
        )
        
        self.skip = (stride == 1 and in_ch == out_ch)
        self.dropout = nn.Dropout2d(dropout_prob) if dropout_prob > 0 else nn.Identity()

    def forward(self, x):
        residual = x
        x = self.expand(x)
        x = self.depthwise(x)
        x = x * self.se(x)
        x = self.project(x)
        x = self.dropout(x)
        
        if self.skip:
            # Proper residual scaling
            return x + residual
        return x

class SimpleSpatialAttention(nn.Module):
    def __init__(self, in_channels):
        super().__init__()
        self.conv = nn.Conv2d(in_channels, 1, kernel_size=7, padding=3, bias=False)
        self.bn = nn.BatchNorm2d(1)

    def forward(self, x):
        attn = self.conv(x)
        attn = self.bn(attn)
        attn = torch.sigmoid(attn)
        return x * attn

class SkeletonAwareSpotDetector(nn.Module):
    def __init__(self, in_ch=1, base_ch=48, dropout_prob=0.1):
        super().__init__()
        
        # Stem
        self.stem_conv1 = nn.Sequential(
            nn.Conv2d(in_ch, base_ch, 3, padding=1, bias=False),
            nn.BatchNorm2d(base_ch),
            nn.SiLU(inplace=True)
        )
        self.stem_conv2 = nn.Sequential(
            nn.Conv2d(base_ch, base_ch, 3, padding=1, bias=False),
            nn.BatchNorm2d(base_ch),
            nn.SiLU(inplace=True),
            nn.Dropout2d(dropout_prob) if dropout_prob > 0 else nn.Identity()
        )
        
        # Encoder
        self.enc1 = nn.Sequential(
            EfficientBlock(base_ch, base_ch*2, stride=2, dropout_prob=dropout_prob),
            EfficientBlock(base_ch*2, base_ch*2, dropout_prob=dropout_prob)
        )
        self.enc2 = nn.Sequential(
            EfficientBlock(base_ch*2, base_ch*4, stride=2, dropout_prob=dropout_prob),
            EfficientBlock(base_ch*4, base_ch*4, dropout_prob=dropout_prob)
        )
        
        # FPN
        self.fpn_lateral_stem = nn.Conv2d(base_ch, base_ch, 1)
        self.fpn_conv2 = nn.Sequential(
            nn.Conv2d(base_ch*4, base_ch*2, 1),
            nn.BatchNorm2d(base_ch*2),
            nn.SiLU(inplace=True)
        )
        self.fpn_conv1 = nn.Sequential(
            nn.Conv2d(base_ch*2, base_ch, 1),
            nn.BatchNorm2d(base_ch),
            nn.SiLU(inplace=True)
        )
        self.fusion_conv = nn.Sequential(
            nn.Conv2d(base_ch*2, base_ch, 1),
            nn.BatchNorm2d(base_ch),
            nn.SiLU(inplace=True)
        )
        
        # Skip connections
        self.skip_conv1 = nn.Conv2d(base_ch, base_ch, 1)
        self.skip_conv2 = nn.Conv2d(base_ch, base_ch, 1)
        
        # Decoder
        self.decoder = nn.Sequential(
            EfficientBlock(base_ch*3, base_ch*2, dropout_prob=dropout_prob),
            EfficientBlock(base_ch*2, base_ch, dropout_prob=dropout_prob),
            EfficientBlock(base_ch, base_ch, dropout_prob=dropout_prob),
            nn.Conv2d(base_ch, base_ch, 3, padding=1, bias=False),
            nn.BatchNorm2d(base_ch),
            nn.SiLU(inplace=True),
            nn.Dropout2d(dropout_prob) if dropout_prob > 0 else nn.Identity()
        )
        
        self.spatial_attn = SimpleSpatialAttention(base_ch)
        
        # Shared head
        self.shared_head = nn.Sequential(
            nn.Conv2d(base_ch, base_ch, 3, padding=1, groups=base_ch, bias=False),
            nn.BatchNorm2d(base_ch),
            nn.SiLU(inplace=True),
            nn.Conv2d(base_ch, base_ch, 1, bias=False),
            nn.BatchNorm2d(base_ch),
            nn.SiLU(inplace=True)
        )
        
        # Task-specific heads - FIXED: Remove internal activations
        self.semantic_head = nn.Conv2d(base_ch, 1, 1)
        
        self.sdt_head = nn.Sequential(
            nn.Conv2d(base_ch, base_ch//2, 3, padding=1, bias=False),
            nn.BatchNorm2d(base_ch//2),
            nn.SiLU(inplace=True),
            nn.Conv2d(base_ch//2, 1, 1)  # Single channel for regression
        )
        
        self.skeleton_head = nn.Sequential(
            nn.Conv2d(base_ch, base_ch//2, 3, padding=1, bias=False),
            nn.BatchNorm2d(base_ch//2),
            nn.SiLU(inplace=True),
            nn.Conv2d(base_ch//2, 1, 1)
        )
        
        self.centroid_head = nn.Sequential(
            nn.Conv2d(base_ch, base_ch//2, 3, padding=1, bias=False),
            nn.BatchNorm2d(base_ch//2),
            nn.SiLU(inplace=True),
            nn.Conv2d(base_ch//2, 1, 1)
        )
        
        self.flow_head = nn.Sequential(
            nn.Conv2d(base_ch, base_ch//2, 3, padding=1, bias=False),
            nn.BatchNorm2d(base_ch//2),
            nn.SiLU(inplace=True),
            nn.Conv2d(base_ch//2, 2, 1)
        )
        
        self.boundary_head = nn.Sequential(
            nn.Conv2d(base_ch, base_ch//2, 3, padding=1, bias=False),
            nn.BatchNorm2d(base_ch//2),
            nn.SiLU(inplace=True),
            nn.Conv2d(base_ch//2, 1, 1)
        )
        
        self._init_weights()

    def _init_weights(self):
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None: 
                    nn.init.zeros_(m.bias)
            elif isinstance(m, nn.BatchNorm2d):
                nn.init.ones_(m.weight)
                nn.init.zeros_(m.bias)
            elif isinstance(m, nn.Linear):
                nn.init.trunc_normal_(m.weight, std=0.02)
                if m.bias is not None: 
                    nn.init.zeros_(m.bias)

    def forward(self, x):
        B, C, H, W = x.shape
        
        # Stem
        x0 = self.stem_conv2(self.stem_conv1(x))
        
        # Encoder
        x1 = self.enc1(x0)
        x2 = self.enc2(x1)
        
        # FPN
        p2 = self.fpn_conv2(x2)
        p2_up = F.interpolate(p2, size=x1.shape[2:], mode='bilinear', align_corners=False)
        p1_in = x1 + p2_up
        p1 = self.fpn_conv1(p1_in)
        p1_up = F.interpolate(p1, size=x0.shape[2:], mode='bilinear', align_corners=False)
        
        # Feature fusion
        lateral_x0 = self.fpn_lateral_stem(x0)
        fused_features = torch.cat([lateral_x0, p1_up], dim=1)
        p1_up_final = self.fusion_conv(fused_features)
        
        # Attention
        attention_map = torch.sigmoid(torch.mean(p1_up_final, dim=1, keepdim=True))
        attended = x0 * (0.5 + 0.5 * attention_map)
        
        # Skip connections
        x0_scaled = self.skip_conv1(x0)
        p1_up_scaled = self.skip_conv2(p1_up_final)
        
        # Final fusion
        fused = torch.cat([x0_scaled, p1_up_scaled, attended], dim=1)
        features = self.decoder(fused)
        features = self.spatial_attn(features)
        
        # Shared features
        shared_feat = self.shared_head(features)
        
        # Task-specific outputs - FIXED: Return raw logits
        outputs = {
            'semantic': self.semantic_head(shared_feat),
            'sdt': self.sdt_head(shared_feat), 
            'skeleton': self.skeleton_head(shared_feat),
            'centroid': self.centroid_head(shared_feat),
            'flow': self.flow_head(shared_feat),
            'boundary': self.boundary_head(shared_feat)
        }
        
        return outputs

print("✅ Fixed SkeletonAwareSpotDetector model ready.")

## NEW LOSS TO FIX FLOW, CENTROIDS, SKELETON: 
import torch
import torch.nn as nn
import torch.nn.functional as F

class DiceLoss(nn.Module):
    def __init__(self, smooth=1.0):
        super().__init__()
        self.smooth = smooth

    def forward(self, predictions, targets):
        predictions = torch.sigmoid(predictions)
        predictions = predictions.reshape(-1)
        targets = targets.reshape(-1)
        intersection = (predictions * targets).sum()
        dice_score = (2. * intersection + self.smooth) / (
            predictions.sum() + targets.sum() + self.smooth
        )
        return 1 - dice_score

class FocalLoss(nn.Module):
    def __init__(self, alpha=0.25, gamma=2.0, reduction='mean'):
        super().__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.reduction = reduction

    def forward(self, inputs, targets):
        bce_loss = F.binary_cross_entropy_with_logits(inputs, targets, reduction='none')
        pt = torch.exp(-bce_loss)
        focal_loss = self.alpha * (1 - pt) ** self.gamma * bce_loss
        if self.reduction == 'mean':
            return focal_loss.mean()
        elif self.reduction == 'sum':
            return focal_loss.sum()
        return focal_loss

class StabilizedFlowLoss(nn.Module):
    def __init__(self, huber_delta=0.1, smoothness_weight=0.05):
        super().__init__()
        self.huber_delta = huber_delta
        self.smoothness_weight = smoothness_weight
        self.huber_loss = nn.SmoothL1Loss(reduction='none', beta=huber_delta)

    def forward(self, flow_pred, flow_gt, semantic_mask):
        flow_pred = torch.tanh(flow_pred)
        semantic_prob = torch.sigmoid(semantic_mask)
        flow_mask = (semantic_prob > 0.2).float()
        if flow_mask.sum() < 4:
            return torch.tensor(0.0, device=flow_pred.device, requires_grad=True)
        flow_diff = self.huber_loss(flow_pred, flow_gt)
        masked_loss = flow_diff * flow_mask.unsqueeze(1)
        data_loss = masked_loss.sum() / (flow_mask.sum() * flow_pred.size(1) + 1e-8)
        # Smoothness regularization
        if flow_mask.sum() > 16:
            grad_x = torch.abs(flow_pred[:, :, :, 1:] - flow_pred[:, :, :, :-1])
            grad_y = torch.abs(flow_pred[:, :, 1:, :] - flow_pred[:, :, :-1, :])
            mask_x = flow_mask[:, :, :, 1:] * flow_mask[:, :, :, :-1]
            mask_y = flow_mask[:, :, 1:, :] * flow_mask[:, :, :-1, :]
            smooth_x = (grad_x * mask_x.unsqueeze(1)).sum() / (mask_x.sum() + 1e-8)
            smooth_y = (grad_y * mask_y.unsqueeze(1)).sum() / (mask_y.sum() + 1e-8)
            smoothness_loss = (smooth_x + smooth_y) / 2
        else:
            smoothness_loss = torch.tensor(0.0, device=flow_pred.device)
        total_loss = data_loss + self.smoothness_weight * smoothness_loss
        return torch.clamp(total_loss, max=10.0)

class SkeletonAwareLoss(nn.Module):
    def __init__(self, weights=None):
        super().__init__()
        # Tuned weights for stability and sharpness
        self.weights = weights or {
            'semantic': 1.8,
            'sdt': 0.8,
            'skeleton': 1.2,
            'centroid': 1.5,
            'flow': 0.4,
            'boundary': 1.0
        }
        self.dice_loss = DiceLoss(smooth=1.0)
        self.focal_loss = FocalLoss(alpha=0.25, gamma=2.2)
        self.mse_loss = nn.MSELoss()
        self.huber_loss = nn.SmoothL1Loss(beta=0.1)
        self.flow_loss_fn = StabilizedFlowLoss(huber_delta=0.1, smoothness_weight=0.05)
        print(f"ImprovedSkeletonAwareLoss initialized with STABILIZED weights: {self.weights}")

    def skeleton_consistency_loss(self, skeleton_pred, semantic_pred):
        skeleton_prob = torch.sigmoid(skeleton_pred)
        semantic_prob = torch.sigmoid(semantic_pred)
        outside_penalty = skeleton_prob * (1 - semantic_prob)
        kernel = torch.ones(1, 1, 3, 3, device=skeleton_pred.device) / 9.0
        eroded = F.conv2d(skeleton_prob, kernel, padding=1)
        thinning_loss = F.mse_loss(skeleton_prob, eroded)
        return outside_penalty.mean() + 0.3 * thinning_loss

    def centroid_sparsity_loss(self, centroid_pred, semantic_pred):
        centroid_prob = torch.sigmoid(centroid_pred)
        semantic_prob = torch.sigmoid(semantic_pred)
        masked_centroid = centroid_prob * semantic_prob
        sparsity = masked_centroid.mean()
        max_pooled = F.max_pool2d(masked_centroid, kernel_size=5, stride=1, padding=2)
        peak_loss = F.mse_loss(masked_centroid, max_pooled)
        return 0.5 * sparsity + 0.5 * peak_loss

    def forward(self, predictions, targets):
        losses = {}
        semantic_gt = targets[:, 0:1]
        sdt_gt = targets[:, 1:2]
        skeleton_gt = targets[:, 2:3]
        centroid_gt = targets[:, 3:4]
        flow_gt = targets[:, 4:6]
        boundary_gt = targets[:, 6:7]

        # 1. Semantic Loss
        semantic_pred = predictions['semantic']
        losses['semantic'] = (
            self.focal_loss(semantic_pred, semantic_gt) +
            self.dice_loss(semantic_pred, semantic_gt)
        ) / 2

        # 2. SDT Loss (Huber)
        sdt_pred = torch.sigmoid(predictions['sdt'])
        losses['sdt'] = self.huber_loss(sdt_pred, sdt_gt)

        # 3. Skeleton Loss + Consistency
        skeleton_pred = predictions['skeleton']
        skeleton_focal = self.focal_loss(skeleton_pred, skeleton_gt)
        skeleton_dice = self.dice_loss(skeleton_pred, skeleton_gt)
        skeleton_consistency = self.skeleton_consistency_loss(skeleton_pred, semantic_pred)
        losses['skeleton'] = (
            (skeleton_focal + skeleton_dice) / 2 +
            0.2 * skeleton_consistency
        )

        # 4. Centroid Loss + Sparsity
        centroid_pred = predictions['centroid']
        centroid_basic = self.huber_loss(torch.sigmoid(centroid_pred), centroid_gt)
        centroid_sparsity = self.centroid_sparsity_loss(centroid_pred, semantic_pred)
        losses['centroid'] = centroid_basic + 0.1 * centroid_sparsity

        # 5. Flow Loss (Stabilized)
        losses['flow'] = self.flow_loss_fn(predictions['flow'], flow_gt, semantic_gt)

        # 6. Boundary Loss
        boundary_pred = predictions['boundary']
        losses['boundary'] = (
            self.focal_loss(boundary_pred, boundary_gt) +
            self.dice_loss(boundary_pred, boundary_gt)
        ) / 2

        # Weighted sum with clamping
        total_loss = 0.0
        for task, loss_val in losses.items():
            clamped_loss = torch.clamp(loss_val, max=50.0)
            total_loss += self.weights[task] * clamped_loss
            losses[task] = clamped_loss
        total_loss = torch.clamp(total_loss, max=100.0)
        losses['total'] = total_loss
        return total_loss, losses

print("✅ ImprovedSkeletonAwareLoss with stabilized flow loss and regularization is ready!")

# import torch
# import torch.nn as nn
# import torch.nn.functional as F

# class DiceLoss(nn.Module):
#     def __init__(self, smooth=1.0):
#         super().__init__()
#         self.smooth = smooth
        
#     def forward(self, predictions, targets):
#         # Apply sigmoid to get probabilities
#         predictions = torch.sigmoid(predictions)
        
#         # FIXED: Use reshape instead of view for better compatibility
#         predictions = predictions.reshape(-1)
#         targets = targets.reshape(-1)
        
#         # Calculate Dice
#         intersection = (predictions * targets).sum()
#         dice_score = (2. * intersection + self.smooth) / (
#             predictions.sum() + targets.sum() + self.smooth
#         )
#         return 1 - dice_score

# class FocalLoss(nn.Module):
#     def __init__(self, alpha=0.25, gamma=2.0, reduction='mean'):
#         super().__init__()
#         self.alpha = alpha
#         self.gamma = gamma
#         self.reduction = reduction

#     def forward(self, inputs, targets):
#         # BCE loss with logits
#         bce_loss = F.binary_cross_entropy_with_logits(inputs, targets, reduction='none')
#         pt = torch.exp(-bce_loss)
#         focal_loss = self.alpha * (1 - pt) ** self.gamma * bce_loss

#         if self.reduction == 'mean':
#             return focal_loss.mean()
#         elif self.reduction == 'sum':
#             return focal_loss.sum()
#         return focal_loss


# class SkeletonAwareLoss(nn.Module):
#     def __init__(self, weights=None):
#         super().__init__()
        
#         # Task weights
#         if weights is None:
#             self.weights = {
#                 'semantic': 2.0,
#                 'sdt': 1.0,
#                 'skeleton': 1.5,
#                 'centroid': 1.2,
#                 'flow': 0.8,
#                 'boundary': 1.0
#             }
#         else:
#             self.weights = weights
            
#         # Loss functions
#         self.dice_loss = DiceLoss(smooth=1.0)
#         self.focal_loss = FocalLoss(alpha=0.25, gamma=2.0)
#         self.mse_loss = nn.MSELoss()
#         self.smooth_l1_loss = nn.SmoothL1Loss()
#         self.bce_loss = nn.BCEWithLogitsLoss()
        
#         print(f"SkeletonAwareLoss initialized with weights: {self.weights}")

#     def forward(self, predictions, targets):
#         """
#         Args:
#             predictions: dict with keys 'semantic', 'sdt', 'skeleton', 'centroid', 'flow', 'boundary'
#             targets: tensor of shape (B, 7, H, W) where:
#                 - targets[:, 0] = semantic mask
#                 - targets[:, 1] = SDT
#                 - targets[:, 2] = skeleton
#                 - targets[:, 3] = centroid heatmap
#                 - targets[:, 4:6] = flow field (Y, X)
#                 - targets[:, 6] = boundary
#         """
#         losses = {}
        
#         # Extract targets
#         semantic_gt = targets[:, 0:1]      # (B, 1, H, W)
#         sdt_gt = targets[:, 1:2]           # (B, 1, H, W)
#         skeleton_gt = targets[:, 2:3]      # (B, 1, H, W)
#         centroid_gt = targets[:, 3:4]      # (B, 1, H, W)
#         flow_gt = targets[:, 4:6]          # (B, 2, H, W)
#         boundary_gt = targets[:, 6:7]      # (B, 1, H, W)
        
#         # Create mask for flow computation (only where semantic mask > 0)
#         flow_mask = (semantic_gt > 0.5).float()
        
#         # 1. Semantic Loss (Binary segmentation)
#         semantic_pred = predictions['semantic']
#         semantic_focal = self.focal_loss(semantic_pred, semantic_gt)
#         semantic_dice = self.dice_loss(semantic_pred, semantic_gt)
#         losses['semantic'] = (semantic_focal + semantic_dice) / 2
        
#         # 2. SDT Loss (Regression)
#         sdt_pred = torch.sigmoid(predictions['sdt'])  # Bound to [0,1]
#         losses['sdt'] = self.mse_loss(sdt_pred, sdt_gt)
        
#         # 3. Skeleton Loss (Binary)
#         skeleton_pred = predictions['skeleton']
#         skeleton_focal = self.focal_loss(skeleton_pred, skeleton_gt)
#         skeleton_dice = self.dice_loss(skeleton_pred, skeleton_gt)
#         losses['skeleton'] = (skeleton_focal + skeleton_dice) / 2
        
#         # 4. Centroid Loss (Regression with peaks)
#         centroid_pred = torch.sigmoid(predictions['centroid'])  # Bound to [0,1]
#         losses['centroid'] = self.mse_loss(centroid_pred, centroid_gt)
        
#         # 5. Flow Loss (Regression, only in foreground regions)
#         flow_pred = torch.tanh(predictions['flow'])  # Bound to [-1,1]
        
#         if flow_mask.sum() > 0:
#             # Masked flow loss
#             flow_diff = (flow_pred - flow_gt) * flow_mask
#             losses['flow'] = torch.sum(flow_diff ** 2) / (flow_mask.sum() + 1e-6)
#         else:
#             losses['flow'] = torch.tensor(0.0, device=predictions['flow'].device, requires_grad=True)
        
#         # 6. Boundary Loss (Binary)
#         boundary_pred = predictions['boundary']
#         boundary_focal = self.focal_loss(boundary_pred, boundary_gt)
#         boundary_dice = self.dice_loss(boundary_pred, boundary_gt)
#         losses['boundary'] = (boundary_focal + boundary_dice) / 2
        
#         # Total weighted loss
#         total_loss = 0.0
#         for task, loss_val in losses.items():
#             total_loss += self.weights[task] * loss_val
        
#         losses['total'] = total_loss
#         return total_loss, losses

# class ImprovedSkeletonAwareLoss(nn.Module):
#     """Enhanced version with additional regularization"""
#     def __init__(self, weights=None):
#         super().__init__()
        
#         if weights is None:
#             self.weights = {
#                 'semantic': 2.0,
#                 'sdt': 1.0,
#                 'skeleton': 1.5,
#                 'centroid': 1.2,
#                 'flow': 0.8,
#                 'boundary': 1.0
#             }
#         else:
#             self.weights = weights
            
#         self.dice_loss = DiceLoss(smooth=1.0)
#         self.focal_loss = FocalLoss(alpha=0.25, gamma=2.0)
#         self.mse_loss = nn.MSELoss()
#         self.smooth_l1_loss = nn.SmoothL1Loss()
        
#         print(f"ImprovedSkeletonAwareLoss initialized with weights: {self.weights}")

#     def centroid_consistency_loss(self, centroid_pred, semantic_pred, centroid_gt):
#         """Ensure centroids are consistent with semantic segmentation"""
#         centroid_prob = torch.sigmoid(centroid_pred)
#         semantic_prob = torch.sigmoid(semantic_pred)
        
#         # Centroids should be high where semantic is high
#         consistency_loss = F.mse_loss(
#             centroid_prob * semantic_prob, 
#             centroid_gt * semantic_prob
#         )
#         return consistency_loss

#     def skeleton_consistency_loss(self, skeleton_pred, semantic_pred):
#         """Skeleton should be inside semantic regions"""
#         skeleton_prob = torch.sigmoid(skeleton_pred)
#         semantic_prob = torch.sigmoid(semantic_pred)
        
#         # Penalize skeleton predictions outside semantic regions
#         outside_penalty = skeleton_prob * (1 - semantic_prob)
#         return outside_penalty.mean()

#     def flow_smoothness_loss(self, flow_pred, mask):
#         """Encourage smooth flow fields"""
#         if mask.sum() < 4:  # Need enough pixels for gradient computation
#             return torch.tensor(0.0, device=flow_pred.device, requires_grad=True)
            
#         flow_masked = flow_pred * mask
        
#         # Compute gradients
#         grad_x = torch.abs(flow_masked[:, :, :, 1:] - flow_masked[:, :, :, :-1])
#         grad_y = torch.abs(flow_masked[:, :, 1:, :] - flow_masked[:, :, :-1, :])
        
#         # Only consider gradients where both pixels are in mask
#         mask_x = mask[:, :, :, 1:] * mask[:, :, :, :-1]
#         mask_y = mask[:, :, 1:, :] * mask[:, :, :-1, :]
        
#         if mask_x.sum() > 0:
#             smoothness_x = (grad_x * mask_x).sum() / (mask_x.sum() + 1e-6)
#         else:
#             smoothness_x = torch.tensor(0.0, device=flow_pred.device)
            
#         if mask_y.sum() > 0:
#             smoothness_y = (grad_y * mask_y).sum() / (mask_y.sum() + 1e-6)
#         else:
#             smoothness_y = torch.tensor(0.0, device=flow_pred.device)
        
#         return (smoothness_x + smoothness_y) / 2

#     def forward(self, predictions, targets):
#         losses = {}
        
#         # Extract targets
#         semantic_gt = targets[:, 0:1]
#         sdt_gt = targets[:, 1:2]
#         skeleton_gt = targets[:, 2:3]
#         centroid_gt = targets[:, 3:4]
#         flow_gt = targets[:, 4:6]
#         boundary_gt = targets[:, 6:7]
        
#         flow_mask = (semantic_gt > 0.5).float()
        
#         # Basic losses (same as before)
#         semantic_pred = predictions['semantic']
#         losses['semantic'] = (
#             self.focal_loss(semantic_pred, semantic_gt) + 
#             self.dice_loss(semantic_pred, semantic_gt)
#         ) / 2
        
#         sdt_pred = torch.sigmoid(predictions['sdt'])
#         losses['sdt'] = self.mse_loss(sdt_pred, sdt_gt)
        
#         skeleton_pred = predictions['skeleton']
#         losses['skeleton'] = (
#             self.focal_loss(skeleton_pred, skeleton_gt) + 
#             self.dice_loss(skeleton_pred, skeleton_gt)
#         ) / 2
        
#         centroid_pred = torch.sigmoid(predictions['centroid'])
#         losses['centroid'] = self.mse_loss(centroid_pred, centroid_gt)
        
#         flow_pred = torch.tanh(predictions['flow'])
#         if flow_mask.sum() > 0:
#             flow_diff = (flow_pred - flow_gt) * flow_mask
#             losses['flow'] = torch.sum(flow_diff ** 2) / (flow_mask.sum() + 1e-6)
#         else:
#             losses['flow'] = torch.tensor(0.0, device=flow_pred.device, requires_grad=True)
            
#         boundary_pred = predictions['boundary']
#         losses['boundary'] = (
#             self.focal_loss(boundary_pred, boundary_gt) + 
#             self.dice_loss(boundary_pred, boundary_gt)
#         ) / 2
        
#         # Additional consistency losses
#         centroid_consistency = self.centroid_consistency_loss(
#             predictions['centroid'], predictions['semantic'], centroid_gt
#         )
#         skeleton_consistency = self.skeleton_consistency_loss(
#             predictions['skeleton'], predictions['semantic']
#         )
#         flow_smoothness = self.flow_smoothness_loss(predictions['flow'], flow_mask)
        
#         # Add regularization terms
#         losses['centroid'] += 0.1 * centroid_consistency
#         losses['skeleton'] += 0.1 * skeleton_consistency
#         losses['flow'] += 0.05 * flow_smoothness
        
#         # Total weighted loss
#         total_loss = sum(self.weights[task] * loss_val for task, loss_val in losses.items())
#         losses['total'] = total_loss
        
#         return total_loss, losses

# print("✅ Fixed loss functions ready!")

## NEW INSTANCE WITH BOUNDARY
import numpy as np
import os
from tqdm import tqdm
from scipy.ndimage import maximum_filter, label, distance_transform_edt
from skimage.measure import regionprops
from skimage.segmentation import watershed
from skimage.morphology import remove_small_objects
import tifffile
import torch
import torch.nn.functional as F
from torch.cuda.amp import autocast

def optimized_skeleton_aware_instance_segmentation(
    semantic,
    sdt_scalar, 
    skeleton,
    centroid_map,
    flow,
    boundary,  # ADD boundary parameter
    min_size=3,
    nms_threshold=0.4,
    semantic_threshold=0.4,
    flow_refine_factor=0.2
):
    """
    Optimized instance segmentation with better parameter tuning
    """
    H, W = semantic.shape
    
    # More conservative semantic threshold for better precision
    binary_mask = (semantic > semantic_threshold).astype(np.uint8)
    
    # Remove small noise first
    binary_mask = remove_small_objects(binary_mask.astype(bool), min_size=min_size).astype(np.uint8)
    
    if np.sum(binary_mask) == 0:
        return np.zeros_like(semantic, dtype=np.int32), []
    
    # ENHANCED Strategy 2: Boundary-guided NMS for perfect touching spot separation
    
    # Step 1: Create boundary penalty map
    boundary_penalty = np.zeros_like(boundary)
    boundary_penalty[boundary > 0.4] = 0.8  # Strong penalty for clear boundaries
    boundary_penalty[boundary > 0.6] = 1.2  # Even stronger for very clear boundaries
    
    # Step 2: Multi-scale boundary detection for touching spots
    from scipy.ndimage import gaussian_filter
    boundary_smooth = gaussian_filter(boundary, sigma=1.0)  # Smooth boundaries
    touching_regions = (boundary_smooth > 0.3) & (boundary_smooth < 0.8)  # Likely touching areas
    boundary_penalty[touching_regions] = 0.6  # Moderate penalty for touching regions
    
    # Step 3: Boundary-guided centroid adjustment
    # Penalize centroids that fall on boundaries (these cause poor separation)
    adjusted_centroids = centroid_map * (1.0 - boundary_penalty * 0.4)
    
    # Step 4: Skeleton-guided enhancement (prefer skeleton intersections)
    skeleton_enhancement = (skeleton > 0.4).astype(float) * 0.3 + 1.0
    final_centroids = adjusted_centroids * skeleton_enhancement
    
    # Step 5: Multi-scale NMS for robust peak detection
    # Use different scales to catch both small and large spots
    local_maxima_3x3 = maximum_filter(final_centroids, size=3) == final_centroids
    local_maxima_5x5 = maximum_filter(final_centroids, size=5) == final_centroids  
    local_maxima_7x7 = maximum_filter(final_centroids, size=7) == final_centroids
    
    # Combine multi-scale maxima (prefer larger scales for separation)
    combined_maxima = local_maxima_5x5 | (local_maxima_3x3 & (final_centroids > nms_threshold * 1.2))
    
    # Final peak selection with boundary avoidance
    peaks = (
        combined_maxima & 
        (final_centroids > nms_threshold) & 
        (binary_mask > 0) &
        (boundary < 0.5)  # CRITICAL: Avoid placing seeds on boundaries
    )
    
    # Fallback for cases where boundary avoidance removes all peaks
    if np.sum(peaks) == 0:
        print("  Boundary avoidance too strict, using relaxed criteria...")
        peaks = (
            local_maxima_5x5 & 
            (centroid_map > nms_threshold * 0.7) & 
            (binary_mask > 0)
        )
    
    centroid_coords = np.column_stack(np.where(peaks))
    
    if len(centroid_coords) == 0:
        return np.zeros_like(semantic, dtype=np.int32), []
    
    print(f"  Boundary-guided NMS: Found {len(centroid_coords)} centroids (avoided boundaries)")
    
    # ENHANCED: Intelligent seed placement for touching spots
    seeds = np.zeros_like(semantic, dtype=np.int32)
    
    for idx, (y, x) in enumerate(centroid_coords, 1):
        # Check local boundary strength around centroid
        y_min, y_max = max(0, y-2), min(H, y+3)
        x_min, x_max = max(0, x-2), min(W, x+3)
        local_boundary = boundary[y_min:y_max, x_min:x_max]
        
        if np.mean(local_boundary) < 0.3:  # Low boundary = good seed location
            # Place seed with slight dilation for better watershed
            seeds[y_min:y_max, x_min:x_max] = np.maximum(seeds[y_min:y_max, x_min:x_max], idx)
        else:
            # High boundary area - find nearest low-boundary pixel within skeleton
            local_skeleton = skeleton[y_min:y_max, x_min:x_max]
            local_sem = semantic[y_min:y_max, x_min:x_max]
            
            # Prefer skeleton pixels with low boundary values
            quality_map = local_skeleton * (1.0 - local_boundary) * local_sem
            if np.max(quality_map) > 0.1:
                best_pos = np.unravel_index(np.argmax(quality_map), quality_map.shape)
                adj_y, adj_x = y_min + best_pos[0], x_min + best_pos[1]
                seeds[adj_y, adj_x] = idx
            else:
                # Fallback: use original position but smaller seed
                seeds[y, x] = idx
    
    # Improved watershed map combining multiple cues
    # 1. Inverted SDT (peaks at centers)
    sdt_component = 1.0 - sdt_scalar
    
    # 2. Skeleton valleys (low values at skeleton)  
    skeleton_component = 1.0 - (skeleton > 0.2).astype(float) * 0.8
    
    # 3. Distance to boundary for separation
    distance_component = distance_transform_edt(binary_mask)
    distance_component = 1.0 - (distance_component / (np.max(distance_component) + 1e-6))
    
    # 4. **NEW**: Boundary prediction for instance separation
    # High boundary values = separation barriers for watershed
    boundary_component = boundary * 0.8  # Scale boundary strength
    
    # Combine components with learned weights (including boundary)
    combined_map = (
        0.35 * sdt_component + 
        0.25 * skeleton_component + 
        0.25 * distance_component +
        0.15 * boundary_component  # Add boundary as separation cue
    )
    
    # Optional: Flow-based separation enhancement
    if flow is not None and flow.shape[0] == 2:
        flow_y, flow_x = flow
        # Compute flow divergence (indicates boundaries)
        dy = np.gradient(flow_y, axis=0)
        dx = np.gradient(flow_x, axis=1) 
        divergence = np.abs(dy) + np.abs(dx)
        divergence = divergence / (np.max(divergence) + 1e-6)  # Normalize
        
        # Add divergence as separation cue (high divergence = boundaries)
        combined_map = 0.8 * combined_map + 0.2 * divergence
    
    # Watershed segmentation
    instance_labels = watershed(combined_map, seeds, mask=binary_mask)
    
    # Post-processing: merge very small fragments
    regions = regionprops(instance_labels)
    for region in regions:
        if region.area < min_size:
            instance_labels[instance_labels == region.label] = 0
    
    # Relabel consecutively
    labeled_array, num_features = label(instance_labels > 0)
    instance_labels = labeled_array
    
    # Extract spot information with flow refinement
    final_spots = []
    regions = regionprops(instance_labels)
    
    for region in regions:
        if region.area < min_size:
            continue
            
        mask = (instance_labels == region.label)
        cy, cx = region.centroid
        
        # Flow-based centroid refinement
        if flow is not None and flow.shape[0] == 2:
            ys, xs = np.where(mask)
            if len(ys) > 0:
                avg_flow_y = np.mean(flow[0, ys, xs])
                avg_flow_x = np.mean(flow[1, ys, xs])
                
                # Only apply refinement if flow is significant and consistent
                flow_magnitude = np.sqrt(avg_flow_y**2 + avg_flow_x**2)
                if flow_magnitude > 0.1:
                    refined_cy = cy + flow_refine_factor * avg_flow_y
                    refined_cx = cx + flow_refine_factor * avg_flow_x
                    
                    # Ensure refined position is within bounds
                    refined_cy = np.clip(refined_cy, 0, H-1)
                    refined_cx = np.clip(refined_cx, 0, W-1)
                else:
                    refined_cy, refined_cx = cy, cx
            else:
                refined_cy, refined_cx = cy, cx
        else:
            refined_cy, refined_cx = cy, cx
        
        # Calculate quality scores (including boundary)
        sdt_score = np.mean(sdt_scalar[mask])
        centroid_score = np.mean(centroid_map[mask]) 
        semantic_score = np.mean(semantic[mask])
        skeleton_score = np.mean(skeleton[mask])
        boundary_score = np.mean(boundary[mask])  # Add boundary score
        
        # Weighted combined score (emphasize centroid and semantic)
        combined_score = (
            0.25 * semantic_score + 
            0.25 * centroid_score + 
            0.2 * sdt_score + 
            0.15 * skeleton_score +
            0.15 * (1.0 - boundary_score)  # Low boundary = good spot interior
        )
        
        final_spots.append({
            'y': float(refined_cy),
            'x': float(refined_cx), 
            'original_y': float(cy),
            'original_x': float(cx),
            'score': float(combined_score),
            'size': int(region.area),
            'sdt_score': float(sdt_score),
            'centroid_score': float(centroid_score),
            'semantic_score': float(semantic_score),
            'skeleton_score': float(skeleton_score),
            'boundary_score': float(boundary_score),
            'eccentricity': float(region.eccentricity),  # Shape info
            'solidity': float(region.solidity)  # Compactness
        })
    
    print(f"Instance segmentation: Found {len(final_spots)} spots (min_size={min_size})")
    return instance_labels, final_spots


def fast_tile_inference(
    model,
    image_path, 
    device='cuda',
    patch_size=256,
    overlap=0.25,  # Reduced overlap for speed
    batch_size=8,
    return_all_outputs=True
):
    """
    Optimized tiled inference with proper model output handling
    """
    # Load and normalize image
    image = tifffile.imread(image_path).astype(np.float32)
    H, W = image.shape
    
    # Robust normalization 
    vmin, vmax = np.percentile(image[image > 0], (1.0, 99.0))  # Ignore zeros
    image_norm = np.clip((image - vmin) / (vmax - vmin + 1e-8), 0, 1).astype(np.float32)
    
    model.eval()
    model.to(device)
    
    # Calculate stride and patches
    stride = max(int(patch_size * (1 - overlap)), patch_size // 4)  # Minimum stride
    
    y_coords = list(range(0, H - patch_size + 1, stride)) + [max(0, H - patch_size)]
    x_coords = list(range(0, W - patch_size + 1, stride)) + [max(0, W - patch_size)]
    
    # Remove duplicates
    y_coords = sorted(list(set(y_coords)))
    x_coords = sorted(list(set(x_coords)))
    
    total_patches = len(y_coords) * len(x_coords)
    print(f"Processing {total_patches} patches ({len(y_coords)}x{len(x_coords)}) with stride={stride}")
    
    # Initialize output arrays
    semantic_sum = np.zeros((H, W), dtype=np.float32)
    sdt_sum = np.zeros((H, W), dtype=np.float32) 
    skeleton_sum = np.zeros((H, W), dtype=np.float32)
    centroid_sum = np.zeros((H, W), dtype=np.float32)
    boundary_sum = np.zeros((H, W), dtype=np.float32)
    flow_sum = np.zeros((2, H, W), dtype=np.float32)
    count_map = np.zeros((H, W), dtype=np.float32)
    
    with torch.no_grad():
        patch_batch = []
        coords_batch = []
        
        pbar = tqdm(total=total_patches, desc="Fast Inference")
        
        for y_start in y_coords:
            for x_start in x_coords:
                y_end = min(y_start + patch_size, H)
                x_end = min(x_start + patch_size, W)
                
                # Extract patch (no padding needed with proper coordinates)
                patch = image_norm[y_start:y_end, x_start:x_end]
                
                # Pad only if necessary
                if patch.shape != (patch_size, patch_size):
                    pad_h = patch_size - patch.shape[0]
                    pad_w = patch_size - patch.shape[1]
                    patch = np.pad(patch, ((0, pad_h), (0, pad_w)), mode='reflect')
                
                patch_batch.append(patch)
                coords_batch.append((y_start, y_end, x_start, x_end))
                
                # Process batch
                if len(patch_batch) >= batch_size or (y_start == y_coords[-1] and x_start == x_coords[-1]):
                    # Prepare batch tensor
                    batch_array = np.stack(patch_batch)
                    batch_tensor = torch.from_numpy(batch_array).unsqueeze(1).float().to(device)
                    
                    # Forward pass with mixed precision
                    with autocast('cuda' if device == 'cuda' else 'cpu'):
                        outputs = model(batch_tensor)
                    
                    # Process each patch in batch
                    for i, (y_s, y_e, x_s, x_e) in enumerate(coords_batch):
                        h, w = y_e - y_s, x_e - x_s
                        
                        # FIXED: Use correct output keys from your model
                        semantic_patch = torch.sigmoid(outputs['semantic'][i, 0]).cpu().numpy()[:h, :w]
                        sdt_patch = torch.sigmoid(outputs['sdt'][i, 0]).cpu().numpy()[:h, :w]  
                        skeleton_patch = torch.sigmoid(outputs['skeleton'][i, 0]).cpu().numpy()[:h, :w]
                        centroid_patch = torch.sigmoid(outputs['centroid'][i, 0]).cpu().numpy()[:h, :w]
                        boundary_patch = torch.sigmoid(outputs['boundary'][i, 0]).cpu().numpy()[:h, :w]
                        flow_patch = torch.tanh(outputs['flow'][i]).cpu().numpy()[:, :h, :w]  # 2 channels
                        
                        # Accumulate results
                        semantic_sum[y_s:y_e, x_s:x_e] += semantic_patch
                        sdt_sum[y_s:y_e, x_s:x_e] += sdt_patch
                        skeleton_sum[y_s:y_e, x_s:x_e] += skeleton_patch 
                        centroid_sum[y_s:y_e, x_s:x_e] += centroid_patch
                        boundary_sum[y_s:y_e, x_s:x_e] += boundary_patch
                        flow_sum[:, y_s:y_e, x_s:x_e] += flow_patch
                        count_map[y_s:y_e, x_s:x_e] += 1
                    
                    # Clear batch
                    patch_batch = []
                    coords_batch = []
                    pbar.update(len(coords_batch) if coords_batch else batch_size)
        
        pbar.close()
    
    # Average overlapping regions
    eps = 1e-8
    semantic_out = semantic_sum / (count_map + eps)
    sdt_out = sdt_sum / (count_map + eps)
    skeleton_out = skeleton_sum / (count_map + eps) 
    centroid_out = centroid_sum / (count_map + eps)
    boundary_out = boundary_sum / (count_map + eps)
    flow_out = flow_sum / (count_map[None, :, :] + eps)
    
    print(f"Inference stats:")
    print(f"  Semantic: [{semantic_out.min():.3f}, {semantic_out.max():.3f}]")
    print(f"  Centroid: [{centroid_out.min():.3f}, {centroid_out.max():.3f}]") 
    print(f"  Flow magnitude: [{np.linalg.norm(flow_out, axis=0).min():.3f}, {np.linalg.norm(flow_out, axis=0).max():.3f}]")
    
    # Perform instance segmentation
    print("Running optimized instance segmentation...")
    instance_labels, filtered_spots = optimized_skeleton_aware_instance_segmentation(
        semantic=semantic_out,
        sdt_scalar=sdt_out,
        skeleton=skeleton_out, 
        centroid_map=centroid_out,
        flow=flow_out,
        boundary=boundary_out,  # ADD boundary input
        min_size=3,  # Slightly larger minimum
        nms_threshold=0.4,  # More conservative
        semantic_threshold=0.4,  # Higher threshold
        flow_refine_factor=0.2  # Less aggressive refinement
    )
    
    results = {
        'instance_labels': instance_labels,
        'spots': filtered_spots,
        'semantic': semantic_out,
        'sdt': sdt_out,
        'skeleton': skeleton_out,
        'centroid': centroid_out,
        'boundary': boundary_out,
        'flow': flow_out,
        'count_map': count_map  # For debugging overlap
    }
    
    print(f"✅ Fast inference completed for {os.path.basename(image_path)}")
    print(f"   Found {len(filtered_spots)} high-quality spots")
    return results




# CORRECTED Usage Example
if __name__ == "__main__":
    import os
    from pathlib import Path
    
    # Configuration
    model_path = '/mnt/d/Users/<USER>/Documents/spot_detector_model_sota_centroid/best_model.pth'
    image_path = '/mnt/d/Users/<USER>/FISH_spots/2d/synthetic/images/0  Series001  Green--FLUO--FITC_tile_1.tif'
    save_dir = Path(image_path).parent.parent / "inference_results"
    save_dir.mkdir(exist_ok=True)
    
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"Using device: {device}")
    
    # Load model
    print(f"Loading model from: {model_path}")
    checkpoint = torch.load(model_path, map_location=device, weights_only=False)
    
    # IMPORTANT: Import your model class here
    # from your_model_file import SkeletonAwareSpotDetector
    model = SkeletonAwareSpotDetector(in_ch=1, base_ch=48)
    model.load_state_dict(checkpoint['model_state_dict'])
    model.to(device)
    
    # Run optimized inference  
    print(f"Running optimized inference on: {Path(image_path).name}")
    results = fast_tile_inference(
        model=model,
        image_path=image_path,
        device=device,
        patch_size=256,
        overlap=0.25,  # Reduced for speed
        batch_size=12,  # Increased batch size
        return_all_outputs=True
    )
    
    # Results
    print(f"✅ Optimized inference completed!")
    print(f"   Found {len(results['spots'])} spots")
    print(f"   Average spot score: {np.mean([s['score'] for s in results['spots']]):.3f}")
    
    # Save results
    base_name = Path(image_path).stem
    tifffile.imwrite(save_dir / f"{base_name}_instance_labels.tif", results['instance_labels'].astype(np.uint16))
    tifffile.imwrite(save_dir / f"{base_name}_semantic.tif", (results['semantic'] * 255).astype(np.uint8))
    tifffile.imwrite(save_dir / f"{base_name}_centroids.tif", (results['centroid'] * 255).astype(np.uint8))
    np.save(save_dir / f"{base_name}_spots.npy", results['spots'])
    
    print(f"💾 Results saved to: {save_dir}")

##OLD INSTANCE SEGMENTATION

import numpy as np
import os
from tqdm import tqdm
from scipy.ndimage import maximum_filter
from skimage.measure import regionprops
from skimage.segmentation import watershed
import tifffile
import torch
import torch.nn.functional as F
from torch.cuda.amp import autocast

def skeleton_aware_instance_segmentation(
    semantic,
    sdt_scalar,
    skeleton,
    centroid_map,
    flow,
    min_size=2,
    nms_threshold=0.3,
    flow_refine_factor=0.3
):
    H, W = semantic.shape
    binary_mask = (semantic > 0.3).astype(np.uint8)

    # NMS for centroid seeds
    peaks = (centroid_map > nms_threshold) & (maximum_filter(centroid_map, size=3) == centroid_map)
    centroid_coords = np.column_stack(np.where(peaks))

    # Seeds for watershed, refined by skeleton
    seeds = np.zeros_like(semantic, dtype=np.int32)
    for idx, (y, x) in enumerate(centroid_coords, 1):
        if skeleton[y, x] > 0.1:
            seeds[y, x] = idx

    # Skeleton-aware watershed map
    skel_mask = (skeleton > 0.5).astype(float)
    skel_ridges = 1.0 - skel_mask
    skel_influence = 0.7
    combined_map = (1.0 - skel_influence) * sdt_scalar + skel_influence * skel_ridges

    # Flow-guided refinement
    if flow is not None and flow.shape[0] == 2:
        flow_divergence = np.zeros_like(semantic)
        flow_y, flow_x = flow
        flow_divergence[1:-1, 1:-1] = (
            np.abs(flow_y[1:-1, 2:] - flow_y[1:-1, :-2]) +
            np.abs(flow_x[2:, 1:-1] - flow_x[:-2, 1:-1])
        )
        flow_influence = 0.3
        combined_map = (1.0 - flow_influence) * combined_map + flow_influence * (1.0 - flow_divergence)

    # Watershed segmentation
    instance_labels = watershed(-combined_map, seeds, mask=binary_mask)

    # Flow-based refinement per instance
    if flow is not None and flow.shape[0] == 2:
        for region_id in np.unique(instance_labels):
            if region_id == 0:
                continue
            mask = (instance_labels == region_id)
            ys, xs = np.where(mask)
            if len(ys) > 0:
                avg_flow_y = np.mean(flow[0, ys, xs])
                avg_flow_x = np.mean(flow[1, ys, xs])
                if np.abs(avg_flow_y) > 0.1 or np.abs(avg_flow_x) > 0.1:
                    centroid_y, centroid_x = np.mean(ys), np.mean(xs)
                    dist_y = centroid_y - ys
                    dist_x = centroid_x - xs
                    dist_mag = np.sqrt(dist_y**2 + dist_x**2) + 1e-6
                    norm_dist_y = dist_y / dist_mag
                    norm_dist_x = dist_x / dist_mag
                    flow_mag = np.sqrt(avg_flow_y**2 + avg_flow_x**2) + 1e-6
                    norm_flow_y = avg_flow_y / flow_mag
                    norm_flow_x = avg_flow_x / flow_mag
                    flow_alignment = norm_dist_y * norm_flow_y + norm_dist_x * norm_flow_x
                    new_mask = np.zeros_like(mask, dtype=bool)
                    new_mask[ys, xs] = (flow_alignment > 0.5)
                    instance_labels[new_mask] = region_id
                    instance_labels[~new_mask & (instance_labels == region_id)] = 0

    # Relabel and filter small regions
    final_labels = np.zeros_like(instance_labels)
    regions = regionprops(instance_labels)
    new_id = 1
    filtered_spots = []
    for region in regions:
        if region.area < min_size:
            continue
        mask = (instance_labels == region.label)
        final_labels[mask] = new_id
        cy, cx = region.centroid
        refined_cy, refined_cx = cy, cx
        if flow is not None and flow.shape[0] == 2:
            ys, xs = np.where(mask)
            if len(ys) > 0:
                avg_flow_y = flow[0, ys, xs].mean()
                avg_flow_x = flow[1, ys, xs].mean()
                refined_cy += flow_refine_factor * avg_flow_y
                refined_cx += flow_refine_factor * avg_flow_x
        sdt_score = np.mean(sdt_scalar[mask])
        centroid_score = np.mean(centroid_map[mask])
        semantic_score = np.mean(semantic[mask])
        combined_score = (sdt_score + centroid_score + semantic_score) / 3.0
        filtered_spots.append({
            'y': float(refined_cy),
            'x': float(refined_cx),
            'original_y': float(cy),
            'original_x': float(cx),
            'score': float(combined_score),
            'size': int(region.area),
            'sdt_score': float(sdt_score),
            'centroid_score': float(centroid_score),
            'semantic_score': float(semantic_score)
        })
        new_id += 1

    print(f"Instance segmentation completed: Found {len(filtered_spots)} spots.")
    return final_labels, filtered_spots

def tile_inference(
    model,
    image_path,
    device='cuda',
    patch_size=256,
    overlap=0.5,
    batch_size=4,
    return_all_outputs=True
):
    image = tifffile.imread(image_path).astype(np.float32)
    H, W = image.shape
    vmin, vmax = np.percentile(image, (0.5, 99.5))
    image_norm = np.clip((image - vmin) / (vmax - vmin + 1e-8), 0, 1).astype(np.float32)

    model.eval()
    model.to(device)
    stride = int(patch_size * (1 - overlap))
    stride = max(stride, 1)

    def init_accumulator():
        return np.zeros((H, W), dtype=np.float32)

    semantic_out = init_accumulator()
    skeleton_out = init_accumulator()
    centroid_out = init_accumulator()
    sdt_probs_out = np.zeros((11, H, W), dtype=np.float32)
    flow_out = np.zeros((2, H, W), dtype=np.float32)
    count_map = init_accumulator()

    y_coords = list(range(0, H, stride))
    x_coords = list(range(0, W, stride))
    total_patches = len(y_coords) * len(x_coords)

    with torch.no_grad():
        patch_batch = []
        coords_batch = []
        pbar = tqdm(total=total_patches, desc="Tiled Inference")
        for y_start in y_coords:
            for x_start in x_coords:
                y_end = min(y_start + patch_size, H)
                x_end = min(x_start + patch_size, W)
                pad_h = patch_size - (y_end - y_start)
                pad_w = patch_size - (x_end - x_start)
                patch = image_norm[y_start:y_end, x_start:x_end]
                patch = np.pad(patch, ((0, pad_h), (0, pad_w)), mode='reflect')
                patch_batch.append(patch)
                coords_batch.append((y_start, y_end, x_start, x_end))
                if len(patch_batch) >= batch_size or (y_start == y_coords[-1] and x_start == x_coords[-1]):
                    patch_tensor = torch.from_numpy(np.stack(patch_batch)[..., None]).permute(0, 3, 1, 2).float().to(device)
                    with autocast('cuda'):
                        outputs = model(patch_tensor)
                    for i, (y_start, y_end, x_start, x_end) in enumerate(coords_batch):
                        h, w = y_end - y_start, x_end - x_start
                        if return_all_outputs:
                            semantic_out[y_start:y_end, x_start:x_end] += torch.sigmoid(outputs['sem_out'][i, 0]).cpu().numpy()[:h, :w]
                            skeleton_out[y_start:y_end, x_start:x_end] += torch.sigmoid(outputs['skeleton_out'][i, 0]).cpu().numpy()[:h, :w]
                            centroid_out[y_start:y_end, x_start:x_end] += torch.sigmoid(outputs['hm_out'][i, 0]).cpu().numpy()[:h, :w]
                            sdt_probs_out[:, y_start:y_end, x_start:x_end] += F.softmax(outputs['sdt_out'][i], dim=0).cpu().numpy()[:, :h, :w]
                            flow_out[:, y_start:y_end, x_start:x_end] += outputs['flow_out'][i].cpu().numpy()[:, :h, :w]
                        count_map[y_start:y_end, x_start:x_end] += 1
                    patch_batch = []
                    coords_batch = []
                    pbar.update(batch_size)
        pbar.close()

    eps = 1e-8
    semantic_out /= (count_map + eps)
    skeleton_out /= (count_map + eps)
    centroid_out /= (count_map + eps)
    sdt_probs_out /= (count_map[None, ...] + eps)
    flow_out /= (count_map[None, ...] + eps)

    bin_centers = (np.arange(11) + 0.5) / 11
    sdt_scalar_out = np.sum(sdt_probs_out * bin_centers[:, None, None], axis=0)

    print("Performing instance segmentation...")
    instance_labels, filtered_spots = skeleton_aware_instance_segmentation(
        semantic=semantic_out,
        sdt_scalar=sdt_scalar_out,
        skeleton=skeleton_out,
        centroid_map=centroid_out,
        flow=flow_out,
        min_size=2,
        nms_threshold=0.3,
        flow_refine_factor=0.3
    )

    results = {
        'instance_labels': instance_labels,
        'spots': filtered_spots,
        'semantic': semantic_out,
        'sdt_scalar': sdt_scalar_out,
        'skeleton': skeleton_out,
        'centroid': centroid_out,
        'flow': flow_out,
        'sdt_probs': sdt_probs_out if return_all_outputs else None
    }

    print(f"✅ Inference completed for {os.path.basename(image_path)}. Found {len(filtered_spots)} spots.")
    return results

def inspect_ground_truth_data(data_loader, device, num_batches=3, save_dir="./gt_inspection_results"):
    """
    Comprehensive inspection of the dataset's ground truth annotations.
    Validates and visualizes all target components, ensuring they are generated
    consistently and can work together for instance segmentation.
    Mirrors the component generation logic of `visualize_full_image_analysis` for validation.
    """
    import os
    import matplotlib.pyplot as plt
    import numpy as np
    from scipy.ndimage import maximum_filter
    from skimage.segmentation import watershed, find_boundaries
    from skimage.measure import regionprops
    import torch

    os.makedirs(save_dir, exist_ok=True)
    inspection_results = {
        'value_ranges': {},
        'sdt_properties': [],
        'flow_validity': [],
        'centroid_accuracy': [],
        'instance_segmentation': [], # For the simplified check within the batch
        'passed_checks': 0,
        'total_checks': 0
    }

    def check(condition, description):
        """Helper to track inspection results with clear pass/fail status"""
        inspection_results['total_checks'] += 1
        if condition:
            inspection_results['passed_checks'] += 1
            status = "✅ PASS"
        else:
            status = "❌ FAIL"
        print(f" {status}: {description}")
        return condition

    with torch.no_grad():
        for batch_idx, (images, targets) in enumerate(data_loader):
            if batch_idx >= num_batches:
                break
            images = images.float().to(device)
            targets = targets.float().to(device)
            batch_size = images.shape[0]
            print(f"\n--- Inspecting Batch {batch_idx} ---")

            # 1. Check target value ranges (with proper flow handling)
            print("1. Checking target value ranges...")
            for i in range(targets.shape[1]):
                channel_data = targets[:, i]
                min_val = channel_data.min().item()
                max_val = channel_data.max().item()

                # Special handling for flow channels (4 and 5)
                if i in [4, 5]:  # Flow Y and X
                    # Flow vectors should be in [-1, 1] (unit vectors)
                    is_valid = -1.0 <= min_val <= max_val <= 1.0
                    check(is_valid,
                          f"Flow channel {i} in [-1,1] (min: {min_val:.3f}, max: {max_val:.3f})")
                elif i == 2:  # Skeleton
                    # Skeleton can be gradient-based (0-1)
                    is_valid = 0.0 <= min_val <= max_val <= 1.0
                    check(is_valid,
                          f"Skeleton channel {i} in [0,1] (min: {min_val:.3f}, max: {max_val:.3f})")
                else:  # All other channels should be in [0, 1]
                    is_valid = 0 <= min_val <= max_val <= 1
                    check(is_valid,
                          f"Target channel {i} in [0,1] (min: {min_val:.3f}, max: {max_val:.3f})")

                inspection_results['value_ranges'].setdefault(i, []).extend([min_val, max_val])

            # 2. Check SDT properties
            print("2. Checking SDT properties...")
            for i in range(min(2, batch_size)):
                gt_sdt = targets[i, 1].cpu().numpy()  # SDT is at index 1
                gt_boundary = targets[i, 6].cpu().numpy() > 0.5  # Boundary at index 6

                # Boundary vs non-boundary comparison
                try:
                    bound_sdt_vals = gt_sdt[gt_boundary]
                    non_bound_sdt_vals = gt_sdt[~gt_boundary]

                    if len(bound_sdt_vals) > 0 and len(non_bound_sdt_vals) > 0:
                        bound_sdt_mean = bound_sdt_vals.mean()
                        non_bound_sdt_mean = non_bound_sdt_vals.mean()
                        # SDT should be higher near boundaries (value increases from center outwards)
                        is_valid = bound_sdt_mean > non_bound_sdt_mean
                        check(is_valid,
                              f"Sample {i}: SDT higher at boundary (bound: {bound_sdt_mean:.3f}, non-bound: {non_bound_sdt_mean:.3f})")
                        inspection_results['sdt_properties'].append({
                            'sample': batch_idx * batch_size + i,
                            'bound_sdt': bound_sdt_mean,
                            'non_bound_sdt': non_bound_sdt_mean
                        })
                    else:
                        print(f"  ⚠️  Insufficient pixels for boundary comparison in sample {i}")
                        inspection_results['sdt_properties'].append({
                            'sample': batch_idx * batch_size + i,
                            'bound_sdt': 0,
                            'non_bound_sdt': 0
                        })
                except Exception as e:
                    print(f"  ⚠️  Error in SDT property check for sample {i}: {e}")
                    inspection_results['sdt_properties'].append({
                        'sample': batch_idx * batch_size + i,
                        'bound_sdt': 0,
                        'non_bound_sdt': 0
                    })

            # 3. Check flow validity
            print("3. Checking flow validity...")
            # Calculate magnitude for the entire batch first, then take max
            flow_mag = torch.sqrt(targets[:, 4]**2 + targets[:, 5]**2 + 1e-8)  # Flow Y/X at indices 4/5
            max_mag = flow_mag.max().item()
            # Flow magnitude should be <= 1.0 for unit vectors
            is_valid = max_mag <= 1.0
            check(is_valid, f"Flow magnitude <= 1.0 (max: {max_mag:.3f})")
            inspection_results['flow_validity'].append(max_mag)

            # 4. Check centroid localization
            print("4. Checking centroid localization...")
            for i in range(min(2, batch_size)):
                gt_centroid = targets[i, 3].cpu().numpy()  # Centroid map at index 3
                gt_semantic = targets[i, 0].cpu().numpy() > 0.5  # Semantic at index 0

                # Centroid peaks within semantic regions
                peaks = (gt_centroid > 0.3) & (maximum_filter(gt_centroid, size=3) == gt_centroid)
                if peaks.sum() > 0:
                    centroid_in_semantic = np.logical_and(peaks, gt_semantic).sum()
                    total_centroids = peaks.sum()
                    if total_centroids > 0:
                        accuracy = centroid_in_semantic / total_centroids
                        is_valid = accuracy > 0.8 # Allow for a small percentage outside
                        check(is_valid, f"Sample {i}: Centroids within semantic ({accuracy*100:.1f}%)")
                        inspection_results['centroid_accuracy'].append(accuracy)
                    else:
                        inspection_results['centroid_accuracy'].append(0.0)
                else:
                    # No peaks found, might be an issue, but not necessarily a FAIL
                    print(f"  ⚠️  No significant centroid peaks found in sample {i}")
                    inspection_results['centroid_accuracy'].append(0.0)


            # 5. Instance Segmentation Check using GT data (Simplified per-patch check)
            # This mimics the logic of visualize_full_image_analysis but on a per-patch basis
            # for validation purposes within the batch.
            print("5. Performing simplified instance segmentation check...")
            for i in range(min(2, batch_size)):
                # Get patch targets
                gt_sem = targets[i, 0].cpu().numpy() > 0.2  # More inclusive semantic mask
                gt_sdt_scalar = targets[i, 1].cpu().numpy() # This is the scalar SDT
                gt_skel = targets[i, 2].cpu().numpy()
                gt_centroid_map = targets[i, 3].cpu().numpy()
                # gt_flow is targets[i, 4:6] - Not used in this specific check

                try:
                    # --- Replicate key logic from visualize_full_image_analysis ---

                    # 1. Create a combined binary mask (similar to full image analysis logic)
                    
                    #Expand binary mask to include skeleton regions
                    binary_mask = (gt_sem > 0.2) | (gt_skel > 0.1) | (gt_centroid > 0.2)
                    
                    # Extract centroids with NMS (seed points)
                    peaks = (gt_centroid > 0.3) & (maximum_filter(gt_centroid, size=3) == gt_centroid)
                    centroid_coords = np.column_stack(np.where(peaks))
                    
                    # Create seeds for watershed - Use centroid heatmap directly
                    seeds = np.zeros_like(gt_sem, dtype=np.int32)
                    for idx, (y, x) in enumerate(centroid_coords, 1):
                        # Use centroid heatmap directly as seeds
                        seeds[y, x] = idx
                    
                    # Alternative: Use GT SDT to refine seeds
                    # Uncomment below if needed
                    # sdt_peaks = (gt_sdt > 0.5) & (maximum_filter(gt_sdt, size=3) == gt_sdt)
                    # sdt_coords = np.column_stack(np.where(sdt_peaks))
                    # for idx, (y, x) in enumerate(sdt_coords, 1):
                    #     seeds[y, x] = idx
                    
                    # 4. Create a SKEL-aware watershed map - like full analysis
                    # Use the same combination formula and thresholds
                    skel_mask = (gt_skel > 0.5).astype(float)
                    skel_ridges = 1.0 - skel_mask
                    skel_influence = 0.7
                    combined_map = (1.0 - skel_influence) * gt_sdt + skel_influence * skel_ridges

                    # 5. Perform watershed - CRITICAL: Use NEGATIVE combined map for proper direction
                    # Watershed flows downhill, but our SDT increases from boundary to center
                    instance_labels = watershed(-combined_map, seeds, mask=binary_mask)

                    # Relabel: one ID per connected component
                    final_labels = np.zeros_like(instance_labels)
                    regions = regionprops(instance_labels)
                    num_instances = len(regions)
                    
                    check(num_instances > 0, f"Sample {i}: Instance segmentation successful ({num_instances} instances)")
                    inspection_results['instance_segmentation'].append(num_instances)
                except Exception as e:
                    print(f"  ⚠️  Instance segmentation failed for sample {i}: {e}")
                    inspection_results['instance_segmentation'].append(0)


            # 6. Visualization
            print("6. Generating visualization...")
            try:
                # Visualize the first sample of the batch
                sample_idx = 0
                fig, axes = plt.subplots(3, 5, figsize=(30, 18)) # 3 rows, 5 columns
                fig.suptitle(f'Ground Truth Inspection - Batch {batch_idx} - Sample {sample_idx}', fontsize=18)

                # --- Row 1: Core GT Components ---
                # Input Image
                axes[0, 0].imshow(images[sample_idx, 0].cpu(), cmap='gray')
                axes[0, 0].set_title('Input Image')
                axes[0, 0].axis('off')

                # Semantic Mask
                im_sem = axes[0, 1].imshow(targets[sample_idx, 0].cpu(), cmap='viridis')
                axes[0, 1].set_title('GT Semantic Mask')
                plt.colorbar(im_sem, ax=axes[0, 1])
                axes[0, 1].axis('off')

                # SDT Scalar
                im_sdt = axes[0, 2].imshow(targets[sample_idx, 1].cpu(), cmap='magma')
                axes[0, 2].set_title('GT SDT (Scalar)')
                plt.colorbar(im_sdt, ax=axes[0, 2])
                axes[0, 2].axis('off')

                # Skeleton
                im_skel = axes[0, 3].imshow(targets[sample_idx, 2].cpu(), cmap='bone')
                axes[0, 3].set_title('GT Skeleton')
                plt.colorbar(im_skel, ax=axes[0, 3])
                axes[0, 3].axis('off')

                # Centroid Heatmap
                im_cent = axes[0, 4].imshow(targets[sample_idx, 3].cpu(), cmap='hot')
                axes[0, 4].set_title('GT Centroid Heatmap')
                plt.colorbar(im_cent, ax=axes[0, 4])
                axes[0, 4].axis('off')

                # --- Row 2: Combined Views & Flow ---
                # Semantic + SDT Overlay
                axes[1, 0].imshow(targets[sample_idx, 0].cpu(), cmap='gray')
                im_sdt_ov = axes[1, 0].imshow(targets[sample_idx, 1].cpu(), cmap='magma', alpha=0.6)
                axes[1, 0].set_title('GT Sem + SDT Overlay')
                plt.colorbar(im_sdt_ov, ax=axes[1, 0])
                axes[1, 0].axis('off')

                # Semantic + Centroid Overlay
                axes[1, 1].imshow(targets[sample_idx, 0].cpu(), cmap='gray')
                im_cent_ov = axes[1, 1].imshow(targets[sample_idx, 3].cpu(), cmap='hot', alpha=0.6)
                axes[1, 1].set_title('GT Sem + Centroid Overlay')
                plt.colorbar(im_cent_ov, ax=axes[1, 1])
                axes[1, 1].axis('off')

                # Instance Segmentation Result (from the check above)
                # Re-calculate for visualization to be sure it matches
                gt_sem_viz = targets[sample_idx, 0].cpu().numpy()
                gt_sdt_scalar_viz = targets[sample_idx, 1].cpu().numpy()
                gt_skel_viz = targets[sample_idx, 2].cpu().numpy()
                gt_centroid_map_viz = targets[sample_idx, 3].cpu().numpy()

                # Replicate the check logic for visualization
                binary_mask_viz = (gt_sem_viz > 0.2) | (gt_skel_viz > 0.1)
                peaks_viz = (gt_centroid_map_viz > 0.3) & (maximum_filter(gt_centroid_map_viz, size=3) == gt_centroid_map_viz)
                centroid_coords_viz = np.column_stack(np.where(peaks_viz))
                seeds_viz = np.zeros_like(gt_sem_viz, dtype=np.int32)
                for idx, (y, x) in enumerate(centroid_coords_viz, 1):
                    if gt_skel_viz[y, x] > 0.1:
                        seeds_viz[y, x] = idx
                skel_mask_viz = (gt_skel_viz > 0.5).astype(np.float32)
                skel_ridges_viz = 1.0 - skel_mask_viz
                combined_map_viz = (1.0 - 0.7) * gt_sdt_scalar_viz + 0.7 * skel_ridges_viz
                instance_labels_viz = watershed(-combined_map_viz, seeds_viz, mask=binary_mask_viz)

                im_inst = axes[1, 2].imshow(instance_labels_viz, cmap='nipy_spectral')
                axes[1, 2].set_title('GT-Based Instance Seg (Check)')
                plt.colorbar(im_inst, ax=axes[1, 2])
                axes[1, 2].axis('off')

                # Flow Y
                im_flow_y = axes[1, 3].imshow(targets[sample_idx, 4].cpu(), cmap='RdBu_r')
                axes[1, 3].set_title('GT Flow Y')
                plt.colorbar(im_flow_y, ax=axes[1, 3])
                axes[1, 3].axis('off')

                # Flow X
                im_flow_x = axes[1, 4].imshow(targets[sample_idx, 5].cpu(), cmap='RdBu_r')
                axes[1, 4].set_title('GT Flow X')
                plt.colorbar(im_flow_x, ax=axes[1, 4])
                axes[1, 4].axis('off')

                # --- Row 3: Diagnostic Views ---
                # Flow Magnitude
                flow_mag_viz = np.hypot(targets[sample_idx, 4].cpu().numpy(), targets[sample_idx, 5].cpu().numpy())
                im_flow_mag = axes[2, 0].imshow(flow_mag_viz, cmap='plasma', vmin=0, vmax=1.0)
                axes[2, 0].set_title('GT Flow Magnitude')
                plt.colorbar(im_flow_mag, ax=axes[2, 0])
                axes[2, 0].axis('off')

                # Centroid Gradient Magnitude (Diagnostic)
                gt_centroid_np = targets[sample_idx, 3].cpu().numpy()
                gy, gx = np.gradient(gt_centroid_np)
                grad_mag_cent = np.hypot(gy, gx)
                im_cent_grad = axes[2, 1].imshow(grad_mag_cent, cmap='viridis', vmin=0, vmax=0.5)
                axes[2, 1].set_title('GT Centroid Grad Mag')
                plt.colorbar(im_cent_grad, ax=axes[2, 1])
                axes[2, 1].axis('off')

                # SDT + Skeleton Combined Map (Diagnostic)
                skel_influence_diag = 0.7
                combined_map_diag = (1.0 - skel_influence_diag) * targets[sample_idx, 1].cpu().numpy() + skel_influence_diag * (1.0 - targets[sample_idx, 2].cpu().numpy())
                im_comb_diag = axes[2, 2].imshow(combined_map_diag, cmap='viridis')
                axes[2, 2].set_title('GT SDT + Skel Comb Map')
                plt.colorbar(im_comb_diag, ax=axes[2, 2])
                axes[2, 2].axis('off')

                # Valid Seeds on Skeleton (Diagnostic)
                seeds_diag_viz = np.zeros_like(gt_sem_viz, dtype=np.float32) # Use float for imshow
                for idx, (y, x) in enumerate(centroid_coords_viz, 1):
                    if targets[sample_idx, 2].cpu().numpy()[y, x] > 0.1:
                        seeds_diag_viz[y, x] = 1.0 # Mark presence
                im_seeds_diag = axes[2, 3].imshow(seeds_diag_viz, cmap='hot')
                axes[2, 3].set_title('GT Valid Seeds (Sk>0.1)')
                plt.colorbar(im_seeds_diag, ax=axes[2, 3])
                axes[2, 3].axis('off')

                # Instance Boundaries (Diagnostic)
                try:
                    boundaries_diag = find_boundaries(instance_labels_viz, connectivity=1)
                    im_bound_diag = axes[2, 4].imshow(boundaries_diag, cmap='gray')
                    axes[2, 4].set_title('GT Instance Boundaries')
                    plt.colorbar(im_bound_diag, ax=axes[2, 4])
                except Exception as e:
                    axes[2, 4].text(0.5, 0.5, f'Boundary Viz Error:\n{str(e)[:20]}', ha='center', va='center', transform=axes[2, 4].transAxes, fontsize=8)
                    axes[2, 4].set_title('GT Instance Boundaries')
                axes[2, 4].axis('off')


                plt.tight_layout(rect=[0, 0, 1, 0.96]) # Make room for suptitle
                plot_filename = os.path.join(save_dir, f"gt_inspection_batch_{batch_idx}_sample_{sample_idx}.png")
                plt.savefig(plot_filename, dpi=150, bbox_inches='tight')
                plt.close(fig) # Explicitly close the figure
                print(f"  📊 GT inspection plot saved to {plot_filename}")

            except Exception as e:
                print(f"  ⚠️  Visualization error for batch {batch_idx}: {e}")
                import traceback
                traceback.print_exc() # Print full traceback for debugging

    # --- Summary ---
    print("\n" + "="*60)
    print("GROUND TRUTH INSPECTION SUMMARY")
    print("="*60)
    print(f"Passed checks: {inspection_results['passed_checks']}/{inspection_results['total_checks']}")
    if inspection_results['flow_validity']:
        print(f"Max flow magnitude: {max(inspection_results['flow_validity']):.3f}")
    if inspection_results['centroid_accuracy']:
        avg_acc = np.mean(inspection_results['centroid_accuracy'])
        print(f"Average centroid accuracy: {avg_acc*100:.1f}%")
    if inspection_results['instance_segmentation']:
        avg_inst = np.mean(inspection_results['instance_segmentation'])
        print(f"Avg instances per sample (simplified check): {avg_inst:.1f}")

    success_rate = inspection_results['passed_checks'] / inspection_results['total_checks'] if inspection_results['total_checks'] > 0 else 0
    if success_rate >= 0.85: # Slightly lower threshold for a "pass"
        print("🎉 INSPECTION INDICATES: Ground truth data is likely consistent and correct.")
        print("   The components generated by the dataset loader align with the expected structure.")
        print("   Instance segmentation logic (simplified check) is functional.")
    else:
        print("⚠️ INSPECTION INDICATES: Potential issues in ground truth data.")
        print("   Please review the dataset loader implementation and the generated targets.")

    # Save detailed results (excluding large arrays like 'value_ranges')
    import json
    # Create a copy of results and remove large items
    results_summary = {}
    for key, value in inspection_results.items():
        if key != 'value_ranges': # Exclude raw value ranges
            results_summary[key] = value
        else:
            # Optionally, summarize value_ranges
            summarized_ranges = {}
            for ch, vals in value.items():
                if vals:
                    summarized_ranges[ch] = {'min': min(vals), 'max': max(vals), 'count': len(vals)}
            results_summary['value_ranges_summary'] = summarized_ranges

    try:
        summary_filename = os.path.join(save_dir, "gt_inspection_summary.json")
        with open(summary_filename, 'w') as f:
            json.dump(results_summary, f, indent=2)
        print(f"📋 Summary of inspection results saved to {summary_filename}")
    except Exception as e:
        print(f"⚠️ Error saving JSON summary: {e}")

    return inspection_results

print("✅ inspect_ground_truth_data function is ready for use.")
print("   It now incorporates logic consistent with visualize_full_image_analysis for validation.")

# Example usage (uncomment to run):
# Assuming you have data_loader, device defined
# results = inspect_ground_truth_data(data_loader, device, num_batches=2, save_dir="./gt_inspection_output_final")




from skimage.segmentation import watershed
# Cell 8: Updated Inspection Function (Corrected)
def inspect_skeleton_aware_model(model, data_loader, device, num_batches=3, save_dir="./model_inspection_results"):
    """
    Comprehensive inspection of the SkeletonAwareSpotDetector model outputs.
    Now includes REAL instance segmentation validation using all components.
    """
    import os
    import matplotlib.pyplot as plt
    import numpy as np
    from scipy.ndimage import maximum_filter
    from skimage.segmentation import watershed
    from skimage.measure import label
    import torch
    import torch.nn.functional as F
    
    try:
        from torch.amp import autocast  # PyTorch 2.0+
    except ImportError:
        from torch.cuda.amp import autocast  # PyTorch < 2.0

    model.eval()
    os.makedirs(save_dir, exist_ok=True)
    inspection_results = {
        'output_shapes': {},
        'value_ranges': {},
        'sdt_properties': [],
        'flow_validity': [],
        'centroid_accuracy': [],
        'instance_segmentation': [],
        'passed_checks': 0,
        'total_checks': 0
    }

    def check(condition, description):
        """Helper to track inspection results"""
        inspection_results['total_checks'] += 1
        if condition:
            inspection_results['passed_checks'] += 1
            status = "✅ PASS"
        else:
            status = "❌ FAIL"
        print(f" {status}: {description}")
        return condition

    with torch.no_grad():
        for batch_idx, (images, targets) in enumerate(data_loader):
            if batch_idx >= num_batches:
                break
            images = images.float().to(device)
            targets = targets.float().to(device)
            
            with autocast('cuda'):
                outputs = model(images)
            batch_size = images.shape[0]

            # 1. Check output shapes
            if batch_idx == 0:
                print("1. Checking output shapes...")
                for name, tensor in outputs.items():
                    inspection_results['output_shapes'][name] = list(tensor.shape)
                    check(len(tensor.shape) == 4, f"{name} has 4 dimensions (B,C,H,W)")
                
                # Channel checks
                check(outputs['sem_out'].shape[1] == 1, "Semantic output has 1 channel")
                check(outputs['sdt_out'].shape[1] == 11, "SDT output has 11 bins")
                check(outputs['skeleton_out'].shape[1] == 1, "Skeleton output has 1 channel")
                check(outputs['hm_out'].shape[1] == 1, "Centroid map has 1 channel")
                check(outputs['flow_out'].shape[1] == 2, "Flow output has 2 channels (y,x)")

            # 2. Check value ranges
            print("2. Checking value ranges...")
            for name, tensor in outputs.items():
                if name == 'sdt_out':
                    # SDT logits can be any value
                    continue
                elif name == 'flow_out':
                    # Check flow magnitude constraint
                    flow_mag = torch.sqrt(torch.sum(tensor**2, dim=1, keepdim=True) + 1e-8)
                    max_mag = flow_mag.max().item()
                    check(max_mag <= 1, f"Flow magnitude <= 1.5 (max: {max_mag:.3f})")
                    inspection_results['flow_validity'].append(max_mag)
                else:
                    min_val = tensor.min().item()
                    max_val = tensor.max().item()
                    check(0 <= min_val <= max_val <= 1, f"{name} in [0,1] (min: {min_val:.3f}, max: {max_val:.3f})")
                    inspection_results['value_ranges'].setdefault(name, []).extend([min_val, max_val])

            # 3. Check SDT properties
            print("3. Checking SDT properties...")
            sdt_probs = F.softmax(outputs['sdt_out'], dim=1)
            bin_centers = (torch.arange(0, sdt_probs.shape[1], dtype=torch.float32, device=sdt_probs.device) + 0.5) / sdt_probs.shape[1]
            bin_centers = bin_centers.view(1, -1, 1, 1)
            sdt_recon = torch.sum(sdt_probs * bin_centers, dim=1)

            for i in range(min(2, batch_size)):
                pred_sdt = sdt_recon[i].cpu().numpy().astype(np.float32)
                gt_sdt = targets[i, 1].cpu().numpy()  # Assuming SDT target is at index 1
                
                # Boundary vs non-boundary comparison
                try:
                    boundary_mask = (targets[i, 6].cpu().numpy() > 0.5).astype(bool)  # Assuming boundary is at index 6
                    bound_sdt_vals = pred_sdt[boundary_mask]
                    non_bound_sdt_vals = pred_sdt[~boundary_mask]
                    
                    bound_sdt_mean = bound_sdt_vals.mean().item()
                    non_bound_sdt_mean = non_bound_sdt_vals.mean().item()
                    check(bound_sdt_mean > non_bound_sdt_mean, 
                        f"Sample {i}: SDT higher at boundary (bound: {bound_sdt_mean:.3f}, non-bound: {non_bound_sdt_mean:.3f})")
                    inspection_results['sdt_properties'].append({
                        'sample': batch_idx*batch_size + i,
                        'bound_sdt': bound_sdt_mean,
                        'non_bound_sdt': non_bound_sdt_mean
                    })
                except Exception as e:
                    print(f" Warning: Insufficient pixels for boundary/non-boundary comparison in sample {i}.")
                    inspection_results['sdt_properties'].append({
                        'sample': batch_idx*batch_size + i,
                        'bound_sdt': 0,
                        'non_bound_sdt': 0
                    })

            # 4. Check centroid localization and smoothness
            print("4. Checking centroid localization and smoothness...")
            for i in range(min(2, batch_size)):
                pred_hm = torch.sigmoid(outputs['hm_out'][i, 0]).cpu().numpy().astype(np.float32)
                pred_semantic = torch.sigmoid(outputs['sem_out'][i, 0]).cpu().numpy() > 0.5
                
                # Centroid within semantic
                peaks = (pred_hm > 0.3) & (maximum_filter(pred_hm, size=3) == pred_hm)
                if peaks.sum() > 0:
                    centroid_in_semantic = np.logical_and(peaks, pred_semantic).sum()
                    total_centroids = peaks.sum()
                    accuracy = centroid_in_semantic / total_centroids
                    check(accuracy > 0.8, f"Sample {i}: Centroids within semantic ({accuracy*100:.1f}%)")
                    inspection_results['centroid_accuracy'].append(accuracy)
                else:
                    inspection_results['centroid_accuracy'].append(0.0)
                
                # Smoothness check
                grad_y, grad_x = np.gradient(pred_hm)
                grad_mag = np.hypot(grad_y, grad_x)
                max_grad = grad_mag.max()
                check(max_grad < 0.3, f"Sample {i}: Centroid heatmap smooth (max grad: {max_grad:.3f})")

            # 5. REAL INSTANCE SEGMENTATION TEST
            print("5. Performing REAL instance segmentation test...")
            for i in range(min(2, batch_size)):
                # Extract model outputs
                pred_sem = torch.sigmoid(outputs['sem_out'][i, 0]).cpu().numpy().astype(np.float32)
                pred_sdt = sdt_recon[i].cpu().numpy().astype(np.float32)
                pred_skel = torch.sigmoid(outputs['skeleton_out'][i, 0]).cpu().numpy().astype(np.float32)
                pred_cent = torch.sigmoid(outputs['hm_out'][i, 0]).cpu().numpy().astype(np.float32)
                pred_flow = outputs['flow_out'][i].cpu().numpy().astype(np.float32)
                
                try:
                    # Perform real instance segmentation
                    final_labels, filtered_spots = skeleton_aware_instance_segmentation(
                        semantic=pred_sem,
                        sdt_scalar=pred_sdt,
                        skeleton=pred_skel,
                        centroid_map=pred_cent,
                        flow=pred_flow,
                        min_size=5,
                        nms_threshold=0.4,
                        flow_refine_factor=0.3
                    )
                    
                    num_instances = len(filtered_spots)
                    check(num_instances > 0, f"Sample {i}: Real instance segmentation successful ({num_instances} instances)")
                    inspection_results['instance_segmentation'].append(num_instances)
                except Exception as e:
                    print(f" Instance segmentation failed for sample {i}: {e}")
                    inspection_results['instance_segmentation'].append(0)

            # 6. Visualization
            print("6. Generating visualization...")
            try:
                fig, axes = plt.subplots(3, 5, figsize=(25, 15))
                sample_idx = 0  # Visualize first sample of the batch
                
                # Input and Main Outputs
                axes[0,0].imshow(images[sample_idx, 0].cpu(), cmap='gray')
                axes[0,0].set_title('Input Image')
                axes[0,0].axis('off')
                
                axes[0,1].imshow(torch.sigmoid(outputs['sem_out'][sample_idx, 0]).cpu(), cmap='viridis')
                axes[0,1].set_title('Semantic Mask')
                axes[0,1].axis('off')
                
                im2 = axes[0,2].imshow(sdt_recon[sample_idx].cpu(), cmap='magma')
                axes[0,2].set_title('SDT (Reconstructed)')
                plt.colorbar(im2, ax=axes[0,2])
                
                axes[0,3].imshow(torch.sigmoid(outputs['skeleton_out'][sample_idx, 0]).cpu(), cmap='bone')
                axes[0,3].set_title('Skeleton')
                axes[0,3].axis('off')
                
                axes[0,4].imshow(torch.sigmoid(outputs['hm_out'][sample_idx, 0]).cpu(), cmap='hot')
                axes[0,4].set_title('Centroid Heatmap')
                axes[0,4].axis('off')
                
                # Combined Visualizations
                axes[1,0].imshow(torch.sigmoid(outputs['sem_out'][sample_idx, 0]).cpu(), cmap='gray')
                axes[1,0].imshow(sdt_recon[sample_idx].cpu(), cmap='magma', alpha=0.6)
                axes[1,0].set_title('Sem + SDT')
                axes[1,0].axis('off')
                
                axes[1,1].imshow(torch.sigmoid(outputs['sem_out'][sample_idx, 0]).cpu(), cmap='gray')
                axes[1,1].imshow(torch.sigmoid(outputs['hm_out'][sample_idx, 0]).cpu(), cmap='hot', alpha=0.6)
                axes[1,1].set_title('Sem + Centroid')
                axes[1,1].axis('off')
                
                # Show REAL instance segmentation result
                axes[1,2].imshow(final_labels, cmap='nipy_spectral')
                axes[1,2].set_title('Real Instance Segmentation')
                axes[1,2].axis('off')
                
                axes[1,3].imshow(pred_flow[sample_idx, 0], cmap='RdBu_r')
                axes[1,3].set_title('Flow Y')
                axes[1,3].axis('off')
                
                axes[1,4].imshow(pred_flow[sample_idx, 1], cmap='RdBu_r')
                axes[1,4].set_title('Flow X')
                axes[1,4].axis('off')
                
                # Diagnostic Visualizations
                grad_mag_vis = np.hypot(
                    pred_flow[sample_idx, 0],
                    pred_flow[sample_idx, 1]
                )
                im = axes[2,0].imshow(grad_mag_vis, cmap='viridis', vmin=0, vmax=0.3)
                plt.colorbar(im, ax=axes[2,0])
                axes[2,0].set_title('Flow Magnitude')
                axes[2,0].axis('off')
                
                axes[2,1].axis('off')  # Empty space
                
                plt.tight_layout()
                plt.suptitle(f'Model Inspection - Batch {batch_idx}', fontsize=16)
                plt.subplots_adjust(top=0.93)
                plt.savefig(os.path.join(save_dir, f"model_inspection_batch_{batch_idx}.png"), dpi=150, bbox_inches='tight')
                plt.close()
            except Exception as e:
                print(f" Visualization error: {e}")

    # Summary
    print("\n" + "="*60)
    print("MODEL INSPECTION SUMMARY")
    print("="*60)
    print(f"Passed checks: {inspection_results['passed_checks']}/{inspection_results['total_checks']}")
    if inspection_results['flow_validity']:
        print(f"Max flow magnitude: {max(inspection_results['flow_validity']):.3f}")
    if inspection_results['centroid_accuracy']:
        avg_acc = np.mean(inspection_results['centroid_accuracy'])
        print(f"Average centroid accuracy: {avg_acc*100:.1f}%")
    if inspection_results['instance_segmentation']:
        avg_inst = np.mean(inspection_results['instance_segmentation'])
        print(f"Avg instances per sample: {avg_inst:.1f}")
    
    success_rate = inspection_results['passed_checks'] / inspection_results['total_checks'] if inspection_results['total_checks'] > 0 else 0
    if success_rate >= 0.9:
        print("🎉 INSPECTION PASSED: Model outputs are consistent and correct.")
    else:
        print("⚠️ INSPECTION FAILED: Some checks did not pass. Please review.")
    
    return inspection_results

# Example usage (uncomment to run):
# device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
# model = SkeletonAwareSpotDetector(in_ch=1, base_ch=48).to(device)
# # Load a trained model if available
# # checkpoint = torch.load('path/to/best_model.pth')
# # model.load_state_dict(checkpoint['model_state_dict'])
# 
# # Create data loaders (replace with your actual paths)
# # train_loader, val_loader = create_skeleton_aware_data_loaders(image_paths, mask_paths)
# 
# # Run inspection
# # results = inspect_skeleton_aware_model(model, val_loader, device, num_batches=2)

print("inspect_skeleton_aware_model function updated with fixes.")

## UPDATED - FIXED AUTOCAST ISSUE:

import os
import numpy as np
import torch
import torch.nn as nn
from torch.cuda.amp import GradScaler, autocast
import matplotlib.pyplot as plt
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore', category=UserWarning)

def train_skeleton_aware_model_sota(model, train_loader, val_loader, num_epochs=300,
                                        device='cuda', model_dir='./',
                                        resume=True, early_stopping_patience=50):
    """
    Optimized training function with better hyperparameters and stability improvements
    """
    os.makedirs(model_dir, exist_ok=True)
    print(f"Model outputs will be saved to: {model_dir}")

    # Use the fixed loss function
    criterion = SkeletonAwareLoss()
    print("Using loss function: SkeletonAwareLoss")

    # FIXED: Better learning rate and weight decay
    base_lr = 1e-4  # Reduced from 5e-4 
    weight_decay = 1e-4  # Reduced from 1e-2
    optimizer_kwargs = {
        'lr': base_lr,
        'weight_decay': weight_decay,
        'betas': (0.9, 0.999),
        'eps': 1e-8
    }
    
    try:
        optimizer = torch.optim.AdamW(model.parameters(), fused=True, **optimizer_kwargs)
        print("✅ Using fused AdamW optimizer.")
    except Exception as e:
        optimizer = torch.optim.AdamW(model.parameters(), **optimizer_kwargs)
        print(f"⚠️ Fused AdamW failed ({e}), using standard AdamW.")

    # FIXED: Better learning rate scheduler
    scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
        optimizer,
        mode='min',
        factor=0.5,
        patience=10,  # Reduce LR if no improvement for 10 epochs
        min_lr=1e-7
        
    )
    print("Using LR Scheduler: ReduceLROnPlateau")

    # FIXED: Updated GradScaler initialization for newer PyTorch versions
    try:
        from torch.amp import GradScaler as NewGradScaler
        scaler = NewGradScaler('cuda')  # New syntax
        print("Using Automatic Mixed Precision (AMP) with new syntax.")
    except (ImportError, TypeError):
        scaler = GradScaler()  # Fallback to old syntax
        print("Using Automatic Mixed Precision (AMP) with legacy syntax.")

    model.to(device)
    print(f"Model moved to device: {device}")

    # Initialize tracking variables
    start_epoch = 0
    best_loss = float('inf')
    epochs_since_improvement = 0
    
    # Loss tracking
    train_losses, val_losses = [], []
    component_train_losses = {
        'semantic': [], 'sdt': [], 'skeleton': [], 'centroid': [], 'flow': [], 'boundary': []
    }
    component_val_losses = {
        'semantic': [], 'sdt': [], 'skeleton': [], 'centroid': [], 'flow': [], 'boundary': []
    }

    best_model_path = os.path.join(model_dir, 'best_model.pth')
    final_model_path = os.path.join(model_dir, 'final_model.pth')

    # Resume training if checkpoint exists
    if resume and os.path.isfile(best_model_path):
        print(f"Attempting to resume training from {best_model_path}...")
        try:
            checkpoint = torch.load(best_model_path, map_location=device, weights_only=False)
            model.load_state_dict(checkpoint['model_state_dict'])
            optimizer.load_state_dict(checkpoint.get('optimizer_state_dict', {}))
            
            start_epoch = checkpoint.get('epoch', 0) + 1
            best_loss = checkpoint.get('loss', float('inf'))
            epochs_since_improvement = checkpoint.get('epochs_since_improvement', 0)
            
            # Load loss history
            train_losses = checkpoint.get('train_losses', [])[:start_epoch]
            val_losses = checkpoint.get('val_losses', [])[:start_epoch]
            
            print(f"✅ Resumed from epoch {start_epoch}, best val loss: {best_loss:.6f}")
        except Exception as e:
            print(f"❌ Failed to resume: {e}. Starting from scratch.")
            start_epoch = 0
            best_loss = float('inf')
            epochs_since_improvement = 0

    print(f"\n--- Starting Training from epoch {start_epoch + 1} to {num_epochs} ---")

    # Training loop
    for epoch in range(start_epoch, num_epochs):
        # Training phase
        model.train()
        epoch_train_losses = []
        epoch_train_components = {k: [] for k in component_train_losses}

        train_pbar = tqdm(train_loader, desc=f"Epoch {epoch+1}/{num_epochs} [Train]")
        
        for batch_idx, (images, targets) in enumerate(train_pbar):
            images = images.float().to(device, non_blocking=True)
            targets = targets.float().to(device, non_blocking=True)

            optimizer.zero_grad(set_to_none=True)

            # FIXED: Forward pass with mixed precision - version compatible autocast
            try:
                # Try new syntax first
                from torch.amp import autocast as new_autocast
                with new_autocast('cuda', dtype=torch.float16):
                    outputs = model(images)
                    loss, loss_components = criterion(outputs, targets)
            except (ImportError, TypeError):
                # Fallback to old syntax
                with autocast():
                    outputs = model(images)
                    loss, loss_components = criterion(outputs, targets)

            # Check for valid loss
            if torch.isnan(loss) or torch.isinf(loss):
                print(f"⚠️ Skipping batch {batch_idx} due to invalid loss: {loss.item()}")
                continue

            # Backward pass
            scaler.scale(loss).backward()
            scaler.unscale_(optimizer)
            
            # FIXED: More conservative gradient clipping
            grad_norm = torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            
            scaler.step(optimizer)
            scaler.update()

            # Track losses
            epoch_train_losses.append(loss.item())
            for k, v in loss_components.items():
                if k != 'total' and k in epoch_train_components:
                    epoch_train_components[k].append(v.item())

            # Update progress bar
            if batch_idx % 10 == 0:
                current_lr = optimizer.param_groups[0]['lr']
                train_pbar.set_postfix({
                    'Loss': f'{loss.item():.4f}',
                    'LR': f'{current_lr:.2e}',
                    'Grad': f'{grad_norm:.2f}'
                })

        # Calculate average training losses
        avg_train_loss = np.mean(epoch_train_losses) if epoch_train_losses else float('inf')
        train_losses.append(avg_train_loss)
        
        for k in component_train_losses:
            if epoch_train_components[k]:
                avg_comp_loss = np.mean(epoch_train_components[k])
                component_train_losses[k].append(avg_comp_loss)
            else:
                component_train_losses[k].append(0.0)

        # Validation phase
        model.eval()
        epoch_val_losses = []
        epoch_val_components = {k: [] for k in component_val_losses}

        with torch.no_grad():
            val_pbar = tqdm(val_loader, desc=f"Epoch {epoch+1}/{num_epochs} [Val]", leave=False)
            
            for images, targets in val_pbar:
                images = images.float().to(device, non_blocking=True)
                targets = targets.float().to(device, non_blocking=True)

                # FIXED: Updated autocast usage for validation - version compatible
                try:
                    # Try new syntax first
                    from torch.amp import autocast as new_autocast
                    with new_autocast('cuda', dtype=torch.float16):
                        outputs = model(images)
                        loss, loss_dict = criterion(outputs, targets)
                except (ImportError, TypeError):
                    # Fallback to old syntax
                    with autocast():
                        outputs = model(images)
                        loss, loss_dict = criterion(outputs, targets)

                if not torch.isnan(loss) and not torch.isinf(loss):
                    epoch_val_losses.append(loss.item())
                    for k, v in loss_dict.items():
                        if k != 'total' and k in epoch_val_components:
                            epoch_val_components[k].append(v.item())

                val_pbar.set_postfix({'Loss': f'{loss.item():.4f}'})

        # Calculate average validation losses
        avg_val_loss = np.mean(epoch_val_losses) if epoch_val_losses else float('inf')
        val_losses.append(avg_val_loss)
        
        for k in component_val_losses:
            if epoch_val_components[k]:
                avg_comp_loss = np.mean(epoch_val_components[k])
                component_val_losses[k].append(avg_comp_loss)
            else:
                component_val_losses[k].append(0.0)

        # Update learning rate scheduler
        scheduler.step(avg_val_loss)
        current_lr = optimizer.param_groups[0]['lr']

        # Print epoch summary
        print(f"\nEpoch {epoch+1} Summary:")
        print(f"  Train Loss: {avg_train_loss:.6f} | Val Loss: {avg_val_loss:.6f} | LR: {current_lr:.2e}")
        
        # Print component losses
        train_comp_str = " | ".join([f"{k}: {v[-1]:.4f}" for k, v in component_train_losses.items()])
        val_comp_str = " | ".join([f"{k}: {v[-1]:.4f}" for k, v in component_val_losses.items()])
        print(f"  Train Components: {train_comp_str}")
        print(f"  Val Components:   {val_comp_str}")

        # Check for improvement and save best model
        if avg_val_loss < best_loss:
            best_loss = avg_val_loss
            epochs_since_improvement = 0
            
            # Save best model
            best_checkpoint = {
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'scheduler_state_dict': scheduler.state_dict(),
                'epoch': epoch,
                'loss': best_loss,
                'epochs_since_improvement': epochs_since_improvement,
                'train_losses': train_losses,
                'val_losses': val_losses,
                'component_train_losses': component_train_losses,
                'component_val_losses': component_val_losses
            }
            torch.save(best_checkpoint, best_model_path)
            print(f"  ✅ New best model saved (Val Loss: {best_loss:.6f})")
            
            # Visualize progress
            if epoch % max(1, num_epochs // 20) == 0 or epoch < 10:
                try:
                    visualize_predictions(model, val_loader, device, model_dir, epoch)
                except Exception as e:
                    print(f"⚠️ Visualization failed: {e}")
        else:
            epochs_since_improvement += 1
            print(f"  ⏱️ Epochs since improvement: {epochs_since_improvement}")

        # Early stopping check
        if epochs_since_improvement >= early_stopping_patience:
            print(f"\n⚠️ Early stopping triggered after {epoch + 1} epochs (patience {early_stopping_patience})")
            break

        # Plot loss curves periodically
        if epoch % 10 == 0 or epoch == num_epochs - 1:
            plot_loss_curves(train_losses, val_losses, component_train_losses, 
                           component_val_losses, model_dir, epoch)

    # Save final model
    print("\n--- Training Completed ---")
    final_checkpoint = {
        'model_state_dict': model.state_dict(),
        'optimizer_state_dict': optimizer.state_dict(),
        'scheduler_state_dict': scheduler.state_dict(),
        'epoch': len(train_losses) - 1,
        'loss': val_losses[-1] if val_losses else float('inf'),
        'train_losses': train_losses,
        'val_losses': val_losses,
        'component_train_losses': component_train_losses,
        'component_val_losses': component_val_losses
    }
    torch.save(final_checkpoint, final_model_path)
    print(f"💾 Final model saved to {final_model_path}")

    print(f"\n🎉 Training finished. Best validation loss: {best_loss:.6f}")
    return model, train_losses, val_losses

def plot_loss_curves(train_losses, val_losses, component_train_losses, 
                    component_val_losses, model_dir, epoch):
    """Fixed plot and save loss curves"""
    try:
        # FIXED: Ensure all arrays have same length
        min_len = min(len(train_losses), len(val_losses))
        train_losses = train_losses[:min_len]
        val_losses = val_losses[:min_len]
        
        # Fix component losses to same length
        for k, v in component_train_losses.items():
            if v and len(v) > min_len:
                component_train_losses[k] = v[:min_len]
        
        for k, v in component_val_losses.items():
            if v and len(v) > min_len:
                component_val_losses[k] = v[:min_len]
        
        plt.figure(figsize=(15, 10))
        epochs = range(1, min_len + 1)
        
        # Overall losses
        plt.subplot(2, 2, 1)
        plt.plot(epochs, train_losses, 'b-', label='Train Loss', alpha=0.8)
        plt.plot(epochs, val_losses, 'r-', label='Val Loss', alpha=0.8)
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.title('Overall Training Progress')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # Component losses - Training
        plt.subplot(2, 2, 2)
        for k, v in component_train_losses.items():
            if v and len(v) == min_len:  # FIXED: Only plot if correct length
                plt.plot(epochs, v, label=f'{k}', alpha=0.7)
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.title('Training Component Losses')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # Component losses - Validation
        plt.subplot(2, 2, 3)
        for k, v in component_val_losses.items():
            if v and len(v) == min_len:  # FIXED: Only plot if correct length
                plt.plot(epochs, v, label=f'{k}', alpha=0.7, linestyle='--')
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.title('Validation Component Losses')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # Combined view
        plt.subplot(2, 2, 4)
        plt.plot(epochs, train_losses, 'b-', label='Train Total', alpha=0.8, linewidth=2)
        plt.plot(epochs, val_losses, 'r-', label='Val Total', alpha=0.8, linewidth=2)
        
        # Add component losses with lower alpha
        for k, v in component_train_losses.items():
            if v and len(v) == min_len:  # FIXED: Only plot if correct length
                plt.plot(epochs, v, alpha=0.3, linewidth=1)
        
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.title('All Losses Combined')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        loss_path = os.path.join(model_dir, f'loss_curves_epoch_{epoch+1}.png')
        plt.savefig(loss_path, dpi=150, bbox_inches='tight')
        plt.close()
        
    except Exception as e:
        print(f"⚠️ Error plotting loss curves: {e}")

def visualize_predictions(model, val_loader, device, model_dir, epoch):
    """Visualize model predictions"""
    model.eval()
    
    with torch.no_grad():
        # Get a batch for visualization
        images, targets = next(iter(val_loader))
        images = images[:4].to(device)  # Take first 4 samples
        targets = targets[:4].to(device)
        
        # FIXED: Get predictions with version compatible autocast
        try:
            # Try new syntax first
            from torch.amp import autocast as new_autocast
            with new_autocast('cuda', dtype=torch.float16):
                outputs = model(images)
        except (ImportError, TypeError):
            # Fallback to old syntax
            with autocast():
                outputs = model(images)
        
        # Create visualization
        fig, axes = plt.subplots(4, 8, figsize=(20, 10))
        
        for i in range(4):
            # Original image
            axes[i, 0].imshow(images[i, 0].cpu().numpy(), cmap='gray')
            axes[i, 0].set_title('Input')
            axes[i, 0].axis('off')
            
            # Ground truth semantic
            axes[i, 1].imshow(targets[i, 0].cpu().numpy(), cmap='viridis')
            axes[i, 1].set_title('GT Semantic')
            axes[i, 1].axis('off')
            
            # Predicted semantic
            sem_pred = torch.sigmoid(outputs['semantic'][i, 0]).cpu().numpy()
            axes[i, 2].imshow(sem_pred, cmap='viridis')
            axes[i, 2].set_title('Pred Semantic')
            axes[i, 2].axis('off')
            
            # Ground truth SDT
            axes[i, 3].imshow(targets[i, 1].cpu().numpy(), cmap='hot')
            axes[i, 3].set_title('GT SDT')
            axes[i, 3].axis('off')
            
            # Predicted SDT
            sdt_pred = torch.sigmoid(outputs['sdt'][i, 0]).cpu().numpy()
            axes[i, 4].imshow(sdt_pred, cmap='hot')
            axes[i, 4].set_title('Pred SDT')
            axes[i, 4].axis('off')
            
            # Ground truth skeleton
            axes[i, 5].imshow(targets[i, 2].cpu().numpy(), cmap='Reds')
            axes[i, 5].set_title('GT Skeleton')
            axes[i, 5].axis('off')
            
            # Predicted skeleton
            skel_pred = torch.sigmoid(outputs['skeleton'][i, 0]).cpu().numpy()
            axes[i, 6].imshow(skel_pred, cmap='Reds')
            axes[i, 6].set_title('Pred Skeleton')
            axes[i, 6].axis('off')
            
            # Centroid comparison
            cent_gt = targets[i, 3].cpu().numpy()
            cent_pred = torch.sigmoid(outputs['centroid'][i, 0]).cpu().numpy()
            combined_cent = np.stack([cent_gt, cent_pred, np.zeros_like(cent_gt)], axis=-1)
            axes[i, 7].imshow(combined_cent)
            axes[i, 7].set_title('Centroid (R=GT, G=Pred)')
            axes[i, 7].axis('off')
        
        plt.tight_layout()
        viz_path = os.path.join(model_dir, f'predictions_epoch_{epoch+1}.png')
        plt.savefig(viz_path, dpi=150, bbox_inches='tight')
        plt.close()

print("✅ Optimized training function with FIXED autocast ready!")

# import os
# import torch
# import torch.nn as nn
# import numpy as np
# import matplotlib.pyplot as plt
# from tqdm import tqdm
# import torch.nn.functional as F
# from scipy.ndimage import maximum_filter
# from skimage.segmentation import watershed, find_boundaries

# # AMP import: compatible with both PyTorch 1.x and 2.x
# try:
#     from torch.amp import GradScaler, autocast  # PyTorch 2.0+
# except ImportError:
#     from torch.cuda.amp import GradScaler, autocast  # PyTorch < 2.0

# def visualize_training_progress(model, data_loader, device, save_dir, epoch,
#                                train_losses=None, val_losses=None,
#                                component_train_losses=None, component_val_losses=None):
#     """
#     Visualization showing all model outputs and instance segmentation.
#     """
#     model.eval()
#     os.makedirs(save_dir, exist_ok=True)
#     with torch.no_grad():
#         images, targets = next(iter(data_loader))
#         images = images.float().to(device)
#         with autocast(device_type=device.type):
#             outputs = model(images)
#         sdt_probs = F.softmax(outputs['sdt_out'], dim=1)
#         bin_centers = (torch.arange(0, sdt_probs.shape[1], dtype=torch.float32, 
#                                  device=sdt_probs.device) + 0.5) / sdt_probs.shape[1]
#         bin_centers = bin_centers.view(1, -1, 1, 1)
#         sdt_scalar = torch.sum(sdt_probs * bin_centers, dim=1)

#     i = 0
#     pred_sem = torch.sigmoid(outputs['sem_out'][i, 0]).cpu().numpy().astype(np.float32)
#     pred_sdt = sdt_scalar[i].cpu().numpy().astype(np.float32)
#     pred_cent = torch.sigmoid(outputs['hm_out'][i, 0]).cpu().numpy().astype(np.float32)
#     pred_skel = torch.sigmoid(outputs['skeleton_out'][i, 0]).cpu().numpy().astype(np.float32)
#     pred_flow = outputs['flow_out'][i].cpu().numpy().astype(np.float32)

#     # Instance Segmentation
#     binary_mask = (pred_sem > 0.3).astype(np.uint8)
#     peaks = (pred_cent > 0.3) & (maximum_filter(pred_cent, size=3) == pred_cent)
#     centroid_coords = np.column_stack(np.where(peaks))
#     seeds = np.zeros_like(pred_sem, dtype=np.int32)
#     for idx, (y, x) in enumerate(centroid_coords, 1):
#         if pred_skel[y, x] > 0.1:
#             seeds[y, x] = idx

#     skel_mask = (pred_skel > 0.5).astype(float)
#     skel_ridges = 1.0 - skel_mask
#     skel_influence = 0.7
#     combined_map = (1.0 - skel_influence) * pred_sdt + skel_influence * skel_ridges

#     # Vectorized flow divergence
#     if pred_flow is not None and pred_flow.shape[0] == 2:
#         flow_divergence = np.zeros_like(pred_sem)
#         flow_y, flow_x = pred_flow
#         flow_divergence[1:-1, 1:-1] = (
#             np.abs(flow_y[1:-1, 2:] - flow_y[1:-1, :-2]) +
#             np.abs(flow_x[2:, 1:-1] - flow_x[:-2, 1:-1])
#         )
#         flow_influence = 0.3
#         combined_map = (1.0 - flow_influence) * combined_map + flow_influence * (1.0 - flow_divergence)

#     instance_labels = watershed(-combined_map, seeds, mask=binary_mask)

#     # Visualization
#     fig, axes = plt.subplots(3, 5, figsize=(30, 18))
#     fig.suptitle(f'Training Progress - Epoch {epoch+1}', fontsize=16)

#     axes[0, 0].imshow(images[i, 0].cpu(), cmap='gray')
#     axes[0, 0].set_title('Input Image')
#     axes[0, 0].axis('off')

#     axes[0, 1].imshow(pred_sem, cmap='viridis')
#     axes[0, 1].set_title('Semantic Mask')
#     axes[0, 1].axis('off')

#     im2 = axes[0, 2].imshow(pred_sdt, cmap='magma')
#     axes[0, 2].set_title('SDT (Reconstructed)')
#     plt.colorbar(im2, ax=axes[0, 2])

#     axes[0, 3].imshow(pred_skel, cmap='bone')
#     axes[0, 3].set_title('Skeleton')
#     axes[0, 3].axis('off')

#     axes[0, 4].imshow(pred_cent, cmap='hot')
#     axes[0, 4].set_title('Centroid Heatmap')
#     axes[0, 4].axis('off')

#     axes[1, 0].imshow(pred_sem, cmap='gray')
#     axes[1, 0].imshow(pred_sdt, cmap='magma', alpha=0.6)
#     axes[1, 0].set_title('Sem + SDT')
#     axes[1, 0].axis('off')

#     axes[1, 1].imshow(pred_sem, cmap='gray')
#     axes[1, 1].imshow(pred_cent, cmap='hot', alpha=0.6)
#     axes[1, 1].set_title('Sem + Centroid')
#     axes[1, 1].axis('off')

#     axes[1, 2].imshow(instance_labels, cmap='nipy_spectral')
#     axes[1, 2].set_title('Instance Segmentation')
#     axes[1, 2].axis('off')

#     axes[1, 3].imshow(pred_flow[0], cmap='RdBu_r')
#     axes[1, 3].set_title('Flow Y')
#     axes[1, 3].axis('off')

#     axes[1, 4].imshow(pred_flow[1], cmap='RdBu_r')
#     axes[1, 4].set_title('Flow X')
#     axes[1, 4].axis('off')

#     skel_mask = (pred_skel > 0.5).astype(float)
#     skel_ridges = 1.0 - skel_mask
#     combined_map = 0.3 * pred_sdt + 0.7 * skel_ridges
#     axes[2, 0].imshow(combined_map, cmap='viridis')
#     axes[2, 0].set_title('SDT + Skeleton Combined')
#     axes[2, 0].axis('off')

#     if pred_flow is not None and pred_flow.shape[0] == 2:
#         flow_divergence = np.zeros_like(pred_sem)
#         flow_y, flow_x = pred_flow
#         flow_divergence[1:-1, 1:-1] = (
#             np.abs(flow_y[1:-1, 2:] - flow_y[1:-1, :-2]) +
#             np.abs(flow_x[2:, 1:-1] - flow_x[:-2, 1:-1])
#         )
#         axes[2, 1].imshow(flow_divergence, cmap='hot')
#         axes[2, 1].set_title('Flow Divergence')
#         axes[2, 1].axis('off')

#     peaks = (pred_cent > 0.4) & (maximum_filter(pred_cent, size=3) == pred_cent)
#     seeds = np.zeros_like(pred_sem, dtype=np.int32)
#     for idx, (y, x) in enumerate(np.column_stack(np.where(peaks)), 1):
#         if pred_skel[y, x] > 0.1:
#             seeds[y, x] = idx
#     axes[2, 2].imshow(seeds > 0, cmap='hot')
#     axes[2, 2].set_title('Valid Seeds (on skeleton)')
#     axes[2, 2].axis('off')

#     boundaries = find_boundaries(instance_labels, connectivity=1)
#     axes[2, 3].imshow(boundaries, cmap='gray')
#     axes[2, 3].set_title('Instance Boundaries')
#     axes[2, 3].axis('off')

#     if train_losses and val_losses:
#         axes[2, 4].clear()
#         epochs_plotted = range(1, len(train_losses) + 1)
#         axes[2, 4].plot(epochs_plotted, train_losses, label='Train Loss')
#         axes[2, 4].plot(epochs_plotted, val_losses, label='Val Loss')
#         axes[2, 4].set_xlabel('Epoch')
#         axes[2, 4].set_ylabel('Loss')
#         axes[2, 4].set_title('Overall Loss')
#         axes[2, 4].legend()
#         axes[2, 4].grid(True)
#     else:
#         axes[2, 4].axis('off')

#     plt.tight_layout()
#     progress_plot_path = os.path.join(save_dir, f'progress_epoch_{epoch+1}.png')
#     plt.savefig(progress_plot_path, dpi=150, bbox_inches='tight')
#     plt.close()
#     print(f"📊 Progress plot saved to {progress_plot_path}")

# def train_skeleton_aware_model_sota(model, train_loader, val_loader, num_epochs=300,
#                                     device='cuda', model_dir='./',
#                                     resume=True, early_stopping_patience=30):
#     os.makedirs(model_dir, exist_ok=True)
#     print(f"Model outputs will be saved to: {model_dir}")

#     criterion = SkeletonAwareLoss()
#     print("Using loss function: SkeletonAwareLoss")

#     base_lr = 5e-4
#     optimizer_kwargs = {
#         'lr': base_lr,
#         'weight_decay': 1e-2,
#         'betas': (0.9, 0.999)
#     }
#     try:
#         optimizer = torch.optim.AdamW(model.parameters(), fused=True, **optimizer_kwargs)
#         print("✅ Using fused AdamW optimizer.")
#     except Exception as e:
#         optimizer = torch.optim.AdamW(model.parameters(), **optimizer_kwargs)
#         print(f"⚠️ Fused AdamW failed ({e}), using standard AdamW.")

#     scheduler = torch.optim.lr_scheduler.CosineAnnealingWarmRestarts(
#         optimizer,
#         T_0=20,
#         T_mult=1,
#         eta_min=1e-6
#     )
#     print("Using LR Scheduler: CosineAnnealingWarmRestarts")

#     scaler = GradScaler()
#     print("Using Automatic Mixed Precision (AMP).")

#     model.to(device)
#     print(f"Model moved to device: {device}")

#     start_epoch = 0
#     best_loss = float('inf')
#     epochs_since_improvement = 0

#     initial_train_losses, initial_val_losses = [], []
#     initial_component_train_losses = {
#         'semantic': [], 'sdt': [], 'skeleton': [], 'centroid': [], 'flow': [], 'boundary': []
#     }
#     initial_component_val_losses = {
#         'semantic': [], 'sdt': [], 'skeleton': [], 'centroid': [], 'flow': [], 'boundary': []
#     }

#     best_model_path = os.path.join(model_dir, 'best_model.pth')
#     final_model_path = os.path.join(model_dir, 'final_model.pth')

#     if resume and os.path.isfile(best_model_path):
#         print(f"Attempting to resume training from {best_model_path}...")
#         try:
#             checkpoint = torch.load(best_model_path, map_location=device, weights_only=False)
#             model.load_state_dict(checkpoint['model_state_dict'])
#             print("✅ Model state loaded successfully.")

#             if 'optimizer_state_dict' in checkpoint:
#                 optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
#                 print("✅ Optimizer state loaded successfully.")
#             else:
#                 print("⚠️ Warning: 'optimizer_state_dict' not found in checkpoint. Starting optimizer from scratch.")

#             if 'scheduler_state_dict' in checkpoint:
#                 try:
#                     scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
#                     print("✅ Scheduler state loaded successfully.")
#                 except Exception as e:
#                     print(f"⚠️ Warning: Failed to load scheduler state: {e}. Scheduler will restart.")
#             else:
#                 print("⚠️ Warning: 'scheduler_state_dict' not found in checkpoint. Scheduler will restart.")

#             start_epoch = checkpoint.get('epoch', 0) + 1
#             best_loss = checkpoint.get('loss', float('inf'))
#             epochs_since_improvement = checkpoint.get('epochs_since_improvement', 0)

#             if 'train_losses' in checkpoint:
#                 initial_train_losses = checkpoint['train_losses'][:start_epoch]
#             if 'val_losses' in checkpoint:
#                 initial_val_losses = checkpoint['val_losses'][:start_epoch]
#             if 'component_train_losses' in checkpoint:
#                 for k in initial_component_train_losses:
#                     if k in checkpoint['component_train_losses']:
#                         initial_component_train_losses[k] = checkpoint['component_train_losses'][k][:start_epoch]
#             if 'component_val_losses' in checkpoint:
#                 for k in initial_component_val_losses:
#                     if k in checkpoint['component_val_losses']:
#                         initial_component_val_losses[k] = checkpoint['component_val_losses'][k][:start_epoch]

#             print(f"✅ Resumed from epoch {start_epoch}, best val loss: {best_loss:.6f}, "
#                   f"epochs since improvement: {epochs_since_improvement}")
#         except Exception as e:
#             print(f"❌ Failed to resume: {e}. Starting from scratch.")
#             start_epoch = 0
#             best_loss = float('inf')
#             epochs_since_improvement = 0
#     else:
#         if resume:
#             print(f"No checkpoint found at {best_model_path}. Starting from scratch.")
#         else:
#             print("Resume disabled. Starting from scratch.")

#     train_losses = initial_train_losses[:]
#     val_losses = initial_val_losses[:]
#     component_train_losses = {k: v[:] for k, v in initial_component_train_losses.items()}
#     component_val_losses = {k: v[:] for k, v in initial_component_val_losses.items()}

#     print(f"\n--- Starting/Resuming Training from epoch {start_epoch + 1} to {num_epochs} ---")

#     for epoch in range(start_epoch, num_epochs):
#         if hasattr(criterion, 'set_epoch'):
#             criterion.set_epoch(epoch)

#         model.train()
#         epoch_train_losses = []
#         epoch_train_components = {k: 0.0 for k in component_train_losses}

#         train_pbar = tqdm(train_loader, desc=f"Epoch {epoch+1}/{num_epochs} [Train]")
#         for images, targets in train_pbar:
#             images = images.float().to(device, non_blocking=True)
#             targets = targets.float().to(device, non_blocking=True)

#             optimizer.zero_grad(set_to_none=True)

#             with autocast(device_type=device.type):
#                 outputs = model(images)
#                 loss, loss_components = criterion(outputs, targets)

#             if not torch.isnan(loss) and not torch.isinf(loss):
#                 scaler.scale(loss).backward()
#                 scaler.unscale_(optimizer)
#                 grad_norm = torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=5.0)
#                 scaler.step(optimizer)
#                 scaler.update()
#                 scheduler.step()

#                 epoch_train_losses.append(loss.item())
#                 for k in epoch_train_components:
#                     if k in loss_components:
#                         epoch_train_components[k] += loss_components[k].item()

#                 if len(epoch_train_losses) % 10 == 0 or len(epoch_train_losses) == len(train_loader):
#                     current_lr = optimizer.param_groups[0]['lr']
#                     train_pbar.set_postfix({
#                         'Loss': f'{loss.item():.4f}',
#                         'LR': f'{current_lr:.2e}',
#                         'Grad_Norm': f'{grad_norm:.2f}'
#                     })
#             else:
#                 print(f"⚠️  Skipping batch due to invalid loss (NaN/Inf): {loss.item()}")
#                 optimizer.zero_grad(set_to_none=True)

#         avg_train_loss = np.mean(epoch_train_losses) if epoch_train_losses else float('inf')
#         for k in epoch_train_components:
#             epoch_train_components[k] /= len(train_loader) if len(train_loader) > 0 else 1
#             component_train_losses[k].append(epoch_train_components[k])
#         train_losses.append(avg_train_loss)

#         model.eval()
#         epoch_val_losses = []
#         epoch_val_components = {k: 0.0 for k in component_val_losses}

#         with torch.no_grad():
#             val_pbar = tqdm(val_loader, desc=f"Epoch {epoch+1}/{num_epochs} [Val]", leave=False)
#             for images, targets in val_pbar:
#                 images = images.float().to(device, non_blocking=True)
#                 targets = targets.float().to(device, non_blocking=True)

#                 with autocast(device_type=device.type):
#                     outputs = model(images)
#                     loss, loss_dict = criterion(outputs, targets)

#                 if not torch.isnan(loss) and not torch.isinf(loss):
#                     epoch_val_losses.append(loss.item())
#                     for k in epoch_val_components:
#                         if k in loss_dict:
#                             epoch_val_components[k] += loss_dict[k].item()

#                 if len(epoch_val_losses) % 10 == 0 or len(epoch_val_losses) == len(val_loader):
#                     val_pbar.set_postfix({'Loss': f'{loss.item():.4f}'})

#         avg_val_loss = np.mean(epoch_val_losses) if epoch_val_losses else float('inf')
#         for k in epoch_val_components:
#             epoch_val_components[k] /= len(val_loader) if len(val_loader) > 0 else 1
#             component_val_losses[k].append(epoch_val_components[k])
#         val_losses.append(avg_val_loss)

#         current_lr = optimizer.param_groups[0]['lr']
#         print(f"Epoch {epoch+1} Summary:")
#         print(f"  Train Loss: {avg_train_loss:.6f} | Val Loss: {avg_val_loss:.6f} | LR: {current_lr:.2e}")
#         train_comp_str = " | ".join([f"{k}: {v[-1]:.4f}" for k, v in component_train_losses.items() if v])
#         val_comp_str = " | ".join([f"{k}: {v[-1]:.4f}" for k, v in component_val_losses.items() if v])
#         print(f"  Train Components: {train_comp_str}")
#         print(f"  Val Components:   {val_comp_str}")

#         new_best = False
#         if avg_val_loss < best_loss:
#             best_loss = avg_val_loss
#             epochs_since_improvement = 0
#             best_checkpoint = {
#                 'model_state_dict': model.state_dict(),
#                 'optimizer_state_dict': optimizer.state_dict(),
#                 'scheduler_state_dict': scheduler.state_dict(),
#                 'epoch': epoch,
#                 'loss': best_loss,
#                 'epochs_since_improvement': epochs_since_improvement,
#                 'train_losses': train_losses,
#                 'val_losses': val_losses,
#                 'component_train_losses': component_train_losses,
#                 'component_val_losses': component_val_losses
#             }
#             torch.save(best_checkpoint, best_model_path)
#             print(f"  ✅ New best model saved (Val Loss: {best_loss:.6f})")
#             new_best = True
#         else:
#             epochs_since_improvement += 1
#             print(f"  ⏱️  Epochs since last improvement: {epochs_since_improvement}")

#         # Always visualize on new best, and periodically
#         if new_best or epoch % max(1, num_epochs // 20) == 0 or epoch == num_epochs - 1 or epoch == start_epoch:
#             visualize_training_progress(
#                 model, val_loader, device, model_dir, epoch,
#                 train_losses, val_losses,
#                 component_train_losses, component_val_losses
#             )

#         if epochs_since_improvement >= early_stopping_patience:
#             print(f"\n⚠️ Early stopping triggered after {epoch + 1} epochs (patience {early_stopping_patience}).")
#             break

#     print("\n--- Training Completed ---")
#     final_checkpoint = {
#         'model_state_dict': model.state_dict(),
#         'optimizer_state_dict': optimizer.state_dict(),
#         'scheduler_state_dict': scheduler.state_dict(),
#         'epoch': len(train_losses) - 1,
#         'loss': val_losses[-1] if val_losses else float('inf'),
#         'epochs_since_improvement': epochs_since_improvement,
#         'train_losses': train_losses,
#         'val_losses': val_losses,
#         'component_train_losses': component_train_losses,
#         'component_val_losses': component_val_losses
#     }
#     torch.save(final_checkpoint, final_model_path)
#     print(f"💾 Final model checkpoint saved to {final_model_path}")

#     try:
#         plt.figure(figsize=(12, 5))
#         plt.subplot(1, 2, 1)
#         epochs_plotted = range(1, len(train_losses) + 1)
#         plt.plot(epochs_plotted, train_losses, label='Train Loss')
#         plt.plot(epochs_plotted, val_losses, label='Val Loss')
#         plt.xlabel('Epoch')
#         plt.ylabel('Loss')
#         plt.title('Overall Training & Validation Loss')
#         plt.legend()
#         plt.grid(True)

#         plt.subplot(1, 2, 2)
#         for k, v in component_train_losses.items():
#             if v:
#                 plt.plot(epochs_plotted, v, label=f'Train {k}')
#         for k, v in component_val_losses.items():
#             if v:
#                 val_comp_aligned = v[:len(epochs_plotted)]
#                 plt.plot(epochs_plotted, val_comp_aligned, label=f'Val {k}', linestyle='--')
#         plt.xlabel('Epoch')
#         plt.ylabel('Loss')
#         plt.title('Component Losses')
#         plt.legend()
#         plt.grid(True)
#         plt.tight_layout()
#         loss_curve_path = os.path.join(model_dir, 'final_loss_curves.png')
#         plt.savefig(loss_curve_path)
#         plt.close()
#         print(f"📊 Final loss curves plot saved to {loss_curve_path}")
#     except Exception as e:
#         print(f"⚠️ Error plotting final loss curves: {e}")

#     print(f"\n🎉 Training finished. Best validation loss: {best_loss:.6f}")
#     return model, train_losses, val_losses

# print("✅ State-of-the-Art training function 'train_skeleton_aware_model_sota' with dynamic curriculum and visualization is ready for use.")

print(f"Fused optimizers available: {hasattr(torch.optim.AdamW, '__init__') and 'fused' in torch.optim.AdamW.__init__.__code__.co_varnames}")




# Set paths
image_dir = "/mnt/d/Users/<USER>/FISH_spots/2d/synthetic/images/"
mask_dir = "/mnt/d/Users/<USER>/FISH_spots/2d/synthetic/masks/"
image_paths = sorted(glob.glob(os.path.join(image_dir, '*.tif')))
mask_paths = sorted(glob.glob(os.path.join(mask_dir, '*.tif')))

# # Example usage (uncomment to run):
# device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
# model = SkeletonAwareSpotDetector(in_ch=1, base_ch=48).to(device)
# # # Load a trained model if available
# # # checkpoint = torch.load('path/to/best_model.pth')
# # # model.load_state_dict(checkpoint['model_state_dict'])
# # 
# # # Create data loaders (replace with your actual paths)
# train_loader, val_loader = create_skeleton_aware_data_loaders_fixed(image_paths, mask_paths)
# # 
# # # Run inspection# 
# results = inspect_skeleton_aware_model(model, val_loader, device, num_batches=2)




# Cell 9: Complete Pipeline with Updated Paths and SOTA Components

import os
import glob
import torch
import numpy as np
import tifffile
import matplotlib.pyplot as plt
from skimage.color import label2rgb

# --- 1. Set Paths ---
image_dir = "/mnt/d/Users/<USER>/FISH_spots/2d/synthetic/images/"
mask_dir = "/mnt/d/Users/<USER>/FISH_spots/2d/synthetic/masks/"
image_paths = sorted(glob.glob(os.path.join(image_dir, '*.tif')))
mask_paths = sorted(glob.glob(os.path.join(mask_dir, '*.tif')))

print(f"Found {len(image_paths)} images and {len(mask_paths)} masks")

# Verify a few paths
if image_paths and mask_paths:
    print("Sample image path:", image_paths[0])
    print("Sample mask path:", mask_paths[0])
    
    # Check if corresponding files exist
    base_name = os.path.splitext(os.path.basename(image_paths[0]))[0]
    expected_mask = os.path.join(mask_dir, base_name + '.tif')
    if os.path.exists(expected_mask):
        print("✓ Matching mask found for first image")
    else:
        print("⚠️  Matching mask not found for first image")

# --- 2. Configuration ---
model_dir = '/mnt/d/Users/<USER>/Documents/spot_detector_model_sota_08072025_afternoon/' # Updated directory name
os.makedirs(model_dir, exist_ok=True)
print(f"Model will be saved to: {model_dir}")

device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"Using device: {device}")

# --- 3. Create Data Loaders (using the FIXED version) ---
print("Creating skeleton-aware data loaders...")
# Disable pin_memory to prevent CUDA OOM errors as identified previously
train_loader, val_loader = create_skeleton_aware_data_loaders_fixed(
    image_paths, mask_paths, 
    batch_size=12, 
    patch_size=256, 
    num_workers=10 # Reduced from 4 to potentially improve stability if stuttering was an issue
)

print(f"Training samples: {len(train_loader.dataset)}")
print(f"Validation samples: {len(val_loader.dataset)}")


# Run inspection on validation data
inspection_results = inspect_ground_truth_data(
    val_loader, 
    device, 
    num_batches=2, 
    save_dir="gt_inspection_output"
)

from scipy.ndimage import maximum_filter
from skimage.segmentation import watershed,find_boundaries
# --- 4. Initialize Model ---
print("Initializing skeleton-aware model...")
# Use the latest, corrected model definition
model = SkeletonAwareSpotDetector(in_ch=1, base_ch=48) 
print(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")

# --- 5. TRAINING with SOTA Function ---
print("Starting SOTA skeleton-aware training (with resume and early stopping)...")
# Use the State-of-the-Art training function with early stopping
trained_model, train_losses, val_losses = train_skeleton_aware_model_sota(
    model, 
    train_loader, 
    val_loader, 
    num_epochs=300,
    device=device,
    model_dir=model_dir,
    resume=True, # Set to False if you want to force a fresh start
    early_stopping_patience=30 # Stop if no improvement for 30 epochs
)

# --- 6. Save Final Model State ---
# The SOTA function already saves 'best_model.pth' and 'final_model.pth'
# This additional save is redundant but kept for explicitness if needed for a specific purpose.
# It saves the model returned by the training function.
final_model_path_additional = os.path.join(model_dir, 'final_model_from_pipeline.pth')
torch.save({
    'model_state_dict': trained_model.state_dict(),
    'model_config': {
        'in_ch': 1,
        'base_ch': 48
    },
    'train_losses': train_losses,
    'val_losses': val_losses
}, final_model_path_additional)
print(f"Additional final model saved to: {final_model_path_additional}")

# --- 7. Evaluate Model ---
print("Running skeleton-aware evaluation...")


def evaluate_skeleton_aware_model(model, val_loader, device='cuda'):
    model.eval()
    all_metrics = {'precision': [], 'recall': [], 'f1': [], 'skeleton_iou': [], 'sdt_mse': [], 'boundary_iou': []}
    with torch.no_grad():
        for images, targets in val_loader:
            images = images.to(device, non_blocking=True)
            targets = targets.to(device, non_blocking=True)
            batch_size = images.shape[0]
            for i in range(batch_size):
                try:
                    with torch.amp.autocast(device_type='cuda', enabled=True):
                        outputs = model(images[i:i+1])
                    # Process SDT
                    sdt_probs = F.softmax(outputs['sdt_out'], dim=1)
                    bin_centers = (torch.arange(0, sdt_probs.shape[1], device=sdt_probs.device, dtype=torch.float32) + 0.5) / sdt_probs.shape[1]
                    bin_centers = bin_centers.view(1, -1, 1, 1)
                    pred_sdt = torch.sum(sdt_probs * bin_centers, dim=1, keepdim=True)
                    # Predictions
                    pred_semantic = torch.sigmoid(outputs['sem_out']) > 0.5
                    pred_skeleton = torch.sigmoid(outputs['skeleton_out']) > 0.5
                    # Ground truth
                    gt_semantic = targets[i:i+1, 0:1] > 0.5
                    gt_skeleton = targets[i:i+1, 2:3] > 0.5
                    gt_boundary = targets[i:i+1, 6:7] > 0.5 # Corrected index
                    # Metrics
                    tp = (pred_semantic & gt_semantic).float().sum().item()
                    fp = (pred_semantic & ~gt_semantic).float().sum().item()
                    fn = (~pred_semantic & gt_semantic).float().sum().item()
                    precision = tp / (tp + fp + 1e-6)
                    recall = tp / (tp + fn + 1e-6)
                    f1 = 2 * (precision * recall) / (precision + recall + 1e-6)
                    skeleton_iou = (pred_skeleton & gt_skeleton).float().sum().item() / ((pred_skeleton | gt_skeleton).float().sum().item() + 1e-6)
                    boundary_iou = (pred_semantic & gt_boundary).float().sum().item() / ((pred_semantic | gt_boundary).float().sum().item() + 1e-6)
                    sdt_mse = F.mse_loss(pred_sdt, targets[i:i+1, 1:2]).item()
                    
                    all_metrics['precision'].append(precision)
                    all_metrics['recall'].append(recall)
                    all_metrics['f1'].append(f1)
                    all_metrics['skeleton_iou'].append(skeleton_iou)
                    all_metrics['sdt_mse'].append(sdt_mse)
                    all_metrics['boundary_iou'].append(boundary_iou)
                except Exception as e:
                    print(f"Evaluation error: {e}")
                    continue
    eval_metrics = {}
    for k, v in all_metrics.items():
        eval_metrics[k] = np.mean(v) if v else 0.0
        eval_metrics[f'std_{k}'] = np.std(v) if v else 0.0
    return eval_metrics


# Ensure evaluate_skeleton_aware_model is defined or imported
eval_metrics = evaluate_skeleton_aware_model(trained_model, val_loader, device)
print(f"Evaluation Results:")
for k, v in eval_metrics.items():
    if not k.startswith('std_'):
        std_key = f'std_{k}'
        std_val = eval_metrics.get(std_key, 'N/A')
        print(f"  {k.capitalize()}: {v:.4f} ± {std_val:.4f}")

# --- 8. Test Inference on Sample Images ---
print("Testing skeleton-aware inference...")
test_images = image_paths[:3]  # Test on first 3 images

# --- CRITICAL: Use the FIXED inference function ---
# This should be the `tile_inference` function that correctly handles SDT reconstruction
# and uses the improved instance segmentation.
# Assuming `tile_inference` is defined in the current notebook/script context.
# If it's in a separate file, import it: from your_inference_module import tile_inference

for i, test_path in enumerate(test_images):
    print(f"Testing on image {i+1}: {os.path.basename(test_path)}")
    
    # --- Use the robust tiled inference function ---
    # This function handles large images, SDT correctly, and instance segmentation
    try:
        inference_results = tile_inference(
            trained_model,
            test_path,
            device=device,
            patch_size=256,
            overlap=0.5, # Standard overlap
            batch_size=1 # Process one patch at a time for stability during testing
        )
        # Extract results
        # The keys depend on the exact output of your `tile_inference` function
        # Based on the provided `tile_inference` code:
        semantic_result = inference_results.get('semantic', np.zeros((10, 10)))
        sdt_result = inference_results.get('sdt_scalar', np.zeros((10, 10))) # Use scalar SDT
        skeleton_result = inference_results.get('skeleton', np.zeros((10, 10)))
        instance_labels_result = inference_results.get('instance_labels', np.zeros((10, 10), dtype=int))
        spots_result = inference_results.get('spots', [])
        
        # Load original image for visualization
        original_image = tifffile.imread(test_path).astype(np.float32)
        vmin, vmax = np.percentile(original_image, (1, 99))
        original_image_norm = np.clip((original_image - vmin) / (vmax - vmin + 1e-8), 0, 1)

    except Exception as e:
        print(f"  ❌ Error during inference for {os.path.basename(test_path)}: {e}")
        # Create dummy results to allow visualization code to run
        original_image_norm = np.zeros((256, 256))
        semantic_result = np.zeros((256, 256))
        sdt_result = np.zeros((256, 256))
        skeleton_result = np.zeros((256, 256))
        instance_labels_result = np.zeros((256, 256), dtype=int)
        spots_result = []

    # --- Visualize results ---
    try:
        plt.figure(figsize=(20, 15)) # Increased figure size for more plots
        
        # 1. Original image with detected spots
        plt.subplot(3, 3, 1)
        plt.imshow(original_image_norm, cmap='gray')
        if spots_result:
            # Handle both list of dicts and array formats
            if isinstance(spots_result, list) and len(spots_result) > 0 and isinstance(spots_result[0], dict):
                 spots_y = [spot['y'] for spot in spots_result]
                 spots_x = [spot['x'] for spot in spots_result]
            elif isinstance(spots_result, np.ndarray) and spots_result.size > 0:
                 spots_y = spots_result[:, 0]
                 spots_x = spots_result[:, 1]
            else:
                 spots_y, spots_x = [], []
            if spots_y:
                plt.scatter(spots_x, spots_y, c='red', s=30, marker='o', alpha=0.7)
        plt.title(f"Detected Spots: {len(spots_result)}")
        plt.axis('off')

        # 2. Semantic mask
        plt.subplot(3, 3, 2)
        plt.imshow(semantic_result, cmap='viridis')
        plt.title("Semantic Mask")
        plt.axis('off')
        plt.colorbar(shrink=0.8)

        # 3. SDT (Scalar)
        plt.subplot(3, 3, 3)
        plt.imshow(sdt_result, cmap='magma')
        plt.title("SDT (Scalar)")
        plt.axis('off')
        plt.colorbar(shrink=0.8)

        # 4. Skeleton
        plt.subplot(3, 3, 4)
        plt.imshow(skeleton_result, cmap='bone')
        plt.title("Skeleton")
        plt.axis('off')

        # 5. Instance segmentation
        plt.subplot(3, 3, 5)
        instance_viz = label2rgb(instance_labels_result, bg_label=0, alpha=0.7)
        plt.imshow(instance_viz)
        plt.title(f"Instance Segmentation ({np.max(instance_labels_result)} instances)")
        plt.axis('off')

        # 6. Centroid Map (if available in results)
        centroid_result = inference_results.get('centroid', np.zeros((10, 10)))
        plt.subplot(3, 3, 6)
        plt.imshow(centroid_result, cmap='hot')
        plt.title("Centroid Heatmap")
        plt.axis('off')
        plt.colorbar(shrink=0.8)
        
        # 7. Flow Magnitude (if available)
        flow_result = inference_results.get('flow', np.zeros((2, 10, 10)))
        if flow_result is not None and flow_result.ndim == 3 and flow_result.shape[0] == 2:
            flow_mag = np.sqrt(flow_result[0]**2 + flow_result[1]**2 + 1e-8)
            plt.subplot(3, 3, 7)
            plt.imshow(flow_mag, cmap='plasma')
            plt.title("Flow Magnitude")
            plt.axis('off')
            plt.colorbar(shrink=0.8)
        else:
             plt.subplot(3, 3, 7)
             plt.text(0.5, 0.5, 'Flow Not Available', ha='center', va='center')
             plt.title("Flow")
             plt.axis('off')

        # 8. Overlay of instances on image
        plt.subplot(3, 3, 8)
        plt.imshow(original_image_norm, cmap='gray')
        # Overlay instance boundaries
        if np.max(instance_labels_result) > 0:
            # Simple boundary visualization
            from skimage.segmentation import find_boundaries
            boundaries = find_boundaries(instance_labels_result, mode='outer')
            plt.imshow(boundaries, cmap='red', alpha=0.5)
        plt.title("Instances Overlay")
        plt.axis('off')
        
        # 9. Summary text or another relevant plot
        plt.subplot(3, 3, 9)
        plt.text(0.1, 0.8, f"Image: {os.path.basename(test_path)}", fontsize=10)
        plt.text(0.1, 0.6, f"Spots Detected: {len(spots_result)}", fontsize=10)
        plt.text(0.1, 0.4, f"Max Instance ID: {np.max(instance_labels_result)}", fontsize=10)
        # Add a simple histogram of SDT values if interesting
        if sdt_result is not None and sdt_result.size > 1:
            plt.text(0.1, 0.2, f"SDT Mean: {np.mean(sdt_result):.3f}", fontsize=10)
        plt.title("Summary")
        plt.axis('off')
        plt.gca().set_facecolor('lightgray') # Background for text

        plt.suptitle(f"Inference Results for {os.path.basename(test_path)}", fontsize=16)
        plt.tight_layout()
        result_plot_path = os.path.join(model_dir, f'inference_result_{i+1}_{os.path.splitext(os.path.basename(test_path))[0]}.png')
        plt.savefig(result_plot_path, dpi=150, bbox_inches='tight')
        plt.close()
        print(f"  📊 Inference results plot saved to: {result_plot_path}")
        print(f"  🧮 Detected {len(spots_result)} spots/instances.")
        
    except Exception as e:
        print(f"  ❌ Error during visualization for {os.path.basename(test_path)}: {e}")
        # Try to close any open figures to prevent resource leaks
        plt.close('all') 

# --- 9. Memory cleanup ---
torch.cuda.empty_cache()
print("\n🎉 Training and inference pipeline completed successfully!")
print(f"🏁 All outputs and models are saved in: {model_dir}")



# Cell: Model Refinement and Further Improvement

import torch
import torch.nn as nn
import numpy as np
import os
import matplotlib.pyplot as plt
from sklearn.metrics import precision_recall_curve, average_precision_score

def refine_trained_model(
    initial_model_path, 
    image_paths, 
    mask_paths,
    refinement_config,
    device='cuda'
):
    """
    Refine a trained model based on initial results.

    Args:
        initial_model_path (str): Path to the 'best_model.pth' or 'final_model.pth'.
        image_paths (list): List of image file paths.
        mask_paths (list): List of mask file paths.
        refinement_config (dict): Configuration for refinement.
            - 'strategy': 'fine_tune' or 'evaluate_only'
            - 'new_lr': New learning rate for fine-tuning.
            - 'new_epochs': Number of epochs for fine-tuning.
            - 'batch_size': Batch size for refinement data loaders.
            - 'patch_size': Patch size for refinement data loaders.
            - 'num_workers': Number of workers for data loaders.
            - 'loss_weights': Optional dict to adjust loss function weights.
        device (str): Device to run on ('cuda' or 'cpu').

    Returns:
        dict: Results of refinement including model path and metrics.
    """
    print("--- Model Refinement Process ---")
    print(f"Loading initial model from: {initial_model_path}")
    
    # --- 1. Load Initial Model ---
    if not os.path.exists(initial_model_path):
        raise FileNotFoundError(f"Initial model path not found: {initial_model_path}")
        
    checkpoint = torch.load(initial_model_path, map_location=device, weights_only=False)
    
    # Determine model configuration
    # Assuming the model config is saved, otherwise use defaults
    model_config = checkpoint.get('model_config', {'in_ch': 1, 'base_ch': 48})
    print(f"Model config: {model_config}")
    
    # Initialize model
    refined_model = SkeletonAwareSpotDetector(**model_config).to(device)
    refined_model.load_state_dict(checkpoint['model_state_dict'])
    print("✅ Initial model loaded successfully.")

    # --- 2. Prepare Data ---
    print("\n--- Preparing Data for Refinement ---")
    # Use the fixed data loader function
    refinement_train_loader, refinement_val_loader = create_skeleton_aware_data_loaders_fixed(
        image_paths, mask_paths,
        batch_size=refinement_config.get('batch_size', 12),
        patch_size=refinement_config.get('patch_size', 256),
        num_workers=refinement_config.get('num_workers', 4)
    )
    print(f"Refinement DataLoader - Train: {len(refinement_train_loader.dataset)}, Val: {len(refinement_val_loader.dataset)}")

    # --- 3. Refinement Strategy ---
    strategy = refinement_config.get('strategy', 'evaluate_only')
    
    if strategy == 'evaluate_only':
        print("\n--- Evaluation Only Strategy ---")
        refined_model.eval()
        print("Running detailed evaluation on validation set...")
        final_metrics = evaluate_skeleton_aware_model(refined_model, refinement_val_loader, device)
        print("Final Refined Model Evaluation Metrics:")
        for k, v in final_metrics.items():
            if not k.startswith('std_'):
                std_key = f'std_{k}'
                std_val = final_metrics.get(std_key, 'N/A')
                print(f"  {k.capitalize()}: {v:.4f} ± {std_val:.4f}")
        
        # Run inspection on the refined model
        print("\nRunning model inspection on refined model...")
        inspection_results = inspect_skeleton_aware_model(
            refined_model, refinement_val_loader, device, num_batches=2,
            save_dir=os.path.join(model_dir, "refinement_inspection")
        )
        
        return {
            'status': 'evaluation_completed',
            'model_path': initial_model_path,
            'metrics': final_metrics,
            'inspection_results': inspection_results
        }

    elif strategy == 'fine_tune':
        print("\n--- Fine-Tuning Strategy ---")
        
        # --- 4. Setup Fine-Tuning ---
        # Create a new model directory for refined model
        refined_model_dir = os.path.join(model_dir, "refined_model")
        os.makedirs(refined_model_dir, exist_ok=True)
        print(f"Refined model will be saved to: {refined_model_dir}")
        
        # Adjust loss function if specified
        if 'loss_weights' in refinement_config:
            print("Adjusting loss function weights for fine-tuning...")
            # This would require modifying the loss function instance
            # For simplicity here, we assume the main loss function is used
            # A more robust approach would be to create a new loss instance
            # with the specified weights.
            print("Note: Loss weight adjustment not implemented in this snippet. Modify SkeletonAwareLoss directly if needed.")
        
        # Freeze certain layers if specified (example)
        if refinement_config.get('freeze_encoder', False):
            print("Freezing encoder layers...")
            for name, param in refined_model.named_parameters():
                if name.startswith(('stem_conv', 'enc1', 'enc2')):
                    param.requires_grad = False
            print("Encoder layers frozen.")
        
        # Setup optimizer and scheduler for fine-tuning
        new_lr = refinement_config.get('new_lr', 1e-4) # Lower LR for fine-tuning
        new_epochs = refinement_config.get('new_epochs', 50)
        print(f"Fine-tuning with LR: {new_lr}, Epochs: {new_epochs}")
        
        optimizer = torch.optim.AdamW(
            filter(lambda p: p.requires_grad, refined_model.parameters()), # Only optimize unfrozen params
            lr=new_lr,
            weight_decay=1e-4,
            betas=(0.9, 0.999)
        )
        scheduler = torch.optim.lr_scheduler.OneCycleLR(
            optimizer,
            max_lr=new_lr,
            epochs=new_epochs,
            steps_per_epoch=len(refinement_train_loader),
            pct_start=0.1,
            anneal_strategy='cos'
        )
        
        # Use the main loss function
        criterion = SkeletonAwareLoss() # Re-instantiate or use existing if accessible
        
        # --- 5. Fine-Tuning Loop (Simplified version of the main training loop) ---
        print("Starting fine-tuning...")
        best_loss = float('inf')
        refined_train_losses, refined_val_losses = [], []
        
        scaler = GradScaler() # Re-use AMP scaler
        
        for epoch in range(new_epochs):
            # Training
            refined_model.train()
            epoch_train_losses = []
            pbar = tqdm(refinement_train_loader, desc=f"FT Epoch {epoch+1}/{new_epochs}")
            for images, targets in pbar:
                images = images.float().to(device, non_blocking=True)
                targets = targets.float().to(device, non_blocking=True)
                optimizer.zero_grad(set_to_none=True)
                with autocast('cuda'):
                    outputs = refined_model(images)
                    loss, _ = criterion(outputs, targets)
                if not torch.isnan(loss):
                    scaler.scale(loss).backward()
                    scaler.unscale_(optimizer)
                    torch.nn.utils.clip_grad_norm_(refined_model.parameters(), max_norm=1.0)
                    scaler.step(optimizer)
                    scaler.update()
                    scheduler.step()
                    epoch_train_losses.append(loss.item())
                pbar.set_postfix({'Loss': f'{loss.item():.4f}' if not torch.isnan(loss) else 'NaN'})
            
            train_loss = np.mean(epoch_train_losses) if epoch_train_losses else float('inf')
            refined_train_losses.append(train_loss)
            
            # Validation
            refined_model.eval()
            epoch_val_losses = []
            with torch.no_grad():
                for images, targets in refinement_val_loader:
                    images = images.float().to(device, non_blocking=True)
                    targets = targets.float().to(device, non_blocking=True)
                    with autocast('cuda'):
                        outputs = refined_model(images)
                        loss, _ = criterion(outputs, targets)
                    if not torch.isnan(loss):
                        epoch_val_losses.append(loss.item())
            
            val_loss = np.mean(epoch_val_losses) if epoch_val_losses else float('inf')
            refined_val_losses.append(val_loss)
            
            print(f"FT Epoch {epoch+1}: Train Loss: {train_loss:.4f} | Val Loss: {val_loss:.4f}")
            print(f"  LR: {optimizer.param_groups[0]['lr']:.6e}")
            
            # Save best refined model
            if val_loss < best_loss:
                best_loss = val_loss
                refined_checkpoint_path = os.path.join(refined_model_dir, 'best_refined_model.pth')
                torch.save({
                    'model_state_dict': refined_model.state_dict(),
                    'optimizer_state_dict': optimizer.state_dict(),
                    'scheduler_state_dict': scheduler.state_dict(),
                    'epoch': epoch,
                    'loss': best_loss,
                    'model_config': model_config,
                    'refinement_config': refinement_config
                }, refined_checkpoint_path)
                print(f"✅ Best refined model saved (val_loss={best_loss:.4f})")
        
        # Plot refinement loss curves
        plt.figure(figsize=(10, 5))
        plt.plot(refined_train_losses, label='Refined Train Loss')
        plt.plot(refined_val_losses, label='Refined Val Loss')
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.title('Refinement Loss Curves')
        plt.legend()
        plt.grid(True)
        plt.savefig(os.path.join(refined_model_dir, 'refinement_loss_curves.png'))
        plt.close()
        print("Refinement loss curves saved.")
        
        # --- 6. Final Evaluation ---
        print("\nRunning final evaluation on refined model...")
        final_metrics = evaluate_skeleton_aware_model(refined_model, refinement_val_loader, device)
        print("Final Refined Model Evaluation Metrics:")
        for k, v in final_metrics.items():
            if not k.startswith('std_'):
                std_key = f'std_{k}'
                std_val = final_metrics.get(std_key, 'N/A')
                print(f"  {k.capitalize()}: {v:.4f} ± {std_val:.4f}")
        
        # Run inspection on the refined model
        print("\nRunning model inspection on refined model...")
        inspection_results = inspect_skeleton_aware_model(
            refined_model, refinement_val_loader, device, num_batches=2,
            save_dir=os.path.join(refined_model_dir, "refinement_inspection")
        )
        
        return {
            'status': 'fine_tuning_completed',
            'model_path': refined_checkpoint_path,
            'metrics': final_metrics,
            'inspection_results': inspection_results,
            'train_losses': refined_train_losses,
            'val_losses': refined_val_losses
        }
        
    else:
        raise ValueError(f"Unknown refinement strategy: {strategy}")


# --- Example Usage ---

# 1. Specify paths (assuming these are already defined from your main script)
# image_dir = "/mnt/d/Users/<USER>/FISH_spots/2d/synthetic/images/"
# mask_dir = "/mnt/d/Users/<USER>/FISH_spots/2d/synthetic/masks/"
# image_paths = sorted(glob.glob(os.path.join(image_dir, '*.tif')))
# mask_paths = sorted(glob.glob(os.path.join(mask_dir, '*.tif')))
# model_dir = '/mnt/d/Users/<USER>/Documents/spot_detector_model/' # Main model directory

# 2. Determine the path to the best model from initial training
# This could be the 'best_model.pth' saved during training or 'final_model.pth'
initial_model_path = os.path.join(model_dir, 'best_model.pth') # Or 'final_model.pth'

# 3. Define refinement configurations

# Option A: Evaluate Only (No further training)
refinement_config_eval = {
    'strategy': 'evaluate_only'
}

# Option B: Fine-Tune with lower LR and fewer epochs
refinement_config_finetune = {
    'strategy': 'fine_tune',
    'new_lr': 5e-5,          # Lower learning rate for fine-tuning
    'new_epochs': 30,        # Fewer epochs for refinement
    'batch_size': 12,        # Can adjust if needed
    'patch_size': 256,
    'num_workers': 2,        # Reduce workers if you had stuttering issues
    # 'loss_weights': {'w_sem': 1.2, 'w_sdt': 2.5} # Example of adjusting weights
    # 'freeze_encoder': True   # Example of freezing encoder layers
}

# 4. Choose a configuration and run refinement
# Uncomment one of the lines below to choose the strategy

# For Evaluation Only:
# refinement_results = refine_trained_model(
#     initial_model_path, image_paths, mask_paths, refinement_config_eval, device
# )

# For Fine-Tuning:
# refinement_results = refine_trained_model(
#     initial_model_path, image_paths, mask_paths, refinement_config_finetune, device
# )

# 5. Access results
# print("\n--- Refinement Results ---")
# print(f"Status: {refinement_results['status']}")
# print(f"Model Path: {refinement_results['model_path']}")
# if 'metrics' in refinement_results:
#     print("Final Metrics:")
#     for k, v in refinement_results['metrics'].items():
#         if not k.startswith('std_'):
#             std_key = f'std_{k}'
#             std_val = refinement_results['metrics'].get(std_key, 'N/A')
#             print(f"  {k.capitalize()}: {v:.4f} ± {std_val:.4f}")

print("Refinement code is ready. Uncomment the example usage section to run.")


Explanation:
refine_trained_model Function:
Takes the path to your initially trained model, data paths, a configuration dictionary for refinement, and the device.
Loads the Model: It loads the state dictionary from the checkpoint file. It tries to get the model configuration from the checkpoint; if not found, it uses defaults. It then initializes a new SkeletonAwareSpotDetector instance and loads the saved weights.
Prepares Data: It uses your create_skeleton_aware_data_loaders_fixed function to create new data loaders for the refinement process. This ensures consistency and applies the fixes (like pin_memory=False).
Strategy Selection:
evaluate_only: If you just want to get a detailed report or run the inspection on the current best model without changing it, this option loads the model, runs evaluate_skeleton_aware_model, and inspect_skeleton_aware_model. It returns the results.
fine_tune: This is for further training. It sets up a new directory for the refined model.
Fine-Tuning Setup (fine_tune strategy):
Creates a new directory refined_model within your main model_dir.
Allows for potential adjustments like changing the learning rate (new_lr), number of epochs (new_epochs), or batch size/worker settings.
Includes an example of how you might freeze encoder layers (useful if you think the encoder is good but heads need more work).
Sets up a new AdamW optimizer, typically with a lower learning rate than the initial training. It uses filter(lambda p: p.requires_grad, ...) to ensure only unfrozen parameters are optimized.
Sets up a new OneCycleLR scheduler tailored for the fine-tuning run.
Fine-Tuning Loop (fine_tune strategy):
Runs a simplified version of the main training loop using the new optimizer and scheduler.
Saves the best model based on validation loss during this refinement phase to best_refined_model.pth.
Plots and saves the loss curves for the refinement phase.
Final Evaluation: Regardless of the strategy, it performs a final evaluate_skeleton_aware_model and inspect_skeleton_aware_model on the resulting model (either the original loaded one or the newly fine-tuned one).
Returns: A dictionary summarizing the outcome, including the path to the final model file, metrics, and inspection results.
Example Usage:
Shows how to define paths and two example refinement_config dictionaries.
Demonstrates how to call the refine_trained_model function with either configuration.
Shows how to access the results returned by the function.
This code provides a structured way to take your trained model and either get a final, detailed analysis or perform targeted fine-tuning to potentially squeeze out a bit more performance. You can easily modify the refinement_config to experiment with different fine-tuning approaches (e.g., different LRs, freezing different parts of the network, adjusting epochs).

import numpy as np
import os
from tqdm import tqdm
from scipy.ndimage import maximum_filter
from skimage.measure import regionprops
from skimage.segmentation import watershed
import tifffile
import torch
import matplotlib.pyplot as plt

def fast_accurate_instance_segmentation(
    semantic,
    sdt_scalar,
    skeleton,
    centroid_map,
    flow,
    boundary,
    min_size=1,
    sdt_seed_threshold=0.5,
    semantic_threshold=0.01
):
    H, W = sdt_scalar.shape
    seed_mask = (sdt_scalar > sdt_seed_threshold) & (semantic > semantic_threshold)
    local_max = (sdt_scalar == maximum_filter(sdt_scalar, size=3))
    peaks = local_max & seed_mask
    marker_coords = np.column_stack(np.where(peaks))
    if len(marker_coords) == 0:
        return np.zeros_like(sdt_scalar, dtype=np.int32), []
    markers = np.zeros_like(sdt_scalar, dtype=np.int32)
    for idx, (y, x) in enumerate(marker_coords, 1):
        markers[y, x] = idx
    mask = semantic > semantic_threshold
    instances = watershed(-sdt_scalar, markers, mask=mask)
    regions = regionprops(instances)
    final_labels = np.zeros_like(instances)
    final_spots = []
    new_id = 1
    for region in regions:
        if region.area < min_size:
            continue
        mask = (instances == region.label)
        cy, cx = region.centroid
        semantic_score = np.mean(semantic[mask])
        centroid_score = np.mean(centroid_map[mask])
        sdt_score = np.mean(sdt_scalar[mask])
        boundary_score = np.mean(boundary[mask])
        combined_score = (
            0.3 * semantic_score +
            0.3 * centroid_score +
            0.2 * sdt_score +
            0.2 * (1.0 - boundary_score)
        )
        final_labels[mask] = new_id
        final_spots.append({
            'y': float(cy),
            'x': float(cx),
            'score': float(combined_score),
            'size': int(region.area),
            'semantic_score': float(semantic_score),
            'centroid_score': float(centroid_score),
            'sdt_score': float(sdt_score),
            'boundary_score': float(boundary_score)
        })
        new_id += 1
    return final_labels, final_spots

def flow_instance_segmentation(
    semantic,
    centroid,
    sdt,
    boundary,
    flow,
    min_size=1,
    semantic_threshold=0.01,
    flow_steps=10,
    flow_step_size=1.0,
    cluster_eps=3,
):
    from sklearn.cluster import DBSCAN
    H, W = semantic.shape
    binary_mask = (semantic > semantic_threshold).astype(np.uint8)
    ys, xs = np.where(binary_mask > 0)
    if len(ys) == 0:
        return np.zeros_like(semantic, dtype=np.int32), []
    coords_fg = np.stack([ys, xs], axis=1).astype(np.float32)
    flow_y, flow_x = flow[0], flow[1]
    pos = coords_fg.copy()
    for _ in range(flow_steps):
        iy = np.clip(np.round(pos[:, 0]).astype(int), 0, H-1)
        ix = np.clip(np.round(pos[:, 1]).astype(int), 0, W-1)
        pos[:, 0] += flow_y[iy, ix] * flow_step_size
        pos[:, 1] += flow_x[iy, ix] * flow_step_size
    clustering = DBSCAN(eps=cluster_eps, min_samples=1).fit(pos)
    labels = clustering.labels_ + 1
    instance_labels = np.zeros_like(semantic, dtype=np.int32)
    instance_labels[ys, xs] = labels
    regions = regionprops(instance_labels)
    filtered_spots = []
    for region in regions:
        if region.label == 0 or region.area < min_size:
            continue
        mask = (instance_labels == region.label)
        cy, cx = region.centroid
        semantic_score = np.mean(semantic[mask])
        centroid_score = np.mean(centroid[mask])
        sdt_score = np.mean(sdt[mask])
        boundary_score = np.mean(boundary[mask])
        score = (
            0.3 * semantic_score +
            0.3 * centroid_score +
            0.2 * sdt_score +
            0.2 * (1.0 - boundary_score)
        )
        filtered_spots.append({
            'y': float(cy),
            'x': float(cx),
            'score': float(score),
            'size': int(region.area),
            'semantic_score': float(semantic_score),
            'centroid_score': float(centroid_score),
            'sdt_score': float(sdt_score),
            'boundary_score': float(boundary_score)
        })
    return instance_labels, filtered_spots

def display_all_outputs(results, image_path):
    original_image = tifffile.imread(image_path).astype(np.float32)
    vmin, vmax = np.percentile(original_image[original_image > 0], [2, 98])
    original_norm = np.clip((original_image - vmin) / (vmax - vmin + 1e-8), 0, 1)
    fig, axes = plt.subplots(3, 4, figsize=(20, 15))
    fig.suptitle(f'Complete Model Analysis - {len(results["spots"])} spots detected', fontsize=16)
    axes[0,0].imshow(original_norm, cmap='gray')
    axes[0,0].set_title('Original Image')
    axes[0,0].axis('off')
    axes[0,1].imshow(results['semantic'], cmap='viridis')
    axes[0,1].set_title(f'Semantic (max: {results["semantic"].max():.3f})')
    axes[0,1].axis('off')
    axes[0,2].imshow(results['centroid'], cmap='hot')
    axes[0,2].set_title(f'Centroid (max: {results["centroid"].max():.3f})')
    axes[0,2].axis('off')
    axes[0,3].imshow(results['sdt'], cmap='plasma')
    axes[0,3].set_title(f'SDT (max: {results["sdt"].max():.3f})')
    axes[0,3].axis('off')
    axes[1,0].imshow(results['skeleton'], cmap='bone')
    axes[1,0].set_title(f'Skeleton (max: {results["skeleton"].max():.3f})')
    axes[1,0].axis('off')
    axes[1,1].imshow(results['boundary'], cmap='copper')
    axes[1,1].set_title(f'Boundary (max: {results["boundary"].max():.3f})')
    axes[1,1].axis('off')
    flow_mag = np.sqrt(results['flow'][0]**2 + results['flow'][1]**2)
    axes[1,2].imshow(flow_mag, cmap='coolwarm')
    axes[1,2].set_title(f'Flow Magnitude (max: {flow_mag.max():.3f})')
    axes[1,2].axis('off')
    axes[1,3].imshow(results['instance_labels'], cmap='nipy_spectral')
    axes[1,3].set_title(f'Instance Labels ({len(results["spots"])} spots)')
    axes[1,3].axis('off')
    axes[2,0].imshow(original_norm, cmap='gray')
    if results['spots']:
        y_coords = [s['y'] for s in results['spots']]
        x_coords = [s['x'] for s in results['spots']]
        sizes = [s['size'] for s in results['spots']]
        axes[2,0].scatter(x_coords, y_coords, c='red', s=[s*2 for s in sizes], alpha=0.7, marker='x')
    axes[2,0].set_title('Detected Spots Overlay')
    axes[2,0].axis('off')
    if results['spots']:
        sizes = [s['size'] for s in results['spots']]
        axes[2,1].hist(sizes, bins=20, alpha=0.7, color='blue')
        axes[2,1].set_title(f'Size Distribution\\nMean: {np.mean(sizes):.1f}')
        axes[2,1].set_xlabel('Spot Size (pixels)')
        axes[2,1].set_ylabel('Count')
    if results['spots']:
        scores = [s['score'] for s in results['spots']]
        axes[2,2].hist(scores, bins=20, alpha=0.7, color='green')
        axes[2,2].set_title(f'Quality Scores\\nMean: {np.mean(scores):.3f}')
        axes[2,2].set_xlabel('Quality Score')
        axes[2,2].set_ylabel('Count')
    if results['spots']:
        sizes = [s['size'] for s in results['spots']]
        small_spots = sum(1 for s in sizes if s <= 5)
        medium_spots = sum(1 for s in sizes if 5 < s <= 20)
        large_spots = sum(1 for s in sizes if s > 20)
        stats_text = f"""Detection Summary:
Total Spots: {len(results['spots'])}
Small (≤5px): {small_spots}
Medium (6-20px): {medium_spots}
Large (>20px): {large_spots}

Size Range: {min(sizes)}-{max(sizes)} px
Avg Size: {np.mean(sizes):.1f} px
Avg Score: {np.mean([s['score'] for s in results['spots']]):.3f}"""
        axes[2,3].text(0.1, 0.5, stats_text, fontsize=10, verticalalignment='center')
        axes[2,3].set_title('Statistics')
        axes[2,3].axis('off')
    plt.tight_layout()
    plt.show()
    if results['spots']:
        print(f"\\n📊 DETAILED SPOT ANALYSIS:")
        print(f"Total spots detected: {len(results['spots'])}")
        sizes = [s['size'] for s in results['spots']]
        print(f"Size range: {min(sizes)} - {max(sizes)} pixels")
        print(f"Average size: {np.mean(sizes):.1f} pixels")
        small = sum(1 for s in sizes if s <= 5)
        medium = sum(1 for s in sizes if 5 < s <= 20)
        large = sum(1 for s in sizes if s > 20)
        print(f"Small spots (≤5px): {small}")
        print(f"Medium spots (6-20px): {medium}")
        print(f"Large spots (>20px): {large}")

def ultra_fast_tile_inference(
    model,
    image_path, 
    device='cuda',
    patch_size=256,
    overlap=0.2,
    batch_size=16,
    min_size=1,
    sdt_seed_threshold=0.5,
    semantic_threshold=0.01,
    use_flow=True,
    flow_steps=10,
    flow_step_size=1.0,
    cluster_eps=3,
):
    image = tifffile.imread(image_path).astype(np.float32)
    H, W = image.shape
    vmin, vmax = np.percentile(image[image > 0], [2, 98])
    image_norm = np.clip((image - vmin) / (vmax - vmin + 1e-8), 0, 1)
    model.eval()
    model.to(device)
    stride = max(int(patch_size * (1 - overlap)), 32)
    y_coords = list(range(0, H, stride))
    x_coords = list(range(0, W, stride))
    if y_coords[-1] + patch_size < H:
        y_coords.append(H - patch_size)
    if x_coords[-1] + patch_size < W:
        x_coords.append(W - patch_size)
    total_patches = len(y_coords) * len(x_coords)
    print(f"Ultra-fast inference: {total_patches} patches, stride={stride}, batch={batch_size}")
    semantic_sum = np.empty((H, W), dtype=np.float32); semantic_sum.fill(0)
    sdt_sum = np.empty((H, W), dtype=np.float32); sdt_sum.fill(0)
    skeleton_sum = np.empty((H, W), dtype=np.float32); skeleton_sum.fill(0)
    centroid_sum = np.empty((H, W), dtype=np.float32); centroid_sum.fill(0)
    boundary_sum = np.empty((H, W), dtype=np.float32); boundary_sum.fill(0)
    flow_sum = np.empty((2, H, W), dtype=np.float32); flow_sum.fill(0)
    count_map = np.empty((H, W), dtype=np.float32); count_map.fill(0)
    with torch.no_grad():
        patches = []
        coords = []
        processed = 0
        pbar = tqdm(total=total_patches, desc="Ultra-Fast Inference")
        for y_start in y_coords:
            for x_start in x_coords:
                y_end = min(y_start + patch_size, H)
                x_end = min(x_start + patch_size, W)
                patch = image_norm[y_start:y_end, x_start:x_end]
                if patch.shape[0] < patch_size or patch.shape[1] < patch_size:
                    pad_h = patch_size - patch.shape[0]
                    pad_w = patch_size - patch.shape[1]
                    patch = np.pad(patch, ((0, pad_h), (0, pad_w)), mode='constant')
                patches.append(patch)
                coords.append((y_start, y_end, x_start, x_end))
                if len(patches) >= batch_size or processed + len(patches) == total_patches:
                    batch_tensor = torch.from_numpy(np.stack(patches)[:, None]).float().to(device)
                    with torch.amp.autocast(device_type='cuda', enabled=True):
                        outputs = model(batch_tensor)
                    for i, (y_s, y_e, x_s, x_e) in enumerate(coords):
                        h, w = y_e - y_s, x_e - x_s
                        sem = torch.sigmoid(outputs['semantic'][i, 0, :h, :w]).cpu().numpy()
                        sdt = torch.sigmoid(outputs['sdt'][i, 0, :h, :w]).cpu().numpy()
                        skel = torch.sigmoid(outputs['skeleton'][i, 0, :h, :w]).cpu().numpy()
                        cent = torch.sigmoid(outputs['centroid'][i, 0, :h, :w]).cpu().numpy()
                        bound = torch.sigmoid(outputs['boundary'][i, 0, :h, :w]).cpu().numpy()
                        flow = torch.tanh(outputs['flow'][i, :, :h, :w]).cpu().numpy()
                        semantic_sum[y_s:y_e, x_s:x_e] += sem
                        sdt_sum[y_s:y_e, x_s:x_e] += sdt
                        skeleton_sum[y_s:y_e, x_s:x_e] += skel
                        centroid_sum[y_s:y_e, x_s:x_e] += cent
                        boundary_sum[y_s:y_e, x_s:x_e] += bound
                        flow_sum[:, y_s:y_e, x_s:x_e] += flow
                        count_map[y_s:y_e, x_s:x_e] += 1
                    processed += len(patches)
                    pbar.update(len(patches))
                    patches.clear()
                    coords.clear()
        pbar.close()
    count_map_safe = np.maximum(count_map, 1)
    semantic_out = semantic_sum / count_map_safe
    sdt_out = sdt_sum / count_map_safe
    skeleton_out = skeleton_sum / count_map_safe
    centroid_out = centroid_sum / count_map_safe
    boundary_out = boundary_sum / count_map_safe
    flow_out = flow_sum / count_map_safe[None, :, :]
    print(f"Fast inference stats: sem[{semantic_out.min():.2f},{semantic_out.max():.2f}], "
          f"cent[{centroid_out.min():.2f},{centroid_out.max():.2f}]")
    if use_flow:
        instance_labels, filtered_spots = flow_instance_segmentation(
            semantic=semantic_out,
            centroid=centroid_out,
            sdt=sdt_out,
            boundary=boundary_out,
            flow=flow_out,
            min_size=min_size,
            semantic_threshold=semantic_threshold,
            flow_steps=flow_steps,
            flow_step_size=flow_step_size,
            cluster_eps=cluster_eps
        )
    else:
        instance_labels, filtered_spots = fast_accurate_instance_segmentation(
            semantic=semantic_out,
            sdt_scalar=sdt_out,
            skeleton=skeleton_out,
            centroid_map=centroid_out,
            flow=flow_out,
            boundary=boundary_out,
            min_size=min_size,
            sdt_seed_threshold=sdt_seed_threshold,
            semantic_threshold=semantic_threshold
        )
    results = {
        'instance_labels': instance_labels,
        'spots': filtered_spots,
        'semantic': semantic_out,
        'sdt': sdt_out,
        'skeleton': skeleton_out,
        'centroid': centroid_out,
        'boundary': boundary_out,
        'flow': flow_out
    }
    display_all_outputs(results, image_path)
    print(f"⚡ Ultra-fast inference: Found {len(filtered_spots)} spots in {os.path.basename(image_path)}")
    return results

if __name__ == "__main__":
    import os
    from pathlib import Path
    import time
    model_path = '/mnt/d/Users/<USER>/Documents/spot_detector_model_sota_08072025_afternoon/best_model.pth'
    image_path = '/mnt/d/Users/<USER>/FISH_spots/2d/synthetic/images/5  Series006  Green--FLUO--FITC_tile_4.tif'
    save_dir = Path(image_path).parent.parent / "inference_results"
    save_dir.mkdir(exist_ok=True)
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"Device: {device}")
    checkpoint = torch.load(model_path, map_location=device, weights_only=False)
    model = SkeletonAwareSpotDetector(in_ch=1, base_ch=48)
    model.load_state_dict(checkpoint['model_state_dict'])
    model.to(device)
    start_time = time.time()
    results = ultra_fast_tile_inference(
        model=model,
        image_path=image_path,
        device=device,
        patch_size=256,
        overlap=0.2,
        batch_size=16,
        use_flow=True
    )
    inference_time = time.time() - start_time
    print(f"⚡ ULTRA-FAST RESULTS (SDT):")
    print(f"   Total time: {inference_time:.2f}s")
    print(f"   Found {len(results['spots'])} spots")
    print(f"   Spots per second: {len(results['spots'])/inference_time:.1f}")
    print(f"   Avg spot quality: {np.mean([s['score'] for s in results['spots']]):.3f}")
    base_name = Path(image_path).stem
    tifffile.imwrite(save_dir / f"{base_name}_fast_labels.tif", results['instance_labels'].astype(np.uint16))
    np.save(save_dir / f"{base_name}_fast_spots.npy", results['spots'])
    print(f"💾 Fast results saved to: {save_dir}")
    start_time = time.time()
    results_flow = ultra_fast_tile_inference(
        model=model,
        image_path=image_path,
        device=device,
        patch_size=256,
        overlap=0.2,
        batch_size=16,
        use_flow=False,
        flow_steps=10,
        flow_step_size=1.0,
        cluster_eps=3
    )
    inference_time = time.time() - start_time
    print(f"⚡ ULTRA-FAST RESULTS (FLOW):")
    print(f"   Total time: {inference_time:.2f}s")
    print(f"   Found {len(results_flow['spots'])} spots")
    print(f"   Spots per second: {len(results_flow['spots'])/inference_time:.1f}")
    print(f"   Avg spot quality: {np.mean([s['score'] for s in results_flow['spots']]):.3f}")
    tifffile.imwrite(save_dir / f"{base_name}_flow_labels.tif", results_flow['instance_labels'].astype(np.uint16))
    np.save(save_dir / f"{base_name}_flow_spots.npy", results_flow['spots'])
    print(f"💾 Flow results saved to: {save_dir}")



import numpy as np
import os
from tqdm import tqdm
from scipy.ndimage import maximum_filter, label as ndi_label
from skimage.measure import regionprops
from skimage.segmentation import watershed
from skimage.morphology import remove_small_objects, local_maxima
import tifffile
import torch
import torch.nn.functional as F
import matplotlib.pyplot as plt

# -------------------- Improved instance segmentation -------------------- #
def fast_accurate_instance_segmentation(
    semantic,
    sdt_scalar,
    skeleton,
    centroid_map,
    flow,
    boundary,
    min_size=1,
    sdt_seed_threshold=0.5,
    centroid_seed_threshold=0.2,
    semantic_threshold=0.01,
    min_distance=1
):
    """
    Create an instance segmentation from model maps.
    Robust seed finding:
      - primary seeds: local maxima of sdt_scalar above sdt_seed_threshold inside semantic mask
      - fallback seeds: local maxima of centroid_map above centroid_seed_threshold inside semantic mask
    Uses scipy watershed on -sdt_scalar with labeled marker mask.
    Returns (labels, spots_list).
    """
    H, W = sdt_scalar.shape
    mask = (semantic > semantic_threshold)

    # --- Primary seeds: local maxima of SDT (robust to floating noise) ---
    sdt_local_max = local_maxima(sdt_scalar)  # boolean mask of local maxima
    seed_mask = sdt_local_max & (sdt_scalar > sdt_seed_threshold) & mask

    # --- Fallback: centroid local maxima (some models encode sharper peaks here) ---
    if seed_mask.sum() == 0:
        cent_local_max = local_maxima(centroid_map)
        seed_mask = cent_local_max & (centroid_map > centroid_seed_threshold) & mask

    # Still none? relax thresholds (last resort) - create seeds at any local maxima inside mask
    if seed_mask.sum() == 0:
        sdt_local_any = local_maxima(sdt_scalar)
        seed_mask = sdt_local_any & mask

    # If still empty, return no instances
    if seed_mask.sum() == 0:
        return np.zeros((H, W), dtype=np.int32), []

    # Label each connected seed blob as a unique marker
    markers, n_markers = ndi_label(seed_mask)
    if n_markers == 0:
        return np.zeros((H, W), dtype=np.int32), []

    # Watershed using negative SDT (so SDT maxima become catchment basins)
    instances = watershed(-sdt_scalar, markers, mask=mask)

    # Post-process: remove small objects and create spot metadata
    if min_size > 1:
        instances = remove_small_objects(instances.astype(np.int32), min_size=min_size)

    regions = regionprops(instances)
    final_labels = np.zeros_like(instances, dtype=np.int32)
    final_spots = []
    new_id = 1
    for region in regions:
        if region.area < min_size:
            continue
        mask_r = (instances == region.label)
        cy, cx = region.centroid
        semantic_score = float(np.mean(semantic[mask_r]))
        centroid_score = float(np.mean(centroid_map[mask_r]))
        sdt_score = float(np.mean(sdt_scalar[mask_r]))
        boundary_score = float(np.mean(boundary[mask_r]))
        combined_score = (
            0.3 * semantic_score +
            0.3 * centroid_score +
            0.2 * sdt_score +
            0.2 * (1.0 - boundary_score)
        )
        final_labels[mask_r] = new_id
        final_spots.append({
            'y': float(cy),
            'x': float(cx),
            'score': float(combined_score),
            'size': int(region.area),
            'semantic_score': semantic_score,
            'centroid_score': centroid_score,
            'sdt_score': sdt_score,
            'boundary_score': boundary_score
        })
        new_id += 1

    return final_labels, final_spots


# -------------------- Ultra-fast tiled inference (max-aggregation for peaks) -------------------- #
def ultra_fast_tile_inference(
    model,
    image_path,
    device='cuda',
    patch_size=256,
    overlap=0.2,
    batch_size=16,
    use_max_aggregation_for=('sdt', 'centroid')  # options: 'sdt', 'centroid', others will be averaged
):
    """
    Tiled inference that:
      - averages semantic, boundary, flow (by default)
      - uses max-aggregation for sdt and centroid to preserve sharp peaks across tile boundaries
    """
    image = tifffile.imread(image_path).astype(np.float32)
    H, W = image.shape

    # Fast normalization
    vmin, vmax = np.percentile(image, (0.5, 99.5))
    image_norm = np.clip((image - vmin) / (vmax - vmin + 1e-8), 0, 1)
    
    model.eval()
    model.to(device)

    stride = max(int(patch_size * (1 - overlap)), 32)
    y_coords = list(range(0, H, stride))
    x_coords = list(range(0, W, stride))
    if y_coords[-1] + patch_size < H:
        y_coords.append(H - patch_size)
    if x_coords[-1] + patch_size < W:
        x_coords.append(W - patch_size)
    total_patches = len(y_coords) * len(x_coords)
    print(f"Ultra-fast inference: {total_patches} patches, stride={stride}, batch={batch_size}")

    # pre-allocate accumulation arrays
    semantic_sum = np.zeros((H, W), dtype=np.float32)
    sdt_max = np.full((H, W), -np.inf, dtype=np.float32)
    centroid_max = np.full((H, W), -np.inf, dtype=np.float32)
    skeleton_sum = np.zeros((H, W), dtype=np.float32)
    boundary_sum = np.zeros((H, W), dtype=np.float32)
    flow_sum = np.zeros((2, H, W), dtype=np.float32)
    count_map = np.zeros((H, W), dtype=np.float32)

    pbar = tqdm(total=total_patches, desc="Ultra-Fast Inference")
    patches, coords = [], []
    processed = 0
    with torch.no_grad():
        for y_start in y_coords:
            for x_start in x_coords:
                y_end = min(y_start + patch_size, H)
                x_end = min(x_start + patch_size, W)
                patch = image_norm[y_start:y_end, x_start:x_end]
                if patch.shape[0] < patch_size or patch.shape[1] < patch_size:
                    pad_h = patch_size - patch.shape[0]
                    pad_w = patch_size - patch.shape[1]
                    patch = np.pad(patch, ((0, pad_h), (0, pad_w)), mode='constant')

                patches.append(patch)
                coords.append((y_start, y_end, x_start, x_end))

                # batch inference
                if len(patches) >= batch_size or processed + len(patches) == total_patches:
                    batch_tensor = torch.from_numpy(np.stack(patches)[:, None]).float().to(device)
                    with torch.amp.autocast(device_type='cuda', enabled=(device == 'cuda')):
                        outputs = model(batch_tensor)

                    for i, (y_s, y_e, x_s, x_e) in enumerate(coords):
                        h, w = y_e - y_s, x_e - x_s

                        sem = torch.sigmoid(outputs['semantic'][i, 0, :h, :w]).cpu().numpy()
                        sdt = torch.sigmoid(outputs['sdt'][i, 0, :h, :w]).cpu().numpy()
                        skel = torch.sigmoid(outputs['skeleton'][i, 0, :h, :w]).cpu().numpy()
                        cent = torch.sigmoid(outputs['centroid'][i, 0, :h, :w]).cpu().numpy()
                        bound = torch.sigmoid(outputs['boundary'][i, 0, :h, :w]).cpu().numpy()
                        flow = torch.tanh(outputs['flow'][i, :, :h, :w]).cpu().numpy()

                        semantic_sum[y_s:y_e, x_s:x_e] += sem
                        skeleton_sum[y_s:y_e, x_s:x_e] += skel
                        boundary_sum[y_s:y_e, x_s:x_e] += bound
                        flow_sum[:, y_s:y_e, x_s:x_e] += flow
                        count_map[y_s:y_e, x_s:x_e] += 1

                        # max-aggregation for sdt and centroid (preserves sharp peaks)
                        sdt_slice = sdt_max[y_s:y_e, x_s:x_e]
                        sdt_max[y_s:y_e, x_s:x_e] = np.maximum(sdt_slice, sdt)

                        cent_slice = centroid_max[y_s:y_e, x_s:x_e]
                        centroid_max[y_s:y_e, x_s:x_e] = np.maximum(cent_slice, cent)

                    processed += len(patches)
                    pbar.update(len(patches))
                    patches.clear()
                    coords.clear()
    pbar.close()

    # finalize outputs
    count_map_safe = np.maximum(count_map, 1.0)
    semantic_out = semantic_sum / count_map_safe
    skeleton_out = skeleton_sum / count_map_safe
    boundary_out = boundary_sum / count_map_safe
    flow_out = flow_sum / count_map_safe[None, :, :]

    # Replace -inf with 0 for max maps (if some pixels never got any patch)
    sdt_out = sdt_max
    sdt_out[np.isneginf(sdt_out)] = 0.0
    centroid_out = centroid_max
    centroid_out[np.isneginf(centroid_out)] = 0.0

    # Instance segmentation (use lower thresholds if you want everything)
    instance_labels, filtered_spots = fast_accurate_instance_segmentation(
        semantic=semantic_out,
        sdt_scalar=sdt_out,
        skeleton=skeleton_out,
        centroid_map=centroid_out,
        flow=flow_out,
        boundary=boundary_out,
        min_size=1,
        sdt_seed_threshold=0.01,       # <-- tuned lower because we used max-aggregation
        centroid_seed_threshold=0.05,  # <-- fallback threshold
        semantic_threshold=0.01
    )

    results = {
        'instance_labels': instance_labels,
        'spots': filtered_spots,
        'semantic': semantic_out,
        'sdt': sdt_out,
        'skeleton': skeleton_out,
        'centroid': centroid_out,
        'boundary': boundary_out,
        'flow': flow_out
    }

    # optional visualization call (keeps your existing display function)
    display_all_outputs(results, image_path)

    print(f"⚡ Ultra-fast inference: Found {len(filtered_spots)} spots in {os.path.basename(image_path)}")
    return results

# ULTRA-FAST Usage Example
if __name__ == "__main__":
    import os
    from pathlib import Path
    import time
    
    # Configuration
    model_path = '/mnt/d/Users/<USER>/Documents/spot_detector_model_sota_08072025_afternoon/best_model.pth'
    image_path = '/mnt/d/Users/<USER>/FISH_spots/2d/image/deepblink_12.tif'
    save_dir = Path(image_path).parent.parent / "inference_results"
    save_dir.mkdir(exist_ok=True)
    
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"Device: {device}")
    
    # Load model
    checkpoint = torch.load(model_path, map_location=device, weights_only=False)
    model = SkeletonAwareSpotDetector(in_ch=1, base_ch=48)
    model.load_state_dict(checkpoint['model_state_dict'])
    model.to(device)
    
    # Benchmark run
    start_time = time.time()
    results = ultra_fast_tile_inference(
        model=model,
        image_path=image_path,
        device=device,
        patch_size=256,
        overlap=0.2,   # Optimized overlap
        batch_size=16  # Max batch size
    )
    inference_time = time.time() - start_time
    
    # Results
    print(f"⚡ ULTRA-FAST RESULTS:")
    print(f"   Total time: {inference_time:.2f}s")
    print(f"   Found {len(results['spots'])} spots")
    print(f"   Spots per second: {len(results['spots'])/inference_time:.1f}")
    print(f"   Avg spot quality: {np.mean([s['score'] for s in results['spots']]):.3f}")
    
    # Save results
    base_name = Path(image_path).stem
    tifffile.imwrite(save_dir / f"{base_name}_fast_labels.tif", results['instance_labels'].astype(np.uint16))
    np.save(save_dir / f"{base_name}_fast_spots.npy", results['spots'])
    
    print(f"💾 Fast results saved to: {save_dir}")


### FASTER - fixed dtype/autocast + batch runner
import os
import time
import glob
import contextlib
from pathlib import Path
from typing import Dict, Tuple, List

import numpy as np
import tifffile
import torch
import torch.nn.functional as F
from tqdm import tqdm

# CPU-side libs for final instance segmentation (used once)
from scipy.ndimage import label as ndi_label
from skimage.measure import regionprops
from skimage.segmentation import watershed
from skimage.morphology import remove_small_objects, local_maxima as sk_local_maxima  # fallback if needed


# -------------------- Utilities (GPU-friendly) -------------------- #
def normalize_image_np(img: np.ndarray, low_pct=0.5, high_pct=99.5) -> np.ndarray:
    img = img.astype(np.float32, copy=False)
    vmin, vmax = np.percentile(img, (low_pct, high_pct))
    return np.clip((img - vmin) / (vmax - vmin + 1e-8), 0.0, 1.0).astype(np.float32)


def fast_local_maxima_torch(x: torch.Tensor, min_distance: int = 1) -> torch.BoolTensor:
    """
    Return boolean mask of local maxima using max-pooling trick on GPU.
    x: 2D tensor (H, W) or (1, 1, H, W)
    """
    if x.ndim == 2:
        x = x.unsqueeze(0).unsqueeze(0)  # [1,1,H,W]
        squeeze_out = True
    elif x.ndim == 3:
        x = x.unsqueeze(0)  # [1,C,H,W]
        squeeze_out = False
    else:
        squeeze_out = False

    kernel = 2 * min_distance + 1
    pooled = F.max_pool2d(x, kernel_size=kernel, stride=1, padding=min_distance)
    maxima = (x == pooled)
    if squeeze_out:
        return maxima.squeeze(0).squeeze(0)
    return maxima.squeeze(0)


# -------------------- Instance segmentation (CPU, single transfer) -------------------- #
def cpu_postprocess_instances(
    semantic: np.ndarray,
    sdt: np.ndarray,
    skeleton: np.ndarray,
    centroid: np.ndarray,
    boundary: np.ndarray,
    min_size: int = 1,
    sdt_seed_threshold: float = 0.01,
    centroid_seed_threshold: float = 0.05,
    semantic_threshold: float = 0.01,
    min_distance: int = 1
) -> Tuple[np.ndarray, list]:
    """
    Performs seed finding (CPU fallback via skimage if needed), watershed, and regionprops scoring.
    This function runs once on CPU after GPU aggregation is finished.
    """
    H, W = semantic.shape
    mask = semantic > semantic_threshold

    # Try a robust seed detection: use SDT local maxima first (fast skimage fallback)
    try:
        sdt_local = sk_local_maxima(sdt)
    except Exception:
        from scipy.ndimage import maximum_filter
        sdt_local = (sdt == maximum_filter(sdt, size=3))

    seed_mask = sdt_local & (sdt > sdt_seed_threshold) & mask

    # fallback to centroid maxima
    if seed_mask.sum() == 0:
        try:
            cent_local = sk_local_maxima(centroid)
        except Exception:
            from scipy.ndimage import maximum_filter
            cent_local = (centroid == maximum_filter(centroid, size=3))
        seed_mask = cent_local & (centroid > centroid_seed_threshold) & mask

    if seed_mask.sum() == 0:
        # last resort: any local maxima in semantic inside mask
        try:
            sem_local = sk_local_maxima(semantic)
        except Exception:
            from scipy.ndimage import maximum_filter
            sem_local = (semantic == maximum_filter(semantic, size=3))
        seed_mask = sem_local & mask

    if seed_mask.sum() == 0:
        return np.zeros((H, W), dtype=np.int32), []

    # markers
    markers, n_markers = ndi_label(seed_mask.astype(np.uint8))
    if n_markers == 0:
        return np.zeros((H, W), dtype=np.int32), []

    instances = watershed(-sdt, markers, mask=mask)

    # remove small objects & relabel
    if min_size > 1:
        instances = remove_small_objects(instances.astype(np.int32), min_size=min_size)
    regions = regionprops(instances)
    final_labels = np.zeros_like(instances, dtype=np.int32)
    final_spots = []
    new_id = 1
    for region in regions:
        if region.area < min_size:
            continue
        mask_r = (instances == region.label)
        cy, cx = region.centroid
        # compute scores
        semantic_score = float(np.mean(semantic[mask_r]))
        centroid_score = float(np.mean(centroid[mask_r]))
        sdt_score = float(np.mean(sdt[mask_r]))
        boundary_score = float(np.mean(boundary[mask_r]))
        combined_score = (
            0.3 * semantic_score +
            0.3 * centroid_score +
            0.2 * sdt_score +
            0.2 * (1.0 - boundary_score)
        )

        final_labels[mask_r] = new_id
        final_spots.append({
            'y': float(cy),
            'x': float(cx),
            'score': float(combined_score),
            'size': int(region.area),
            'semantic_score': semantic_score,
            'centroid_score': centroid_score,
            'sdt_score': sdt_score,
            'boundary_score': boundary_score
        })
        new_id += 1

    return final_labels, final_spots


# -------------------- Main optimized tiled inference -------------------- #
def ultra_fast_tile_inference_gpu(
    model,
    image_path: str,
    device: str = 'cuda',
    patch_size: int = 256,
    overlap: float = 0.2,
    batch_size: int = 16,
    use_amp: bool = True,
    min_size: int = 1,
    sdt_seed_threshold: float = 0.01,
    centroid_seed_threshold: float = 0.01,
    semantic_threshold: float = 0.01,
    min_distance_seed: int = 1
) -> Dict:
    """
    GPU-first tiled inference:
     - keeps aggregation on GPU (torch tensors)
     - uses max-aggregation for sdt & centroid (torch.maximum)
     - moves to CPU only once for final instance segmentation
    """
    assert device in ('cuda', 'cpu')
    device_t = torch.device(device)

    # --- load & normalize ---
    image = tifffile.imread(image_path).astype(np.float32)
    H, W = image.shape
    image_norm = normalize_image_np(image)  # guaranteed float32

    # push normalized image to GPU as single tensor to allow slicing from GPU if memory fits
    bytes_needed = image_norm.nbytes
    image_torch_full = None
    if device_t.type == 'cuda' and torch.cuda.is_available():
        try:
            free_mem, total_mem = torch.cuda.mem_get_info()
            # require some margin (60%) to be safe
            mem_ok = bytes_needed < int(free_mem * 0.6)
        except Exception:
            mem_ok = False
        if mem_ok:
            image_torch_full = torch.from_numpy(image_norm).to(device_t, dtype=torch.float32)

    stride = max(int(patch_size * (1 - overlap)), 32)
    y_coords = list(range(0, H, stride))
    x_coords = list(range(0, W, stride))
    if y_coords[-1] + patch_size < H:
        y_coords.append(H - patch_size)
    if x_coords[-1] + patch_size < W:
        x_coords.append(W - patch_size)
    total_patches = len(y_coords) * len(x_coords)

    # --- allocate aggregation tensors on device ---
    semantic_sum = torch.zeros((H, W), dtype=torch.float32, device=device_t)
    sdt_max = torch.full((H, W), -float('inf'), dtype=torch.float32, device=device_t)
    centroid_max = torch.full((H, W), -float('inf'), dtype=torch.float32, device=device_t)
    skeleton_sum = torch.zeros((H, W), dtype=torch.float32, device=device_t)
    boundary_sum = torch.zeros((H, W), dtype=torch.float32, device=device_t)
    flow_sum = torch.zeros((2, H, W), dtype=torch.float32, device=device_t)
    count_map = torch.zeros((H, W), dtype=torch.float32, device=device_t)

    model.eval()
    model.to(device_t)

    pbar = tqdm(total=total_patches, desc="Ultra-Fast Inference (GPU agg)")
    patches: List[torch.Tensor] = []
    coords: List[Tuple[int, int, int, int]] = []
    processed = 0

    # modern autocast context (cuda) or nullcontext for CPU
    from torch import amp
    autocast_ctx = (lambda: amp.autocast("cuda")) if (use_amp and device_t.type == 'cuda') else contextlib.nullcontext

    with torch.no_grad():
        with autocast_ctx():
            for y_start in y_coords:
                for x_start in x_coords:
                    y_end = min(y_start + patch_size, H)
                    x_end = min(x_start + patch_size, W)
                    h, w = y_end - y_start, x_end - x_start

                    if image_torch_full is not None:
                        patch_t = image_torch_full[y_start:y_end, x_start:x_end]
                    else:
                        # fallback: copy from numpy and push to device (ensure float32)
                        patch_np = image_norm[y_start:y_end, x_start:x_end]
                        patch_t = torch.from_numpy(patch_np).float().to(device_t)

                    # pad to patch_size if needed (patch_t on device)
                    if (h != patch_size) or (w != patch_size):
                        pad_h = patch_size - h
                        pad_w = patch_size - w
                        # F.pad expects (left, right, top, bottom)
                        patch_t = F.pad(patch_t, (0, pad_w, 0, pad_h), mode='constant', value=0.0)

                    # Ensure float32 (covers cases where image_torch_full might be float16)
                    if patch_t.dtype != torch.float32:
                        patch_t = patch_t.to(dtype=torch.float32)

                    patches.append(patch_t)
                    coords.append((y_start, y_end, x_start, x_end))

                    # run batch
                    if len(patches) >= batch_size or (processed + len(patches) == total_patches):
                        # stack on device (already on device) and add channel dim
                        batch_tensor = torch.stack(patches, dim=0).unsqueeze(1).to(device_t, dtype=torch.float32)  # [B,1,H,W]

                        outputs = model(batch_tensor)  # expected dict of tensors on device
                        # Expected keys and shapes: 'semantic','sdt','skeleton','centroid','boundary','flow'
                        # Force float32 & device for safety
                        sem_b = torch.sigmoid(outputs['semantic']).to(device_t, dtype=torch.float32)
                        sdt_b = torch.sigmoid(outputs['sdt']).to(device_t, dtype=torch.float32)
                        skel_b = torch.sigmoid(outputs['skeleton']).to(device_t, dtype=torch.float32)
                        cent_b = torch.sigmoid(outputs['centroid']).to(device_t, dtype=torch.float32)
                        bound_b = torch.sigmoid(outputs['boundary']).to(device_t, dtype=torch.float32)
                        flow_b = torch.tanh(outputs['flow']).to(device_t, dtype=torch.float32)  # [B,2,H,W]

                        # apply patches to aggregate tensors
                        for i, (y_s, y_e, x_s, x_e) in enumerate(coords):
                            h_orig, w_orig = y_e - y_s, x_e - x_s

                            # crop to original patch size (in case of padding)
                            sem_patch = sem_b[i, 0, :h_orig, :w_orig]
                            sdt_patch = sdt_b[i, 0, :h_orig, :w_orig]
                            skel_patch = skel_b[i, 0, :h_orig, :w_orig]
                            cent_patch = cent_b[i, 0, :h_orig, :w_orig]
                            bound_patch = bound_b[i, 0, :h_orig, :w_orig]
                            flow_patch = flow_b[i, : , :h_orig, :w_orig]

                            # sum-aggregation
                            semantic_sum[y_s:y_e, x_s:x_e] += sem_patch
                            skeleton_sum[y_s:y_e, x_s:x_e] += skel_patch
                            boundary_sum[y_s:y_e, x_s:x_e] += bound_patch
                            flow_sum[:, y_s:y_e, x_s:x_e] += flow_patch
                            count_map[y_s:y_e, x_s:x_e] += 1.0

                            # max-aggregation (GPU)
                            sdt_max[y_s:y_e, x_s:x_e] = torch.maximum(sdt_max[y_s:y_e, x_s:x_e], sdt_patch)
                            centroid_max[y_s:y_e, x_s:x_e] = torch.maximum(centroid_max[y_s:y_e, x_s:x_e], cent_patch)

                        processed += len(patches)
                        pbar.update(len(patches))
                        patches.clear()
                        coords.clear()

    pbar.close()

    # --- finalize outputs (move to CPU only once) ---
    count_safe = torch.clamp(count_map, min=1.0)
    semantic_out = (semantic_sum / count_safe).cpu().numpy()
    skeleton_out = (skeleton_sum / count_safe).cpu().numpy()
    boundary_out = (boundary_sum / count_safe).cpu().numpy()
    flow_out = (flow_sum / count_safe.unsqueeze(0)).cpu().numpy()
    sdt_out = sdt_max.cpu().numpy()
    centroid_out = centroid_max.cpu().numpy()

    # replace -inf (unvisited positions) with 0
    sdt_out[np.isneginf(sdt_out)] = 0.0
    centroid_out[np.isneginf(centroid_out)] = 0.0

    # Instance segmentation on CPU (single transfer)
    instance_labels, filtered_spots = cpu_postprocess_instances(
        semantic=semantic_out,
        sdt=sdt_out,
        skeleton=skeleton_out,
        centroid=centroid_out,
        boundary=boundary_out,
        min_size=min_size,
        sdt_seed_threshold=sdt_seed_threshold,
        centroid_seed_threshold=centroid_seed_threshold,
        semantic_threshold=semantic_threshold,
        min_distance=min_distance_seed
    )

    results = {
        'instance_labels': instance_labels,
        'spots': filtered_spots,
        'semantic': semantic_out,
        'sdt': sdt_out,
        'skeleton': skeleton_out,
        'centroid': centroid_out,
        'boundary': boundary_out,
        'flow': flow_out
    }

    return results


# -------------------- Batch folder runner -------------------- #
def batch_segment_folder(
    input_folder: str,
    output_folder: str,
    model,
    device: str = None,
    patch_size: int = 256,
    overlap: float = 0.2,
    batch_size: int = 16,
    use_amp: bool = True,
    min_size: int = 1,
    save_maps: bool = True
) -> None:
    """
    Run ultra_fast_tile_inference_gpu over all .tif images in input_folder and save results to output_folder.

    Saved files per image:
      - <basename>_labels.tif       (instance labels as uint16)
      - <basename>_spots.npy        (list of spot dicts)
      - optional: semantic/sdt/... npy files if save_maps=True
    """
    if device is None:
        device = "cuda" if torch.cuda.is_available() else "cpu"
    model.to(device)
    model.eval()

    os.makedirs(output_folder, exist_ok=True)
    image_paths = sorted(glob.glob(os.path.join(input_folder, "*.tif")))
    if not image_paths:
        print("No .tif images found in", input_folder)
        return

    for img_path in tqdm(image_paths, desc="Batch inference"):
        base = Path(img_path).stem
        out_base = Path(output_folder) / base
        out_base.parent.mkdir(parents=True, exist_ok=True)

        try:
            results = ultra_fast_tile_inference_gpu(
                model=model,
                image_path=img_path,
                device=device,
                patch_size=patch_size,
                overlap=overlap,
                batch_size=batch_size,
                use_amp=use_amp,
                min_size=min_size
            )
        except Exception as e:
            print(f"ERROR processing {img_path}: {e}")
            continue

        # save labels
        labels = results['instance_labels'].astype(np.uint16)
        tifffile.imwrite(str(out_base) + "_labels.tif", labels)

        # save spots
        np.save(str(out_base) + "_spots.npy", results['spots'])

        # Save outputs as TIFF if output_base given and save_maps=True
        if save_maps and output_base is not None:
            tifffile.imwrite(output_base + "_labels.tif", instance_labels.astype(np.uint16))
            tifffile.imwrite(output_base + "_semantic.tif", (semantic_out * 65535).astype(np.uint16))
            tifffile.imwrite(output_base + "_sdt.tif", (sdt_out * 65535).astype(np.uint16))
            tifffile.imwrite(output_base + "_skeleton.tif", (skeleton_out * 65535).astype(np.uint16))
            tifffile.imwrite(output_base + "_centroid.tif", (centroid_out * 65535).astype(np.uint16))
            tifffile.imwrite(output_base + "_boundary.tif", (boundary_out * 65535).astype(np.uint16))
            # flow is 2 channels, save as multi-page TIFF
            flow_scaled = ((flow_out + 1) / 2 * 65535).astype(np.uint16)  # scale from [-1,1] to [0,65535]
            tifffile.imwrite(output_base + "_flow.tif", flow_scaled)

    print("Batch done. Results in:", output_folder)


# -------------------- Example usage -------------------- #
if __name__ == "__main__":
    # adapt these paths
    model_path = '/mnt/d/Users/<USER>/Documents/spot_detector_model_sota_08072025_afternoon/best_model.pth'
    image_path = '/mnt/d/Users/<USER>/FISH_spots/2d/image/deepblink_12.tif'
    input_folder = '/mnt/d/Users/<USER>/FISH_spots/2d/test_batch'
    output_folder = '/mnt/d/Users/<USER>/FISH_spots/2d/test_batch_inference'
    device = 'cuda' if torch.cuda.is_available() else 'cpu'

    # load checkpoint & model (your class name)
    checkpoint = torch.load(model_path, map_location=device,weights_only=False)
    model = SkeletonAwareSpotDetector(in_ch=1, base_ch=48)
    model.load_state_dict(checkpoint['model_state_dict'])
    model.to(device)

    # Single image test
    t0 = time.time()
    results = ultra_fast_tile_inference_gpu(
        model=model,
        image_path=image_path,
        device=device,
        patch_size=256,
        overlap=0.2,
        batch_size=16,
        use_amp=True
    )
    t1 = time.time()
    print(f"Inference time: {t1 - t0:.2f}s, spots: {len(results['spots'])}")

    # Batch run example
    batch_segment_folder(input_folder, output_folder, model, device=device, batch_size=16, save_maps=False)


import os
import glob
import time
from pathlib import Path
from typing import Dict, Union
import numpy as np
import tifffile
import torch
from tqdm import tqdm


# ---------- Save utility (optimized for minimal I/O overhead) ---------- #
def save_results_maps(results: Dict, out_base: Path, save_maps: bool = True):
    """
    Save instance labels, spots, and optionally all maps from results dict.
    """
    # Labels
    tifffile.imwrite(f"{out_base}_labels.tif", results['instance_labels'].astype(np.uint16))
    np.save(f"{out_base}_spots.npy", results['spots'])

    if save_maps:
        to_uint16 = lambda arr: (arr * 65535).astype(np.uint16)
        tifffile.imwrite(f"{out_base}_semantic.tif", to_uint16(results['semantic']))
        tifffile.imwrite(f"{out_base}_sdt.tif", to_uint16(results['sdt']))
        tifffile.imwrite(f"{out_base}_skeleton.tif", to_uint16(results['skeleton']))
        tifffile.imwrite(f"{out_base}_centroid.tif", to_uint16(results['centroid']))
        tifffile.imwrite(f"{out_base}_boundary.tif", to_uint16(results['boundary']))
        # Flow: scale from [-1, 1] to [0, 65535]
        flow_scaled = ((results['flow'] + 1) / 2 * 65535).astype(np.uint16)
        tifffile.imwrite(f"{out_base}_flow.tif", flow_scaled)


# ---------- Unified Runner (single or batch) ---------- #
def segment_images(
    input_path: Union[str, Path],
    output_folder: Union[str, Path],
    model,
    device: str = None,
    patch_size: int = 256,
    overlap: float = 0.2,
    batch_size: int = 16,
    use_amp: bool = True,
    min_size: int = 1,
    save_maps: bool = True
):
    """
    Unified high-speed runner for both single image and folder of images.
    Automatically detects input type and runs GPU-optimized inference.
    """
    t_start = time.time()
    if device is None:
        device = "cuda" if torch.cuda.is_available() else "cpu"
    model.to(device).eval()

    input_path = Path(input_path)
    output_folder = Path(output_folder)
    os.makedirs(output_folder, exist_ok=True)

    # Detect if input is file or folder
    if input_path.is_file():
        image_paths = [input_path]
    else:
        image_paths = sorted(glob.glob(str(input_path / "*.tif")))
        if not image_paths:
            print(f"No .tif images found in {input_path}")
            return

    pbar = tqdm(image_paths, desc="Segmenting", unit="image")
    for img_path in pbar:
        img_path = Path(img_path)
        out_base = output_folder / img_path.stem

        try:
            results = ultra_fast_tile_inference_gpu(
                model=model,
                image_path=str(img_path),
                device=device,
                patch_size=patch_size,
                overlap=overlap,
                batch_size=batch_size,
                use_amp=use_amp,
                min_size=min_size
            )
        except Exception as e:
            print(f"[ERROR] Failed on {img_path}: {e}")
            continue

        save_results_maps(results, out_base, save_maps=save_maps)

    t_total = time.time() - t_start
    print(f"✅ Finished {len(image_paths)} image(s) in {t_total:.2f}s "
          f"({t_total/len(image_paths):.2f}s per image)")


# ---------- Example Usage ---------- #
if __name__ == "__main__":
    model_path = '/mnt/d/Users/<USER>/Documents/spot_detector_model_sota_08072025_afternoon/best_model.pth'
    input_path = '/mnt/d/Users/<USER>/FISH_spots/2d/test_batch'  # Can also be a single .tif
    output_folder = '/mnt/d/Users/<USER>/FISH_spots/2d/output_maps'
    device = 'cuda' if torch.cuda.is_available() else 'cpu'

    # Load model
    checkpoint = torch.load(model_path, map_location=device, weights_only=False)
    model = SkeletonAwareSpotDetector(in_ch=1, base_ch=48)
    model.load_state_dict(checkpoint['model_state_dict'])
    model.to(device)

    # Run inference (single image OR folder)
    segment_images(
        input_path=input_path,
        output_folder=output_folder,
        model=model,
        device=device,
        patch_size=256,
        overlap=0.2,
        batch_size=16,
        use_amp=True,
        save_maps=True
    )


### MULTITHREADED and save as tif 
import multiprocessing as mp
mp.set_start_method('spawn', force=True)  # <-- MUST be at top, before torch import
import os
import time
import glob
import contextlib
import multiprocessing
from functools import partial
from pathlib import Path
from typing import Dict, Tuple, List

import numpy as np
import tifffile
import torch
import torch.nn.functional as F
from tqdm import tqdm

# CPU libs for final instance segmentation
from scipy.ndimage import label as ndi_label
from skimage.measure import regionprops
from skimage.segmentation import watershed
from skimage.morphology import remove_small_objects, local_maxima as sk_local_maxima

# -------------------- Utilities -------------------- #
def normalize_image_np(img: np.ndarray, low_pct=0.5, high_pct=99.5) -> np.ndarray:
    img = img.astype(np.float32, copy=False)
    vmin, vmax = np.percentile(img, (low_pct, high_pct))
    return np.clip((img - vmin) / (vmax - vmin + 1e-8), 0.0, 1.0).astype(np.float32)

def fast_local_maxima_torch(x: torch.Tensor, min_distance: int = 1) -> torch.BoolTensor:
    if x.ndim == 2:
        x = x.unsqueeze(0).unsqueeze(0)  # [1,1,H,W]
        squeeze_out = True
    elif x.ndim == 3:
        x = x.unsqueeze(0)  # [1,C,H,W]
        squeeze_out = False
    else:
        squeeze_out = False
    kernel = 2 * min_distance + 1
    pooled = F.max_pool2d(x, kernel_size=kernel, stride=1, padding=min_distance)
    maxima = (x == pooled)
    if squeeze_out:
        return maxima.squeeze(0).squeeze(0)
    return maxima.squeeze(0)

# -------------------- CPU instance segmentation -------------------- #
def cpu_postprocess_instances(
    semantic: np.ndarray,
    sdt: np.ndarray,
    skeleton: np.ndarray,
    centroid: np.ndarray,
    boundary: np.ndarray,
    min_size: int = 1,
    sdt_seed_threshold: float = 0.5,
    centroid_seed_threshold: float = 0.2,
    semantic_threshold: float = 0.01,
    min_distance: int = 1
) -> Tuple[np.ndarray, list]:
    H, W = semantic.shape
    mask = semantic > semantic_threshold

    try:
        sdt_local = sk_local_maxima(sdt)
    except Exception:
        from scipy.ndimage import maximum_filter
        sdt_local = (sdt == maximum_filter(sdt, size=3))

    seed_mask = sdt_local & (sdt > sdt_seed_threshold) & mask

    if seed_mask.sum() == 0:
        try:
            cent_local = sk_local_maxima(centroid)
        except Exception:
            from scipy.ndimage import maximum_filter
            cent_local = (centroid == maximum_filter(centroid, size=3))
        seed_mask = cent_local & (centroid > centroid_seed_threshold) & mask

    if seed_mask.sum() == 0:
        try:
            sem_local = sk_local_maxima(semantic)
        except Exception:
            from scipy.ndimage import maximum_filter
            sem_local = (semantic == maximum_filter(semantic, size=3))
        seed_mask = sem_local & mask

    if seed_mask.sum() == 0:
        return np.zeros((H, W), dtype=np.int32), []

    markers, n_markers = ndi_label(seed_mask.astype(np.uint8))
    if n_markers == 0:
        return np.zeros((H, W), dtype=np.int32), []

    instances = watershed(-sdt, markers, mask=mask)

    if min_size > 1:
        instances = remove_small_objects(instances.astype(np.int32), min_size=min_size)
    regions = regionprops(instances)
    final_labels = np.zeros_like(instances, dtype=np.int32)
    final_spots = []
    new_id = 1
    for region in regions:
        if region.area < min_size:
            continue
        mask_r = (instances == region.label)
        cy, cx = region.centroid
        semantic_score = float(np.mean(semantic[mask_r]))
        centroid_score = float(np.mean(centroid[mask_r]))
        sdt_score = float(np.mean(sdt[mask_r]))
        boundary_score = float(np.mean(boundary[mask_r]))
        combined_score = (
            0.3 * semantic_score +
            0.3 * centroid_score +
            0.2 * sdt_score +
            0.2 * (1.0 - boundary_score)
        )
        final_labels[mask_r] = new_id
        final_spots.append({
            'y': float(cy),
            'x': float(cx),
            'score': float(combined_score),
            'size': int(region.area),
            'semantic_score': semantic_score,
            'centroid_score': centroid_score,
            'sdt_score': sdt_score,
            'boundary_score': boundary_score
        })
        new_id += 1

    return final_labels, final_spots

# -------------------- Main tiled inference -------------------- #
def ultra_fast_tile_inference_gpu(
    model,
    image_path: str,
    device: str = 'cuda',
    patch_size: int = 256,
    overlap: float = 0.2,
    batch_size: int = 16,
    use_amp: bool = True,
    min_size: int = 1,
    sdt_seed_threshold: float = 0.01,
    centroid_seed_threshold: float = 0.05,
    semantic_threshold: float = 0.01,
    min_distance_seed: int = 1,
    save_maps: bool = True,
    output_base: str = None,
) -> Dict:
    assert device in ('cuda', 'cpu')
    device_t = torch.device(device)

    image = tifffile.imread(image_path).astype(np.float32)
    H, W = image.shape
    image_norm = normalize_image_np(image)

    bytes_needed = image_norm.nbytes
    image_torch_full = None
    if device_t.type == 'cuda' and torch.cuda.is_available():
        try:
            free_mem, _ = torch.cuda.mem_get_info()
            mem_ok = bytes_needed < int(free_mem * 0.6)
        except Exception:
            mem_ok = False
        if mem_ok:
            image_torch_full = torch.from_numpy(image_norm).to(device_t, dtype=torch.float32)

    stride = max(int(patch_size * (1 - overlap)), 32)
    y_coords = list(range(0, H, stride))
    x_coords = list(range(0, W, stride))
    if y_coords[-1] + patch_size < H:
        y_coords.append(H - patch_size)
    if x_coords[-1] + patch_size < W:
        x_coords.append(W - patch_size)
    total_patches = len(y_coords) * len(x_coords)

    semantic_sum = torch.zeros((H, W), dtype=torch.float32, device=device_t)
    sdt_max = torch.full((H, W), -float('inf'), dtype=torch.float32, device=device_t)
    centroid_max = torch.full((H, W), -float('inf'), dtype=torch.float32, device=device_t)
    skeleton_sum = torch.zeros((H, W), dtype=torch.float32, device=device_t)
    boundary_sum = torch.zeros((H, W), dtype=torch.float32, device=device_t)
    flow_sum = torch.zeros((2, H, W), dtype=torch.float32, device=device_t)
    count_map = torch.zeros((H, W), dtype=torch.float32, device=device_t)

    model.eval()
    model.to(device_t)

    pbar = tqdm(total=total_patches, desc="Ultra-Fast Inference (GPU agg)")
    patches: List[torch.Tensor] = []
    coords: List[Tuple[int, int, int, int]] = []
    processed = 0

    from torch import amp
    autocast_ctx = (lambda: amp.autocast("cuda")) if (use_amp and device_t.type == 'cuda') else contextlib.nullcontext

    with torch.no_grad():
        with autocast_ctx():
            for y_start in y_coords:
                for x_start in x_coords:
                    y_end = min(y_start + patch_size, H)
                    x_end = min(x_start + patch_size, W)
                    h, w = y_end - y_start, x_end - x_start

                    if image_torch_full is not None:
                        patch_t = image_torch_full[y_start:y_end, x_start:x_end]
                    else:
                        patch_np = image_norm[y_start:y_end, x_start:x_end]
                        patch_t = torch.from_numpy(patch_np).float().to(device_t)

                    if (h != patch_size) or (w != patch_size):
                        pad_h = patch_size - h
                        pad_w = patch_size - w
                        patch_t = F.pad(patch_t, (0, pad_w, 0, pad_h), mode='constant', value=0.0)

                    if patch_t.dtype != torch.float32:
                        patch_t = patch_t.to(dtype=torch.float32)

                    patches.append(patch_t)
                    coords.append((y_start, y_end, x_start, x_end))

                    if len(patches) >= batch_size or (processed + len(patches) == total_patches):
                        batch_tensor = torch.stack(patches, dim=0).unsqueeze(1).to(device_t, dtype=torch.float32)

                        outputs = model(batch_tensor)

                        sem_b = torch.sigmoid(outputs['semantic']).to(device_t, dtype=torch.float32)
                        sdt_b = torch.sigmoid(outputs['sdt']).to(device_t, dtype=torch.float32)
                        skel_b = torch.sigmoid(outputs['skeleton']).to(device_t, dtype=torch.float32)
                        cent_b = torch.sigmoid(outputs['centroid']).to(device_t, dtype=torch.float32)
                        bound_b = torch.sigmoid(outputs['boundary']).to(device_t, dtype=torch.float32)
                        flow_b = torch.tanh(outputs['flow']).to(device_t, dtype=torch.float32)

                        for i, (y_s, y_e, x_s, x_e) in enumerate(coords):
                            h_orig, w_orig = y_e - y_s, x_e - x_s

                            sem_patch = sem_b[i, 0, :h_orig, :w_orig]
                            sdt_patch = sdt_b[i, 0, :h_orig, :w_orig]
                            skel_patch = skel_b[i, 0, :h_orig, :w_orig]
                            cent_patch = cent_b[i, 0, :h_orig, :w_orig]
                            bound_patch = bound_b[i, 0, :h_orig, :w_orig]
                            flow_patch = flow_b[i, :, :h_orig, :w_orig]

                            semantic_sum[y_s:y_e, x_s:x_e] += sem_patch
                            skeleton_sum[y_s:y_e, x_s:x_e] += skel_patch
                            boundary_sum[y_s:y_e, x_s:x_e] += bound_patch
                            flow_sum[:, y_s:y_e, x_s:x_e] += flow_patch
                            count_map[y_s:y_e, x_s:x_e] += 1.0

                            sdt_max[y_s:y_e, x_s:x_e] = torch.maximum(sdt_max[y_s:y_e, x_s:x_e], sdt_patch)
                            centroid_max[y_s:y_e, x_s:x_e] = torch.maximum(centroid_max[y_s:y_e, x_s:x_e], cent_patch)

                        processed += len(patches)
                        pbar.update(len(patches))
                        patches.clear()
                        coords.clear()

    pbar.close()

    count_safe = torch.clamp(count_map, min=1.0)
    semantic_out = (semantic_sum / count_safe).cpu().numpy()
    skeleton_out = (skeleton_sum / count_safe).cpu().numpy()
    boundary_out = (boundary_sum / count_safe).cpu().numpy()
    flow_out = (flow_sum / count_safe.unsqueeze(0)).cpu().numpy()
    sdt_out = sdt_max.cpu().numpy()
    centroid_out = centroid_max.cpu().numpy()

    sdt_out[np.isneginf(sdt_out)] = 0.0
    centroid_out[np.isneginf(centroid_out)] = 0.0

    instance_labels, filtered_spots = cpu_postprocess_instances(
        semantic=semantic_out,
        sdt=sdt_out,
        skeleton=skeleton_out,
        centroid=centroid_out,
        boundary=boundary_out,
        min_size=min_size,
        sdt_seed_threshold=sdt_seed_threshold,
        centroid_seed_threshold=centroid_seed_threshold,
        semantic_threshold=semantic_threshold,
        min_distance=min_distance_seed
    )

    results = {
        'instance_labels': instance_labels,
        'spots': filtered_spots,
        'semantic': semantic_out,
        'sdt': sdt_out,
        'skeleton': skeleton_out,
        'centroid': centroid_out,
        'boundary': boundary_out,
        'flow': flow_out
    }

    # Save outputs as TIFF if output_base given and save_maps=True
    if save_maps and output_base is not None:
        tifffile.imwrite(output_base + "_labels.tif", instance_labels.astype(np.uint16))
        tifffile.imwrite(output_base + "_semantic.tif", (semantic_out * 65535).astype(np.uint16))
        tifffile.imwrite(output_base + "_sdt.tif", (sdt_out * 65535).astype(np.uint16))
        tifffile.imwrite(output_base + "_skeleton.tif", (skeleton_out * 65535).astype(np.uint16))
        tifffile.imwrite(output_base + "_centroid.tif", (centroid_out * 65535).astype(np.uint16))
        tifffile.imwrite(output_base + "_boundary.tif", (boundary_out * 65535).astype(np.uint16))
        # flow is 2 channels, save as multi-page TIFF
        flow_scaled = ((flow_out + 1) / 2 * 65535).astype(np.uint16)  # scale from [-1,1] to [0,65535]
        tifffile.imwrite(output_base + "_flow.tif", flow_scaled)

    return results

# -------------- Worker function for multiprocessing -------------- #
def _process_single_image(
    img_path: str,
    model_path: str,
    device: str,
    output_folder: str,
    patch_size: int,
    overlap: float,
    batch_size: int,
    use_amp: bool,
    min_size: int,
    save_maps: bool,
):
    import torch  # must be imported inside worker for spawn safety
    checkpoint = torch.load(model_path, map_location=device)
    model = SkeletonAwareSpotDetector(in_ch=1, base_ch=48)
    model.load_state_dict(checkpoint['model_state_dict'])
    model.to(device)
    model.eval()

    base = Path(img_path).stem
    out_base = Path(output_folder) / base
    out_base.parent.mkdir(parents=True, exist_ok=True)

    try:
        results = ultra_fast_tile_inference_gpu(
            model=model,
            image_path=img_path,
            device=device,
            patch_size=patch_size,
            overlap=overlap,
            batch_size=batch_size,
            use_amp=use_amp,
            min_size=min_size,
            save_maps=save_maps,
            output_base=str(out_base) if save_maps else None,
        )
    except Exception as e:
        print(f"ERROR processing {img_path}: {e}")
        return False

    np.save(str(out_base) + "_spots.npy", results['spots'])
    print(f"Processed {img_path} | spots: {len(results['spots'])}")
    return True

# -------------- Batch runner with multiprocessing -------------- #
def batch_segment_folder_multiproc(
    input_folder: str,
    output_folder: str,
    model_path: str,
    device: str = None,
    patch_size: int = 256,
    overlap: float = 0.2,
    batch_size: int = 16,
    use_amp: bool = True,
    min_size: int = 1,
    save_maps: bool = True,
    num_workers: int = None,
):
    if device is None:
        device = "cuda" if torch.cuda.is_available() else "cpu"

    os.makedirs(output_folder, exist_ok=True)
    image_paths = sorted(glob.glob(os.path.join(input_folder, "*.tif")))
    if not image_paths:
        print("No .tif images found in", input_folder)
        return

    if num_workers is None:
        num_workers = max(1, mp.cpu_count() - 1)

    print(f"Starting batch inference with {num_workers} workers on device {device}...")

    worker_func = partial(
        _process_single_image,
        model_path=model_path,
        device=device,
        output_folder=output_folder,
        patch_size=patch_size,
        overlap=overlap,
        batch_size=batch_size,
        use_amp=use_amp,
        min_size=min_size,
        save_maps=save_maps,
    )

    with mp.Pool(num_workers) as pool:
        list(tqdm(pool.imap(worker_func, image_paths), total=len(image_paths)))

    print("Batch multiprocessing done. Results in:", output_folder)





#FOR BATCH INFERENCE
# -------------------- Example usage -------------------- #
if __name__ == "__main__":
    # Replace with your actual paths
    model_path = '/mnt/d/Users/<USER>/Documents/spot_detector_model_sota_08072025_afternoon/best_model.pth'
    input_folder = '/mnt/d/Users/<USER>/FISH_spots/2d/test_batch'
    output_folder = '/mnt/d/Users/<USER>/FISH_spots/2d/test_batch_inference'
    device = 'cuda' if torch.cuda.is_available() else 'cpu'

    batch_segment_folder_multiproc(
        input_folder=input_folder,
        output_folder=output_folder,
        model_path=model_path,
        device=device,
        patch_size=256,
        overlap=0.2,
        batch_size=16,
        use_amp=True,
        min_size=1,
        save_maps=True,
        num_workers=4  # Adjust according to your CPU/GPU resources
    )

















"""
Comprehensive spot detection pipeline
- Ultra-fast tiled inference with TTA + multi-scale
- Max-aggregation for peak maps (sdt, centroid, optional semantic)
- Permissive seed discovery and post-hoc filtering (recover weak spots)
- Debug saver to write intermediate maps/overlays for manual inspection
- Training recipe skeleton for fine-tuning if inference fixes insufficient

Usage:
  - Edit CONFIG at the bottom for model path, image path, device
  - Run: python spot_detection_full_pipeline.py

This file is intended as a drop-in, single-file patch. It focuses on clarity,
clean code and performance. Tune thresholds in CONFIG.

Author: ChatGPT (for user)
"""

from pathlib import Path
from typing import Dict, List, Tuple, Optional
import os
import time
import numpy as np
import tifffile
from tqdm import tqdm
import torch
import torch.nn.functional as F
from scipy.ndimage import label as ndi_label
from skimage.morphology import local_maxima, remove_small_objects
from skimage.segmentation import watershed
from skimage.measure import regionprops
from skimage.transform import resize
import matplotlib.pyplot as plt


# -------------------- Instance segmentation (permissive seeds) -------------------- #

def fast_accurate_instance_segmentation(
    semantic: np.ndarray,
    sdt_scalar: np.ndarray,
    skeleton: np.ndarray,
    centroid_map: np.ndarray,
    flow: np.ndarray,
    boundary: np.ndarray,
    min_size: int = 1,
    sdt_seed_threshold: float = 0.25,
    centroid_seed_threshold: float = 0.05,
    semantic_threshold: float = 1e-4,
    combined_threshold: Optional[float] = None,
) -> Tuple[np.ndarray, List[Dict]]:
    """
    Permissive seed-based watershed instance segmentation.

    Strategy:
      - Primary seeds: local maxima of SDT above sdt_seed_threshold (no strict semantic gating)
      - Fallback seeds: local maxima of centroid_map above centroid_seed_threshold
      - Last resort: any SDT local maxima inside a permissive semantic mask
      - Run watershed on -sdt (so SDT maxima become basins)
      - Post-filter by min_size and optional combined score threshold

    Returns:
      - final_labels: int32 label image
      - final_spots: list of dicts with (y,x,score,size, per-head scores)
    """
    H, W = sdt_scalar.shape

    # permissive semantic mask: avoids fully masking tiny objects
    mask = (semantic > semantic_threshold)

    # primary seeds: SDT local maxima
    sdt_local = local_maxima(sdt_scalar)
    seed_mask = sdt_local & (sdt_scalar > sdt_seed_threshold) & mask

    # fallback: centroid peaks
    if seed_mask.sum() == 0:
        cent_local = local_maxima(centroid_map)
        seed_mask = cent_local & (centroid_map > centroid_seed_threshold) & mask

    # last resort: any SDT local maxima inside mask
    if seed_mask.sum() == 0:
        seed_mask = sdt_local & mask

    if seed_mask.sum() == 0:
        return np.zeros((H, W), dtype=np.int32), []

    # label connected seeds
    markers, n_markers = ndi_label(seed_mask)
    if n_markers == 0:
        return np.zeros((H, W), dtype=np.int32), []

    instances = watershed(-sdt_scalar, markers, mask=mask)

    # Optional: remove very small components before measuring
    if min_size > 1:
        instances = remove_small_objects(instances.astype(np.int32), min_size=min_size)

    regions = regionprops(instances)
    final_labels = np.zeros_like(instances, dtype=np.int32)
    final_spots: List[Dict] = []
    new_id = 1
    for region in regions:
        if region.area < min_size:
            continue
        m = (instances == region.label)
        cy, cx = region.centroid
        sem_score = float(np.mean(semantic[m]))
        cent_score = float(np.mean(centroid_map[m]))
        sdt_score = float(np.mean(sdt_scalar[m]))
        bnd_score = float(np.mean(boundary[m]))

        combined_score = (
            0.3 * sem_score +
            0.3 * cent_score +
            0.2 * sdt_score +
            0.2 * (1.0 - bnd_score)
        )

        # If caller asked for a combined threshold, enforce it here
        if combined_threshold is not None and combined_score < combined_threshold:
            continue

        final_labels[m] = new_id
        final_spots.append({
            'y': float(cy),
            'x': float(cx),
            'score': float(combined_score),
            'size': int(region.area),
            'semantic_score': sem_score,
            'centroid_score': cent_score,
            'sdt_score': sdt_score,
            'boundary_score': bnd_score
        })
        new_id += 1

    return final_labels, final_spots


# -------------------- TTA utilities -------------------- #

def _apply_transform_numpy(arr: np.ndarray, t: str) -> np.ndarray:
    # arr shape: (C, H, W) or (H, W)
    if t == 'none':
        return arr
    if t == 'flip_h':
        return np.flip(arr, axis=-1)
    if t == 'flip_v':
        return np.flip(arr, axis=-2)
    if t == 'rot90':
        return np.rot90(arr, k=1, axes=(-2, -1))
    raise ValueError(f'Unknown transform: {t}')


def _undo_transform_numpy(arr: np.ndarray, t: str) -> np.ndarray:
    if t == 'none':
        return arr
    if t == 'flip_h':
        return np.flip(arr, axis=-1)
    if t == 'flip_v':
        return np.flip(arr, axis=-2)
    if t == 'rot90':
        return np.rot90(arr, k=-1, axes=(-2, -1))
    raise ValueError(f'Unknown transform: {t}')


# -------------------- Single-patch TTA prediction -------------------- #

def tta_predict_single_patch(
    model: torch.nn.Module,
    patch: np.ndarray,
    device: str,
    transforms: Tuple[str, ...] = ('none', 'flip_h', 'flip_v'),
) -> Dict[str, np.ndarray]:
    """
    Run TTA for a single patch (H, W) and aggregate outputs.
    Returns a dict with keys matching model outputs; values are numpy arrays shaped like model output maps.
    Aggregation: max for ('sdt','centroid','semantic'), mean for others.

    NOTE: use `np.ascontiguousarray` before creating tensors because some numpy views
    (e.g. from np.flip / np.rot90) have negative or non-contiguous strides which
    torch.from_numpy does not accept.
    """
    model.to(device)
    model.eval()

    outs = []
    with torch.no_grad():
        for t in transforms:
            arr = _apply_transform_numpy(patch, t)
            # Ensure array is contiguous with positive strides before converting to torch
            arr = np.ascontiguousarray(arr)

            tensor = torch.from_numpy(arr[None, None]).float().to(device)
            with torch.amp.autocast(device_type='cuda', enabled=(device == 'cuda')):
                out = model(tensor)

            # Convert outputs to numpy and undo transform
            out_np = {}
            for k, v in out.items():
                a = v.cpu().numpy()
                # remove batch dim
                a = a[0]
                # ensure channels-first (C,H,W) or (H,W) for single-channel
                a = np.squeeze(a)
                a = _undo_transform_numpy(a, t)
                # undo_transform can also produce views; make contiguous to be safe downstream
                a = np.ascontiguousarray(a)
                out_np[k] = a
            outs.append(out_np)

    # Aggregate
    keys = outs[0].keys()
    agg: Dict[str, np.ndarray] = {}
    for k in keys:
        stack = np.stack([o[k] for o in outs], axis=0)
        if k in ('sdt', 'centroid', 'semantic'):
            agg[k] = np.max(stack, axis=0)
        else:
            agg[k] = np.mean(stack, axis=0)
    return agg


# -------------------- Multi-scale wrapper -------------------- #

def multi_scale_predict(
    model: torch.nn.Module,
    patch: np.ndarray,
    device: str,
    scales: Tuple[float, ...] = (1.0, 0.8),
    transforms: Tuple[str, ...] = ('none', 'flip_h'),
) -> Dict[str, np.ndarray]:
    """
    Run inference on several rescaled versions of the patch and max-aggregate maps that are peak-like
    (sdt, centroid, semantic). This recovers weak spots that appear at slightly different scales.

    This version correctly handles multi-channel outputs (e.g. flow with shape (C,H,W)) by
    resizing each channel independently and preserving channel-first layout.
    """
    aggregated: Dict[str, Optional[np.ndarray]] = {}

    for s in scales:
        if s == 1.0:
            patch_s = patch
        else:
            # use skimage.resize to scale patch; preserve range [0,1]
            H, W = patch.shape
            patch_s = resize(patch, (int(round(H * s)), int(round(W * s))), preserve_range=True)

        out = tta_predict_single_patch(model, patch_s, device=device, transforms=transforms)

        # resize outputs back to original patch size and aggregate
        for k, v in out.items():
            # standardize v to either (H,W) or (C,H,W)
            if v.ndim == 2:
                v_rs = resize(v, patch.shape, preserve_range=True)
            elif v.ndim == 3:
                # heuristics: if first dim small (<=8) assume channel-first (C,H,W)
                if v.shape[0] <= 8:
                    channels = v
                else:
                    # otherwise assume H,W,C and transpose
                    channels = np.transpose(v, (2, 0, 1))

                c, hh, ww = channels.shape
                v_rs = np.zeros((c, patch.shape[0], patch.shape[1]), dtype=channels.dtype)
                for ci in range(c):
                    v_rs[ci] = resize(channels[ci], patch.shape, preserve_range=True)
            else:
                # fallback: squeeze then resize
                v_s = np.squeeze(v)
                v_rs = resize(v_s, patch.shape, preserve_range=True)

            # ensure float32 contiguous
            v_rs = np.ascontiguousarray(v_rs.astype(np.float32))

            if k not in aggregated or aggregated[k] is None:
                aggregated[k] = v_rs
            else:
                # both must have compatible shapes
                a = aggregated[k]
                # if shapes mismatch (rare), try to coerce by squeezing singleton channel
                if a.shape != v_rs.shape:
                    # if one is (C,H,W) and the other is (H,W) -> expand dims
                    if a.ndim == 3 and v_rs.ndim == 2:
                        v_rs = v_rs[None, ...]
                    elif a.ndim == 2 and v_rs.ndim == 3 and v_rs.shape[0] == 1:
                        v_rs = np.squeeze(v_rs, axis=0)

                if a.shape != v_rs.shape:
                    raise ValueError(f"Shape mismatch during multi-scale aggregation for key '{k}': {a.shape} vs {v_rs.shape}")

                if k in ('sdt', 'centroid', 'semantic'):
                    aggregated[k] = np.maximum(a, v_rs)
                else:
                    aggregated[k] = (a + v_rs) / 2.0

    # mypy-friendly return
    return {k: aggregated[k] for k in aggregated}


# -------------------- Ultra-fast tiled inference (integrated) -------------------- #

def ultra_fast_tile_inference(
    model: torch.nn.Module,
    image_path: str,
    device: str = 'cuda',
    patch_size: int = 256,
    overlap: float = 0.2,
    batch_size: int = 8,
    scales: Tuple[float, ...] = (1.0, 0.8),
    transforms: Tuple[str, ...] = ('none', 'flip_h'),
    use_max_for_semantic: bool = False,
    min_size: int = 1,
    sdt_seed_threshold: float = 0.25,
    centroid_seed_threshold: float = 0.05,
    semantic_threshold: float = 1e-4,
    combined_threshold: Optional[float] = None,
    save_debug: bool = False,
    debug_dir: Optional[str] = None,
) -> Dict:
    """
    Full tiled inference with multi-scale + TTA + permissive instance segmentation.

    Important performance notes:
      - sdt/centroid/semantic use max-aggregation across tiles & TTA/scales to preserve peaks
      - other maps averaged
      - batch_size should fit your GPU memory.
    """
    image = tifffile.imread(image_path).astype(np.float32)
    H, W = image.shape

    # normalization
    vmin, vmax = np.percentile(image, (0.5, 99.5))
    image_norm = np.clip((image - vmin) / (vmax - vmin + 1e-8), 0.0, 1.0)

    model.eval()
    model.to(device)

    stride = max(int(patch_size * (1 - overlap)), 32)
    y_coords = list(range(0, H, stride))
    x_coords = list(range(0, W, stride))
    if y_coords[-1] + patch_size < H:
        y_coords.append(H - patch_size)
    if x_coords[-1] + patch_size < W:
        x_coords.append(W - patch_size)
    total_patches = len(y_coords) * len(x_coords)

    # pre-allocate
    semantic_sum = np.zeros((H, W), dtype=np.float32)
    sdt_max = np.full((H, W), -np.inf, dtype=np.float32)
    centroid_max = np.full((H, W), -np.inf, dtype=np.float32)
    skeleton_sum = np.zeros((H, W), dtype=np.float32)
    boundary_sum = np.zeros((H, W), dtype=np.float32)
    flow_sum = np.zeros((2, H, W), dtype=np.float32)
    count_map = np.zeros((H, W), dtype=np.float32)

    pbar = tqdm(total=total_patches, desc='Ultra-Fast Inference')

    coords: List[Tuple[int, int, int, int]] = []
    patches: List[np.ndarray] = []
    processed = 0

    for y_start in y_coords:
        for x_start in x_coords:
            y_end = min(y_start + patch_size, H)
            x_end = min(x_start + patch_size, W)
            patch = image_norm[y_start:y_end, x_start:x_end]
            if patch.shape[0] < patch_size or patch.shape[1] < patch_size:
                pad_h = patch_size - patch.shape[0]
                pad_w = patch_size - patch.shape[1]
                patch = np.pad(patch, ((0, pad_h), (0, pad_w)), mode='constant')

            patches.append(patch)
            coords.append((y_start, y_end, x_start, x_end))

            if len(patches) >= batch_size or processed + len(patches) == total_patches:
                # process batch with multi-scale + TTA per patch
                for i, patch_arr in enumerate(patches):
                    out = multi_scale_predict(model, patch_arr, device=device, scales=scales, transforms=transforms)

                    # expected keys: semantic, sdt, skeleton, centroid, boundary, flow
                    h = coords[i][1] - coords[i][0]
                    w = coords[i][3] - coords[i][2]

                    sem = out.get('semantic')[:h, :w]
                    sdt = out.get('sdt')[:h, :w]
                    skel = out.get('skeleton')[:h, :w]
                    cent = out.get('centroid')[:h, :w]
                    bound = out.get('boundary')[:h, :w]
                    flow = out.get('flow')[:, :h, :w] if 'flow' in out else np.zeros((2, h, w), dtype=np.float32)

                    y_s, y_e, x_s, x_e = coords[i]

                    # accumulate: average for semantic optionally use max
                    if use_max_for_semantic:
                        semantic_sum[y_s:y_e, x_s:x_e] = np.maximum(semantic_sum[y_s:y_e, x_s:x_e], sem)
                    else:
                        semantic_sum[y_s:y_e, x_s:x_e] += sem

                    sdt_max[y_s:y_e, x_s:x_e] = np.maximum(sdt_max[y_s:y_e, x_s:x_e], sdt)
                    centroid_max[y_s:y_e, x_s:x_e] = np.maximum(centroid_max[y_s:y_e, x_s:x_e], cent)
                    skeleton_sum[y_s:y_e, x_s:x_e] += skel
                    boundary_sum[y_s:y_e, x_s:x_e] += bound
                    flow_sum[:, y_s:y_e, x_s:x_e] += flow
                    count_map[y_s:y_e, x_s:x_e] += 1

                    # optional debug save per-patch (lightweight)
                    if save_debug and debug_dir is not None:
                        dbg_dir = Path(debug_dir)
                        dbg_dir.mkdir(parents=True, exist_ok=True)
                        base = f'patch_y{y_s}_x{x_s}'
                        tifffile.imwrite(dbg_dir / f"{base}_patch.tif", (patch_arr * 65535).astype(np.uint16))
                        tifffile.imwrite(dbg_dir / f"{base}_semantic.tif", (sem * 65535).astype(np.uint16))
                        tifffile.imwrite(dbg_dir / f"{base}_sdt.tif", (sdt * 65535).astype(np.uint16))

                processed += len(patches)
                pbar.update(len(patches))
                patches.clear()
                coords.clear()

    pbar.close()

    # finalize averaged outputs
    count_map_safe = np.maximum(count_map, 1.0)
    if not use_max_for_semantic:
        semantic_out = semantic_sum / count_map_safe
    else:
        semantic_out = semantic_sum  # already max-aggregated
    sdt_out = sdt_max
    sdt_out[np.isneginf(sdt_out)] = 0.0
    centroid_out = centroid_max
    centroid_out[np.isneginf(centroid_out)] = 0.0
    skeleton_out = skeleton_sum / count_map_safe
    boundary_out = boundary_sum / count_map_safe
    flow_out = flow_sum / count_map_safe[None, :, :]

    # instance segmentation
    instance_labels, filtered_spots = fast_accurate_instance_segmentation(
        semantic=semantic_out,
        sdt_scalar=sdt_out,
        skeleton=skeleton_out,
        centroid_map=centroid_out,
        flow=flow_out,
        boundary=boundary_out,
        min_size=min_size,
        sdt_seed_threshold=sdt_seed_threshold,
        centroid_seed_threshold=centroid_seed_threshold,
        semantic_threshold=semantic_threshold,
        combined_threshold=combined_threshold,
    )

    results = {
        'instance_labels': instance_labels,
        'spots': filtered_spots,
        'semantic': semantic_out,
        'sdt': sdt_out,
        'skeleton': skeleton_out,
        'centroid': centroid_out,
        'boundary': boundary_out,
        'flow': flow_out,
    }

    return results


# -------------------- Debug saver & overlay helper -------------------- #

def save_debug_overlays(results: Dict, image_path: str, out_dir: str):
    """
    Save intermediate maps and a colored overlay for quick inspection.
    Writes: original, semantic, sdt, centroid, seeds_overlay, instance_labels as uint16 TIFFs and a PNG overlay.
    """
    p = Path(out_dir)
    p.mkdir(parents=True, exist_ok=True)

    orig = tifffile.imread(image_path).astype(np.float32)
    vmin, vmax = np.percentile(orig[orig > 0], (2, 98))
    orig_n = np.clip((orig - vmin) / (vmax - vmin + 1e-8), 0, 1)

    tifffile.imwrite(p / 'original_uint16.tif', (orig_n * 65535).astype(np.uint16))
    tifffile.imwrite(p / 'semantic_uint16.tif', (results['semantic'] * 65535).astype(np.uint16))
    tifffile.imwrite(p / 'sdt_uint16.tif', (results['sdt'] * 65535).astype(np.uint16))
    tifffile.imwrite(p / 'centroid_uint16.tif', (results['centroid'] * 65535).astype(np.uint16))
    tifffile.imwrite(p / 'boundary_uint16.tif', (results['boundary'] * 65535).astype(np.uint16))

    # seeds overlay: centroid points
    inst = results['instance_labels']
    cmap = plt.get_cmap('nipy_spectral')
    rgba = cmap((inst.astype(np.float32) / (inst.max() + 1e-8)).clip(0, 1))
    overlay = np.stack([orig_n, orig_n, orig_n], axis=-1)
    mask = inst > 0
    overlay[mask] = rgba[mask, :3]
    # write overlay PNG
    import imageio
    imageio.imwrite(str(p / 'overlay.png'), (overlay * 255).astype(np.uint8))

    # tiny JSON-like text summary
    with open(p / 'summary.txt', 'w') as fh:
        fh.write(f"Spots: {len(results['spots'])}\n")
        sizes = [s['size'] for s in results['spots']]
        if sizes:
            fh.write(f"Size range: {min(sizes)}-{max(sizes)}\n")
            fh.write(f"Avg size: {np.mean(sizes):.2f}\n")

    print(f"Saved debug outputs to {p}")


# -------------------- Training recipe (skeleton) -------------------- #

def training_recipe_description():
    """
    Prints a compact, actionable training recipe for fine-tuning the model to recover weak/small spots.

    Key ingredients:
      - Data augmentation that preserves tiny spots (light blur, intensity jitter, scaling)
      - Weighted/focal loss for semantic
      - L1/L2 loss for centroid & sdt heads
      - Multi-scale supervision
      - Oversample patches containing small/weak spots
    """
    recipe = r"""
    ===== Training / Fine-tuning Recipe =====

    1) Data & labels
      - Ensure GT centroids and SDT computed at full resolution.
      - For tiny spots, use small gaussian sigma when creating centroid/heatmap targets (e.g. sigma=1-1.5 px).
      - Create balanced sampling: more patches that contain at least one small/low-contrast spot.

    2) Loss
      - Semantic head: Focal loss (gamma=2.0, alpha=0.25) to upweight hard/rare positive pixels
      - Centroid head: MSE or L1 on heatmap targets (optionally gaussian mixture loss)
      - SDT head: L1/L2 on distance transform target
      - Boundary head: BCE or weighted BCE
      - Flow: smooth L1 on flow vectors

    3) Optimization
      - Optimizer: AdamW lr=1e-4; lower to 1e-5 for fine-tuning
      - Scheduler: ReduceLROnPlateau or CosineLR
      - Mixed precision enabled
      - Batch size: as large as GPU allows; use gradient accumulation if needed

    4) Training tricks
      - Multi-scale supervision: add auxiliary losses at decoder scales
      - Random small translations and small scale augmentations (0.8-1.2)
      - Use validation that focuses on small spot F1 (custom metric)

    5) Quick PyTorch skeleton (pseudocode)

    for epoch in range(epochs):
        for batch in train_loader:
            images, targets = batch
            preds = model(images)
            loss_sem = focal_loss(preds['semantic'], targets['semantic'])
            loss_cent = F.mse_loss(preds['centroid'], targets['centroid'])
            loss_sdt  = F.l1_loss(preds['sdt'], targets['sdt'])
            loss = loss_sem * 1.0 + loss_cent * 1.0 + loss_sdt * 1.0
            loss.backward()
            optimizer.step()
            optimizer.zero_grad()

    """
    print(recipe)


# -------------------- Example CLI usage / config -------------------- #

if __name__ == '__main__':
    # === CONFIG - tune these ===
    MODEL_PATH = '/mnt/d/Users/<USER>/Documents/spot_detector_model_sota_08072025_afternoon/best_model.pth'  # change me
    IMAGE_PATH = '/mnt/d/Users/<USER>/FISH_spots/2d/synthetic/images/5  Series006  Green--FLUO--FITC_tile_4.tif'      # change me
    DEVICE = 'cuda' if torch.cuda.is_available() else 'cpu'
    PATCH_SIZE = 256
    OVERLAP = 0.25
    BATCH_SIZE = 8
    SCALES = (1.0, 0.8)     # multi-scale: add 0.6 or 1.2 if needed
    TRANSFORMS = ('none', 'flip_h')
    USE_MAX_FOR_SEM = False
    MIN_SIZE = 1
    SDT_SEED_THRESH = 0.25
    CENTROID_SEED_THRESH = 0.05
    SEMANTIC_THRESH = 1e-4
    COMBINED_THRESH = None   # set to small positive like 0.01 to remove worst noise
    SAVE_DEBUG = True
    DEBUG_DIR = './inference_debug'

    # === load model - user must adapt this part to their model class ===
    # The model must accept a tensor (B,1,H,W) and return dict of tensors matching keys used above.
    # Example expected outputs shapes (per-item):
    #   'semantic': (1, H, W), 'sdt': (1, H, W), 'skeleton': (1, H, W),
    #   'centroid': (1, H, W), 'boundary': (1, H, W), 'flow': (2, H, W)

    print(f"Device: {DEVICE}")

    # minimal loader - adapt to your code
    checkpoint = torch.load(model_path, map_location=device, weights_only=False)
    # user: replace `SkeletonAwareSpotDetector` with your real model class
    try:
        
        model = SkeletonAwareSpotDetector(in_ch=1, base_ch=48)
        model.load_state_dict(checkpoint['model_state_dict'])
    except Exception:
        # fallback: try direct object in checkpoint
        if 'model_state_dict' in checkpoint:
            raise RuntimeError('Please import your model class and adapt loader in this script.')
        else:
            model = checkpoint.get('model')
            if model is None:
                raise RuntimeError('Model could not be loaded. Edit the loading section in script.')

    model.to(DEVICE)

    start = time.time()
    results = ultra_fast_tile_inference(
        model=model,
        image_path=IMAGE_PATH,
        device=DEVICE,
        patch_size=PATCH_SIZE,
        overlap=OVERLAP,
        batch_size=BATCH_SIZE,
        scales=SCALES,
        transforms=TRANSFORMS,
        use_max_for_semantic=USE_MAX_FOR_SEM,
        min_size=MIN_SIZE,
        sdt_seed_threshold=SDT_SEED_THRESH,
        centroid_seed_threshold=CENTROID_SEED_THRESH,
        semantic_threshold=SEMANTIC_THRESH,
        combined_threshold=COMBINED_THRESH,
        save_debug=SAVE_DEBUG,
        debug_dir=DEBUG_DIR,
    )
    elapsed = time.time() - start
    print(f"Inference done in {elapsed:.2f}s - spots found: {len(results['spots'])}")

    # save outputs
    out_dir = Path(IMAGE_PATH).parent / 'inference_results'
    out_dir.mkdir(exist_ok=True)
    base = Path(IMAGE_PATH).stem
    tifffile.imwrite(out_dir / f"{base}_labels.tif", results['instance_labels'].astype(np.uint16))
    np.save(out_dir / f"{base}_spots.npy", results['spots'])

    if SAVE_DEBUG:
        save_debug_overlays(results, IMAGE_PATH, DEBUG_DIR)

    # print short stats
    if results['spots']:
        sizes = [s['size'] for s in results['spots']]
        print(f"Total spots: {len(results['spots'])} size_range: {min(sizes)}-{max(sizes)} avg_size: {np.mean(sizes):.2f}")

    print('\n-- Training recipe --\n')
    training_recipe_description()










# Cell: Optimized Tiled Inference & Instance Segmentation
import torch
import torch.nn.functional as F
import numpy as np
from tqdm import tqdm
import tifffile
from scipy.ndimage import maximum_filter
from skimage.segmentation import watershed, find_boundaries
from skimage.measure import regionprops
import cv2
import os

try:
    from torch.amp import autocast  # PyTorch 2.0+
except ImportError:
    from torch.cuda.amp import autocast  # PyTorch < 2.0


def skeleton_aware_instance_segmentation(
    semantic,
    sdt_scalar,
    skeleton,
    centroid_map,
    flow,
    min_size=5,
    nms_threshold=0.4,
    flow_refine_factor=0.3
):
    """
    Fast and accurate instance segmentation using all model outputs.
    Returns instance labels and a list of detected spots with refined centroids.
    """
    H, W = semantic.shape
    binary_mask = (semantic > 0.3).astype(np.uint8)

    # 1. Seed detection from centroid map
    peaks = (centroid_map > nms_threshold) & (maximum_filter(centroid_map, size=3) == centroid_map)
    centroid_coords = np.column_stack(np.where(peaks))

    if len(centroid_coords) == 0:
        print("No peaks detected above threshold.")
        return np.zeros((H, W), dtype=np.int32), []

    # 2. Create seeds for watershed
    seeds = np.zeros((H, W), dtype=np.int32)
    for idx, (y, x) in enumerate(centroid_coords, 1):
        seeds[y, x] = idx

    # 3. Watershed using negative SDT
    instance_labels = watershed(-sdt_scalar, seeds, mask=binary_mask)

    # 4. Relabel: one ID per connected component
    final_labels = np.zeros_like(instance_labels)
    regions = regionprops(instance_labels)
    new_id = 1
    filtered_spots = []

    for region in regions:
        if region.area < min_size:
            continue
        mask = (instance_labels == region.label)
        final_labels[mask] = new_id

        # 5. Refine centroid using flow
        cy, cx = region.centroid
        refined_cy, refined_cx = cy, cx
        if flow is not None and flow.shape[0] == 2:
            ys, xs = np.where(mask)
            if len(ys) > 0:
                avg_flow_y = flow[0][ys, xs].mean()
                avg_flow_x = flow[1][ys, xs].mean()
                refined_cy += flow_refine_factor * avg_flow_y
                refined_cx += flow_refine_factor * avg_flow_x

        # 6. Compute scores
        sdt_score = np.mean(sdt_scalar[mask])
        centroid_score = np.mean(centroid_map[mask])
        semantic_score = np.mean(semantic[mask])
        combined_score = (sdt_score + centroid_score + semantic_score) / 3.0

        filtered_spots.append({
            'y': float(refined_cy),
            'x': float(refined_cx),
            'original_y': float(cy),
            'original_x': float(cx),
            'score': float(combined_score),
            'size': int(region.area),
            'sdt_score': float(sdt_score),
            'centroid_score': float(centroid_score),
            'semantic_score': float(semantic_score)
        })
        new_id += 1

    print(f"Instance segmentation completed: Found {len(filtered_spots)} spots.")
    return final_labels, filtered_spots


def tile_inference(
    model,
    image_path,
    device='cuda',
    patch_size=256,
    overlap=0.5,
    batch_size=4,
    return_all_outputs=True
):
    """
    Fast tiled inference with full model output extraction and instance segmentation.
    Returns all outputs and final instance labels.
    """
    # 1. Load and normalize image
    image = tifffile.imread(image_path).astype(np.float32)
    H, W = image.shape
    vmin, vmax = np.percentile(image, (0.5, 99.5))
    image_norm = np.clip((image - vmin) / (vmax - vmin + 1e-8), 0, 1).astype(np.float32)

    # 2. Set model to eval mode
    model.eval()
    model.to(device)

    # 3. Calculate patch stride
    stride = int(patch_size * (1 - overlap))
    if stride <= 0:
        stride = patch_size // 2

    # 4. Initialize output accumulators
    def init_accumulator():
        return np.zeros((H, W), dtype=np.float32)

    semantic_out = init_accumulator()
    skeleton_out = init_accumulator()
    centroid_out = init_accumulator()
    sdt_probs_out = np.zeros((11, H, W), dtype=np.float32)  # 11 bins
    flow_out = np.zeros((2, H, W), dtype=np.float32)
    count_map = init_accumulator()

    # 5. Prepare patch indices
    y_coords = list(range(0, H, stride))
    x_coords = list(range(0, W, stride))
    total_patches = len(y_coords) * len(x_coords)

    # 6. Batched inference
    with torch.no_grad():
        patch_batch = []
        coords_batch = []
        pbar = tqdm(total=total_patches, desc="Tiled Inference")

        for y_start in y_coords:
            for x_start in x_coords:
                y_end = min(y_start + patch_size, H)
                x_end = min(x_start + patch_size, W)

                # Pad if needed
                pad_h = patch_size - (y_end - y_start)
                pad_w = patch_size - (x_end - x_start)
                patch = image_norm[y_start:y_end, x_start:x_end]
                patch = np.pad(patch, ((0, pad_h), (0, pad_w)), mode='reflect')

                patch_batch.append(patch)
                coords_batch.append((y_start, y_end, x_start, x_end))

                if len(patch_batch) >= batch_size or (y_start == y_coords[-1] and x_start == x_coords[-1]):
                    # Batch inference
                    patch_tensor = torch.from_numpy(np.stack(patch_batch)[..., None]).permute(0, 3, 1, 2).float().to(device)
                    with autocast('cuda'):
                        outputs = model(patch_tensor)

                    # Extract and stitch
                    for i, (y_start, y_end, x_start, x_end) in enumerate(coords_batch):
                        # Remove padding
                        h, w = y_end - y_start, x_end - x_start

                        if return_all_outputs:
                            semantic_out[y_start:y_end, x_start:x_end] += torch.sigmoid(outputs['sem_out'][i, 0]).cpu().numpy()[:h, :w]
                            skeleton_out[y_start:y_end, x_start:x_end] += torch.sigmoid(outputs['skeleton_out'][i, 0]).cpu().numpy()[:h, :w]
                            centroid_out[y_start:y_end, x_start:x_end] += torch.sigmoid(outputs['hm_out'][i, 0]).cpu().numpy()[:h, :w]
                            sdt_probs_out[:, y_start:y_end, x_start:x_end] += F.softmax(outputs['sdt_out'][i], dim=0).cpu().numpy()[:, :h, :w]
                            flow_out[:, y_start:y_end, x_start:x_end] += outputs['flow_out'][i].cpu().numpy()[:, :h, :w]

                        count_map[y_start:y_end, x_start:x_end] += 1
                    patch_batch = []
                    coords_batch = []
                    pbar.update(len(coords_batch) if not coords_batch else len(coords_batch))

        pbar.close()

    # 7. Normalize by count
    eps = 1e-8
    semantic_out /= (count_map + eps)
    skeleton_out /= (count_map + eps)
    centroid_out /= (count_map + eps)
    sdt_probs_out /= (count_map[None, ...] + eps)
    flow_out /= (count_map[None, ...] + eps)

    # 8. Reconstruct scalar SDT
    bin_centers = (np.arange(11) + 0.5) / 11
    sdt_scalar_out = np.sum(sdt_probs_out * bin_centers[:, None, None], axis=0)

    # 9. Instance Segmentation
    print("Performing instance segmentation...")
    instance_labels, filtered_spots = skeleton_aware_instance_segmentation(
        semantic=semantic_out,
        sdt_scalar=sdt_scalar_out,
        skeleton=skeleton_out,
        centroid_map=centroid_out,
        flow=flow_out,
        min_size=5,
        nms_threshold=0.4,
        flow_refine_factor=0.3
    )

    # 10. Prepare results
    results = {
        'instance_labels': instance_labels,
        'spots': filtered_spots,
        'semantic': semantic_out,
        'sdt_scalar': sdt_scalar_out,
        'skeleton': skeleton_out,
        'centroid': centroid_out,
        'flow': flow_out,
        'sdt_probs': sdt_probs_out if return_all_outputs else None
    }

    print(f"✅ Inference completed for {os.path.basename(image_path)}. Found {len(filtered_spots)} spots.")
    return results

# --- Example Usage: Load Model & Run Inference ---
import os
from pathlib import Path

# 1. Configuration
model_path = '/mnt/d/Users/<USER>/Documents/spot_detector_model_sota_centroid/best_model.pth'
image_path = '/mnt/d/Users/<USER>/FISH_spots/2d/synthetic/images/0  Series001  Green--FLUO--FITC_tile_1.tif'
save_dir = Path(image_path).parent.parent / "inference_results"
save_dir.mkdir(exist_ok=True)

device = 'cuda' if torch.cuda.is_available() else 'cpu'
print(f"Using device: {device}")

# 2. Load Trained Model
print(f"Loading model from: {model_path}")
checkpoint = torch.load(model_path, map_location=device, weights_only=False)
model = SkeletonAwareSpotDetector(in_ch=1, base_ch=48)
model.load_state_dict(checkpoint['model_state_dict'])
model.to(device)
model.eval()

# 3. Run Fast Tiled Inference
print(f"Running inference on: {Path(image_path).name}")
results = tile_inference(
    model=model,
    image_path=image_path,
    device=device,
    patch_size=256,
    overlap=0.5,
    batch_size=8,  # Maximize GPU utilization
    return_all_outputs=True
)

# 4. Print Results
print(f"✅ Inference completed!")
print(f"   Found {len(results['spots'])} spots.")
print(f"   Output keys: {list(results.keys())}")
print(f"   Instance labels shape: {results['instance_labels'].shape}")
print(f"   Semantic mask range: [{results['semantic'].min():.3f}, {results['semantic'].max():.3f}]")

# 5. Save Results (Optional)
base_name = Path(image_path).stem
tifffile.imwrite(save_dir / f"{base_name}_instance_labels.tif", results['instance_labels'].astype(np.uint16))
np.save(save_dir / f"{base_name}_spots.npy", results['spots'])  # Save spot list
print(f"💾 Results saved to: {save_dir}")

# # Cell 9: Complete Pipeline with Updated Paths
# import os
# import glob

# # Set paths
# image_dir = "/mnt/d/Users/<USER>/FISH_spots/2d/synthetic/images/"
# mask_dir = "/mnt/d/Users/<USER>/FISH_spots/2d/synthetic/masks/"
# image_paths = sorted(glob.glob(os.path.join(image_dir, '*.tif')))
# mask_paths = sorted(glob.glob(os.path.join(mask_dir, '*.tif')))

# print(f"Found {len(image_paths)} images and {len(mask_paths)} masks")

# # Verify a few paths
# if image_paths and mask_paths:
#     print("Sample image path:", image_paths[0])
#     print("Sample mask path:", mask_paths[0])
    
#     # Check if corresponding files exist
#     base_name = os.path.splitext(os.path.basename(image_paths[0]))[0]
#     expected_mask = os.path.join(mask_dir, base_name + '.tif')
#     if os.path.exists(expected_mask):
#         print("✓ Matching mask found for first image")
#     else:
#         print("⚠️  Matching mask not found for first image")
        
# # Model output directory
# model_dir = '/mnt/d/Users/<USER>/Documents/spot_detector_model/'
# os.makedirs(model_dir, exist_ok=True)
# print(f"Model will be saved to: {model_dir}")

# # Set device
# device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
# print(f"Using device: {device}")

# # Create data loaders
# print("Creating skeleton-aware data loaders...")
# train_loader, val_loader = create_skeleton_aware_data_loaders(
#     image_paths, mask_paths, 
#     batch_size=12,  # Increased batch size for speed
#     patch_size=256, 
#     num_workers=4  # Reduced workers to avoid overhead
# )
# print(f"Training samples: {len(train_loader.dataset)}")
# print(f"Validation samples: {len(val_loader.dataset)}")

# # Initialize model
# print("Initializing skeleton-aware model...")
# model = SkeletonAwareSpotDetector(in_ch=1, base_ch=48)
# print(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")

# # Train the model
# print("Starting skeleton-aware training...")
# trained_model, train_losses, val_losses = train_skeleton_aware_model_optimized(
#     model, 
#     train_loader, 
#     val_loader, 
#     num_epochs=300,  # Reduced epochs due to faster convergence
#     device=device,
#     model_dir=model_dir
# )

# # Save final model
# final_model_path = os.path.join(model_dir, 'final_model.pth')
# torch.save({
#     'model_state_dict': trained_model.state_dict(),
#     'model_config': {
#         'in_ch': 1,
#         'base_ch': 48
#     },
#     'train_losses': train_losses,
#     'val_losses': val_losses
# }, final_model_path)
# print(f"Final model saved to: {final_model_path}")

# # Evaluate model
# print("Running skeleton-aware evaluation...")
# eval_metrics = evaluate_skeleton_aware_model(trained_model, val_loader, device)
# print(f"Evaluation Results:")
# print(f"  Precision: {eval_metrics['precision']:.3f} ± {eval_metrics['std_precision']:.3f}")
# print(f"  Recall: {eval_metrics['recall']:.3f} ± {eval_metrics['std_recall']:.3f}")
# print(f"  F1-Score: {eval_metrics['f1']:.3f} ± {eval_metrics['std_f1']:.3f}")
# print(f"  Skeleton IoU: {eval_metrics['skeleton_iou']:.3f} ± {eval_metrics['std_skeleton_iou']:.3f}")
# print(f"  SDT MSE: {eval_metrics['sdt_mse']:.5f} ± {eval_metrics['std_sdt_mse']:.5f}")

# # Test inference on sample images
# print("Testing skeleton-aware inference...")
# test_images = image_paths[:3]  # Test on first 3 images
# for i, test_path in enumerate(test_images):
#     print(f"Testing on image {i+1}: {os.path.basename(test_path)}")
#     # Load test image
#     test_image = tifffile.imread(test_path).astype(np.float32)
#     test_image = test_image / (test_image.max() + 1e-8)
#     # Run inference
#     result = skeleton_aware_inference(
#         trained_model, 
#         test_image, 
#         device=device,
#         threshold=0.3,
#         min_distance=3,
#         nms_threshold=0.2
#     )
#     # Visualize results
#     plt.figure(figsize=(20, 10))
#     # Original image with spots
#     plt.subplot(2, 3, 1)
#     plt.imshow(test_image, cmap='gray')
#     if len(result['spots']) > 0:
#         spots_arr = np.array([[spot['y'], spot['x']] for spot in result['spots']])
#         plt.scatter(spots_arr[:, 1], spots_arr[:, 0], c='red', s=30, marker='o')
#     plt.title(f"Detected Spots: {len(result['spots'])}")
#     plt.axis('off')
#     # Semantic mask
#     plt.subplot(2, 3, 2)
#     plt.imshow(result['semantic'], cmap='viridis')
#     plt.title("Semantic Mask")
#     plt.axis('off')
#     # SDT
#     plt.subplot(2, 3, 3)
#     plt.imshow(result['sdt'], cmap='magma')
#     plt.title("Skeleton-Aware Distance Transform")
#     plt.axis('off')
#     # Skeleton
#     plt.subplot(2, 3, 4)
#     plt.imshow(result['skeleton'], cmap='bone')
#     plt.title("Skeleton")
#     plt.axis('off')
#     # Instance segmentation
#     instance_viz = label2rgb(result['instance_labels'], bg_label=0, alpha=0.7)
#     plt.subplot(2, 3, 5)
#     plt.imshow(instance_viz)
#     plt.title("Instance Segmentation")
#     plt.axis('off')
#     # Save visualization
#     plt.tight_layout()
#     plt.savefig(os.path.join(model_dir, f'test_result_{i+1}.png'), dpi=150)
#     plt.close()
#     print(f"  Detected {len(result['spots'])} spots")

# # Memory cleanup
# torch.cuda.empty_cache()
# print("Training completed successfully!")

# Improved fast_accurate_instance_segmentation for all spot sizes

def fast_accurate_instance_segmentation(
    semantic,
    sdt_scalar, 
    skeleton,
    centroid_map,
    flow,
    boundary,
    min_size=1,  # Allow all sizes
    nms_threshold=0.15,  # Lower for medium/large spots
    semantic_threshold=0.15  # Lower threshold
):
    """
    Detects ALL spot sizes - small, medium, and large, with fallback for large/flat spots.
    """
    H, W = semantic.shape
    
    binary_mask = (semantic > semantic_threshold).astype(np.uint8)
    if np.sum(binary_mask) < min_size:
        return np.zeros_like(semantic, dtype=np.int32), []
    
    boundary_mask = boundary > 0.7
    adjusted_centroids = centroid_map * (1.0 - boundary_mask.astype(np.float32) * 0.3)
    
    local_max_3 = maximum_filter(adjusted_centroids, size=3) == adjusted_centroids
    local_max_5 = maximum_filter(adjusted_centroids, size=5) == adjusted_centroids
    local_max_7 = maximum_filter(adjusted_centroids, size=7) == adjusted_centroids
    
    peaks_small = local_max_3 & (adjusted_centroids > nms_threshold * 0.8) & (binary_mask > 0)
    peaks_medium = local_max_5 & (adjusted_centroids > nms_threshold * 0.6) & (binary_mask > 0)
    peaks_large = local_max_7 & (adjusted_centroids > nms_threshold * 0.4) & (binary_mask > 0)
    
    peaks = peaks_small | peaks_medium | peaks_large
    
    if np.sum(peaks) == 0:
        peaks = local_max_3 & (centroid_map > 0.1) & (binary_mask > 0)
    
    centroid_coords = np.column_stack(np.where(peaks))
    # Fallback: ensure every connected component has a seed
    labeled_mask, num = label(binary_mask)
    for region in regionprops(labeled_mask):
        cy, cx = map(int, region.centroid)
        if not any(np.allclose([cy, cx], c, atol=3) for c in centroid_coords):
            centroid_coords = np.vstack([centroid_coords, [cy, cx]]) if len(centroid_coords) else np.array([[cy, cx]])
    if len(centroid_coords) == 0:
        return np.zeros_like(semantic, dtype=np.int32), []
    
    # Adaptive seed dilation
    seeds = np.zeros_like(semantic, dtype=np.int32)
    for idx, (y, x) in enumerate(centroid_coords, 1):
        seeds[y, x] = idx
        dilation = 1
        for dy in range(-dilation, dilation+1):
            for dx in range(-dilation, dilation+1):
                ny, nx = y+dy, x+dx
                if 0 <= ny < H and 0 <= nx < W:
                    seeds[ny, nx] = max(seeds[ny, nx], idx)
    
    sdt_inverted = 1.0 - sdt_scalar
    boundary_barriers = boundary * 0.8
    combined_map = (
        0.6 * sdt_inverted +
        0.2 * boundary_barriers +
        0.2 * (1.0 - skeleton)
    )
    
    instance_labels = watershed(combined_map, seeds, mask=binary_mask)
    
    regions = regionprops(instance_labels)
    final_labels = np.zeros_like(instance_labels)
    final_spots = []
    new_id = 1
    for region in regions:
        if region.area < min_size:
            continue
        mask = (instance_labels == region.label)
        cy, cx = region.centroid
        semantic_score = np.mean(semantic[mask])
        centroid_score = np.mean(centroid_map[mask]) 
        sdt_score = np.mean(sdt_scalar[mask])
        boundary_score = np.mean(boundary[mask])
        combined_score = (
            0.3 * semantic_score + 
            0.3 * centroid_score + 
            0.2 * sdt_score + 
            0.2 * (1.0 - boundary_score)
        )
        if flow is not None and flow.shape[0] == 2:
            ys, xs = np.where(mask)
            if len(ys) > 5:
                avg_flow_y = np.mean(flow[0, ys, xs])
                avg_flow_x = np.mean(flow[1, ys, xs])
                flow_mag = np.sqrt(avg_flow_y**2 + avg_flow_x**2)
                if flow_mag > 0.15:
                    cy += 0.15 * avg_flow_y
                    cx += 0.15 * avg_flow_x
                    cy = np.clip(cy, 0, H-1)
                    cx = np.clip(cx, 0, W-1)
        final_labels[mask] = new_id
        final_spots.append({
            'y': float(cy),
            'x': float(cx), 
            'score': float(combined_score),
            'size': int(region.area),
            'semantic_score': float(semantic_score),
            'centroid_score': float(centroid_score),
            'sdt_score': float(sdt_score),
            'boundary_score': float(boundary_score)
        })
        new_id += 1
    return final_labels, final_spots


# Bypass flow and do not filter any regions by size or score
def fast_accurate_instance_segmentation_nofilter(
    semantic,
    sdt_scalar, 
    skeleton,
    centroid_map,
    flow,
    boundary,
    min_size=1,  # Allow all sizes
    nms_threshold=0.05,  # Very permissive
    semantic_threshold=0.05  # Very permissive
):
    H, W = semantic.shape
    from scipy.ndimage import maximum_filter, label
    from skimage.measure import regionprops
    from skimage.segmentation import watershed
    # Binary mask
    binary_mask = (semantic > semantic_threshold).astype(np.uint8)
    if np.sum(binary_mask) < 1:
        return np.zeros_like(semantic, dtype=np.int32), []
    # Centroid detection (multi-scale, permissive)
    boundary_mask = boundary > 0.7
    adjusted_centroids = centroid_map * (1.0 - boundary_mask.astype(np.float32) * 0.3)
    local_max_3 = maximum_filter(adjusted_centroids, size=3) == adjusted_centroids
    local_max_5 = maximum_filter(adjusted_centroids, size=5) == adjusted_centroids
    local_max_7 = maximum_filter(adjusted_centroids, size=7) == adjusted_centroids
    peaks = (local_max_3 | local_max_5 | local_max_7) & (adjusted_centroids > nms_threshold) & (binary_mask > 0)
    if np.sum(peaks) == 0:
        peaks = local_max_3 & (centroid_map > 0.01) & (binary_mask > 0)
    centroid_coords = np.column_stack(np.where(peaks))
    # Fallback: ensure every connected component has a centroid
    labeled_mask, num = label(binary_mask)
    for region in regionprops(labeled_mask):
        cy, cx = map(int, region.centroid)
        if not any(np.allclose([cy, cx], c, atol=3) for c in centroid_coords):
            centroid_coords = np.vstack([centroid_coords, [cy, cx]]) if len(centroid_coords) else np.array([[cy, cx]])
    if len(centroid_coords) == 0:
        return np.zeros_like(semantic, dtype=np.int32), []
    # Seeds (minimal dilation)
    seeds = np.zeros_like(semantic, dtype=np.int32)
    for idx, (y, x) in enumerate(centroid_coords, 1):
        seeds[y, x] = idx
        for dy in range(-1, 2):
            for dx in range(-1, 2):
                ny, nx = y+dy, x+dx
                if 0 <= ny < H and 0 <= nx < W:
                    seeds[ny, nx] = max(seeds[ny, nx], idx)
    # Watershed
    sdt_inverted = 1.0 - sdt_scalar
    boundary_barriers = boundary * 0.8
    combined_map = (
        0.6 * sdt_inverted +
        0.2 * boundary_barriers +
        0.2 * (1.0 - skeleton)
    )
    instance_labels = watershed(combined_map, seeds, mask=binary_mask)
    # No filtering: keep all regions
    regions = regionprops(instance_labels)
    final_labels = np.zeros_like(instance_labels)
    final_spots = []
    new_id = 1
    for region in regions:
        mask = (instance_labels == region.label)
        cy, cx = region.centroid
        semantic_score = np.mean(semantic[mask])
        centroid_score = np.mean(centroid_map[mask]) 
        sdt_score = np.mean(sdt_scalar[mask])
        boundary_score = np.mean(boundary[mask])
        combined_score = (
            0.3 * semantic_score + 
            0.3 * centroid_score + 
            0.2 * sdt_score + 
            0.2 * (1.0 - boundary_score)
        )
        final_labels[mask] = new_id
        final_spots.append({
            'y': float(cy),
            'x': float(cx), 
            'score': float(combined_score),
            'size': int(region.area),
            'semantic_score': float(semantic_score),
            'centroid_score': float(centroid_score),
            'sdt_score': float(sdt_score),
            'boundary_score': float(boundary_score)
        })
        new_id += 1
    return final_labels, final_spots




def ultra_fast_tile_inference(
    model,
    image_path, 
    device='cuda',
    patch_size=256,
    overlap=0.2,
    batch_size=16,
    min_size=1,
    sdt_seed_threshold=0.5,
    semantic_threshold=0.01,
    use_flow=False,  # New argument: if True, use flow-based instance segmentation
    flow_steps=10,
    flow_step_size=1.0,
    cluster_eps=3
):
    """
    Ultra-fast tiled inference optimized for speed + accuracy.
    If use_flow is True, uses flow-based instance segmentation (DBSCAN on flow endpoints).
    Otherwise, uses SDT local maxima as seeds for watershed.
    """
    import tifffile
    import numpy as np
    import torch
    from tqdm import tqdm
    from sklearn.cluster import DBSCAN
    from skimage.measure import regionprops
    from skimage.segmentation import watershed
    image = tifffile.imread(image_path).astype(np.float32)
    H, W = image.shape
    vmin, vmax = np.percentile(image[image > 0], [2, 98])
    image_norm = np.clip((image - vmin) / (vmax - vmin + 1e-8), 0, 1)
    model.eval()
    model.to(device)
    stride = max(int(patch_size * (1 - overlap)), 32)
    y_coords = list(range(0, H, stride))
    x_coords = list(range(0, W, stride))
    if y_coords[-1] + patch_size < H:
        y_coords.append(H - patch_size)
    if x_coords[-1] + patch_size < W:
        x_coords.append(W - patch_size)
    total_patches = len(y_coords) * len(x_coords)
    print(f"Ultra-fast inference: {total_patches} patches, stride={stride}, batch={batch_size}")
    semantic_sum = np.empty((H, W), dtype=np.float32); semantic_sum.fill(0)
    sdt_sum = np.empty((H, W), dtype=np.float32); sdt_sum.fill(0)
    skeleton_sum = np.empty((H, W), dtype=np.float32); skeleton_sum.fill(0)
    centroid_sum = np.empty((H, W), dtype=np.float32); centroid_sum.fill(0)
    boundary_sum = np.empty((H, W), dtype=np.float32); boundary_sum.fill(0)
    flow_sum = np.empty((2, H, W), dtype=np.float32); flow_sum.fill(0)
    count_map = np.empty((H, W), dtype=np.float32); count_map.fill(0)
    with torch.no_grad():
        patches = []
        coords = []
        processed = 0
        pbar = tqdm(total=total_patches, desc="Ultra-Fast Inference")
        for y_start in y_coords:
            for x_start in x_coords:
                y_end = min(y_start + patch_size, H)
                x_end = min(x_start + patch_size, W)
                patch = image_norm[y_start:y_end, x_start:x_end]
                if patch.shape[0] < patch_size or patch.shape[1] < patch_size:
                    pad_h = patch_size - patch.shape[0]
                    pad_w = patch_size - patch.shape[1]
                    patch = np.pad(patch, ((0, pad_h), (0, pad_w)), mode='constant')
                patches.append(patch)
                coords.append((y_start, y_end, x_start, x_end))
                if len(patches) >= batch_size or processed + len(patches) == total_patches:
                    batch_tensor = torch.from_numpy(np.stack(patches)[:, None]).float().to(device)
                    with torch.amp.autocast(device_type='cuda', enabled=True):
                        outputs = model(batch_tensor)
                    for i, (y_s, y_e, x_s, x_e) in enumerate(coords):
                        h, w = y_e - y_s, x_e - x_s
                        sem = torch.sigmoid(outputs['semantic'][i, 0, :h, :w]).cpu().numpy()
                        sdt = torch.sigmoid(outputs['sdt'][i, 0, :h, :w]).cpu().numpy()
                        skel = torch.sigmoid(outputs['skeleton'][i, 0, :h, :w]).cpu().numpy()
                        cent = torch.sigmoid(outputs['centroid'][i, 0, :h, :w]).cpu().numpy()
                        bound = torch.sigmoid(outputs['boundary'][i, 0, :h, :w]).cpu().numpy()
                        flow = torch.tanh(outputs['flow'][i, :, :h, :w]).cpu().numpy()
                        semantic_sum[y_s:y_e, x_s:x_e] += sem
                        sdt_sum[y_s:y_e, x_s:x_e] += sdt
                        skeleton_sum[y_s:y_e, x_s:x_e] += skel
                        centroid_sum[y_s:y_e, x_s:x_e] += cent
                        boundary_sum[y_s:y_e, x_s:x_e] += bound
                        flow_sum[:, y_s:y_e, x_s:x_e] += flow
                        count_map[y_s:y_e, x_s:x_e] += 1
                    processed += len(patches)
                    pbar.update(len(patches))
                    patches.clear()
                    coords.clear()
        pbar.close()
    count_map_safe = np.maximum(count_map, 1)
    semantic_out = semantic_sum / count_map_safe
    sdt_out = sdt_sum / count_map_safe
    skeleton_out = skeleton_sum / count_map_safe
    centroid_out = centroid_sum / count_map_safe
    boundary_out = boundary_sum / count_map_safe
    flow_out = flow_sum / count_map_safe[None, :, :]
    print(f"Fast inference stats: sem[{semantic_out.min():.2f},{semantic_out.max():.2f}], "
          f"cent[{centroid_out.min():.2f},{centroid_out.max():.2f}]")
    if use_flow:
        # Flow-based instance segmentation (DBSCAN on flow endpoints)
        H, W = semantic_out.shape
        binary_mask = (semantic_out > semantic_threshold).astype(np.uint8)
        ys, xs = np.where(binary_mask > 0)
        coords_fg = np.stack([ys, xs], axis=1).astype(np.float32)
        flow_y, flow_x = flow_out[0], flow_out[1]
        pos = coords_fg.copy()
        for _ in range(flow_steps):
            iy = np.clip(np.round(pos[:, 0]).astype(int), 0, H-1)
            ix = np.clip(np.round(pos[:, 1]).astype(int), 0, W-1)
            pos[:, 0] += flow_y[iy, ix] * flow_step_size
            pos[:, 1] += flow_x[iy, ix] * flow_step_size
        clustering = DBSCAN(eps=cluster_eps, min_samples=1).fit(pos)
        labels = clustering.labels_ + 1
        instance_labels = np.zeros_like(semantic_out, dtype=np.int32)
        instance_labels[ys, xs] = labels
        # Extract spot info
        regions = regionprops(instance_labels)
        filtered_spots = []
        for region in regions:
            if region.label == 0 or region.area < min_size:
                continue
            mask = (instance_labels == region.label)
            cy, cx = region.centroid
            semantic_score = np.mean(semantic_out[mask])
            centroid_score = np.mean(centroid_out[mask])
            sdt_score = np.mean(sdt_out[mask])
            boundary_score = np.mean(boundary_out[mask])
            score = (
                0.3 * semantic_score +
                0.3 * centroid_score +
                0.2 * sdt_score +
                0.2 * (1.0 - boundary_score)
            )
            filtered_spots.append({
                'y': float(cy),
                'x': float(cx),
                'score': float(score),
                'size': int(region.area),
                'semantic_score': float(semantic_score),
                'centroid_score': float(centroid_score),
                'sdt_score': float(sdt_score),
                'boundary_score': float(boundary_score)
            })
    else:
        # SDT-based instance segmentation (default)
        instance_labels, filtered_spots = fast_accurate_instance_segmentation(
            semantic=semantic_out,
            sdt_scalar=sdt_out,
            skeleton=skeleton_out,
            centroid_map=centroid_out,
            flow=flow_out,
            boundary=boundary_out,
            min_size=min_size,
            sdt_seed_threshold=sdt_seed_threshold,
            semantic_threshold=semantic_threshold
        )
    results = {
        'instance_labels': instance_labels,
        'spots': filtered_spots,
        'semantic': semantic_out,
        'sdt': sdt_out,
        'skeleton': skeleton_out,
        'centroid': centroid_out,
        'boundary': boundary_out,
        'flow': flow_out
    }
    display_all_outputs(results, image_path)
    print(f"⚡ Ultra-fast inference: Found {len(filtered_spots)} spots in {os.path.basename(image_path)}")
    return results


# Visualize flow magnitude and direction from model output
import matplotlib.pyplot as plt
import numpy as np

def show_flow_quiver(flow, background=None, step=8, scale=1, title='Flow Field Quiver'):
    """Show flow field as quiver plot over optional background image."""
    flow_y, flow_x = flow[0], flow[1]
    H, W = flow_y.shape
    Y, X = np.mgrid[0:H, 0:W]
    if background is not None:
        plt.imshow(background, cmap='gray', alpha=0.7)
    plt.quiver(X[::step, ::step], Y[::step, ::step],
               flow_x[::step, ::step], flow_y[::step, ::step],
               np.sqrt(flow_x[::step, ::step]**2 + flow_y[::step, ::step]**2),
               angles='xy', scale_units='xy', scale=scale, cmap='cool', width=0.003)
    plt.title(title)
    plt.axis('off')
    plt.colorbar(label='Flow Magnitude')
    plt.show()

# Example usage after inference:
if 'results' in locals():
    flow = results['flow']
    image = tifffile.imread(image_path).astype(np.float32)
    vmin, vmax = np.percentile(image[image > 0], [2, 98])
    image_norm = np.clip((image - vmin) / (vmax - vmin + 1e-8), 0, 1)
    show_flow_quiver(flow, background=image_norm, step=8, scale=0.5, title='Model Output Flow Field')
# Add instance segmentation to inference

def inference_with_instance_segmentation(model, image, device='cuda', nms_threshold=0.3):
    """
    Inference with individual spot instance masks
    """
    from skimage.segmentation import watershed
    from skimage.feature import peak_local_max
    
    model.eval()
    with torch.no_grad():
        # ... existing code for image preprocessing ...
        
        outputs = model(image)
        sem = torch.sigmoid(outputs['semantic'])[0, 0].cpu().numpy()
        cmap = torch.sigmoid(outputs['heatmaps'])[0, 1].cpu().numpy()
        dist = torch.sigmoid(outputs['distance'])[0, 0].cpu().numpy()
        
        # Extract spot coordinates
        coordinates = peak_local_max(cmap, min_distance=2, threshold_abs=0.3)
        
        if len(coordinates) == 0:
            return {'spots': [], 'instance_masks': [], 'semantic': sem}
        
        # Create markers for watershed
        markers = np.zeros_like(sem, dtype=int)
        for i, (y, x) in enumerate(coordinates):
            markers[y, x] = i + 1
        
        # Watershed segmentation using distance transform
        instance_labels = watershed(-dist, markers, mask=sem > 0.5)
        
        # Extract individual instance masks
        instance_masks = []
        spots = []
        
        for i, (y, x) in enumerate(coordinates):
            label_id = i + 1
            instance_mask = (instance_labels == label_id).astype(np.uint8)
            
            if instance_mask.sum() > 0:  # Valid instance
                instance_masks.append(instance_mask)
                score = cmap[y, x]
                spots.append([y, x, score])
        
        return {
            'spots': np.array(spots),           # [y, x, score] coordinates
            'instance_masks': instance_masks,   # List of individual spot masks
            'instance_labels': instance_labels, # Full labeled image
            'semantic': sem,
            'centroid_map': cmap
        }
# Add this cell to compare the original vs simplified dataloader

# Copy the SimpleSpotDataset class from above, then run this:

def compare_dataloaders():
    """Compare original vs simplified dataloader"""
    
    # Test paths
    image_paths = [
        "/mnt/d/Users/<USER>/FISH_spots/2d/image_raw/large spots/images/synthetic_000596.tif",
        "/mnt/d/Users/<USER>/FISH_spots/2d/image_raw/large spots/images/synthetic_000597.tif"
    ]
    
    mask_paths = [
        "/mnt/d/Users/<USER>/FISH_spots/2d/image_raw/large spots/masks/synthetic_000596.tif",
        "/mnt/d/Users/<USER>/FISH_spots/2d/image_raw/large spots/masks/synthetic_000597.tif"
    ]
    
    # Create both datasets
    original_dataset = AdaptiveSpotDataset(
        image_paths=image_paths,
        mask_paths=mask_paths,
        transform=None,
        patch_size=128
    )
    
    simple_dataset = SimpleSpotDataset(
        image_paths=image_paths,
        mask_paths=mask_paths,
        transform=None,
        patch_size=128
    )
    
    # Compare first sample
    idx = 0
    print(f"Comparing sample {idx}...")
    
    # Get outputs from both
    orig_img, orig_mask, orig_flow, orig_conf = original_dataset[idx]
    simp_img, simp_mask, simp_flow, simp_conf = simple_dataset[idx]
    
    # Convert to numpy
    orig_img_np = orig_img[0].numpy()
    orig_masks_np = orig_mask.numpy()
    simp_img_np = simp_img[0].numpy()
    simp_masks_np = simp_mask.numpy()
    
    # Create comparison plot
    fig, axes = plt.subplots(3, 6, figsize=(24, 12))
    
    titles = ['Input', 'Semantic', 'Boundary', 'Distance', 'Instance', 'Centroid']
    
    # Row 1: Original dataloader
    axes[0, 0].imshow(orig_img_np, cmap='gray')
    axes[0, 0].set_title('Original: Input')
    axes[0, 0].axis('off')
    
    for i in range(5):
        axes[0, i+1].imshow(orig_masks_np[i], cmap='hot' if i == 4 else 'gray')
        axes[0, i+1].set_title(f'Original: {titles[i+1]}')
        axes[0, i+1].axis('off')
    
    # Row 2: Simple dataloader
    axes[1, 0].imshow(simp_img_np, cmap='gray')
    axes[1, 0].set_title('Simple: Input')
    axes[1, 0].axis('off')
    
    for i in range(5):
        axes[1, i+1].imshow(simp_masks_np[i], cmap='hot' if i == 4 else 'gray')
        axes[1, i+1].set_title(f'Simple: {titles[i+1]}')
        axes[1, i+1].axis('off')
    
    # Row 3: Differences
    img_diff = np.abs(orig_img_np - simp_img_np)
    axes[2, 0].imshow(img_diff, cmap='hot')
    axes[2, 0].set_title(f'Img Diff (max: {img_diff.max():.3f})')
    axes[2, 0].axis('off')
    
    for i in range(5):
        mask_diff = np.abs(orig_masks_np[i] - simp_masks_np[i])
        axes[2, i+1].imshow(mask_diff, cmap='hot')
        axes[2, i+1].set_title(f'{titles[i+1]} Diff (max: {mask_diff.max():.3f})')
        axes[2, i+1].axis('off')
    
    plt.tight_layout()
    plt.show()
    
    # Print statistics
    print("\n=== Comparison Statistics ===")
    print(f"Image difference: max={img_diff.max():.4f}, mean={img_diff.mean():.4f}")
    
    for i, name in enumerate(['Semantic', 'Boundary', 'Distance', 'Instance', 'Centroid']):
        diff = np.abs(orig_masks_np[i] - simp_masks_np[i])
        print(f"{name}: max_diff={diff.max():.4f}, mean_diff={diff.mean():.4f}")
        print(f"  Original: pixels={orig_masks_np[i].sum():.0f}, max={orig_masks_np[i].max():.3f}")
        print(f"  Simple:   pixels={simp_masks_np[i].sum():.0f}, max={simp_masks_np[i].max():.3f}")

# Run comparison
compare_dataloaders()
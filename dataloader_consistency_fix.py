# Replace these lines in your dataloader:

# BEFORE (inconsistent):
patch_msk_eroded = self._apply_erosion_to_mask(patch_msk, kernel_size=2, iterations=1)
sem = (patch_msk_eroded > 0).astype(np.float32)

# Generate boundary mask
bnd = np.zeros_like(sem, dtype=np.float32)
for iid in np.unique(patch_msk):  # ❌ WRONG - uses original
    if iid == 0: continue
    im = (patch_msk == iid).astype(np.uint8)  # ❌ WRONG

distance_map = self._generate_distance_transform(patch_msk)  # ❌ WRONG
flow_field = self._generate_flow_field(patch_msk, centroids)  # ❌ WRONG

# AFTER (consistent):
patch_msk_eroded = self._apply_erosion_to_mask(patch_msk, kernel_size=2, iterations=1)
sem = (patch_msk_eroded > 0).astype(np.float32)

# Generate boundary mask using ERODED mask
bnd = np.zeros_like(sem, dtype=np.float32)
for iid in np.unique(patch_msk_eroded):  # ✅ FIXED - uses eroded
    if iid == 0: continue
    im = (patch_msk_eroded == iid).astype(np.uint8)  # ✅ FIXED
    kernel = np.ones((2,2), np.uint8)
    er = cv2.erode(im, kernel, iterations=1)
    bnd += ((im - er) > 0).astype(np.float32)
bnd = np.clip(bnd, 0, 1)

distance_map = self._generate_distance_transform(patch_msk_eroded)  # ✅ FIXED
flow_field = self._generate_flow_field(patch_msk_eroded, centroids)  # ✅ FIXED
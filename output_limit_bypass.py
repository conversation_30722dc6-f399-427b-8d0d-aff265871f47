# Add this at the start of your notebook cell to bypass output limits

import sys
from IPython.core.interactiveshell import InteractiveShell

# Remove output limit
InteractiveShell.ast_node_interactivity = "all"
sys.stdout.flush = lambda: None

# Alternative: Set higher limit
from IPython.core.display import display, HTML
display(HTML("<style>.output_result { max-height: none !important; }</style>"))

# For specific cells, use this magic command:
# %config IPCompleter.limit_to__all__ = False

# Or suppress specific outputs:
import warnings
warnings.filterwarnings('ignore')

# Redirect verbose outputs to file instead of notebook
import contextlib
import io

@contextlib.contextmanager
def suppress_stdout():
    with contextlib.redirect_stdout(io.StringIO()):
        yield

# Usage:
# with suppress_stdout():
#     your_verbose_code_here()
import torch
import numpy as np
import tifffile
from pathlib import Path
import json

def extract_model_outputs(
    model_path: str,
    image_path: str,
    output_dir: str,
    model_class_name: str = None
):
    """
    Extract and save all raw model outputs without any post-processing
    """
    # Create output directory
    output_dir = Path(output_dir)
    output_dir.mkdir(exist_ok=True, parents=True)
    
    # Load model
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # Import model class dynamically
    if model_class_name:
        import importlib
        module_name = model_class_name.split('.')[0]
        class_name = model_class_name.split('.')[-1]
        module = importlib.import_module(module_name)
        ModelClass = getattr(module, class_name)
        model = ModelClass()
    else:
        # Try to find model class in common locations
        try:
            from AdaptiveSpotDetector import AdaptiveSpotDetector
            model = AdaptiveSpotDetector()
        except ImportError:
            try:
                from OptimizedSpotDetection_model import OptimizedSpotDetectionModel
                model = OptimizedSpotDetectionModel()
            except ImportError:
                raise ImportError("Could not find model class. Please specify model_class_name.")
    
    # Load model weights
    checkpoint = torch.load(model_path, map_location=device)
    if 'model_state_dict' in checkpoint:
        model.load_state_dict(checkpoint['model_state_dict'])
    else:
        model.load_state_dict(checkpoint)
    
    model.to(device)
    model.eval()
    
    # Load image
    image = tifffile.imread(image_path).astype(np.float32)
    if len(image.shape) == 3:
        image = image[:, :, 0] if image.shape[2] > 1 else image.squeeze()
    
    # Save original image
    tifffile.imwrite(output_dir / 'original_image.tif', image)
    
    # Normalize image
    image_norm = image / (image.max() + 1e-8)
    
    # Convert to tensor
    image_tensor = torch.from_numpy(image_norm).unsqueeze(0).unsqueeze(0).to(device)
    
    # Run inference
    with torch.no_grad():
        outputs = model(image_tensor)
    
    # Save all outputs
    output_info = {}
    
    for key, value in outputs.items():
        if isinstance(value, torch.Tensor):
            # Convert tensor to numpy
            output_np = value.cpu().numpy()
            
            # Save shape and dtype info
            output_info[key] = {
                'shape': list(output_np.shape),
                'dtype': str(output_np.dtype),
                'min': float(output_np.min()),
                'max': float(output_np.max()),
                'mean': float(output_np.mean())
            }
            
            # Save as NPY file
            np.save(output_dir / f"{key}.npy", output_np)
            
            # For visualization, save first channel as TIFF
            if output_np.ndim >= 4:  # [B, C, H, W] or more
                if key in ['heatmap', 'semantic', 'boundary']:
                    # Apply sigmoid for these outputs
                    vis_data = torch.sigmoid(value[0, 0]).cpu().numpy()
                else:
                    vis_data = output_np[0, 0]
                
                # Normalize for visualization
                if vis_data.min() != vis_data.max():
                    vis_data = (vis_data - vis_data.min()) / (vis_data.max() - vis_data.min())
                
                tifffile.imwrite(output_dir / f"{key}.tif", (vis_data * 255).astype(np.uint8))
        
        elif isinstance(value, list) and all(isinstance(item, torch.Tensor) for item in value):
            # Handle lists of tensors
            output_info[key] = []
            for i, tensor in enumerate(value):
                tensor_np = tensor.cpu().numpy()
                output_info[key].append({
                    'shape': list(tensor_np.shape),
                    'dtype': str(tensor_np.dtype),
                    'min': float(tensor_np.min()),
                    'max': float(tensor_np.max()),
                    'mean': float(tensor_np.mean())
                })
                np.save(output_dir / f"{key}_{i}.npy", tensor_np)
    
    # Save output info
    with open(output_dir / 'output_info.json', 'w') as f:
        json.dump(output_info, f, indent=2)
    
    print(f"All raw model outputs saved to {output_dir}")
    print(f"Available outputs: {list(outputs.keys())}")
    
    return outputs

if __name__ == "__main__":
    import argparse
    parser = argparse.ArgumentParser(description='Extract all raw model outputs')
    parser.add_argument('--model', type=str, required=True, help='Path to model weights')
    parser.add_argument('--image', type=str, required=True, help='Path to input image')
    parser.add_argument('--output', type=str, required=True, help='Output directory')
    parser.add_argument('--model_class', type=str, help='Model class name (e.g., "module.ClassName")')
    args = parser.parse_args()
    
    extract_model_outputs(
        args.model, 
        args.image, 
        args.output,
        args.model_class
    )
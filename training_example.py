import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import os
import matplotlib.pyplot as plt
from improved_trainer import ImprovedSpotTrainer
from enhanced_spot_loss import EnhancedSpotLoss
from enhanced_metrics import EnhancedSpotMetrics
from predict_fixed import SpotDetectionPredictor

# Assuming your model and data loaders are already defined
# model = YourSpotDetectionModel(...)
# train_loader = DataLoader(...)
# val_loader = DataLoader(...)

def train_with_improved_optimizer(
    model,
    train_loader,
    val_loader,
    device,
    num_epochs=200,
    learning_rate=0.0001,
    weight_decay=0.01,
    save_dir='./training_visualizations'
):
    """
    Train the model with improved AdamW optimizer and visualizations
    
    Args:
        model: The model to train
        train_loader: Training data loader
        val_loader: Validation data loader
        device: Device to use for training
        num_epochs: Number of epochs to train
        learning_rate: Learning rate for optimizer
        weight_decay: Weight decay for optimizer
        save_dir: Directory to save visualizations
    """
    # Create save directory if it doesn't exist
    os.makedirs(save_dir, exist_ok=True)
    
    # Initialize loss function
    loss_fn = EnhancedSpotLoss(
        bce_weight=1.0,
        dice_weight=1.0,
        focal_weight=0.5,
        focal_gamma=2.0,
        size_adaptive=True,
        density_aware=True,
        confidence_weighted=True,
        loss_scale=10.0
    )
    
    # Initialize metrics calculator
    metrics_calculator = EnhancedSpotMetrics(threshold=0.3, iou_threshold=0.3)
    
    # Initialize optimizer - AdamW instead of Adam
    optimizer = optim.AdamW(
        model.parameters(),
        lr=learning_rate,
        weight_decay=weight_decay,
        betas=(0.9, 0.999),
        eps=1e-8
    )
    
    # Initialize learning rate scheduler
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(
        optimizer,
        mode='min',
        factor=0.5,
        patience=5,
        verbose=True
    )
    
    # Initialize trainer
    trainer = ImprovedSpotTrainer(
        model=model,
        loss_fn=loss_fn,
        optimizer=optimizer,
        device=device,
        metrics_calculator=metrics_calculator,
        scheduler=scheduler,
        save_dir=save_dir
    )
    
    # Train model
    history = trainer.train(
        train_loader=train_loader,
        val_loader=val_loader,
        num_epochs=num_epochs,
        early_stopping_patience=10,
        save_best_model=True,
        model_save_path=os.path.join(save_dir, 'best_model.pth')
    )
    
    # Plot training history
    trainer.plot_history()
    
    # Create a GIF from the saved visualizations (first 100 epochs)
    try:
        import imageio
        import glob
        
        # Get all visualization images
        images = []
        for filename in sorted(glob.glob(f"{save_dir}/epoch_*.png")):
            if 'spots' not in filename:  # Skip the spot visualization images
                images.append(imageio.imread(filename))
                
        # Create GIF
        if images:
            imageio.mimsave(f"{save_dir}/training_progress.gif", images, duration=0.5)
            print(f"Created training progress GIF at {save_dir}/training_progress.gif")
    except Exception as e:
        print(f"Could not create GIF: {e}")
    
    return trainer, history

# Example usage:
# trainer, history = train_with_improved_optimizer(model, train_loader, val_loader, device)
import torch
import numpy as np
import matplotlib.pyplot as plt
import tifffile
from pathlib import Path
import cv2

def visualize_all_heads(
    model_path: str,
    image_path: str,
    output_dir: str,
    patch_size: int = 256,
    overlap: int = 32
):
    """
    Visualize all model output heads before any post-processing
    """
    # Create output directory
    output_dir = Path(output_dir)
    output_dir.mkdir(exist_ok=True, parents=True)
    
    # Load model
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # Load model weights
    checkpoint = torch.load(model_path, map_location=device)
    
    # Load image
    image = tifffile.imread(image_path).astype(np.float32)
    if len(image.shape) == 3:
        image = image[:, :, 0] if image.shape[2] > 1 else image.squeeze()
    
    # Save original image
    tifffile.imwrite(output_dir / 'original_image.tif', image)
    
    # Normalize image
    image_norm = image / (image.max() + 1e-8)
    H, W = image_norm.shape
    
    # Tiling parameters
    stride = patch_size - overlap
    n_h = (H + stride - 1) // stride
    n_w = (W + stride - 1) // stride
    
    # Initialize output arrays
    heatmap_full = np.zeros((H, W), dtype=np.float32)
    semantic_full = np.zeros((H, W), dtype=np.float32)
    boundary_full = np.zeros((H, W), dtype=np.float32)
    distance_full = np.zeros((H, W), dtype=np.float32)
    flow_full = np.zeros((2, H, W), dtype=np.float32)
    weight_map = np.zeros((H, W), dtype=np.float32)
    
    # Set model to evaluation mode
    model.eval()
    model.to(device)
    
    # Process image in tiles
    with torch.no_grad():
        for i in range(n_h):
            for j in range(n_w):
                # Extract tile coordinates
                start_h = i * stride
                start_w = j * stride
                end_h = min(start_h + patch_size, H)
                end_w = min(start_w + patch_size, W)
                
                # Extract and pad tile if needed
                patch = image_norm[start_h:end_h, start_w:end_w]
                pad_h = patch_size - patch.shape[0]
                pad_w = patch_size - patch.shape[1]
                if pad_h > 0 or pad_w > 0:
                    patch = np.pad(patch, ((0, pad_h), (0, pad_w)), 'reflect')
                
                # Convert to tensor and run inference
                patch_tensor = torch.from_numpy(patch).unsqueeze(0).unsqueeze(0).to(device)
                outputs = model(patch_tensor)
                
                # Extract all outputs
                heatmap = torch.sigmoid(outputs['heatmap']).cpu().numpy()[0, 0]
                semantic = torch.sigmoid(outputs['semantic']).cpu().numpy()[0, 0] if 'semantic' in outputs else None
                boundary = torch.sigmoid(outputs['boundary']).cpu().numpy()[0, 0] if 'boundary' in outputs else None
                distance = outputs['distance'].cpu().numpy()[0, 0] if 'distance' in outputs else None
                flow = outputs['flow'].cpu().numpy()[0] if 'flow' in outputs else None
                
                # Remove padding if needed
                if pad_h > 0 or pad_w > 0:
                    heatmap = heatmap[:patch_size-pad_h, :patch_size-pad_w]
                    if semantic is not None:
                        semantic = semantic[:patch_size-pad_h, :patch_size-pad_w]
                    if boundary is not None:
                        boundary = boundary[:patch_size-pad_h, :patch_size-pad_w]
                    if distance is not None:
                        distance = distance[:patch_size-pad_h, :patch_size-pad_w]
                    if flow is not None:
                        flow = flow[:, :patch_size-pad_h, :patch_size-pad_w]
                
                # Create weight map for blending
                weight = np.ones_like(heatmap)
                if overlap > 0:
                    # Taper edges for smooth blending
                    taper = min(overlap // 2, 16)
                    if start_h > 0:
                        weight[:taper, :] *= np.linspace(0, 1, taper)[:, None]
                    if end_h < H:
                        weight[-taper:, :] *= np.linspace(1, 0, taper)[:, None]
                    if start_w > 0:
                        weight[:, :taper] *= np.linspace(0, 1, taper)[None, :]
                    if end_w < W:
                        weight[:, -taper:] *= np.linspace(1, 0, taper)[None, :]
                
                # Accumulate weighted results
                heatmap_full[start_h:end_h, start_w:end_w] += heatmap * weight
                weight_map[start_h:end_h, start_w:end_w] += weight
                
                if semantic is not None:
                    semantic_full[start_h:end_h, start_w:end_w] += semantic * weight
                if boundary is not None:
                    boundary_full[start_h:end_h, start_w:end_w] += boundary * weight
                if distance is not None:
                    distance_full[start_h:end_h, start_w:end_w] += distance * weight
                if flow is not None:
                    flow_full[:, start_h:end_h, start_w:end_w] += flow * weight[None, :, :]
    
    # Normalize by weights
    weight_map = np.maximum(weight_map, 1e-8)
    heatmap_full /= weight_map
    if semantic_full.any():
        semantic_full /= weight_map
    if boundary_full.any():
        boundary_full /= weight_map
    if distance_full.any():
        distance_full /= weight_map
    if flow_full.any():
        flow_full[0] /= weight_map
        flow_full[1] /= weight_map
    
    # Save all outputs as TIFF files
    tifffile.imwrite(output_dir / 'heatmap.tif', (heatmap_full * 255).astype(np.uint8))
    if semantic_full.any():
        tifffile.imwrite(output_dir / 'semantic.tif', (semantic_full * 255).astype(np.uint8))
    if boundary_full.any():
        tifffile.imwrite(output_dir / 'boundary.tif', (boundary_full * 255).astype(np.uint8))
    if distance_full.any():
        # Normalize distance map for visualization
        dist_norm = distance_full / (distance_full.max() + 1e-8)
        tifffile.imwrite(output_dir / 'distance.tif', (dist_norm * 255).astype(np.uint8))
    if flow_full.any():
        # Convert flow to RGB visualization
        flow_rgb = flow_to_rgb(flow_full)
        tifffile.imwrite(output_dir / 'flow_rgb.tif', flow_rgb)
    
    # Create visualization
    plt.figure(figsize=(15, 10))
    
    plt.subplot(2, 3, 1)
    plt.imshow(image_norm, cmap='gray')
    plt.title('Original Image')
    plt.axis('off')
    
    plt.subplot(2, 3, 2)
    plt.imshow(heatmap_full, cmap='hot')
    plt.title('Heatmap')
    plt.axis('off')
    
    if semantic_full.any():
        plt.subplot(2, 3, 3)
        plt.imshow(semantic_full, cmap='viridis')
        plt.title('Semantic')
        plt.axis('off')
    
    if boundary_full.any():
        plt.subplot(2, 3, 4)
        plt.imshow(boundary_full, cmap='magma')
        plt.title('Boundary')
        plt.axis('off')
    
    if distance_full.any():
        plt.subplot(2, 3, 5)
        plt.imshow(dist_norm, cmap='plasma')
        plt.title('Distance')
        plt.axis('off')
    
    if flow_full.any():
        plt.subplot(2, 3, 6)
        plt.imshow(flow_rgb)
        plt.title('Flow')
        plt.axis('off')
    
    plt.tight_layout()
    plt.savefig(output_dir / 'all_heads.png', dpi=150)
    plt.close()
    
    # Create binary masks from each head
    heatmap_binary = (heatmap_full > 0.5).astype(np.uint8) * 255
    semantic_binary = (semantic_full > 0.5).astype(np.uint8) * 255 if semantic_full.any() else None
    boundary_binary = (boundary_full > 0.5).astype(np.uint8) * 255 if boundary_full.any() else None
    
    # Save binary masks
    tifffile.imwrite(output_dir / 'heatmap_binary.tif', heatmap_binary)
    if semantic_binary is not None:
        tifffile.imwrite(output_dir / 'semantic_binary.tif', semantic_binary)
    if boundary_binary is not None:
        tifffile.imwrite(output_dir / 'boundary_binary.tif', boundary_binary)
    
    # Create visualization of binary masks
    plt.figure(figsize=(15, 5))
    
    plt.subplot(1, 3, 1)
    plt.imshow(heatmap_binary, cmap='gray')
    plt.title('Heatmap Binary')
    plt.axis('off')
    
    if semantic_binary is not None:
        plt.subplot(1, 3, 2)
        plt.imshow(semantic_binary, cmap='gray')
        plt.title('Semantic Binary')
        plt.axis('off')
    
    if boundary_binary is not None:
        plt.subplot(1, 3, 3)
        plt.imshow(boundary_binary, cmap='gray')
        plt.title('Boundary Binary')
        plt.axis('off')
    
    plt.tight_layout()
    plt.savefig(output_dir / 'binary_masks.png', dpi=150)
    plt.close()
    
    print(f"All outputs saved to {output_dir}")

def flow_to_rgb(flow):
    """Convert flow field to RGB visualization."""
    h, w = flow.shape[1:]
    hsv = np.zeros((h, w, 3), dtype=np.uint8)
    
    # Convert flow vectors to polar coordinates
    mag = np.sqrt(flow[0]**2 + flow[1]**2)
    ang = np.arctan2(flow[0], flow[1]) + np.pi
    
    # Normalize magnitude for better visualization
    mag = np.clip(mag / (mag.max() + 1e-8), 0, 1)
    
    # Convert to HSV (hue = angle, saturation = 1, value = magnitude)
    hsv[..., 0] = ang * 180 / np.pi / 2  # Hue
    hsv[..., 1] = 255  # Saturation
    hsv[..., 2] = (mag * 255).astype(np.uint8)  # Value
    
    # Convert to RGB
    rgb = cv2.cvtColor(hsv, cv2.COLOR_HSV2RGB)
    
    return rgb

if __name__ == "__main__":
    import argparse
    parser = argparse.ArgumentParser(description='Visualize all model output heads')
    parser.add_argument('--model', type=str, required=True, help='Path to model weights')
    parser.add_argument('--image', type=str, required=True, help='Path to input image')
    parser.add_argument('--output', type=str, required=True, help='Output directory')
    parser.add_argument('--patch_size', type=int, default=256, help='Patch size for tiling')
    parser.add_argument('--overlap', type=int, default=32, help='Overlap between patches')
    args = parser.parse_args()
    
    visualize_all_heads(
        args.model, 
        args.image, 
        args.output, 
        args.patch_size, 
        args.overlap
    )
# Enhanced Spot Detection Inference

This module provides enhanced inference capabilities for the optimized spot detection model, including automatic tiling for large images, GPU acceleration, and comprehensive instance segmentation.

## Features

- **Automatic Tiling**: Handles images of any size by splitting into overlapping patches
- **GPU Acceleration**: Automatic GPU detection and usage when available
- **Instance Segmentation**: Returns individual spot masks and labeled instances
- **Flow-based Separation**: Uses flow fields to separate touching spots
- **Non-Maximum Suppression**: Optional NMS to remove duplicate detections
- **Comprehensive Output**: Coordinates, scores, sizes, and intensities for each spot

## Files

- `enhanced_inference.py`: Core inference function with tiling and instance segmentation
- `spot_inference_api.py`: High-level API for easy integration
- `inference_example.py`: Example usage and visualization

## Quick Start

### Basic Usage

```python
from spot_inference_api import SpotDetector
import numpy as np

# Initialize detector
detector = SpotDetector(model_path="path/to/trained_model.pth")

# Load and process image
results = detector.detect_spots(
    image="path/to/image.tif",
    threshold=0.5,
    min_distance=3,
    min_size=4
)

# Access results
print(f"Detected {len(results['spots'])} spots")
for spot in results['spots']:
    print(f"Spot at ({spot['x']:.1f}, {spot['y']:.1f}) with score {spot['score']:.3f}")
```

### Advanced Usage

```python
# Custom parameters for large images
results = detector.detect_spots(
    image="large_image.tif",
    patch_size=512,        # Larger patches for better context
    overlap=64,            # More overlap for smoother blending
    threshold=0.6,         # Higher threshold for precision
    min_distance=5,        # Minimum distance between spots
    min_size=10,           # Minimum spot size
    use_nms=True,          # Enable Non-Maximum Suppression
    nms_threshold=0.3,     # NMS overlap threshold
    use_flow=True          # Use flow field for separation
)
```

## Output Format

The inference function returns a dictionary with three main components:

### 1. Instance Masks
```python
results['instance_masks']  # List[np.ndarray]
```
- List of individual binary masks (one per detected spot)
- Each mask is a 2D numpy array with the same shape as the input image
- Values are 0 (background) or 1 (spot)

### 2. Instance Labels
```python
results['instance_labels']  # np.ndarray
```
- 2D numpy array with the same shape as the input image
- Each detected spot has a unique integer ID (1, 2, 3, ...)
- Background pixels have value 0

### 3. Spot Coordinates and Properties
```python
results['spots']  # List[Dict]
```
Each spot dictionary contains:
- `x`, `y`: Centroid coordinates
- `score`: Detection confidence score (0-1)
- `size`: Area in pixels
- `intensity`: Average intensity from original image

## Parameters

### Core Parameters
- `patch_size` (int, default=256): Size of patches for tiling
- `overlap` (int, default=32): Overlap between adjacent patches
- `threshold` (float, default=0.5): Detection threshold for spot classification

### Quality Control
- `min_distance` (int, default=3): Minimum distance between spot centers
- `min_size` (int, default=4): Minimum spot size in pixels
- `use_nms` (bool, default=False): Enable Non-Maximum Suppression
- `nms_threshold` (float, default=0.3): NMS overlap threshold

### Advanced Features
- `use_flow` (bool, default=True): Use flow field for separating touching spots
- `device` (str, default='auto'): Device to use ('auto', 'cuda', or 'cpu')

## Image Preprocessing

The inference function automatically handles:

1. **Normalization**: Images are normalized to [0, 1] range using `image / (image.max() + 1e-8)`
2. **Tiling**: Large images are split into overlapping patches
3. **Padding**: Patches are padded if necessary to maintain consistent size
4. **Blending**: Overlapping regions are smoothly blended using weight maps

## Performance Optimization

### GPU Usage
- Automatic GPU detection and usage when available
- Efficient batch processing of patches
- Memory-optimized operations

### Tiling Strategy
- Overlapping patches ensure no spots are missed at boundaries
- Smooth blending prevents artifacts at patch edges
- Configurable patch size and overlap for different image sizes

### Memory Management
- Processes patches individually to handle large images
- Efficient tensor operations on GPU
- Minimal CPU-GPU transfers

## Example Workflows

### Single Image Processing
```python
from spot_inference_api import quick_detect

# Quick detection with default parameters
results = quick_detect("image.tif", model_path="model.pth")

# Save results
import pandas as pd
df = pd.DataFrame(results['spots'])
df.to_csv("detected_spots.csv", index=False)
```

### Batch Processing
```python
detector = SpotDetector(model_path="model.pth")

# Process multiple images
image_paths = ["img1.tif", "img2.tif", "img3.tif"]
batch_results = detector.detect_spots_batch(
    image_paths,
    threshold=0.5,
    use_nms=True
)

# Save all results
for i, results in enumerate(batch_results):
    detector.save_results(results, f"output_dir", f"image_{i}")
```

### Large Image Processing
```python
# For very large images (>2000x2000 pixels)
results = detector.detect_spots(
    "large_image.tif",
    patch_size=512,     # Larger patches
    overlap=128,        # More overlap
    threshold=0.6,      # Higher threshold
    min_distance=10,    # Larger minimum distance
    use_nms=True        # Remove duplicates
)
```

## Troubleshooting

### Common Issues

1. **Out of Memory Errors**
   - Reduce `patch_size` (e.g., from 256 to 128)
   - Use CPU instead of GPU: `device='cpu'`

2. **Too Many False Positives**
   - Increase `threshold` (e.g., from 0.5 to 0.7)
   - Increase `min_size` to filter small detections
   - Enable NMS: `use_nms=True`

3. **Missing Spots at Patch Boundaries**
   - Increase `overlap` (e.g., from 32 to 64)
   - Use smaller `patch_size` with more overlap

4. **Touching Spots Not Separated**
   - Ensure `use_flow=True`
   - Adjust `min_distance` parameter
   - Check if model was trained with flow supervision

### Performance Tips

1. **For Speed**: Use larger `patch_size` with less `overlap`
2. **For Accuracy**: Use smaller `patch_size` with more `overlap`
3. **For Memory**: Use smaller `patch_size` or `device='cpu'`
4. **For Precision**: Increase `threshold` and enable `use_nms`

## Integration with Existing Code

The inference functions are designed to be easily integrated with existing workflows:

```python
# Replace existing inference code
# OLD:
# outputs = model(image_tensor)
# heatmap = torch.sigmoid(outputs['heatmap'])

# NEW:
from enhanced_inference import enhanced_inference
results = enhanced_inference(model, image_array)
instance_masks = results['instance_masks']
spots = results['spots']
```

## Requirements

- PyTorch >= 1.8
- NumPy
- SciPy
- scikit-image
- OpenCV (cv2)
- tifffile
- matplotlib (for visualization)
- pandas (for CSV export)

## Citation

If you use this inference code in your research, please cite the original spot detection paper and mention the enhanced inference implementation.
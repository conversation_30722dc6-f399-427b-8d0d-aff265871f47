import numpy as np
import matplotlib.pyplot as plt
from sparse_data_utils import SparseSpotDataset
from data_utils import create_data_loaders
import random
import cv2
import torch
from torch.utils.data import DataLoader

def normalize_orientation(arr):
    """Ensure consistent orientation for all arrays"""
    if torch.is_tensor(arr):
        arr = arr.squeeze().cpu().numpy()
    arr = np.asarray(arr)
    if arr.ndim > 2:
        if arr.shape[0] in [1, 3, 4]:  # Channel-first format
            arr = arr.transpose(1, 2, 0)
        if arr.shape[-1] in [3, 4]:  # RGB/RGBA to grayscale
            arr = cv2.cvtColor(arr, cv2.COLOR_RGB2GRAY)
    arr = arr.squeeze()
    # Normalize to [0,1] range if needed
    if arr.max() > 1.0 or arr.min() < 0:
        arr = (arr - arr.min()) / (arr.max() - arr.min())
    return arr.astype(np.float32)

def visualize_samples(dataset, num_samples=5):
    """Visualize samples from the dataset with consistent orientation"""
    # Create figure
    fig, axes = plt.subplots(num_samples, 4, figsize=(16, 4*num_samples))
    
    # Handle single sample case
    if num_samples == 1:
        axes = axes.reshape(1, -1)
    
    print("\nVisualizing training samples...")
    print(f"Dataset length: {len(dataset)}\n")
    
    indices = random.sample(range(len(dataset)), num_samples)
    
    for i, idx in enumerate(indices):
        print(f"\nProcessing sample {i+1}, index {idx}")
        try:
            sample = dataset[idx]
            
            # Extract and normalize all arrays
            image = normalize_orientation(sample['image'])
            mask = normalize_orientation(sample['mask'])
            confidence = normalize_orientation(sample['confidence']) if 'confidence' in sample else np.ones_like(mask)
            
            # Ensure confidence is in [0,1] range
            confidence = np.clip(confidence, 0, 1)
            
            print(f"Image shape: {image.shape}")
            print(f"Mask shape: {mask.shape}")
            print(f"Confidence shape: {confidence.shape}")
            
            try:
                # All plots use origin='lower' for consistent orientation
                # Plot original image
                im0 = axes[i, 0].imshow(image, cmap='gray', origin='lower')
                axes[i, 0].set_title(f"Image {idx}")
                axes[i, 0].axis('off')
                
                # Plot ground truth mask
                im1 = axes[i, 1].imshow(mask, cmap='hot', origin='lower', vmin=0, vmax=1)
                axes[i, 1].set_title(f"Mask {idx}")
                axes[i, 1].axis('off')
                
                # Plot confidence map with fixed orientation and range
                im2 = axes[i, 2].imshow(confidence, cmap='viridis', origin='lower', vmin=0, vmax=1)
                axes[i, 2].set_title(f"Confidence {idx}")
                axes[i, 2].axis('off')
                
                # Create overlay
                overlay = np.zeros((*image.shape, 3))
                overlay[..., 0] = image  # Red channel
                overlay[..., 1] = image  # Green channel
                overlay[..., 2] = image  # Blue channel
                
                # Add red overlay only for spots with high confidence
                spot_mask = (mask > 0.5) & (confidence > 0.5)
                overlay[spot_mask] = [1, 0, 0]  # Red for spots
                
                im3 = axes[i, 3].imshow(overlay, origin='lower')
                axes[i, 3].set_title(f"Overlay {idx}")
                axes[i, 3].axis('off')
                
            except Exception as e:
                print(f"Error plotting: {str(e)}")
                
        except Exception as e:
            print(f"Error processing sample: {str(e)}")
            continue
    
    plt.tight_layout()
    plt.show()

if __name__ == "__main__":
    # Create dataset with synthetic data for testing
    dataset = SparseSpotDataset(
        data_dir=None,  # Use synthetic data
        image_size=(256, 256),
        synthetic=True,
        synthetic_size=10
    )
    
    # Visualize samples
    print("Testing visualization...")
    visualize_samples(dataset, num_samples=5)
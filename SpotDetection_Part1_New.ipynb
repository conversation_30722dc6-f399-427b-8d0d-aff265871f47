{\n "cells": [\n  {\n   "cell_type": "code",\n   "execution_count": null,\n   "metadata": {},\n   "outputs": [],\n   "source": [\n    "# Setup workspace paths\\n",\n    "import os\\n",\n    "\\n",\n    "# Get the current notebook directory\\n",\n    "NOTEBOOK_DIR = os.path.dirname(os.path.abspath('__file__'))\\n",\n    "print(NOTEBOOK_DIR)\\n",\n    "# Create workspace directories\\n",\n    "WORKSPACE = {\\n",\n    "    'models': os.path.join(NOTEBOOK_DIR, 'models'),\\n",\n    "    'logs': os.path.join(NOTEBOOK_DIR, 'logs'),\\n",\n    "    'results': os.path.join(NOTEBOOK_DIR, 'results'),\\n",\n    "    'tensorboard': os.path.join(NOTEBOOK_DIR, 'tensorboard_logs')\\n",\n    "}\\n",\n    "\\n",\n    "# Create directories if they don't exist\\n",\n    "for dir_path in WORKSPACE.values():\\n",\n    "    os.makedirs(dir_path, exist_ok=True)\\n",\n    "    print(f'Created directory: {dir_path}')\\n",\n    "\\n",\n    "# Import TensorBoard for visualization\\n",\n    "from torch.utils.tensorboard import SummaryWriter\\n",\n    "import datetime\\n",\n    "\\n",\n    "# Create a timestamp for this run\\n",\n    "TIMESTAMP = datetime.datetime.now().strftime('%Y%m%d-%H%M%S')\\n",\n    "\\n",\n    "# Setup paths for this run\\n",\n    "RUN_DIR = os.path.join(WORKSPACE['models'], f'run_{TIMESTAMP}')\\n",\n    "os.makedirs(RUN_DIR, exist_ok=True)\\n",\n    "TENSORBOARD_DIR = os.path.join(WORKSPACE['tensorboard'], f'run_{TIMESTAMP}')\\n",\n    "\\n",\n    "print(f'\\\\nWorkspace setup complete. Run directory: {RUN_DIR}')"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "# Optimized Spot Detection with Mixture of Experts\n",
    "\n",
    "This notebook provides a comprehensive guide to training and evaluating the spot detection model with a Mixture of Experts approach. The model is specifically designed to detect spots of varying sizes and densities in images."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Import standard libraries\n",
    "import os\n",
    "import sys\n",
    "import numpy as np\n",
    "import matplotlib.pyplot as plt\n",
    "import torch\n",
    "import torch.nn as nn\n",
    "import torch.optim as optim\n",
    "from torch.utils.data import DataLoader\n",
    "import albumentations as A\n",
    "from albumentations.pytorch import ToTensorV2\n",
    "from tqdm.notebook import tqdm\n",
    "import cv2\n",
    "from skimage import io, measure\n",
    "from scipy.ndimage import binary_erosion\n",
    "from typing import Dict, List, Tuple, Optional, Union\n",
    "import random\n",
    "import time\n",
    "import json\n",
    "\n",
    "# Set random seed for reproducibility\n",
    "def set_seed(seed=42):\n",
    "    random.seed(seed)\n",
    "    np.random.seed(seed)\n",
    "    torch.manual_seed(seed)\n",
    "    if torch.cuda.is_available():\n",
    "        torch.cuda.manual_seed(seed)\n",
    "        torch.cuda.manual_seed_all(seed)\n",
    "        torch.backends.cudnn.deterministic = True\n",
    "        torch.backends.cudnn.benchmark = False\n",
    "\n",
    "set_seed()\n",
    "\n",
    "# Check if GPU is available\n",
    "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n",
    "print(f\"Using device: {device}\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Import our modules\n",
    "from OptimizedSpotDetection_model import OptimizedSpotDetectionModel\n",
    "from data_utils import SpotDataset, create_data_loaders\n",
    "from sparse_data_utils import SparseSpotDataset, create_sparse_data_loaders\n",
    "from synthetic_data_generator import SyntheticSpotGenerator, AdvancedSyntheticSpotGenerator\n",
    "from fixed_spot_loss import FixedSpotLoss\n",
    "from fixed_metrics import FixedSpotMetrics\n",
    "from fixed_trainer import FixedSpotTrainer\n",
    "from enhanced_spot_loss import EnhancedSpotLoss\n",
    "from enhanced_metrics import EnhancedSpotMetrics\n",
    "from improved_trainer import ImprovedSpotTrainer\n",
    "from direct_peak_detection import test_peak_detection_parameters, detect_peaks_directly\n",
    "from force_spot_separation import force_spot_separation, visualize_spot_detection_steps\n",
    "from debug_binary_mask import debug_binary_mask, fix_spot_detection\n",
    "from fix_spot_detection import detect_individual_spots, visualize_spot_detection"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 1. Configurable Synthetic Data Generation\n",
    "\n",
    "Let's create configurable synthetic data with parameters we can adjust."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Configurable parameters for synthetic data generation\n",
    "synthetic_params = {\n",
    "    'image_size': (256, 256),\n",
    "    'min_spots': 10,\n",
    "    'max_spots': 50,\n",
    "    'min_radius': 2,\n",
    "    'max_radius': 8,\n",
    "    'density_factor': 1.0,  # Higher values create more dense spot patterns\n",
    "    'mask_threshold': 0.3,  # Controls mask size\n",
    "    'allow_touching': True,  # Allow spots to touch but not overlap\n",
    "    'shape_variation': 0.2,  # Amount of variation in spot shapes\n",
    "    'add_gradients': True,   # Add intensity gradients\n",
    "    'realistic_noise': True  # Use realistic noise patterns\n",
    "}"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Function to generate and visualize synthetic data with given parameters\n",
    "def test_synthetic_params(params, num_samples=4):\n",
    "    \"\"\"Generate and visualize synthetic data with given parameters\"\"\"\n",
    "    # Create generator with parameters\n",
    "    generator = AdvancedSyntheticSpotGenerator(**params)\n",
    "    \n",
    "    # Generate samples\n",
    "    images, masks = generator.generate_batch(num_samples)\n",
    "    \n",
    "    # Visualize samples\n",
    "    fig, axes = plt.subplots(num_samples, 2, figsize=(10, 5*num_samples))\n",
    "    \n",
    "    for i in range(num_samples):\n",
    "        # Display image\n",
    "        axes[i, 0].imshow(images[i], cmap='gray')\n",
    "        axes[i, 0].set_title(f'Synthetic Image {i+1}')\n",
    "        axes[i, 0].axis('off')\n",
    "        \n",
    "        # Display mask\n",
    "        axes[i, 1].imshow(masks[i], cmap='nipy_spectral')\n",
    "        axes[i, 1].set_title(f'Mask {i+1} (Unique IDs)')\n",
    "        axes[i, 1].axis('off')\n",
    "    \n",
    "    plt.tight_layout()\n",
    "    plt.show()\n",
    "    \n",
    "    # Count spots in each mask\n",
    "    for i, mask in enumerate(masks):\n",
    "        unique_ids = np.unique(mask)\n",
    "        num_spots = len(unique_ids) - (1 if 0 in unique_ids else 0)  # Subtract background\n",
    "        print(f\"Sample {i+1}: {num_spots} spots detected\")\n",
    "    \n",
    "    return images, masks\n",
    "\n",
    "# Test with our parameters\n",
    "test_images, test_masks = test_synthetic_params(synthetic_params)"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 2. Peak Detection Optimization\n",
    "\n",
    "Let's create a model and optimize peak detection parameters."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Create a model\n",
    "model = OptimizedSpotDetectionModel(\n",
    "    in_channels=1,\n",
    "    num_experts=3,\n",
    "    base_filters=64,\n",
    "    dropout_rate=0.2\n",
    ")\n",
    "model = model.to(device)\n",
    "\n",
    "# Use the first synthetic image for testing\n",
    "test_image = test_images[0]\n",
    "\n",
    "# Function to get prediction from model\n",
    "def get_prediction(model, image):\n",
    "    \"\"\"Get prediction heatmap from model\"\"\"\n",
    "    # Convert to tensor\n",
    "    if isinstance(image, np.ndarray):\n",
    "        if len(image.shape) == 2:\n",
    "            image_tensor = torch.from_numpy(image).unsqueeze(0).unsqueeze(0).float()\n",
    "        else:\n",
    "            image_tensor = torch.from_numpy(image).unsqueeze(0).float()\n",
    "    else:\n",
    "        image_tensor = image\n",
    "    \n",
    "    # Ensure values are in [0, 1]\n",
    "    if image_tensor.max() > 1.0:\n",
    "        image_tensor = image_tensor / 255.0\n",
    "    \n",
    "    # Move to device\n",
    "    image_tensor = image_tensor.to(device)\n",
    "    \n",
    "    # Get prediction\n",
    "    with torch.no_grad():\n",
    "        outputs = model(image_tensor)\n",
    "        \n",
    "        # Get main output\n",
    "        if isinstance(outputs, dict):\n",
    "            pred = outputs.get('output', outputs.get('combined_output', None))\n",
    "        else:\n",
    "            pred = outputs\n",
    "        \n",
    "        # Apply sigmoid\n",
    "        pred_sigmoid = torch.sigmoid(pred)\n",
    "        \n",
    "        # Convert to numpy\n",
    "        heatmap = pred_sigmoid.squeeze().cpu().numpy()\n",
    "    \n",
    "    return heatmap\n",
    "\n",
    "# Get prediction\n",
    "heatmap = get_prediction(model, test_image)\n",
    "\n",
    "# Test different peak detection parameters\n",
    "best_distance, best_intensity, best_result = test_peak_detection_parameters(test_image, heatmap)"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 3. Training with Peak Detection Integration"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Create data loaders with our synthetic parameters\n",
    "train_loader, val_loader, train_dataset, val_dataset = create_data_loaders(\n",
    "    data_dir=None,  # No real data, using synthetic only\n",
    "    batch_size=8,\n",
    "    image_size=256,\n",
    "    train_val_split=0.8,\n",
    "    synthetic=True,\n",
    "    synthetic_size=100,  # Small dataset for demonstration\n",
    "    augmentation_level='strong',\n",
    "    num_workers=4,\n",
    "    synthetic_params=synthetic_params  # Use our custom parameters\n",
    ")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Define loss function with peak detection integration\n",
    "class PeakDetectionLoss(nn.Module):\n",
    "    def __init__(self, base_loss=None, peak_weight=0.5, min_distance=5, min_intensity=0.1):\n",
    "        super().__init__()\n",
    "        self.base_loss = base_loss or EnhancedSpotLoss()\n",
    "        self.peak_weight = peak_weight\n",
    "        self.min_distance = min_distance\n",
    "        self.min_intensity = min_intensity\n",
    "    \n",
    "    def forward(self, pred, target):\n",
    "        # Base loss\n",
    "        base_loss_val = self.base_loss(pred, target)\n",
    "        \n",
    "        # Peak detection loss\n",
    "        batch_size = pred.size(0)\n",
    "        peak_loss = 0.0\n",
    "        \n",
    "        for i in range(batch_size):\n",
    "            # Get prediction and target for this sample\n",
    "            pred_i = torch.sigmoid(pred[i]).squeeze().detach().cpu().numpy()\n",
    "            target_i = target[i].squeeze().cpu().numpy()\n",
    "            \n",
    "            # Find peaks in prediction\n",
    "            pred_peaks = peak_local_max(\n",
    "                pred_i,\n",
    "                min_distance=self.min_distance,\n",
    "                threshold_abs=self.min_intensity,\n",
    "                exclude_border=False\n",
    "            )\n",
    "            \n",
    "            # Find peaks in target\n",
    "            target_peaks = peak_local_max(\n",
    "                target_i,\n",
    "                min_distance=self.min_distance,\n",
    "                threshold_abs=0.5,  # Higher threshold for ground truth\n",
    "                exclude_border=False\n",
    "            )\n",
    "            \n",
    "            # Count difference in number of peaks\n",
    "            peak_diff = abs(len(pred_peaks) - len(target_peaks))\n",
    "            peak_loss += peak_diff / max(1, len(target_peaks))\n",
    "        \n",
    "        # Average over batch\n",
    "        peak_loss = peak_loss / batch_size\n",
    "        \n",
    "        # Combine losses\n",
    "        total_loss = base_loss_val + self.peak_weight * peak_loss\n",
    "        \n",
    "        return total_loss\n",
    "\n",
    "# Create loss function with peak detection\n",
    "loss_fn = PeakDetectionLoss(\n",
    "    base_loss=EnhancedSpotLoss(),\n",
    "    peak_weight=0.5,\n",
    "    min_distance=best_distance,\n",
    "    min_intensity=best_intensity\n",
    ")\n",
    "\n",
    "# Create optimizer\n",
    "optimizer = torch.optim.Adam(model.parameters(), lr=0.001)\n",
    "\n",
    "# Create trainer\n",
    "trainer = ImprovedSpotTrainer(\n",
    "    model=model,\n",
    "    loss_fn=loss_fn,\n",
    "    optimizer=optimizer,\n",
    "    device=device,\n",
    "    metrics=EnhancedSpotMetrics(),\n",
    "    tensorboard_dir=TENSORBOARD_DIR\n",
    ")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Train for a few epochs (for demonstration)\n",
    "best_model_path = os.path.join(RUN_DIR, 'best_model.pth')\n",
    "trainer.train(\n",
    "    train_loader=train_loader,\n",
    "    val_loader=val_loader,\n",
    "    num_epochs=5,  # Small number for demonstration\n",
    "    save_path=best_model_path\n",
    ")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 4. Peak Detection Predictor"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Create a peak detection predictor class\n",
    "class PeakDetectionPredictor:\n",
    "    def __init__(self, model, device, min_distance=5, min_intensity=0.1, min_spot_size=3):\n",
    "        self.model = model\n",
    "        self.device = device\n",
    "        self.min_distance = min_distance\n",
    "        self.min_intensity = min_intensity\n",
    "        self.min_spot_size = min_spot_size\n",
    "    \n",
    "    def predict(self, image):\n",
    "        # Get prediction heatmap\n",
    "        heatmap = get_prediction(self.model, image)\n",
    "        \n",
    "        # Detect spots using direct peak detection\n",
    "        result = detect_peaks_directly(\n",
    "            image, \n",
    "            heatmap, \n",
    "            min_distance=self.min_distance, \n",
    "            min_intensity=self.min_intensity\n",
    "        )\n",
    "        \n",
    "        return {\n",
    "            'heatmap': heatmap,\n",
    "            'num_spots': result['num_spots'],\n",
    "            'coordinates': result['coordinates'],\n",
    "            'visualization': result['visualization']\n",
    "        }\n",
    "    \n",
    "    def visualize(self, image):\n",
    "        # Get prediction\n",
    "        result = self.predict(image)\n",
    "        \n",
    "        # Display results\n",
    "        plt.figure(figsize=(15, 5))\n",
    "        \n",
    "        plt.subplot(131)\n",
    "        plt.imshow(image, cmap='gray')\n",
    "        plt.title('Original Image')\n",
    "        plt.axis('off')\n",
    "        \n",
    "        plt.subplot(132)\n",
    "        plt.imshow(result['heatmap'], cmap='hot')\n",
    "        plt.title('Prediction Heatmap')\n",
    "        plt.axis('off')\n",
    "        \n",
    "        plt.subplot(133)\n",
    "        plt.imshow(result['visualization'])\n",
    "        plt.title(f'Detected Spots: {result[\"num_spots\"]}')\n",
    "        plt.axis('off')\n",
    "        \n",
    "        plt.tight_layout()\n",
    "        plt.show()\n",
    "        \n",
    "        return result\n",
    "\n",
    "# Create predictor with optimized parameters\n",
    "predictor = PeakDetectionPredictor(\n",
    "    model=model,\n",
    "    device=device,\n",
    "    min_distance=best_distance,\n",
    "    min_intensity=best_intensity,\n",
    "    min_spot_size=3\n",
    ")\n",
    "\n",
    "# Test on a synthetic image\n",
    "result = predictor.visualize(test_image)"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 5. Compare Different Synthetic Data Parameters"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Define different parameter sets to compare\n",
    "parameter_sets = {\n",
    "    'sparse': {\n",
    "        'image_size': (256, 256),\n",
    "        'min_spots': 5,\n",
    "        'max_spots': 15,\n",
    "        'min_radius': 3,\n",
    "        'max_radius': 8,\n",
    "        'density_factor': 0.5,\n",
    "        'mask_threshold': 0.3,\n",
    "        'allow_touching': False,\n",
    "        'shape_variation': 0.1,\n",
    "        'add_gradients': False,\n",
    "        'realistic_noise': True\n",
    "    },\n",
    "    'dense': {\n",
    "        'image_size': (256, 256),\n",
    "        'min_spots': 30,\n",
    "        'max_spots': 100,\n",
    "        'min_radius': 2,\n",
    "        'max_radius': 6,\n",
    "        'density_factor': 2.0,\n",
    "        'mask_threshold': 0.3,\n",
    "        'allow_touching': True,\n",
    "        'shape_variation': 0.2,\n",
    "        'add_gradients': True,\n",
    "        'realistic_noise': True\n",
    "    },\n",
    "    'varied_sizes': {\n",
    "        'image_size': (256, 256),\n",
    "        'min_spots': 10,\n",
    "        'max_spots': 40,\n",
    "        'min_radius': 1,\n",
    "        'max_radius': 12,\n",
    "        'density_factor': 1.0,\n",
    "        'mask_threshold': 0.3,\n",
    "        'allow_touching': True,\n",
    "        'shape_variation': 0.3,\n",
    "        'add_gradients': True,\n",
    "        'realistic_noise': True\n",
    "    }\n",
    "}\n",
    "\n",
    "# Compare parameter sets\n",
    "for name, params in parameter_sets.items():\n",
    "    print(f\"\\n=== Testing {name} parameter set ===\")\n",
    "    images, masks = test_synthetic_params(params, num_samples=2)\n",
    "    \n",
    "    # Test peak detection on the first image\n",
    "    result = predictor.visualize(images[0])"
   ]
  }
 ],
 "metadata": {
  "kernelspec": {
   "display_name": "Python 3",
   "language": "python",
   "name": "python3"
  },
  "language_info": {
   "codemirror_mode": {
    "name": "ipython",
    "version": 3
   },
   "file_extension": ".py",
   "mimetype": "text/x-python",
   "name": "python",
   "nbconvert_exporter": "python",
   "pygments_lexer": "ipython3",
   "version": "3.11.5"
  }
 },
 "nbformat": 4,
 "nbformat_minor": 2
}
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from tqdm.notebook import tqdm
import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple, Optional, Union, Callable
import time
import os
from predict import SpotDetectionPredictor

class ImprovedSpotTrainer:
    """
    Improved trainer for spot detection with better optimizer and visualization
    """

    def __init__(self,
                 model: nn.Module,
                 loss_fn: Callable,
                 optimizer: optim.Optimizer,
                 device: torch.device,
                 metrics_calculator: Optional[Callable] = None,
                 scheduler: Optional[object] = None,
                 tensorboard_writer: Optional[object] = None,
                 save_dir: str = './training_visualizations'):
        """
        Initialize the trainer
        """
        self.model = model
        self.loss_fn = loss_fn
        self.optimizer = optimizer
        self.device = device
        self.metrics_calculator = metrics_calculator
        self.scheduler = scheduler
        self.tensorboard_writer = tensorboard_writer
        self.save_dir = save_dir
        
        # Create save directory if it doesn't exist
        os.makedirs(save_dir, exist_ok=True)

        # Initialize history
        self.history = {
            'train_loss': [],
            'val_loss': [],
            'train_metrics': [],
            'val_metrics': [],
            'learning_rates': []
        }
        
        # Create predictor for visualization
        self.predictor = SpotDetectionPredictor(
            model=model,
            device=device,
            threshold=0.5,
            min_spot_size=3,
            min_distance=5
        )

    def train_epoch(self, train_loader: DataLoader) -> Dict[str, float]:
        """
        Train for one epoch with improved stability
        """
        self.model.train()
        epoch_loss = 0
        epoch_metrics = {}
        num_batches = 0

        # Use tqdm for progress bar
        with tqdm(train_loader, desc="Training", leave=False) as pbar:
            for batch in pbar:
                try:
                    # Get data
                    images = batch['image'].to(self.device)
                    masks = batch['mask'].to(self.device)
                    confidence = batch.get('confidence', None)
                    if confidence is not None:
                        confidence = confidence.to(self.device)

                    # Zero gradients
                    self.optimizer.zero_grad()

                    # Forward pass
                    outputs = self.model(images)

                    # Get main output
                    if isinstance(outputs, dict):
                        pred = outputs.get('output', outputs.get('combined_output', outputs.get('heatmap', None)))
                    else:
                        pred = outputs

                    # Calculate loss
                    if confidence is not None:
                        loss_dict = self.loss_fn(pred, masks, confidence)
                    else:
                        loss_dict = self.loss_fn(pred, masks)

                    # Handle different return types from loss function
                    if isinstance(loss_dict, dict):
                        loss = loss_dict['loss']
                    elif isinstance(loss_dict, torch.Tensor):
                        loss = loss_dict
                    else:
                        # Convert to tensor if it's not already (ensure it's a scalar)
                        if isinstance(loss_dict, (int, float)):
                            loss = torch.tensor(loss_dict, device=self.device, dtype=torch.float32)
                        else:
                            # If it's a list or array, take the mean to get a scalar
                            loss = torch.tensor(loss_dict, device=self.device, dtype=torch.float32).mean()

                    # Ensure loss is a scalar tensor
                    if loss.dim() > 0:
                        loss = loss.mean()

                    # Check for invalid loss values
                    if torch.isnan(loss) or torch.isinf(loss):
                        print(f"Warning: Invalid loss value: {loss.item()}, skipping batch")
                        continue

                    # Backward pass and optimize
                    loss.backward()
                    
                    # Clip gradients to prevent exploding gradients
                    torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
                    
                    self.optimizer.step()

                    # Update metrics
                    epoch_loss += loss.item()
                    num_batches += 1

                    # Calculate additional metrics if available
                    if self.metrics_calculator is not None:
                        with torch.no_grad():
                            batch_metrics = self.metrics_calculator.calculate_metrics(
                                torch.sigmoid(pred), masks
                            )

                            # Update epoch metrics
                            for k, v in batch_metrics.items():
                                if k not in epoch_metrics:
                                    epoch_metrics[k] = 0
                                epoch_metrics[k] += v

                    # Update progress bar
                    pbar.set_postfix(loss=loss.item())
                    
                except Exception as e:
                    print(f"Error in training batch: {e}")
                    continue

        # Calculate average metrics
        if num_batches > 0:  # Avoid division by zero
            epoch_loss /= num_batches
            for k in epoch_metrics:
                epoch_metrics[k] /= num_batches

        # Add loss to metrics
        epoch_metrics['loss'] = epoch_loss

        return epoch_metrics

    def validate(self, val_loader: DataLoader) -> Dict[str, float]:
        """
        Validate the model with improved stability
        """
        self.model.eval()
        val_loss = 0
        val_metrics = {}
        num_batches = 0

        with torch.no_grad():
            # Use tqdm for progress bar
            with tqdm(val_loader, desc="Validation", leave=False) as pbar:
                for batch in pbar:
                    try:
                        # Get data
                        images = batch['image'].to(self.device)
                        masks = batch['mask'].to(self.device)
                        confidence = batch.get('confidence', None)
                        if confidence is not None:
                            confidence = confidence.to(self.device)

                        # Forward pass
                        outputs = self.model(images)

                        # Get main output
                        if isinstance(outputs, dict):
                            pred = outputs.get('output', outputs.get('combined_output', outputs.get('heatmap', None)))
                        else:
                            pred = outputs

                        # Calculate loss
                        if confidence is not None:
                            loss_dict = self.loss_fn(pred, masks, confidence)
                        else:
                            loss_dict = self.loss_fn(pred, masks)

                        # Handle different return types from loss function
                        if isinstance(loss_dict, dict):
                            loss = loss_dict['loss']
                        elif isinstance(loss_dict, torch.Tensor):
                            loss = loss_dict
                        else:
                            # Convert to tensor if it's not already (ensure it's a scalar)
                            if isinstance(loss_dict, (int, float)):
                                loss = torch.tensor(loss_dict, device=self.device, dtype=torch.float32)
                            else:
                                # If it's a list or array, take the mean to get a scalar
                                loss = torch.tensor(loss_dict, device=self.device, dtype=torch.float32).mean()

                        # Ensure loss is a scalar tensor
                        if loss.dim() > 0:
                            loss = loss.mean()

                        # Check for invalid loss values
                        if torch.isnan(loss) or torch.isinf(loss):
                            print(f"Warning: Invalid validation loss value: {loss.item()}, skipping batch")
                            continue

                        # Update metrics
                        val_loss += loss.item()
                        num_batches += 1

                        # Calculate additional metrics if available
                        if self.metrics_calculator is not None:
                            batch_metrics = self.metrics_calculator.calculate_metrics(
                                torch.sigmoid(pred), masks
                            )

                            # Update epoch metrics
                            for k, v in batch_metrics.items():
                                if k not in val_metrics:
                                    val_metrics[k] = 0
                                val_metrics[k] += v

                        # Update progress bar
                        pbar.set_postfix(loss=loss.item())
                        
                    except Exception as e:
                        print(f"Error in validation batch: {e}")
                        continue

        # Calculate average metrics
        if num_batches > 0:  # Avoid division by zero
            val_loss /= num_batches
            for k in val_metrics:
                val_metrics[k] /= num_batches

        # Add loss to metrics
        val_metrics['loss'] = val_loss

        return val_metrics
    
    def visualize_predictions(self, val_loader: DataLoader, epoch: int) -> None:
        """
        Visualize model predictions during training
        
        Args:
            val_loader: Validation data loader
            epoch: Current epoch number
        """
        # Skip if not in first 100 epochs
        if epoch > 100:
            return
            
        # Get a batch from validation set
        batch = next(iter(val_loader))
        images = batch['image'].to(self.device)
        masks = batch['mask'].to(self.device)
        
        # Select a single image for visualization
        image = images[0:1]
        mask = masks[0:1]
        
        # Get prediction
        self.model.eval()
        with torch.no_grad():
            outputs = self.model(image)
            
            # Get main output
            if isinstance(outputs, dict):
                pred = outputs.get('output', outputs.get('combined_output', outputs.get('heatmap', None)))
            else:
                pred = outputs
        
        # Convert to numpy
        image_np = image.squeeze().cpu().numpy()
        mask_np = mask.squeeze().cpu().numpy()
        pred_np = torch.sigmoid(pred).squeeze().cpu().numpy()
        
        # Create figure
        fig, axes = plt.subplots(1, 3, figsize=(15, 5))
        
        # Plot original image
        axes[0].imshow(image_np, cmap='gray')
        axes[0].set_title('Original Image')
        axes[0].axis('off')
        
        # Plot ground truth mask
        axes[1].imshow(mask_np, cmap='gray')
        axes[1].set_title('Ground Truth')
        axes[1].axis('off')
        
        # Plot prediction
        axes[2].imshow(pred_np, cmap='hot')
        axes[2].set_title(f'Prediction (Epoch {epoch+1})')
        axes[2].axis('off')
        
        # Save figure
        plt.tight_layout()
        plt.savefig(f"{self.save_dir}/epoch_{epoch+1:03d}.png")
        plt.close()
        
        # Also save a version with detected spots
        result = self.predictor.predict(image_np, return_heatmap=True)
        self.predictor.visualize(
            image_np, 
            result=result,
            show_spots=True,
            show_heatmap=True,
            figsize=(15, 5)
        )
        plt.savefig(f"{self.save_dir}/epoch_{epoch+1:03d}_spots.png")
        plt.close()

    def train(self,
             train_loader: DataLoader,
             val_loader: DataLoader,
             num_epochs: int,
             early_stopping_patience: int = 0,
             save_best_model: bool = True,
             model_save_path: str = 'best_model.pth') -> Dict[str, List]:
        """
        Train the model with improved stability and visualization
        """
        # Initialize variables for early stopping
        best_val_loss = float('inf')
        patience_counter = 0

        # Train for specified number of epochs
        for epoch in range(num_epochs):
            start_time = time.time()

            # Train for one epoch
            train_metrics = self.train_epoch(train_loader)

            # Validate
            val_metrics = self.validate(val_loader)
            
            # Visualize predictions (only for first 100 epochs)
            self.visualize_predictions(val_loader, epoch)

            # Update learning rate if scheduler is available
            if self.scheduler is not None:
                if isinstance(self.scheduler, optim.lr_scheduler.ReduceLROnPlateau):
                    self.scheduler.step(val_metrics['loss'])
                else:
                    self.scheduler.step()

            # Get current learning rate
            current_lr = self.optimizer.param_groups[0]['lr']

            # Update history
            self.history['train_loss'].append(train_metrics['loss'])
            self.history['val_loss'].append(val_metrics['loss'])
            self.history['train_metrics'].append(train_metrics)
            self.history['val_metrics'].append(val_metrics)
            self.history['learning_rates'].append(current_lr)

            # Calculate epoch time
            epoch_time = time.time() - start_time

            # Print epoch summary
            print(f"Epoch {epoch+1}/{num_epochs} - {epoch_time:.1f}s - "
                  f"Train Loss: {train_metrics['loss']:.4f} - "
                  f"Val Loss: {val_metrics['loss']:.4f} - "
                  f"LR: {current_lr:.6f}")

            # Print additional metrics if available
            if 'precision' in val_metrics:
                f1_key = 'f1_score' if 'f1_score' in val_metrics else 'f1'
                print(f"Val Precision: {val_metrics['precision']:.4f} - "
                      f"Val Recall: {val_metrics['recall']:.4f} - "
                      f"Val F1: {val_metrics[f1_key]:.4f} - "
                      f"Val IoU: {val_metrics['iou']:.4f}")

            # Check for best model
            if val_metrics['loss'] < best_val_loss:
                best_val_loss = val_metrics['loss']
                patience_counter = 0

                # Save best model if requested
                if save_best_model:
                    torch.save(self.model.state_dict(), model_save_path)
                    print(f"Saved best model to {model_save_path}")
            else:
                patience_counter += 1

            # Check for early stopping
            if early_stopping_patience > 0 and patience_counter >= early_stopping_patience:
                print(f"Early stopping after {epoch+1} epochs")
                break

        # Load best model if saved
        if save_best_model:
            self.model.load_state_dict(torch.load(model_save_path))

        return self.history

    def plot_history(self):
        """
        Plot training history
        """
        # Create figure with subplots
        fig, axes = plt.subplots(1, 2, figsize=(15, 5))

        # Plot loss
        axes[0].plot(self.history['train_loss'], label='Train Loss')
        axes[0].plot(self.history['val_loss'], label='Val Loss')
        axes[0].set_xlabel('Epoch')
        axes[0].set_ylabel('Loss')
        axes[0].set_title('Training and Validation Loss')
        axes[0].legend()
        axes[0].grid(True)

        # Plot metrics if available
        if len(self.history['val_metrics']) > 0 and 'precision' in self.history['val_metrics'][0]:
            # Extract metrics
            val_precision = [m['precision'] for m in self.history['val_metrics']]
            val_recall = [m['recall'] for m in self.history['val_metrics']]
            # Handle both f1 and f1_score keys
            f1_key = 'f1_score' if 'f1_score' in self.history['val_metrics'][0] else 'f1'
            val_f1 = [m[f1_key] for m in self.history['val_metrics']]
            val_iou = [m['iou'] for m in self.history['val_metrics']]

            # Plot metrics
            axes[1].plot(val_precision, label='Precision')
            axes[1].plot(val_recall, label='Recall')
            axes[1].plot(val_f1, label='F1')
            axes[1].plot(val_iou, label='IoU')
            axes[1].set_xlabel('Epoch')
            axes[1].set_ylabel('Metric')
            axes[1].set_title('Validation Metrics')
            axes[1].legend()
            axes[1].grid(True)
        else:
            # Plot learning rate
            axes[1].plot(self.history['learning_rates'], label='Learning Rate')
            axes[1].set_xlabel('Epoch')
            axes[1].set_ylabel('Learning Rate')
            axes[1].set_title('Learning Rate Schedule')
            axes[1].legend()
            axes[1].grid(True)

        plt.tight_layout()
        plt.savefig(f"{self.save_dir}/training_history.png")
        plt.show()
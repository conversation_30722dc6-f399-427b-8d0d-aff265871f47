import torch
import torch.nn.functional as F
from torch.amp import GradScaler, autocast
from torch.optim.lr_scheduler import OneCycleLR
import numpy as np
import os
from tqdm import tqdm
import matplotlib.pyplot as plt

def train_skeleton_aware_model_refined(model, train_loader, val_loader, num_epochs, device, 
                                     start_epoch=0, best_val_loss=float('inf'), optimizer_state=None):
    """Refined training with latest PyTorch methods (2025)"""
    
    # Use compile for faster execution (PyTorch 2.0+)
    if hasattr(torch, 'compile'):
        model = torch.compile(model, mode='reduce-overhead')
    
    # Optimized loss function
    criterion = SkeletonAwareLoss()
    
    # Mixed precision with optimized settings
    scaler = GradScaler('cuda', enabled=True)
    
    # AdamW with fused implementation for speed
    optimizer = torch.optim.AdamW(
        model.parameters(), 
        lr=3e-4,  # Slightly higher LR for OneCycle
        weight_decay=1e-2,
        betas=(0.9, 0.95),  # Updated betas
        fused=True  # Faster fused implementation
    )
    
    if optimizer_state:
        optimizer.load_state_dict(optimizer_state)
    
    # OneCycleLR scheduler (more effective than CosineAnnealing)
    scheduler = OneCycleLR(
        optimizer,
        max_lr=3e-4,
        epochs=num_epochs,
        steps_per_epoch=len(train_loader),
        pct_start=0.1,  # 10% warmup
        div_factor=10,  # Initial LR = max_lr/10
        final_div_factor=100  # Final LR = max_lr/100
    )
    
    model.to(device)
    train_losses, val_losses = [], []
    
    # Early stopping with patience
    patience = 20
    early_stop_counter = 0
    
    for epoch in range(start_epoch, num_epochs):
        # Training phase
        model.train()
        epoch_losses = []
        
        pbar = tqdm(train_loader, desc=f"Epoch {epoch+1}/{num_epochs}")
        for images, targets in pbar:
            images = images.to(device, non_blocking=True)
            targets = targets.to(device, non_blocking=True)
            
            optimizer.zero_grad(set_to_none=True)
            
            with autocast('cuda'):
                outputs = model(images)
                loss, loss_dict = criterion(outputs, targets)
            
            if torch.isnan(loss):
                continue
            
            scaler.scale(loss).backward()
            scaler.unscale_(optimizer)
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            scaler.step(optimizer)
            scaler.update()
            scheduler.step()  # Step per batch for OneCycleLR
            
            epoch_losses.append(loss.item())
            pbar.set_postfix({'Loss': f'{loss.item():.4f}', 'LR': f'{scheduler.get_last_lr()[0]:.2e}'})
        
        train_losses.append(np.mean(epoch_losses))
        
        # Validation phase
        model.eval()
        val_epoch_losses = []
        
        with torch.no_grad():
            for images, targets in val_loader:
                images = images.to(device, non_blocking=True)
                targets = targets.to(device, non_blocking=True)
                
                with autocast('cuda'):
                    outputs = model(images)
                    loss, _ = criterion(outputs, targets)
                
                if not torch.isnan(loss):
                    val_epoch_losses.append(loss.item())
        
        val_losses.append(np.mean(val_epoch_losses))
        
        print(f"Epoch {epoch+1}: Train={train_losses[-1]:.4f}, Val={val_losses[-1]:.4f}")
        
        # Save best model
        if val_losses[-1] < best_val_loss:
            best_val_loss = val_losses[-1]
            torch.save({
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'scheduler_state_dict': scheduler.state_dict(),
                'epoch': epoch,
                'best_val_loss': best_val_loss,
                'train_losses': train_losses,
                'val_losses': val_losses
            }, os.path.join(model_dir, 'best_model.pth'))
            print(f"✅ Best model saved (val_loss={best_val_loss:.4f})")
            early_stop_counter = 0
        else:
            early_stop_counter += 1
            if early_stop_counter >= patience:
                print(f"⚠️ Early stopping after {patience} epochs")
                break
    
    return model, train_losses, val_losses

def initialize_model_refined(resume_from_checkpoint=None):
    """Initialize model with resume capability"""
    model = SkeletonAwareSpotDetector(in_ch=1, base_ch=48)
    start_epoch = 0
    best_val_loss = float('inf')
    optimizer_state = None
    
    if resume_from_checkpoint and os.path.exists(resume_from_checkpoint):
        checkpoint = torch.load(resume_from_checkpoint, map_location='cpu')
        model.load_state_dict(checkpoint['model_state_dict'])
        
        if 'epoch' in checkpoint:
            start_epoch = checkpoint['epoch'] + 1
        if 'best_val_loss' in checkpoint:
            best_val_loss = checkpoint['best_val_loss']
        if 'optimizer_state_dict' in checkpoint:
            optimizer_state = checkpoint['optimizer_state_dict']
            
        print(f"Resumed from epoch {start_epoch}, best val loss: {best_val_loss:.4f}")
    
    return model, start_epoch, best_val_loss, optimizer_state

def evaluate_model_refined(model, test_loader, device='cuda'):
    """Refined evaluation with comprehensive metrics"""
    model.eval()
    all_metrics = []
    
    with torch.no_grad():
        for images, targets in test_loader:
            images = images.to(device, non_blocking=True)
            batch_size = images.shape[0]
            
            for i in range(batch_size):
                try:
                    with autocast('cuda'):
                        outputs = model(images[i:i+1])
                    
                    # Extract predictions
                    semantic = torch.sigmoid(outputs['sem_out'])[0, 0].cpu().numpy()
                    centroid_map = torch.sigmoid(outputs['hm_out'])[0, 0].cpu().numpy()
                    
                    # Handle SDT output
                    if outputs['sdt_out'].shape[1] > 1:
                        probs = F.softmax(outputs['sdt_out'], dim=1)
                        bin_indices = torch.arange(0, probs.shape[1]).float().to(probs.device)
                        bin_indices = bin_indices.view(1, -1, 1, 1) / probs.shape[1]
                        sdt = torch.sum(probs * bin_indices, dim=1)[0].cpu().numpy()
                    else:
                        sdt = torch.sigmoid(outputs['sdt_out'])[0, 0].cpu().numpy()
                    
                    skeleton = torch.sigmoid(outputs['skeleton_out'])[0, 0].cpu().numpy()
                    flow = outputs['flow_out'][0].cpu().numpy()
                    
                    # Extract spots
                    pred_spots = extract_precise_spots(centroid_map, semantic, flow, threshold=0.3)
                    pred_spots_list = [[spot[0], spot[1], spot[2]] for spot in pred_spots]
                    
                    # Ground truth
                    gt_centroid = targets[i, 3].cpu().numpy()
                    gt_semantic = targets[i, 0].cpu().numpy()
                    gt_spots = extract_precise_spots(gt_centroid, gt_semantic, threshold=0.3)
                    
                    # Calculate metrics
                    precision, recall, f1 = calculate_metrics(pred_spots_list, gt_spots)
                    
                    # Additional metrics
                    gt_skeleton = targets[i, 2].cpu().numpy()
                    skeleton_iou = calculate_iou(skeleton > 0.5, gt_skeleton > 0.5)
                    
                    gt_sdt = targets[i, 1].cpu().numpy()
                    sdt_mse = np.mean((sdt - gt_sdt)**2)
                    
                    all_metrics.append([precision, recall, f1, skeleton_iou, sdt_mse])
                    
                except Exception as e:
                    continue
    
    if not all_metrics:
        return {k: 0 for k in ['precision', 'recall', 'f1', 'skeleton_iou', 'sdt_mse']}
    
    metrics = np.array(all_metrics)
    return {
        'precision': np.mean(metrics[:, 0]),
        'recall': np.mean(metrics[:, 1]),
        'f1': np.mean(metrics[:, 2]),
        'skeleton_iou': np.mean(metrics[:, 3]),
        'sdt_mse': np.mean(metrics[:, 4])
    }

def skeleton_aware_inference_refined(model, image, device='cuda', threshold=0.3):
    """Refined inference with optimizations"""
    model.eval()
    
    # Prepare image
    if len(image.shape) == 2:
        image = image[None, None, ...]  # Add batch and channel dims
    elif len(image.shape) == 3:
        image = image[None, ...]  # Add batch dim
    
    image_tensor = torch.from_numpy(image).float().to(device)
    
    with torch.no_grad():
        with autocast('cuda'):
            outputs = model(image_tensor)
        
        # Extract outputs
        semantic = torch.sigmoid(outputs['sem_out'])[0, 0].cpu().numpy()
        centroid_map = torch.sigmoid(outputs['hm_out'])[0, 0].cpu().numpy()
        
        # Handle SDT
        if outputs['sdt_out'].shape[1] > 1:
            probs = F.softmax(outputs['sdt_out'], dim=1)
            bin_indices = torch.arange(0, probs.shape[1]).float().to(probs.device)
            bin_indices = bin_indices.view(1, -1, 1, 1) / probs.shape[1]
            sdt = torch.sum(probs * bin_indices, dim=1)[0].cpu().numpy()
        else:
            sdt = torch.sigmoid(outputs['sdt_out'])[0, 0].cpu().numpy()
        
        skeleton = torch.sigmoid(outputs['skeleton_out'])[0, 0].cpu().numpy()
        flow = outputs['flow_out'][0].cpu().numpy()
    
    # Extract spots
    spots = extract_precise_spots(centroid_map, semantic, flow, threshold=threshold)
    
    # Instance segmentation
    from skimage.segmentation import watershed
    from skimage.feature import peak_local_maxima
    
    peaks = peak_local_maxima(centroid_map, min_distance=3, threshold_abs=threshold)
    markers = np.zeros_like(centroid_map, dtype=int)
    for i, (y, x) in enumerate(peaks):
        markers[y, x] = i + 1
    
    instance_labels = watershed(-sdt, markers, mask=semantic > 0.5)
    
    return {
        'spots': [{'y': spot[0], 'x': spot[1], 'confidence': spot[2]} for spot in spots],
        'semantic': semantic,
        'sdt': sdt,
        'skeleton': skeleton,
        'instance_labels': instance_labels
    }
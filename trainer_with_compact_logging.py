import torch
import torch.nn as nn
import numpy as np
import matplotlib.pyplot as plt
from tqdm import tqdm
import time
import os
from typing import Dict, List, Tuple, Optional, Union, Any, Callable
from compact_logger import CompactTrainingLogger

class CompactSpotTrainer:
    """
    Trainer for spot detection model with compact logging
    
    This class handles training, validation, and logging with minimal output.
    """
    
    def __init__(
        self,
        model: nn.Module,
        loss_fn: Callable,
        optimizer: torch.optim.Optimizer,
        device: torch.device,
        metrics_calculator: Optional[Any] = None,
        scheduler: Optional[torch.optim.lr_scheduler._LRScheduler] = None,
        save_dir: str = './training_results'
    ):
        """
        Initialize the trainer
        
        Args:
            model: Model to train
            loss_fn: Loss function
            optimizer: Optimizer
            device: Device to use for training
            metrics_calculator: Object to calculate metrics
            scheduler: Learning rate scheduler
            save_dir: Directory to save results
        """
        self.model = model
        self.loss_fn = loss_fn
        self.optimizer = optimizer
        self.device = device
        self.metrics_calculator = metrics_calculator
        self.scheduler = scheduler
        self.save_dir = save_dir
        
        # Create save directory
        os.makedirs(save_dir, exist_ok=True)
        
        # Initialize logger
        self.logger = CompactTrainingLogger(
            log_interval=10,  # Log every 10 batches
            save_dir=save_dir,
            track_confidence=True  # Track confidence score evolution
        )
        
        # Initialize best validation loss for early stopping
        self.best_val_loss = float('inf')
        self.best_epoch = -1
    
    def train_one_epoch(self, train_loader):
        """
        Train for one epoch
        
        Args:
            train_loader: Training data loader
            
        Returns:
            Average loss and metrics for this epoch
        """
        self.model.train()
        total_loss = 0
        all_metrics = {}
        
        # Use tqdm for progress bar but with minimal output
        for batch_idx, batch in enumerate(train_loader):
            # Get data
            images = batch['image'].to(self.device)
            masks = batch['mask'].to(self.device)
            
            # Get confidence masks if available
            confidence = batch.get('confidence')
            if confidence is not None:
                confidence = confidence.to(self.device)
            
            # Forward pass
            outputs = self.model(images)
            
            # Handle outputs - get the main output tensor if it's a dictionary
            if isinstance(outputs, dict):
                main_output = outputs.get('output', outputs.get('combined_output', None))
            else:
                main_output = outputs
            
            # Calculate loss
            if confidence is not None:
                loss = self.loss_fn(main_output, masks, confidence)
            else:
                loss = self.loss_fn(main_output, masks)
            
            # Backward pass
            self.optimizer.zero_grad()
            loss.backward()
            self.optimizer.step()
            
            # Update total loss
            total_loss += loss.item()
            
            # Calculate metrics if available
            metrics = {}
            if self.metrics_calculator is not None:
                metrics = self.metrics_calculator(main_output, masks)
            
            # Log batch results with compact logger
            self.logger.log_batch(
                epoch=self.current_epoch,
                batch_idx=batch_idx,
                loss=loss.item(),
                metrics=metrics,
                outputs=outputs,
                optimizer=self.optimizer,
                total_batches=len(train_loader)
            )
            
            # Update all metrics
            for key, value in metrics.items():
                if key not in all_metrics:
                    all_metrics[key] = 0
                all_metrics[key] += value
        
        # Calculate average loss and metrics
        avg_loss = total_loss / len(train_loader)
        avg_metrics = {k: v / len(train_loader) for k, v in all_metrics.items()}
        
        return avg_loss, avg_metrics
    
    def validate(self, val_loader):
        """
        Validate the model
        
        Args:
            val_loader: Validation data loader
            
        Returns:
            Validation loss and metrics
        """
        self.model.eval()
        total_loss = 0
        all_metrics = {}
        all_outputs = []
        
        with torch.no_grad():
            for batch in val_loader:
                # Get data
                images = batch['image'].to(self.device)
                masks = batch['mask'].to(self.device)
                
                # Get confidence masks if available
                confidence = batch.get('confidence')
                if confidence is not None:
                    confidence = confidence.to(self.device)
                
                # Forward pass
                outputs = self.model(images)
                
                # Store outputs for confidence analysis
                all_outputs.append(outputs)
                
                # Handle outputs - get the main output tensor if it's a dictionary
                if isinstance(outputs, dict):
                    main_output = outputs.get('output', outputs.get('combined_output', None))
                else:
                    main_output = outputs
                
                # Calculate loss
                if confidence is not None:
                    loss = self.loss_fn(main_output, masks, confidence)
                else:
                    loss = self.loss_fn(main_output, masks)
                
                # Update total loss
                total_loss += loss.item()
                
                # Calculate metrics if available
                if self.metrics_calculator is not None:
                    metrics = self.metrics_calculator(main_output, masks)
                    
                    # Update all metrics
                    for key, value in metrics.items():
                        if key not in all_metrics:
                            all_metrics[key] = 0
                        all_metrics[key] += value
        
        # Calculate average loss and metrics
        avg_loss = total_loss / len(val_loader)
        avg_metrics = {k: v / len(val_loader) for k, v in all_metrics.items()}
        
        # Combine outputs for confidence analysis
        combined_outputs = torch.cat([out['output'] if isinstance(out, dict) else out 
                                     for out in all_outputs], dim=0)
        
        return avg_loss, avg_metrics, combined_outputs
    
    def train(
        self,
        train_loader,
        val_loader,
        num_epochs: int = 100,
        early_stopping_patience: int = 10,
        save_best_model: bool = True,
        model_save_path: Optional[str] = None
    ):
        """
        Train the model
        
        Args:
            train_loader: Training data loader
            val_loader: Validation data loader
            num_epochs: Number of epochs to train
            early_stopping_patience: Number of epochs to wait for improvement
            save_best_model: Whether to save the best model
            model_save_path: Path to save the best model
            
        Returns:
            Training history
        """
        # Set model save path
        if model_save_path is None:
            model_save_path = os.path.join(self.save_dir, 'best_model.pth')
        
        # Initialize early stopping counter
        early_stopping_counter = 0
        
        # Train for specified number of epochs
        for epoch in range(num_epochs):
            self.current_epoch = epoch
            
            # Train one epoch
            train_loss, train_metrics = self.train_one_epoch(train_loader)
            
            # Validate
            val_loss, val_metrics, val_outputs = self.validate(val_loader)
            
            # Log epoch results
            self.logger.log_epoch(
                epoch=epoch,
                train_loss=train_loss,
                val_loss=val_loss,
                train_metrics=train_metrics,
                val_metrics=val_metrics,
                val_outputs=val_outputs
            )
            
            # Update learning rate scheduler if available
            if self.scheduler is not None:
                if isinstance(self.scheduler, torch.optim.lr_scheduler.ReduceLROnPlateau):
                    self.scheduler.step(val_loss)
                else:
                    self.scheduler.step()
            
            # Check if this is the best model
            if val_loss < self.best_val_loss:
                self.best_val_loss = val_loss
                self.best_epoch = epoch
                early_stopping_counter = 0
                
                # Save best model if requested
                if save_best_model:
                    torch.save(self.model.state_dict(), model_save_path)
                    print(f"Saved best model at epoch {epoch} with val_loss={val_loss:.4f}")
            else:
                early_stopping_counter += 1
            
            # Check early stopping
            if early_stopping_counter >= early_stopping_patience:
                print(f"Early stopping at epoch {epoch}. Best epoch was {self.best_epoch} with val_loss={self.best_val_loss:.4f}")
                break
            
            # Create and save visualizations every 10 epochs
            if epoch % 10 == 0 or epoch == num_epochs - 1:
                self.logger.plot_metrics(save=True)
                self.logger.plot_confidence_evolution(save=True)
        
        # Final plots
        self.logger.plot_metrics(save=True)
        self.logger.plot_confidence_evolution(save=True)
        
        return self.logger.history
    
    def load_best_model(self, model_path: Optional[str] = None):
        """
        Load the best model
        
        Args:
            model_path: Path to the model
        """
        if model_path is None:
            model_path = os.path.join(self.save_dir, 'best_model.pth')
        
        self.model.load_state_dict(torch.load(model_path))
        self.model.to(self.device)
        print(f"Loaded model from {model_path}")


# Example usage:
"""
# Create model, loss function, optimizer, etc.
model = OptimizedSpotDetectionModel(...)
loss_fn = EnhancedSpotLoss(...)
optimizer = torch.optim.AdamW(model.parameters(), lr=0.001)
metrics_calculator = EnhancedSpotMetrics(...)

# Create trainer with compact logging
trainer = CompactSpotTrainer(
    model=model,
    loss_fn=loss_fn,
    optimizer=optimizer,
    device=device,
    metrics_calculator=metrics_calculator,
    scheduler=torch.optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', factor=0.5, patience=5),
    save_dir='./spot_detection_results'
)

# Train model
history = trainer.train(
    train_loader=train_loader,
    val_loader=val_loader,
    num_epochs=100,
    early_stopping_patience=10
)
"""
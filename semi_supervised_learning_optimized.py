import torch
import torch.nn as nn
import numpy as np
import os
from tqdm import tqdm
import gc

def semi_supervised_learning(model, train_dataset, val_dataset, config):
    """
    Memory-optimized semi-supervised learning function
    
    Args:
        model: The model to train
        train_dataset: Training dataset
        val_dataset: Validation dataset
        config: Dictionary containing training configuration
    """
    # Create TensorBoard writer
    from torch.utils.tensorboard import SummaryWriter
    writer = SummaryWriter(os.path.join(config.get('tensorboard_dir', './tensorboard'), 'semi_supervised'))
    
    # Keep track of best model
    best_model_path = None
    best_loss = float('inf')
    
    # Use smaller batch size to avoid OOM
    batch_size = min(8, config.get('batch_size', 32))
    
    # Track total steps
    total_steps = 0
    
    for iteration in range(config['num_ssl_iterations']):
        print(f'\nSemi-supervised learning iteration {iteration+1}/{config["num_ssl_iterations"]}')
        print(f'Current training set size: {len(train_dataset)} samples')
        
        # Clear GPU memory
        torch.cuda.empty_cache()
        gc.collect()
        
        # Create data loader with smaller batch size
        train_loader = torch.utils.data.DataLoader(
            train_dataset,
            batch_size=batch_size,
            shuffle=False,
            num_workers=2  # Reduced workers
        )
        
        # Collect predictions
        all_images = []
        all_preds = []
        
        model.eval()
        with torch.no_grad():
            for batch_idx, batch in enumerate(tqdm(train_loader, desc="Making predictions")):
                # Process in smaller chunks if needed
                images = batch['image'].to(device)
                
                # Forward pass
                outputs = model(images)
                
                # Model returns heatmap directly
                pred = outputs
                
                # Add to lists
                all_images.extend([img for img in batch['image']])
                all_preds.extend([p for p in pred])
                
                # Clear memory after each batch
                del images, outputs, pred
                torch.cuda.empty_cache()
        
        # Update dataset with high-confidence predictions
        train_dataset.update_with_predictions(all_images, all_preds, threshold=config['confidence_threshold'])
        
        # Clear memory
        del all_images, all_preds
        torch.cuda.empty_cache()
        gc.collect()
        
        # Create new data loaders with smaller batch size
        train_loader = torch.utils.data.DataLoader(
            train_dataset,
            batch_size=batch_size,
            shuffle=True,
            num_workers=2,  # Reduced workers
            pin_memory=False  # Disable pin_memory to save memory
        )
        
        val_loader = torch.utils.data.DataLoader(
            val_dataset,
            batch_size=batch_size,
            shuffle=False,
            num_workers=2,  # Reduced workers
            pin_memory=False  # Disable pin_memory to save memory
        )
        
        # Train for this iteration
        print(f'Training for {config["epochs_per_iteration"]} epochs')
        
        # Create trainer with memory optimization
        from trainer import SpotDetectionTrainer
        trainer = SpotDetectionTrainer(
            model=model,
            loss_fn=loss_fn,
            optimizer=optimizer,
            device=device,
            metrics_calculator=metrics_calculator,
            scheduler=scheduler
        )
        
        # Train with reduced epochs to avoid OOM
        epochs = min(5, config['epochs_per_iteration'])
        history = trainer.train(
            train_loader=train_loader,
            val_loader=val_loader,
            num_epochs=epochs,
            early_stopping_patience=config['early_stopping_patience'],
            save_best_model=True,
            model_save_path=os.path.join(RUN_DIR, f'model_iter{iteration+1}_best.pth')
        )
        
        # Update total steps
        steps_per_epoch = len(train_dataset) // batch_size
        total_steps += steps_per_epoch * epochs
        
        # Log training progress
        writer.add_scalar('progress/total_steps', total_steps, iteration)
        writer.add_scalar('progress/dataset_size', len(train_dataset), iteration)
        
        # Evaluate
        val_metrics = trainer.validate(val_loader)
        print(f"Iteration {iteration+1} Validation Metrics:")
        for k, v in val_metrics.items():
            print(f"{k}: {v:.4f}")
        
        # Update best model if validation loss improves
        val_loss = val_metrics.get('loss', float('inf'))
        if val_loss < best_loss:
            best_loss = val_loss
            best_model_path = os.path.join(RUN_DIR, f'model_iter{iteration+1}_best.pth')
        
        # Clear memory after each iteration
        torch.cuda.empty_cache()
        gc.collect()
    
    writer.close()
    return model, best_model_path
import os
import random
import cv2
import numpy as np
import tifffile
import torch
from torch.utils.data import Dataset
from skimage.measure import regionprops
from scipy.spatial.distance import cdist
from scipy.ndimage import distance_transform_edt, gaussian_filter
from skimage.morphology import skeletonize
import albumentations as A
from albumentations.pytorch import ToTensorV2

class SkeletonAwareSpotDataset(Dataset):
    def __init__(self, image_paths, mask_paths, transform=None, patch_size=256, alpha=0.5):
        """
        Dataset for spot detection using Skeleton-Aware Distance Transform.
        
        Args:
            image_paths: List of paths to images
            mask_paths: List of paths to instance masks
            transform: Albumentations transforms
            patch_size: Size of patches to extract
            alpha: Parameter controlling SDT energy function curvature (0.3-0.5 recommended for spots)
        """
        assert len(image_paths) == len(mask_paths)
        self.image_paths = image_paths
        self.mask_paths = mask_paths
        self.transform = transform
        self.patch_size = patch_size
        self.alpha = alpha  # SDT energy function parameter

    def _get_valid_spots(self, mask, max_id=254):
        """Filter out spot IDs above max_id (default 254 for 8-bit masks)."""
        valid_ids = np.unique(mask)
        return valid_ids[(valid_ids > 0) & (valid_ids <= max_id)]

    def _extract_spot_info(self, mask, valid_ids):
        """Extract centroids and binary masks for each valid spot."""
        centroids = []
        spot_masks = []
        
        for spot_id in valid_ids:
            spot_mask = (mask == spot_id).astype(np.uint8)
            props = regionprops(spot_mask)
            if props:
                centroids.append(props[0].centroid)
                spot_masks.append(spot_mask)
        
        return centroids, spot_masks

    def _generate_semantic_mask(self, spot_masks, shape):
        """Generate a binary semantic mask from individual spot masks."""
        semantic = np.zeros(shape, dtype=np.float32)
        for mask in spot_masks:
            semantic = np.maximum(semantic, mask.astype(np.float32))
        return semantic

    def _generate_boundary_mask(self, mask, valid_ids):
        """Generate boundary mask for all spots."""
        boundary = np.zeros_like(mask, dtype=np.float32)
        kernel = np.ones((3, 3), np.uint8)
        
        for spot_id in valid_ids:
            spot_mask = (mask == spot_id).astype(np.uint8)
            eroded = cv2.erode(spot_mask, kernel, iterations=1)
            boundary += ((spot_mask - eroded) > 0).astype(np.float32)
        
        return np.clip(boundary, 0, 1)

    def _generate_skeleton_aware_distance_transform(self, semantic_mask):
        """
        Generate Skeleton-Aware Distance Transform optimized for small spots.
        
        For very small spots (area < 10 pixels), uses centroid as skeleton.
        For larger spots, computes proper skeleton.
        """
        if not semantic_mask.any():
            return np.zeros_like(semantic_mask, dtype=np.float32), np.zeros_like(semantic_mask, dtype=np.float32)
        
        # For small spots, minimal or no smoothing to preserve details
        # Use smaller sigma for spots compared to cells
        smoothed_mask = gaussian_filter(semantic_mask.astype(float), sigma=0.3)
        smoothed_mask = (smoothed_mask > 0.5).astype(np.uint8)
        
        # Compute distance to boundary
        dist_to_boundary = distance_transform_edt(smoothed_mask)
        
        # For very small spots (area < 10), use centroid as skeleton
        # Otherwise use regular skeletonization
        if np.sum(smoothed_mask) < 10:
            # For tiny spots, use centroid as skeleton
            props = regionprops(smoothed_mask)
            skeleton = np.zeros_like(smoothed_mask, dtype=np.float32)
            if props:
                cy, cx = map(int, props[0].centroid)
                if 0 <= cy < skeleton.shape[0] and 0 <= cx < skeleton.shape[1]:
                    skeleton[cy, cx] = 1
        else:
            # For larger spots, compute proper skeleton
            skeleton = skeletonize(smoothed_mask).astype(np.float32)
        
        # Compute distance to skeleton
        dist_to_skeleton = distance_transform_edt(~skeleton.astype(bool))
        
        # Normalize distances for better numerical stability
        # Use 95th percentile for boundary distance to avoid outliers
        if dist_to_boundary.max() > 0:
            max_dist = np.percentile(dist_to_boundary, 95)
            if max_dist > 0:
                dist_to_boundary = dist_to_boundary / max_dist
                dist_to_boundary = np.clip(dist_to_boundary, 0, 1)
        
        if dist_to_skeleton.max() > 0:
            dist_to_skeleton = dist_to_skeleton / dist_to_skeleton.max()
        
        # Apply the SDT energy function with alpha parameter
        # Lower alpha (0.3-0.5) creates steeper gradients better for small spots
        numerator = np.power(dist_to_boundary, self.alpha)
        denominator = dist_to_skeleton + np.power(dist_to_boundary, self.alpha) + 1e-8
        sdt = numerator / denominator
        
        return sdt, skeleton

    def _generate_centroid_map(self, shape, centroids):
        """Generate centroid map with small 3x3 regions for better learning."""
        centroid_map = np.zeros(shape, dtype=np.float32)
        
        for cy, cx in centroids:
            cy, cx = int(round(cy)), int(round(cx))
            if 0 <= cy < shape[0] and 0 <= cx < shape[1]:
                # Simple point instead of expensive Gaussian
                centroid_map[cy, cx] = 1.0
                
                # Add small 3x3 region for better learning
                for dy in [-1, 0, 1]:
                    for dx in [-1, 0, 1]:
                        ny, nx = cy + dy, cx + dx
                        if 0 <= ny < shape[0] and 0 <= nx < shape[1]:
                            centroid_map[ny, nx] = max(centroid_map[ny, nx], 0.5)
        
        return centroid_map

    def _generate_flow_field(self, semantic_mask, centroids):
        """Generate flow field from each pixel to its nearest centroid."""
        h, w = semantic_mask.shape
        
        if len(centroids) == 0:
            return np.zeros((2, h, w), dtype=np.float32)
        
        # Only compute for spot pixels to save memory
        spot_pixels = np.column_stack(np.where(semantic_mask > 0))
        if len(spot_pixels) == 0:
            return np.zeros((2, h, w), dtype=np.float32)
        
        # Limit number of pixels processed to prevent memory explosion
        if len(spot_pixels) > 10000:  # Limit to 10k pixels max
            indices = np.random.choice(len(spot_pixels), 10000, replace=False)
            spot_pixels = spot_pixels[indices]
        
        flow_y = np.zeros((h, w), dtype=np.float32)
        flow_x = np.zeros((h, w), dtype=np.float32)
        
        centroids_arr = np.array(centroids)
        distances = cdist(spot_pixels, centroids_arr)
        nearest_idx = np.argmin(distances, axis=1)
        
        for i, (py, px) in enumerate(spot_pixels):
            cy, cx = centroids_arr[nearest_idx[i]]
            dy, dx = cy - py, cx - px
            norm = np.sqrt(dy**2 + dx**2) + 1e-8
            flow_y[py, px] = dy / norm
            flow_x[py, px] = dx / norm
        
        return np.stack([flow_y, flow_x])

    def __len__(self):
        return len(self.image_paths)

    def __getitem__(self, idx):
        # Fallback tensors
        empty_img = torch.zeros((1, self.patch_size, self.patch_size), dtype=torch.float32)
        empty_targets = torch.zeros((6, self.patch_size, self.patch_size), dtype=torch.float32)
        
        for attempt in range(3):
            try:
                # Load image and mask
                image = tifffile.imread(self.image_paths[idx]).astype(np.float32)
                mask = tifffile.imread(self.mask_paths[idx]).astype(np.int32)
                
                # Normalize image
                image = image / (image.max() + 1e-8)
                image = np.clip(image, 0, 1)
                
                H, W = image.shape[:2]
                
                # Smart patch extraction - bias toward patches containing spots
                valid_ids = self._get_valid_spots(mask)
                if len(valid_ids) > 0 and random.random() < 0.8:
                    spot_id = random.choice(valid_ids)
                    coords = np.argwhere(mask == spot_id)
                    cy, cx = coords.mean(axis=0).astype(int)
                    x0 = np.clip(cy - self.patch_size // 2, 0, H - self.patch_size)
                    y0 = np.clip(cx - self.patch_size // 2, 0, W - self.patch_size)
                else:
                    x0 = random.randint(0, max(0, H - self.patch_size))
                    y0 = random.randint(0, max(0, W - self.patch_size))
                
                # Extract patch
                patch_img = image[x0:x0+self.patch_size, y0:y0+self.patch_size]
                patch_mask = mask[x0:x0+self.patch_size, y0:y0+self.patch_size]
                
                # Pad if necessary
                ph = self.patch_size - patch_img.shape[0]
                pw = self.patch_size - patch_img.shape[1]
                if ph > 0 or pw > 0:
                    patch_img = np.pad(patch_img, ((0, ph), (0, pw)), 'reflect')
                    patch_mask = np.pad(patch_mask, ((0, ph), (0, pw)), 'constant')
                
                # Get spot information
                valid_ids = self._get_valid_spots(patch_mask)
                if len(valid_ids) == 0:
                    continue
                
                centroids, spot_masks = self._extract_spot_info(patch_mask, valid_ids)
                
                # Generate target maps
                semantic = self._generate_semantic_mask(spot_masks, patch_mask.shape)
                boundary = self._generate_boundary_mask(patch_mask, valid_ids)
                
                # Generate skeleton-aware distance transform
                sdt, skeleton = self._generate_skeleton_aware_distance_transform(semantic)
                
                centroid_map = self._generate_centroid_map(patch_mask.shape, centroids)
                flow_field = self._generate_flow_field(semantic, centroids)
                
                # Flow magnitude
                flow_magnitude = np.sqrt(flow_field[0]**2 + flow_field[1]**2)
                
                # Stack targets (now includes skeleton)
                targets = np.stack([semantic, boundary, sdt, skeleton, centroid_map, flow_magnitude])
                
                # Apply augmentations
                if self.transform is not None:
                    img_u8 = (patch_img * 255).astype(np.uint8)
                    targets_hwc = np.moveaxis(targets, 0, -1)
                    
                    augmented = self.transform(image=img_u8, mask=targets_hwc)
                    patch_img = augmented['image'].astype(np.float32) / 255.0
                    targets = np.moveaxis(augmented['mask'], -1, 0).astype(np.float32)
                    
                    # Ensure binary masks remain binary
                    targets[0] = (targets[0] > 0.5).astype(np.float32)
                    targets[1] = (targets[1] > 0.5).astype(np.float32)
                    targets[3] = (targets[3] > 0.5).astype(np.float32)  # Skeleton is binary
                
                # Convert to tensors
                img_tensor = torch.from_numpy(patch_img).unsqueeze(0) if len(patch_img.shape) == 2 else torch.from_numpy(np.transpose(patch_img, (2, 0, 1)))
                targets_tensor = torch.from_numpy(targets)
                
                return img_tensor, targets_tensor
                
            except Exception as e:
                print(f"Dataset error (attempt {attempt}): {e}")
                continue
        
        return empty_img, empty_targets


def get_transforms(phase):
    """Get augmentation transforms for training/validation."""
    if phase == 'train':
        return A.Compose([
            A.RandomRotate90(p=0.5),
            A.Flip(p=0.5),
            A.ShiftScaleRotate(shift_limit=0.0625, scale_limit=0.1, rotate_limit=15, p=0.5),
            A.OneOf([
                A.GaussNoise(var_limit=(10.0, 50.0), p=0.5),
                A.GaussianBlur(blur_limit=3, p=0.5),
            ], p=0.5),
            A.RandomBrightnessContrast(brightness_limit=0.2, contrast_limit=0.2, p=0.5),
        ], additional_targets={'mask': 'mask'})
    else:
        return None  # No augmentation for validation/test
import torch
import numpy as np
import matplotlib.pyplot as plt
from skimage import io, measure
from skimage.feature import peak_local_max
import cv2
from typing import Dict, List, Tuple, Optional, Union, Any

class ImprovedSpotPredictor:
    """
    Improved predictor for spot detection using direct peak detection
    """
    
    def __init__(
        self,
        model: torch.nn.Module,
        device: torch.device,
        min_distance: int = 5,
        min_intensity: float = 0.1,
        min_spot_size: int = 3
    ):
        """
        Initialize the predictor
        
        Args:
            model: Trained model
            device: Device to use for prediction
            min_distance: Minimum distance between peaks
            min_intensity: Minimum intensity for peak detection
            min_spot_size: Minimum spot size in pixels
        """
        self.model = model
        self.device = device
        self.min_distance = min_distance
        self.min_intensity = min_intensity
        self.min_spot_size = min_spot_size
        
        # Set model to evaluation mode
        self.model.eval()
    
    def predict(
        self,
        image: Union[str, np.ndarray, torch.Tensor],
        return_heatmap: bool = True
    ) -> Dict[str, Any]:
        """
        Predict spots in an image using direct peak detection
        
        Args:
            image: Image to predict (path, numpy array, or tensor)
            return_heatmap: Whether to return the raw heatmap
            
        Returns:
            Dict with prediction results
        """
        # Load image if it's a path
        if isinstance(image, str):
            image_np = io.imread(image)
            if len(image_np.shape) == 3 and image_np.shape[2] > 1:
                image_np = cv2.cvtColor(image_np, cv2.COLOR_RGB2GRAY)
            image_np = image_np.astype(np.float32) / 255.0
        elif isinstance(image, np.ndarray):
            image_np = image.copy()
            if len(image_np.shape) == 3 and image_np.shape[2] > 1:
                image_np = cv2.cvtColor(image_np, cv2.COLOR_RGB2GRAY)
            if image_np.max() > 1.0:
                image_np = image_np.astype(np.float32) / 255.0
        else:
            # Convert tensor to numpy
            image_np = image.squeeze().cpu().numpy()
            if len(image_np.shape) == 3 and image_np.shape[0] == 1:
                image_np = image_np[0]
        
        # Ensure image has correct dimensions
        if len(image_np.shape) == 2:
            image_tensor = torch.from_numpy(image_np).unsqueeze(0).unsqueeze(0)
        else:
            image_tensor = torch.from_numpy(image_np).unsqueeze(0)
        
        # Normalize if needed
        if image_tensor.max() > 1.0:
            image_tensor = image_tensor / 255.0
        
        # Move to device
        image_tensor = image_tensor.to(self.device)
        
        # Predict
        with torch.no_grad():
            outputs = self.model(image_tensor)
            
            # Get main output
            if isinstance(outputs, dict):
                pred = outputs.get('output', outputs.get('combined_output', None))
                expert_outputs = outputs.get('expert_outputs', None)
                gating_weights = outputs.get('gating_weights', None)
            else:
                pred = outputs
                expert_outputs = None
                gating_weights = None
            
            # Apply sigmoid
            pred_sigmoid = torch.sigmoid(pred)
            
            # Convert to numpy
            heatmap = pred_sigmoid.squeeze().cpu().numpy()
        
        # Find local maxima directly in the heatmap
        coordinates = peak_local_max(
            heatmap,
            min_distance=self.min_distance,
            threshold_abs=self.min_intensity,
            exclude_border=False
        )
        
        # Create spot properties
        spot_props = []
        for i, (y, x) in enumerate(coordinates):
            # Get intensity at this peak
            intensity = heatmap[y, x]
            
            # Calculate radius based on intensity (higher intensity = larger radius)
            r = max(2, int(5 * intensity))
            
            # Calculate area from radius
            area = np.pi * r * r
            
            # Add spot properties
            spot_props.append({
                'id': i + 1,
                'centroid': (y, x),
                'area': area,
                'mean_intensity': intensity,
                'max_intensity': intensity,
                'bbox': (max(0, y-r), max(0, x-r), min(heatmap.shape[0], y+r), min(heatmap.shape[1], x+r))
            })
        
        # Prepare result
        result = {
            'num_spots': len(spot_props),
            'spot_props': spot_props,
            'coordinates': coordinates
        }
        
        # Add raw heatmap if requested
        if return_heatmap:
            result['heatmap'] = heatmap
        
        # Add expert outputs if available
        if expert_outputs is not None:
            expert_heatmaps = [torch.sigmoid(out).squeeze().cpu().numpy() 
                              for out in expert_outputs]
            result['expert_heatmaps'] = expert_heatmaps
            result['gating_weights'] = gating_weights.squeeze().cpu().numpy()
        
        return result
    
    def visualize(
        self,
        image: Union[str, np.ndarray, torch.Tensor],
        result: Optional[Dict] = None,
        show_spots: bool = True,
        show_heatmap: bool = True,
        show_experts: bool = False,
        figsize: Tuple[int, int] = (15, 10)
    ) -> None:
        """
        Visualize prediction results
        
        Args:
            image: Image to visualize
            result: Prediction result (if None, will be computed)
            show_spots: Whether to show detected spots
            show_heatmap: Whether to show heatmap
            show_experts: Whether to show expert outputs
            figsize: Figure size
        """
        # Load and normalize image
        if isinstance(image, str):
            image_np = io.imread(image)
            if len(image_np.shape) == 3 and image_np.shape[2] > 1:
                image_np = cv2.cvtColor(image_np, cv2.COLOR_RGB2GRAY)
            image_np = image_np.astype(np.float32) / 255.0
        elif isinstance(image, np.ndarray):
            image_np = image.copy()
            if len(image_np.shape) == 3 and image_np.shape[2] > 1:
                image_np = cv2.cvtColor(image_np, cv2.COLOR_RGB2GRAY)
            if image_np.max() > 1.0:
                image_np = image_np.astype(np.float32) / 255.0
        else:
            # Convert tensor to numpy
            image_np = image.squeeze().cpu().numpy()
            if len(image_np.shape) == 3 and image_np.shape[0] == 1:
                image_np = image_np[0]
        
        # Get prediction if not provided
        if result is None:
            result = self.predict(image_np, return_heatmap=True)
        
        # Create figure
        num_plots = 1  # Original image
        if show_spots:
            num_plots += 1
        if show_heatmap:
            num_plots += 1
        if show_experts and 'expert_heatmaps' in result:
            num_plots += len(result['expert_heatmaps'])
        
        fig, axes = plt.subplots(1, num_plots, figsize=figsize)
        if num_plots == 1:
            axes = [axes]
        
        # Plot original image
        axes[0].imshow(image_np, cmap='gray')
        axes[0].set_title('Original Image')
        axes[0].axis('off')
        
        plot_idx = 1
        
        # Plot spots
        if show_spots and 'spot_props' in result:
            # Create RGB image for visualization
            rgb_mask = np.zeros((*image_np.shape, 3))
            rgb_mask[..., 0] = image_np  # Red channel
            rgb_mask[..., 1] = image_np  # Green channel
            rgb_mask[..., 2] = image_np  # Blue channel
            
            # Overlay spots
            for prop in result['spot_props']:
                y, x = prop['centroid']
                r = int(np.sqrt(prop['area'] / np.pi))
                
                # Draw circle
                cv2.circle(
                    rgb_mask,
                    (int(x), int(y)),
                    r,
                    (1, 0, 0),  # Red
                    1
                )
                
                # Add ID
                cv2.putText(
                    rgb_mask,
                    str(prop['id']),
                    (int(x), int(y)),
                    cv2.FONT_HERSHEY_SIMPLEX,
                    0.3,
                    (0, 1, 0),  # Green
                    1
                )
            
            axes[plot_idx].imshow(rgb_mask)
            axes[plot_idx].set_title(f'Detected Spots: {result["num_spots"]}')
            axes[plot_idx].axis('off')
            plot_idx += 1
        
        # Plot heatmap
        if show_heatmap and 'heatmap' in result:
            axes[plot_idx].imshow(result['heatmap'], cmap='hot')
            axes[plot_idx].set_title('Prediction Heatmap')
            axes[plot_idx].axis('off')
            plot_idx += 1
        
        # Plot expert outputs
        if show_experts and 'expert_heatmaps' in result:
            for i, expert_heatmap in enumerate(result['expert_heatmaps']):
                weight = result['gating_weights'][i] if 'gating_weights' in result else None
                weight_str = f" (w={weight:.2f})" if weight is not None else ""
                
                axes[plot_idx].imshow(expert_heatmap, cmap='hot')
                axes[plot_idx].set_title(f'Expert {i+1}{weight_str}')
                axes[plot_idx].axis('off')
                plot_idx += 1
        
        plt.tight_layout()
        plt.show()
    
    def test_parameters(
        self,
        image: Union[str, np.ndarray, torch.Tensor],
        min_distances: List[int] = [3, 5, 7, 10],
        min_intensities: List[float] = [0.05, 0.1, 0.15, 0.2, 0.3]
    ) -> Tuple[int, float, Dict]:
        """
        Test different parameters for peak detection
        
        Args:
            image: Image to test
            min_distances: List of minimum distances to test
            min_intensities: List of minimum intensities to test
            
        Returns:
            Tuple of (best_distance, best_intensity, best_result)
        """
        # Get prediction heatmap
        if isinstance(image, str) or isinstance(image, np.ndarray) or isinstance(image, torch.Tensor):
            result = self.predict(image, return_heatmap=True)
            heatmap = result['heatmap']
        else:
            heatmap = image
        
        # Load image if needed
        if isinstance(image, str):
            image_np = io.imread(image)
            if len(image_np.shape) == 3 and image_np.shape[2] > 1:
                image_np = cv2.cvtColor(image_np, cv2.COLOR_RGB2GRAY)
            image_np = image_np.astype(np.float32) / 255.0
        elif isinstance(image, np.ndarray):
            image_np = image.copy()
            if len(image_np.shape) == 3 and image_np.shape[2] > 1:
                image_np = cv2.cvtColor(image_np, cv2.COLOR_RGB2GRAY)
            if image_np.max() > 1.0:
                image_np = image_np.astype(np.float32) / 255.0
        else:
            # Convert tensor to numpy
            image_np = image.squeeze().cpu().numpy()
            if len(image_np.shape) == 3 and image_np.shape[0] == 1:
                image_np = image_np[0]
        
        # Create figure
        fig, axes = plt.subplots(len(min_distances), len(min_intensities), figsize=(20, 16))
        
        # Test each combination
        results = {}
        
        for i, min_distance in enumerate(min_distances):
            for j, min_intensity in enumerate(min_intensities):
                # Find local maxima
                coordinates = peak_local_max(
                    heatmap,
                    min_distance=min_distance,
                    threshold_abs=min_intensity,
                    exclude_border=False
                )
                
                # Create visualization
                rgb_mask = np.zeros((*image_np.shape, 3))
                rgb_mask[..., 0] = image_np  # Red channel
                rgb_mask[..., 1] = image_np  # Green channel
                rgb_mask[..., 2] = image_np  # Blue channel
                
                # Draw circles at peak locations
                for k, (y, x) in enumerate(coordinates):
                    # Get intensity at this peak
                    intensity = heatmap[y, x]
                    
                    # Calculate radius based on intensity
                    r = max(2, int(5 * intensity))
                    
                    # Draw circle
                    cv2.circle(
                        rgb_mask,
                        (int(x), int(y)),
                        r,
                        (1, 0, 0),  # Red
                        1
                    )
                
                # Store result
                key = f"distance_{min_distance}_intensity_{min_intensity}"
                results[key] = {
                    'num_spots': len(coordinates),
                    'coordinates': coordinates,
                    'visualization': rgb_mask
                }
                
                # Plot result
                axes[i, j].imshow(rgb_mask)
                axes[i, j].set_title(f'D={min_distance}, I={min_intensity}, N={len(coordinates)}')
                axes[i, j].axis('off')
        
        plt.tight_layout()
        plt.show()
        
        # Find best parameters (most spots detected)
        best_params = max(results.items(), key=lambda x: x[1]['num_spots'])
        best_key = best_params[0]
        best_result = best_params[1]
        
        parts = best_key.split('_')
        best_distance = int(parts[1])
        best_intensity = float(parts[3])
        
        print(f"\nBest parameters: min_distance={best_distance}, min_intensity={best_intensity}")
        print(f"Number of spots detected: {best_result['num_spots']}")
        
        # Show best result
        plt.figure(figsize=(10, 10))
        plt.imshow(best_result['visualization'])
        plt.title(f'Best Result: {best_result["num_spots"]} spots')
        plt.axis('off')
        plt.show()
        
        return best_distance, best_intensity, best_result
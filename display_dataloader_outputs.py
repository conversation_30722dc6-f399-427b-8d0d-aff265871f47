import matplotlib.pyplot as plt
import numpy as np
import torch
import tifffile
from pathlib import Path

def display_dataloader_outputs(dataset, idx=0, save_path=None):
    """
    Display all outputs from the AdaptiveSpotDataset to verify correctness.
    
    Args:
        dataset: AdaptiveSpotDataset instance
        idx: Index of the sample to display
        save_path: Optional path to save the visualization
    """
    # Get a sample from the dataset
    img_tensor, mask_tensor, flow_tensor, confidence_tensor = dataset[idx]
    
    # Convert tensors to numpy arrays
    img = img_tensor[0].numpy()  # Remove channel dimension
    masks = mask_tensor.numpy()  # Shape: (5, H, W)
    flow = flow_tensor.numpy()   # Shape: (2, H, W)
    confidence = confidence_tensor[0].numpy()  # Remove channel dimension
    
    # Extract individual mask channels
    semantic_mask = masks[0]      # Binary mask of all spots
    boundary_mask = masks[1]      # Boundary/edge mask
    distance_map = masks[2]       # Distance transform
    instance_mask = masks[3]      # Instance mask (union of all spots)
    centroid_map = masks[4]       # Centroid map (point locations)
    
    # Extract flow components
    flow_y = flow[0]  # Flow in Y direction
    flow_x = flow[1]  # Flow in X direction
    
    # Create the visualization
    fig, axes = plt.subplots(3, 4, figsize=(20, 15))
    fig.suptitle(f'DataLoader Outputs - Sample {idx}', fontsize=16)
    
    # Row 1: Input and main outputs
    axes[0, 0].imshow(img, cmap='gray')
    axes[0, 0].set_title('Input Image')
    axes[0, 0].axis('off')
    
    axes[0, 1].imshow(semantic_mask, cmap='gray')
    axes[0, 1].set_title('Semantic Mask\n(All spots binary)')
    axes[0, 1].axis('off')
    
    axes[0, 2].imshow(instance_mask, cmap='gray')
    axes[0, 2].set_title('Instance Mask\n(Union of spot masks)')
    axes[0, 2].axis('off')
    
    axes[0, 3].imshow(centroid_map, cmap='hot')
    axes[0, 3].set_title('Centroid Map\n(Point locations)')
    axes[0, 3].axis('off')
    
    # Row 2: Boundary, distance, and flow
    axes[1, 0].imshow(boundary_mask, cmap='gray')
    axes[1, 0].set_title('Boundary Mask\n(Spot edges)')
    axes[1, 0].axis('off')
    
    axes[1, 1].imshow(distance_map, cmap='viridis')
    axes[1, 1].set_title('Distance Transform\n(Distance from edges)')
    axes[1, 1].axis('off')
    
    # Flow field visualization
    axes[1, 2].imshow(img, cmap='gray', alpha=0.7)
    step = max(1, img.shape[0] // 16)  # Subsample for cleaner visualization
    Y, X = np.mgrid[0:img.shape[0]:step, 0:img.shape[1]:step]
    axes[1, 2].quiver(X, Y, flow_x[::step, ::step], flow_y[::step, ::step], 
                     color='cyan', angles='xy', scale_units='xy', scale=1, width=0.003)
    axes[1, 2].set_title('Flow Field\n(Vectors to centroids)')
    axes[1, 2].axis('off')
    
    axes[1, 3].imshow(confidence, cmap='viridis')
    axes[1, 3].set_title('Confidence Map\n(Training weights)')
    axes[1, 3].axis('off')
    
    # Row 3: Flow components and overlays
    axes[2, 0].imshow(flow_y, cmap='RdBu_r', vmin=-1, vmax=1)
    axes[2, 0].set_title('Flow Y Component\n(Vertical flow)')
    axes[2, 0].axis('off')
    
    axes[2, 1].imshow(flow_x, cmap='RdBu_r', vmin=-1, vmax=1)
    axes[2, 1].set_title('Flow X Component\n(Horizontal flow)')
    axes[2, 1].axis('off')
    
    # Overlay semantic mask on image
    axes[2, 2].imshow(img, cmap='gray')
    axes[2, 2].imshow(semantic_mask, cmap='Reds', alpha=0.5)
    axes[2, 2].set_title('Image + Semantic Overlay')
    axes[2, 2].axis('off')
    
    # Overlay centroids on image
    axes[2, 3].imshow(img, cmap='gray')
    # Find centroid locations
    centroid_coords = np.where(centroid_map > 0.5)
    if len(centroid_coords[0]) > 0:
        axes[2, 3].scatter(centroid_coords[1], centroid_coords[0], 
                          c='red', s=30, marker='x', linewidths=2)
    axes[2, 3].set_title('Image + Centroids')
    axes[2, 3].axis('off')
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        print(f"Visualization saved to: {save_path}")
    
    plt.show()
    
    # Print statistics
    print(f"\n=== Sample {idx} Statistics ===")
    print(f"Image shape: {img.shape}")
    print(f"Image range: [{img.min():.3f}, {img.max():.3f}]")
    print(f"Semantic mask: {semantic_mask.sum():.0f} pixels ({semantic_mask.mean()*100:.1f}%)")
    print(f"Boundary mask: {boundary_mask.sum():.0f} pixels ({boundary_mask.mean()*100:.1f}%)")
    print(f"Instance mask: {instance_mask.sum():.0f} pixels ({instance_mask.mean()*100:.1f}%)")
    print(f"Centroids found: {(centroid_map > 0.5).sum():.0f}")
    print(f"Distance map range: [{distance_map.min():.3f}, {distance_map.max():.3f}]")
    print(f"Flow Y range: [{flow_y.min():.3f}, {flow_y.max():.3f}]")
    print(f"Flow X range: [{flow_x.min():.3f}, {flow_x.max():.3f}]")
    print(f"Confidence range: [{confidence.min():.3f}, {confidence.max():.3f}]")
    
    return {
        'image': img,
        'semantic_mask': semantic_mask,
        'boundary_mask': boundary_mask,
        'distance_map': distance_map,
        'instance_mask': instance_mask,
        'centroid_map': centroid_map,
        'flow_y': flow_y,
        'flow_x': flow_x,
        'confidence': confidence
    }

def test_dataloader_with_sample_images():
    """
    Test the dataloader with sample images to verify all outputs are correct.
    """
    # Import the dataset class (assuming it's available in the notebook)
    import sys
    sys.path.append('/mnt/d/Users/<USER>/Documents/spot detector git amazon 2025')
    
    # Sample image and mask paths - update these to your actual paths
    image_paths = [
        "/mnt/d/Users/<USER>/FISH_spots/2d/image_raw/large spots/images/synthetic_000596.tif",
        "/mnt/d/Users/<USER>/FISH_spots/2d/image_raw/large spots/images/synthetic_000597.tif"
    ]
    
    mask_paths = [
        "/mnt/d/Users/<USER>/FISH_spots/2d/image_raw/large spots/masks/synthetic_000596.tif",
        "/mnt/d/Users/<USER>/FISH_spots/2d/image_raw/large spots/masks/synthetic_000597.tif"
    ]
    
    # Create dataset instance
    from AdaptiveSpotDataset import AdaptiveSpotDataset  # Adjust import as needed
    
    dataset = AdaptiveSpotDataset(
        image_paths=image_paths,
        mask_paths=mask_paths,
        transform=None,  # No augmentation for testing
        patch_size=128
    )
    
    print(f"Dataset created with {len(dataset)} samples")
    
    # Display outputs for first few samples
    for i in range(min(3, len(dataset))):
        print(f"\n{'='*50}")
        print(f"Displaying sample {i}")
        print(f"{'='*50}")
        
        outputs = display_dataloader_outputs(
            dataset, 
            idx=i, 
            save_path=f"/mnt/d/Users/<USER>/Documents/spot detector git amazon 2025/dataloader_test_sample_{i}.png"
        )

if __name__ == "__main__":
    test_dataloader_with_sample_images()
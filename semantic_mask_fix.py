# The issue: Model learned large spots from non-eroded GT, now predicts large spots

# SOLUTION 1: Apply erosion to predictions during inference
def apply_erosion_to_predictions(semantic_pred, kernel_size=2, iterations=1):
    """Apply erosion to predicted semantic mask"""
    import cv2
    kernel = np.ones((kernel_size, kernel_size), np.uint8)
    eroded = cv2.erode((semantic_pred > 0.5).astype(np.uint8), kernel, iterations=iterations)
    return eroded.astype(np.float32)

# In your training visualization, replace:
# sem = torch.sigmoid(outputs['semantic'])[0, 0].cpu().numpy()

# With:
sem_raw = torch.sigmoid(outputs['semantic'])[0, 0].cpu().numpy()
sem = apply_erosion_to_predictions(sem_raw, kernel_size=2, iterations=1)

# SOLUTION 2: Adjust prediction threshold
# Instead of 0.5, use higher threshold like 0.7 to get smaller spots:
sem = (torch.sigmoid(outputs['semantic'])[0, 0].cpu().numpy() > 0.7).astype(np.float32)

# SOLUTION 3: Train longer - the model needs time to learn the new eroded GT
# The model will eventually learn to predict smaller spots if you continue training
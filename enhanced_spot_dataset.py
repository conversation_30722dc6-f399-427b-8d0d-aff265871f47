import torch
import numpy as np
from torch.utils.data import Dataset
from typing import Dict, List, Tuple, Optional, Union
from enhanced_synthetic_data_generator import EnhancedSyntheticSpotGenerator

class EnhancedSpotDataset(Dataset):
    """
    Dataset class for enhanced spot detection that stores spot centroids and masks
    
    This dataset class generates synthetic data with spot centroids and masks,
    which can be used for more accurate spot detection during training.
    """
    
    def __init__(self, 
                num_samples=1000,
                image_size=(256, 256),
                min_spots=5,
                max_spots=50,
                min_radius=2,
                max_radius=10,
                density_factor=1.0,
                mask_threshold=0.15,
                shape_variation=0.3,
                add_gradients=True,
                realistic_noise=True,
                transform=None):
        """Initialize the enhanced spot dataset"""
        self.num_samples = num_samples
        self.image_size = image_size
        self.transform = transform
        
        # Initialize generator
        self.generator = EnhancedSyntheticSpotGenerator(
            image_size=image_size,
            min_spots=min_spots,
            max_spots=max_spots,
            min_radius=min_radius,
            max_radius=max_radius,
            density_factor=density_factor,
            mask_threshold=mask_threshold,
            shape_variation=shape_variation,
            add_gradients=add_gradients,
            realistic_noise=realistic_noise
        )
        
        # Generate dataset
        self.images, self.masks, self.spot_data_list = self.generator.generate_dataset(
            num_samples=num_samples,
            variable_params=True
        )
    
    def __len__(self):
        """Return the number of samples in the dataset"""
        return len(self.images)
    
    def __getitem__(self, idx):
        """Get a sample from the dataset"""
        # Get image and mask
        image = self.images[idx]
        mask = self.masks[idx]
        spot_data = self.spot_data_list[idx]
        
        # Convert to tensors
        image_tensor = torch.from_numpy(image).float().unsqueeze(0)  # Add channel dimension
        mask_tensor = torch.from_numpy(mask).float().unsqueeze(0)  # Add channel dimension
        
        # Apply transform if provided
        if self.transform:
            image_tensor = self.transform(image_tensor)
        
        # Count spots
        true_spot_count = len(spot_data)
        
        # Return sample
        return {
            'image': image_tensor,
            'mask': mask_tensor,
            'spot_data': spot_data,
            'true_spot_count': true_spot_count
        }
    
    def get_spot_data(self, idx):
        """Get spot data for a sample"""
        return self.spot_data_list[idx]
    
    def visualize_sample(self, idx):
        """Visualize a sample"""
        image = self.images[idx]
        mask = self.masks[idx]
        spot_data = self.spot_data_list[idx]
        
        return self.generator.visualize_sample(image, mask, spot_data)


class CombinedEnhancedDataset(Dataset):
    """
    Combined dataset class that mixes synthetic and real data
    
    This dataset class combines synthetic data with real data,
    using the enhanced spot detection approach for both.
    """
    
    def __init__(self, 
                synthetic_dataset,
                real_dataset=None,
                synthetic_ratio=0.5,
                transform=None):
        """Initialize the combined dataset"""
        self.synthetic_dataset = synthetic_dataset
        self.real_dataset = real_dataset
        self.synthetic_ratio = synthetic_ratio
        self.transform = transform
        
        # Calculate dataset sizes
        self.synthetic_size = len(synthetic_dataset)
        self.real_size = len(real_dataset) if real_dataset is not None else 0
        self.total_size = self.synthetic_size + self.real_size
    
    def __len__(self):
        """Return the number of samples in the dataset"""
        return self.total_size
    
    def __getitem__(self, idx):
        """Get a sample from the dataset"""
        # Determine if we should return a synthetic or real sample
        if idx < self.synthetic_size:
            # Get synthetic sample
            sample = self.synthetic_dataset[idx]
            sample['is_synthetic'] = True
        else:
            # Get real sample
            real_idx = idx - self.synthetic_size
            sample = self.real_dataset[real_idx]
            sample['is_synthetic'] = False
        
        # Apply transform if provided
        if self.transform:
            sample['image'] = self.transform(sample['image'])
        
        return sample
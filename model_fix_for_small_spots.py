# PROBLEM: ASPP dilation rates [1,4,8] create large receptive fields
# For small spots (2-6 pixels), this causes the model to "see" too much context

# SOLUTION 1: Reduce ASPP dilation rates for small spots
class SepASPP(nn.Module):
    def __init__(self, in_ch, out_ch, rates=[1,2,4]):  # ✅ SMALLER rates
        super().__init__()
        # ... rest unchanged

# SOLUTION 2: Add size-aware loss to penalize large predictions
class AdaptiveSpotLoss(nn.Module):
    def __init__(self, epsilon=1e-6):
        super().__init__()
        self.eps = epsilon
        # Adjusted weights for small spots
        self.w_sem = 0.3      # Further reduce semantic
        self.w_bnd = 3.0      # Increase boundary (sharp edges)
        self.w_dist = 4.0     # Increase distance (shape info)
        self.w_inst = 0.5     # Reduce instance
        self.w_hm = 2.0
        self.w_flow = 1.0
        self.w_ds = 0.5
        self.w_flowds = 0.5

    def _size_penalty_loss(self, pred_logits, gt_mask, gt_distance):
        """Penalize predictions that are larger than ground truth"""
        pred_prob = torch.sigmoid(pred_logits)
        
        # Penalty for predicting positive where GT distance is low (far from center)
        size_penalty = torch.mean(pred_prob * (1 - gt_distance) * (gt_mask == 0))
        return size_penalty

    def forward(self, outputs, targets, flow_targets, confidence=None):
        losses = {}
        
        # unpack targets
        gt_sem = targets[:,0:1]
        gt_bnd = targets[:,1:2]
        gt_dist = targets[:,2:3]
        gt_inst = targets[:,3:4]
        gt_cent = targets[:,4:5]

        # semantic with size penalty
        l1 = self._focal(outputs['semantic'], gt_sem)
        l2 = self._dice(outputs['semantic'], gt_sem, confidence)
        l3 = self._size_penalty_loss(outputs['semantic'], gt_sem, gt_dist)  # NEW
        losses['semantic'] = l1 + l2 + 0.5 * l3

        # ... rest of losses unchanged ...

# SOLUTION 3: Use smaller kernels in final layers
class AdaptiveSpotDetector(nn.Module):
    def __init__(self, ...):
        # ... existing code ...
        
        # Use 1x1 kernels for final heads to avoid spatial spreading
        self.semantic_head = nn.Conv2d(base_ch, 1, 1)  # ✅ Already 1x1
        self.boundary_head = nn.Conv2d(base_ch, 1, 1)  # ✅ Already 1x1
        self.distance_head = nn.Conv2d(base_ch, 1, 1)  # ✅ Already 1x1
import numpy as np
import torch
from torch.utils.data import Dataset
import tifffile
import cv2
from torchvision import transforms
from tqdm import tqdm

class MicroscopyDatasetFixed(Dataset):
    """
    Fixed dataset class that generates smooth heatmaps without grid artifacts
    """
    def __init__(self, image_paths, mask_paths, patch_size=256, 
                 transform=None, gaussian_sigma=3.0):
        self.image_paths = image_paths
        self.mask_paths = mask_paths
        self.patch_size = patch_size
        self.transform = transform
        self.gaussian_sigma = gaussian_sigma
        
    def __len__(self):
        return len(self.image_paths)
    
    def _create_smooth_heatmap(self, mask):
        """Create smooth Gaussian heatmaps from instance masks"""
        heatmap = np.zeros_like(mask, dtype=np.float32)
        
        # Find unique instances (excluding background)
        instance_ids = np.unique(mask)
        instance_ids = instance_ids[instance_ids > 0]
        
        for instance_id in instance_ids:
            # Get instance mask
            instance_mask = (mask == instance_id).astype(np.uint8)
            
            # Find centroid using moments for better accuracy
            moments = cv2.moments(instance_mask)
            if moments['m00'] > 0:
                cx = int(moments['m10'] / moments['m00'])
                cy = int(moments['m01'] / moments['m00'])
                
                # Create Gaussian around centroid
                h, w = mask.shape
                y, x = np.ogrid[:h, :w]
                
                # Gaussian formula with configurable sigma
                gaussian = np.exp(-((x - cx)**2 + (y - cy)**2) / (2 * self.gaussian_sigma**2))
                
                # Add to heatmap (max operation to handle overlapping spots)
                heatmap = np.maximum(heatmap, gaussian)
        
        return heatmap
    
    def __getitem__(self, idx):
        try:
            # Load image and mask
            image = tifffile.imread(self.image_paths[idx])
            mask = tifffile.imread(self.mask_paths[idx])
            
            # Normalize image
            if np.issubdtype(image.dtype, np.floating):
                image = image.astype(np.float32)
            else:
                image = image.astype(np.float32)
                if np.max(image) > 0:
                    image = image / np.max(image)
            
            # Clip to [0, 1] and handle NaN/Inf
            image = np.clip(image, 0.0, 1.0)
            image = np.nan_to_num(image, nan=0.0, posinf=1.0, neginf=0.0)
            
            # Random crop with preference for areas with spots
            h, w = image.shape
            if h >= self.patch_size and w >= self.patch_size:
                # Try to find a good crop location near spots
                instance_ids = np.unique(mask)
                if len(instance_ids) > 1:  # Has spots
                    non_zero_instances = instance_ids[instance_ids > 0]
                    if len(non_zero_instances) > 0:
                        # Pick random instance and center crop around it
                        target_instance = np.random.choice(non_zero_instances)
                        instance_coords = np.argwhere(mask == target_instance)
                        if len(instance_coords) > 0:
                            center_y, center_x = np.mean(instance_coords, axis=0).astype(int)
                            
                            # Calculate crop boundaries
                            start_y = max(0, center_y - self.patch_size // 2)
                            start_x = max(0, center_x - self.patch_size // 2)
                            start_y = min(start_y, h - self.patch_size)
                            start_x = min(start_x, w - self.patch_size)
                        else:
                            # Fallback to random crop
                            start_y = np.random.randint(0, h - self.patch_size + 1)
                            start_x = np.random.randint(0, w - self.patch_size + 1)
                    else:
                        # Random crop if no spots
                        start_y = np.random.randint(0, h - self.patch_size + 1)
                        start_x = np.random.randint(0, w - self.patch_size + 1)
                else:
                    # Random crop if no spots
                    start_y = np.random.randint(0, h - self.patch_size + 1)
                    start_x = np.random.randint(0, w - self.patch_size + 1)
                
                # Extract patches
                image_patch = image[start_y:start_y+self.patch_size, 
                                  start_x:start_x+self.patch_size]
                mask_patch = mask[start_y:start_y+self.patch_size, 
                                start_x:start_x+self.patch_size]
            else:
                # Pad if image is smaller than patch size
                pad_h = max(0, self.patch_size - h)
                pad_w = max(0, self.patch_size - w)
                image_patch = np.pad(image, ((0, pad_h), (0, pad_w)), mode='reflect')
                mask_patch = np.pad(mask, ((0, pad_h), (0, pad_w)), mode='constant')
                
                # Crop to exact patch size if needed
                image_patch = image_patch[:self.patch_size, :self.patch_size]
                mask_patch = mask_patch[:self.patch_size, :self.patch_size]
            
            # Create smooth heatmap (this is the key fix!)
            heatmap = self._create_smooth_heatmap(mask_patch)
            
            # Apply transforms if specified
            if self.transform:
                # Convert to PIL for transforms
                image_pil = transforms.ToPILImage()(image_patch)
                
                # Apply same transform to image and heatmap
                seed = np.random.randint(2147483647)
                
                # Transform image
                torch.manual_seed(seed)
                image_transformed = self.transform(image_pil)
                
                # Transform heatmap (resize to match transformed image)
                if isinstance(image_transformed, torch.Tensor):
                    target_size = image_transformed.shape[-2:]
                    heatmap = cv2.resize(heatmap, (target_size[1], target_size[0]), 
                                       interpolation=cv2.INTER_LINEAR)
                
                image_tensor = image_transformed
            else:
                image_tensor = torch.FloatTensor(image_patch).unsqueeze(0)
            
            # Convert heatmap to tensor
            heatmap_tensor = torch.FloatTensor(heatmap).unsqueeze(0)
            
            # Ensure tensors have correct dimensions
            if image_tensor.dim() == 2:
                image_tensor = image_tensor.unsqueeze(0)
            if heatmap_tensor.dim() == 2:
                heatmap_tensor = heatmap_tensor.unsqueeze(0)
            
            return image_tensor, heatmap_tensor
            
        except Exception as e:
            print(f"Error processing sample {idx}: {str(e)}")
            # Return empty tensors as fallback
            empty_image = torch.zeros((1, self.patch_size, self.patch_size))
            empty_heatmap = torch.zeros((1, self.patch_size, self.patch_size))
            return empty_image, empty_heatmap


def visualize_heatmap_comparison(dataset, idx=0):
    """
    Utility function to visualize the difference between old and new heatmap generation
    """
    import matplotlib.pyplot as plt
    
    # Get sample
    image, heatmap = dataset[idx]
    
    # Convert to numpy for visualization
    if torch.is_tensor(image):
        image_np = image.squeeze().numpy()
    else:
        image_np = image
        
    if torch.is_tensor(heatmap):
        heatmap_np = heatmap.squeeze().numpy()
    else:
        heatmap_np = heatmap
    
    # Create visualization
    fig, axes = plt.subplots(1, 3, figsize=(15, 5))
    
    # Original image
    axes[0].imshow(image_np, cmap='gray')
    axes[0].set_title('Original Image')
    axes[0].axis('off')
    
    # Smooth heatmap
    axes[1].imshow(heatmap_np, cmap='hot')
    axes[1].set_title('Smooth Gaussian Heatmap')
    axes[1].axis('off')
    
    # Overlay
    axes[2].imshow(image_np, cmap='gray', alpha=0.7)
    axes[2].imshow(heatmap_np, cmap='hot', alpha=0.5)
    axes[2].set_title('Overlay')
    axes[2].axis('off')
    
    plt.tight_layout()
    plt.show()
    
    print(f"Heatmap stats: min={heatmap_np.min():.3f}, max={heatmap_np.max():.3f}, "
          f"mean={heatmap_np.mean():.3f}")
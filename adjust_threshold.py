import torch
import numpy as np
import matplotlib.pyplot as plt
from skimage import io, measure, morphology
import os
import sys
import argparse
from typing import Dict, List, Tuple, Optional, Union, Any

# Import your model and predictor
from OptimizedSpotDetection_model import OptimizedSpotDetectionModel
from predict import SpotDetectionPredictor

def load_model(model_path, device):
    """Load the trained model from disk"""
    # Create model instance
    model = OptimizedSpotDetectionModel(
        in_channels=1,
        num_experts=3,
        base_filters=64,
        dropout_rate=0.2
    )
    
    # Load weights
    model.load_state_dict(torch.load(model_path, map_location=device))
    model = model.to(device)
    model.eval()
    
    return model

def analyze_threshold_impact(
    image_path: str,
    model_path: str,
    thresholds: List[float] = [0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9],
    min_spot_sizes: List[int] = [1, 3, 5, 10],
    device: str = 'cuda' if torch.cuda.is_available() else 'cpu'
):
    """
    Analyze the impact of different threshold and min_spot_size values on spot detection
    
    Args:
        image_path: Path to the image
        model_path: Path to the trained model
        thresholds: List of threshold values to test
        min_spot_sizes: List of minimum spot sizes to test
        device: Device to use for inference
    """
    # Load model
    model = load_model(model_path, device)
    
    # Load image
    image = io.imread(image_path)
    if len(image.shape) == 3 and image.shape[2] > 1:
        image = image[:, :, 0]  # Take first channel if RGB
    
    # Normalize image
    image = image.astype(np.float32) / 255.0
    
    # Create figure for visualization
    n_thresholds = len(thresholds)
    n_sizes = len(min_spot_sizes)
    fig, axes = plt.subplots(n_sizes, n_thresholds, figsize=(n_thresholds*3, n_sizes*3))
    
    # Store results
    results = {}
    
    # Test each combination
    for i, min_size in enumerate(min_spot_sizes):
        for j, threshold in enumerate(thresholds):
            # Create predictor with current parameters
            predictor = SpotDetectionPredictor(
                model=model,
                device=device,
                threshold=threshold,
                min_spot_size=min_size
            )
            
            # Make prediction
            result = predictor.predict(image, return_heatmap=True)
            
            # Store result
            key = f"threshold_{threshold}_minsize_{min_size}"
            results[key] = {
                'num_spots': result['num_spots'],
                'spot_props': result['spot_props']
            }
            
            # Plot result
            ax = axes[i, j]
            
            # Create RGB overlay
            rgb_mask = np.zeros((*image.shape, 3))
            rgb_mask[..., 0] = image  # Red channel
            rgb_mask[..., 1] = image  # Green channel
            rgb_mask[..., 2] = image  # Blue channel
            
            # Add binary mask as overlay
            binary_mask = result['binary_mask']
            rgb_mask[binary_mask > 0, 0] = 1.0  # Red channel for detected spots
            
            # Plot image with overlay
            ax.imshow(rgb_mask)
            ax.set_title(f"T={threshold}, Min={min_size}\nSpots: {result['num_spots']}")
            ax.axis('off')
    
    plt.tight_layout()
    plt.show()
    
    # Print summary
    print("\nSummary of Results:")
    print("-" * 50)
    print(f"{'Threshold':<10} {'Min Size':<10} {'Num Spots':<10}")
    print("-" * 50)
    
    for key, value in results.items():
        parts = key.split('_')
        threshold = float(parts[1])
        min_size = int(parts[3])
        print(f"{threshold:<10.1f} {min_size:<10d} {value['num_spots']:<10d}")
    
    return results

def analyze_watershed_parameters(
    image_path: str,
    model_path: str,
    threshold: float = 0.3,
    min_spot_size: int = 3,
    min_distances: List[int] = [1, 3, 5, 7, 10],
    device: str = 'cuda' if torch.cuda.is_available() else 'cpu'
):
    """
    Analyze the impact of watershed parameters on spot detection
    
    Args:
        image_path: Path to the image
        model_path: Path to the trained model
        threshold: Confidence threshold
        min_spot_size: Minimum spot size
        min_distances: List of minimum distances for watershed
        device: Device to use for inference
    """
    # Load model
    model = load_model(model_path, device)
    
    # Load image
    image = io.imread(image_path)
    if len(image.shape) == 3 and image.shape[2] > 1:
        image = image[:, :, 0]  # Take first channel if RGB
    
    # Normalize image
    image = image.astype(np.float32) / 255.0
    
    # Create figure for visualization
    fig, axes = plt.subplots(1, len(min_distances), figsize=(len(min_distances)*4, 4))
    
    # Get raw prediction once
    base_predictor = SpotDetectionPredictor(
        model=model,
        device=device,
        threshold=threshold,
        min_spot_size=min_spot_size
    )
    
    base_result = base_predictor.predict(image, return_heatmap=True)
    heatmap = base_result['heatmap']
    
    # Test each min_distance
    for i, min_distance in enumerate(min_distances):
        # Create predictor with current parameters
        predictor = SpotDetectionPredictor(
            model=model,
            device=device,
            threshold=threshold,
            min_spot_size=min_spot_size,
            min_distance=min_distance
        )
        
        # Get instance segmentation
        labels, props = predictor.get_instance_segmentation(torch.from_numpy(heatmap))
        
        # Plot result
        ax = axes[i]
        
        # Create RGB overlay
        rgb_mask = np.zeros((*image.shape, 3))
        rgb_mask[..., 0] = image  # Red channel
        rgb_mask[..., 1] = image  # Green channel
        rgb_mask[..., 2] = image  # Blue channel
        
        # Add colored labels
        for prop in props:
            y, x = prop['centroid']
            r = int(np.sqrt(prop['area'] / np.pi))
            
            # Draw circle
            from skimage.draw import circle
            rr, cc = circle(int(y), int(x), r, shape=image.shape)
            rgb_mask[rr, cc, 0] = 1.0  # Red
            rgb_mask[rr, cc, 1] = 0.0  # Green
            rgb_mask[rr, cc, 2] = 0.0  # Blue
        
        # Plot image with overlay
        ax.imshow(rgb_mask)
        ax.set_title(f"Min Distance = {min_distance}\nSpots: {len(props)}")
        ax.axis('off')
    
    plt.tight_layout()
    plt.show()

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Analyze spot detection parameters")
    parser.add_argument("--image", type=str, required=True, help="Path to input image")
    parser.add_argument("--model", type=str, required=True, help="Path to trained model")
    parser.add_argument("--mode", type=str, default="threshold", choices=["threshold", "watershed"], 
                        help="Analysis mode: threshold or watershed")
    
    args = parser.parse_args()
    
    if args.mode == "threshold":
        # Use lower thresholds to detect more spots
        thresholds = [0.05, 0.1, 0.15, 0.2, 0.25, 0.3, 0.4, 0.5]
        min_spot_sizes = [1, 2, 3, 5, 10]
        analyze_threshold_impact(args.image, args.model, thresholds, min_spot_sizes)
    else:
        min_distances = [1, 2, 3, 5, 7, 10]
        analyze_watershed_parameters(args.image, args.model, threshold=0.2, min_spot_size=3, min_distances=min_distances)
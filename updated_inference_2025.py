# Updated Ultra-Fast Inference for 2025 Optimized Models
import torch
import torch.nn.functional as F
import os
import tifffile
import numpy as np
import matplotlib.pyplot as plt
from skimage.segmentation import watershed
from skimage.feature import peak_local_maxima
from skimage.morphology import remove_small_objects
from skimage.measure import regionprops, label
import cv2
import pandas as pd

def load_optimized_model(model_path, device='cuda'):
    """Load the latest optimized skeleton-aware model"""
    from OptimizedSpotDetection_model import SkeletonAwareSpotDetector
    
    model = SkeletonAwareSpotDetector(in_ch=1, base_ch=48)
    
    checkpoint = torch.load(model_path, map_location=device, weights_only=False)
    
    if 'model_state_dict' in checkpoint:
        model.load_state_dict(checkpoint['model_state_dict'])
        print(f"Loaded checkpoint epoch {checkpoint.get('epoch', 'N/A')}")
        print(f"Best val loss: {checkpoint.get('best_val_loss', 'N/A')}")
    else:
        model.load_state_dict(checkpoint)
    
    model.to(device)
    model.eval()
    print(f"Model loaded from: {model_path}")
    return model

def extract_precise_spots(centroid_map, semantic, flow, min_distance=3, threshold=0.3):
    """Extract spots using centroid map and flow field"""
    peaks = peak_local_maxima(
        centroid_map, 
        min_distance=min_distance, 
        threshold_abs=threshold,
        exclude_border=True
    )
    
    spots = []
    for y, x in peaks:
        confidence = centroid_map[y, x]
        if semantic[y, x] > 0.5:  # Only in foreground
            spots.append([y, x, confidence])
    
    return spots

def fast_nms(spots, nms_threshold=0.2):
    """Fast non-maximum suppression"""
    if len(spots) <= 1:
        return spots
    
    spots = np.array(spots)
    indices = np.argsort(spots[:, 2])[::-1]  # Sort by confidence
    keep = []
    
    while len(indices) > 0:
        current = indices[0]
        keep.append(current)
        
        if len(indices) == 1:
            break
            
        # Calculate distances
        distances = np.sqrt(
            (spots[current, 0] - spots[indices[1:], 0])**2 + 
            (spots[current, 1] - spots[indices[1:], 1])**2
        )
        
        # Keep spots that are far enough
        indices = indices[1:][distances > nms_threshold]
    
    return spots[keep].tolist()

def skeleton_aware_instance_segmentation(semantic, sdt, skeleton, spots, min_size=2):
    """Instance segmentation using skeleton-aware watershed"""
    # Create markers from spots
    markers = np.zeros_like(semantic, dtype=int)
    final_spots = []
    
    for i, spot in enumerate(spots):
        y, x = int(spot[0]), int(spot[1])
        if 0 <= y < markers.shape[0] and 0 <= x < markers.shape[1]:
            markers[y, x] = i + 1
            final_spots.append(spot)
    
    # Watershed segmentation
    instance_labels = watershed(
        -sdt, 
        markers, 
        mask=semantic > 0.5,
        watershed_line=True
    )
    
    # Remove small objects
    instance_labels = remove_small_objects(instance_labels, min_size=min_size)
    
    return instance_labels, final_spots

def ultra_fast_inference_2025(model, image, device='cuda', threshold=0.3, min_distance=3):
    """Your exact skeleton_aware_inference pipeline with 2025 optimizations"""
    
    model.eval()
    with torch.no_grad():
        # Prepare image (your exact code)
        if isinstance(image, np.ndarray):
            if image.ndim == 2:
                image = torch.from_numpy(image).unsqueeze(0).unsqueeze(0)
            elif image.ndim == 3:
                image = torch.from_numpy(image).unsqueeze(0)
        
        image = image.to(device).float()
        if image.max() > 1.0:
            image = image / 255.0
        
        # Model inference with mixed precision
        with torch.amp.autocast('cuda'):
            outputs = model(image)
        
        # Extract outputs (your exact code)
        semantic = torch.sigmoid(outputs['sem_out'])[0, 0].cpu().numpy()
        
        # Handle quantized SDT output (your exact code)
        if outputs['sdt_out'].shape[1] > 1:
            probs = F.softmax(outputs['sdt_out'], dim=1)
            bin_indices = torch.arange(0, probs.shape[1]).float().to(probs.device)
            bin_indices = bin_indices.view(1, -1, 1, 1) / probs.shape[1]
            sdt = torch.sum(probs * bin_indices, dim=1)[0].cpu().numpy()
        else:
            sdt = torch.sigmoid(outputs['sdt_out'])[0, 0].cpu().numpy()
            
        skeleton = torch.sigmoid(outputs['skeleton_out'])[0, 0].cpu().numpy()
        centroid_map = torch.sigmoid(outputs['hm_out'])[0, 0].cpu().numpy()
        flow = outputs['flow_out'][0].cpu().numpy()
        
        # Extract spots (your exact pipeline)
        spots = extract_precise_spots(
            centroid_map, semantic, flow, 
            min_distance=min_distance, threshold=threshold
        )
        
        # Fast NMS (your exact code)
        if len(spots) > 1:
            spots = fast_nms(spots, 0.2)
        
        # Instance segmentation using SDT (your exact code)
        instance_labels, final_spots = skeleton_aware_instance_segmentation(
            semantic, sdt, skeleton, spots, min_size=2
        )
        
        # Extract boundary from SDT (your exact code)
        boundary = (sdt < 0.1) & (semantic > 0.5)
        
        # Extract detailed spot information (keeping your useful spot info)
        detailed_spots = []
        for region_id in np.unique(instance_labels):
            if region_id == 0:
                continue
            
            mask = instance_labels == region_id
            props = regionprops(mask.astype(int))[0]
            
            detailed_spots.append({
                'id': region_id,
                'y': props.centroid[0],
                'x': props.centroid[1],
                'area': props.area,
                'confidence': centroid_map[int(props.centroid[0]), int(props.centroid[1])],
                'mask': mask
            })
        
        return {
            'spots': detailed_spots,  # Detailed spot info
            'instance_labels': instance_labels,
            'semantic': semantic,
            'centroid': centroid_map,
            'sdt': sdt,
            'skeleton': skeleton,
            'flow': flow,
            'boundary': boundary.astype(np.float32)
        }

def save_inference_results(results, image_path, output_dir):
    """Save inference results with comprehensive outputs"""
    os.makedirs(output_dir, exist_ok=True)
    
    image_name = os.path.splitext(os.path.basename(image_path))[0]
    
    # Save spot coordinates
    if results['spots']:
        spots_data = []
        for spot in results['spots']:
            spots_data.append({
                'id': spot['id'],
                'y': spot['y'],
                'x': spot['x'],
                'area': spot['area'],
                'confidence': spot['confidence']
            })
        
        spots_df = pd.DataFrame(spots_data)
        spots_df.to_csv(os.path.join(output_dir, f'{image_name}_spots.csv'), index=False)
    
    # Save instance labels
    tifffile.imwrite(
        os.path.join(output_dir, f'{image_name}_instance_labels.tif'), 
        results['instance_labels'].astype(np.uint16)
    )
    
    # Save visualization
    create_inference_visualization(results, image_path, output_dir)
    
    return len(results['spots'])

def create_inference_visualization(results, image_path, output_dir):
    """Create comprehensive visualization"""
    image_name = os.path.splitext(os.path.basename(image_path))[0]
    
    # Load original image
    original_image = tifffile.imread(image_path).astype(np.float32)
    original_image = (original_image - original_image.min()) / (original_image.max() - original_image.min())
    
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    
    # Original image with spots
    axes[0, 0].imshow(original_image, cmap='gray')
    if results['spots']:
        for spot in results['spots']:
            axes[0, 0].plot(spot['x'], spot['y'], 'ro', markersize=8, alpha=0.7)
    axes[0, 0].set_title(f'Detected Spots: {len(results["spots"])}')
    axes[0, 0].axis('off')
    
    # Semantic mask
    axes[0, 1].imshow(results['semantic'], cmap='viridis')
    axes[0, 1].set_title('Semantic Mask')
    axes[0, 1].axis('off')
    
    # Centroid heatmap
    axes[0, 2].imshow(results['centroid'], cmap='hot')
    axes[0, 2].set_title('Centroid Heatmap')
    axes[0, 2].axis('off')
    
    # SDT
    axes[1, 0].imshow(results['sdt'], cmap='magma')
    axes[1, 0].set_title('Skeleton-Aware Distance Transform')
    axes[1, 0].axis('off')
    
    # Skeleton
    axes[1, 1].imshow(results['skeleton'], cmap='bone')
    axes[1, 1].set_title('Skeleton')
    axes[1, 1].axis('off')
    
    # Instance segmentation
    from skimage.color import label2rgb
    instance_viz = label2rgb(results['instance_labels'], bg_label=0, alpha=0.7)
    axes[1, 2].imshow(instance_viz)
    axes[1, 2].set_title('Instance Segmentation')
    axes[1, 2].axis('off')
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, f'{image_name}_results.png'), dpi=150, bbox_inches='tight')
    plt.close()

def run_single_inference():
    """Run inference on a single image"""
    # Configuration
    model_path = '/mnt/d/Users/<USER>/Documents/spot detector git amazon 2025/skeleton_aware_model_improved_spots_qwen_alpha_8.5/best_model.pth'
    image_path = '/mnt/d/Users/<USER>/FISH_spots/2d/refined/images/0  Series001  Green--FLUO--FITC_tile_2.tif'
    output_dir = '/mnt/d/Users/<USER>/Documents/spot detector git amazon 2025/inference_output_2025/'
    
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"Using device: {device}")
    
    try:
        # Load model
        model = load_optimized_model(model_path, device)
        
        # Load image
        image = tifffile.imread(image_path).astype(np.float32)
        image = (image - image.min()) / (image.max() - image.min() + 1e-8)
        
        print(f"Processing image: {os.path.basename(image_path)}")
        print(f"Image shape: {image.shape}")
        
        # Run inference
        results = ultra_fast_inference_2025(model, image, device, threshold=0.3)
        
        # Save results
        num_spots = save_inference_results(results, image_path, output_dir)
        
        print(f"✅ Inference completed!")
        print(f"   Detected spots: {num_spots}")
        print(f"   Results saved to: {output_dir}")
        
        return results
        
    except Exception as e:
        print(f"❌ Error during inference: {e}")
        import traceback
        traceback.print_exc()
        return None

def run_batch_inference():
    """Run batch inference on multiple images"""
    # Configuration
    model_path = '/mnt/d/Users/<USER>/Documents/spot detector git amazon 2025/skeleton_aware_model_improved_spots_qwen_alpha_8.5/best_model.pth'
    image_dir = '/mnt/d/Users/<USER>/FISH_spots/2d/refined/images/'
    output_base_dir = '/mnt/d/Users/<USER>/Documents/spot detector git amazon 2025/batch_inference_2025/'
    
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"Using device: {device}")
    
    # Load model once
    model = load_optimized_model(model_path, device)
    
    # Find images
    image_paths = [os.path.join(image_dir, f) for f in os.listdir(image_dir) 
                   if f.lower().endswith(('.tif', '.tiff'))]
    image_paths.sort()
    
    if not image_paths:
        print(f"No images found in {image_dir}")
        return
    
    print(f"Found {len(image_paths)} images to process")
    
    results_summary = []
    
    for i, image_path in enumerate(image_paths):
        print(f"\n--- Processing {i+1}/{len(image_paths)}: {os.path.basename(image_path)} ---")
        
        try:
            # Load image
            image = tifffile.imread(image_path).astype(np.float32)
            image = (image - image.min()) / (image.max() - image.min() + 1e-8)
            
            # Create output directory
            image_name = os.path.splitext(os.path.basename(image_path))[0]
            output_dir = os.path.join(output_base_dir, image_name)
            
            # Run inference
            results = ultra_fast_inference_2025(model, image, device, threshold=0.3)
            
            # Save results
            num_spots = save_inference_results(results, image_path, output_dir)
            
            results_summary.append({
                'image': image_name,
                'spots_detected': num_spots,
                'status': 'success',
                'output_dir': output_dir
            })
            
            print(f"   ✅ Success: {num_spots} spots detected")
            
        except Exception as e:
            print(f"   ❌ Error: {e}")
            results_summary.append({
                'image': os.path.basename(image_path),
                'spots_detected': 0,
                'status': 'failed',
                'error': str(e)
            })
    
    # Save summary
    summary_df = pd.DataFrame(results_summary)
    summary_path = os.path.join(output_base_dir, 'batch_summary.csv')
    os.makedirs(output_base_dir, exist_ok=True)
    summary_df.to_csv(summary_path, index=False)
    
    print(f"\n✅ Batch processing completed!")
    print(f"   Total images: {len(image_paths)}")
    print(f"   Successful: {sum(1 for r in results_summary if r['status'] == 'success')}")
    print(f"   Failed: {sum(1 for r in results_summary if r['status'] == 'failed')}")
    print(f"   Summary saved to: {summary_path}")
    
    return results_summary

if __name__ == '__main__':
    # Choose inference type
    print("Choose inference type:")
    print("1. Single image inference")
    print("2. Batch inference")
    
    choice = input("Enter choice (1 or 2): ").strip()
    
    if choice == '1':
        run_single_inference()
    elif choice == '2':
        run_batch_inference()
    else:
        print("Invalid choice. Running single inference by default.")
        run_single_inference()
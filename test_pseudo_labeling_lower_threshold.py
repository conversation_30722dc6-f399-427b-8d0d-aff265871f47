import torch
import numpy as np
import os
from SparseSpotDataset import Sparse<PERSON>potDataset
from semi_supervised import <PERSON><PERSON>upervisedTrainer
from torch.utils.data import <PERSON><PERSON><PERSON><PERSON>

def create_synthetic_dataset(num_samples=10, image_size=(64, 64)):
    """Create a synthetic dataset for testing pseudo-labeling"""
    dataset = SparseSpotDataset(data_dir=None)
    
    # Create synthetic images and masks
    for i in range(num_samples):
        # Create random image with spots
        image = np.zeros(image_size, dtype=np.float32)
        # Add random noise
        image += np.random.normal(0, 0.1, image_size)
        # Add random spots
        num_spots = np.random.randint(3, 10)
        mask = np.zeros(image_size, dtype=np.float32)
        
        for _ in range(num_spots):
            x = np.random.randint(5, image_size[0]-5)
            y = np.random.randint(5, image_size[1]-5)
            radius = np.random.randint(2, 5)
            
            # Create spot in image (brighter)
            for i in range(max(0, x-radius), min(image_size[0], x+radius+1)):
                for j in range(max(0, y-radius), min(image_size[1], y+radius+1)):
                    if ((i-x)**2 + (j-y)**2) <= radius**2:
                        image[i, j] += 0.5
                        mask[i, j] = 1.0
        
        # Normalize image
        image = np.clip(image, 0, 1)
        
        # Create confidence mask (all 1s for labeled data)
        confidence = mask.copy()
        
        # Add to dataset
        dataset.images.append(image)
        dataset.masks.append(mask)
        dataset.confidence_masks.append(confidence)
    
    return dataset

def create_unlabeled_dataset(labeled_dataset, num_samples=5):
    """Create unlabeled dataset from labeled dataset by hiding labels"""
    unlabeled_dataset = SparseSpotDataset(data_dir=None)
    
    # Copy images but set masks to zeros
    for i in range(min(num_samples, len(labeled_dataset))):
        if isinstance(labeled_dataset.images[i], str):
            # Skip file paths
            continue
            
        # Copy image
        unlabeled_dataset.images.append(labeled_dataset.images[i].copy())
        
        # Create empty mask
        mask_shape = labeled_dataset.masks[i].shape if isinstance(labeled_dataset.masks[i], np.ndarray) else (64, 64)
        unlabeled_dataset.masks.append(np.zeros(mask_shape, dtype=np.float32))
        
        # Create zero confidence mask
        unlabeled_dataset.confidence_masks.append(np.zeros(mask_shape, dtype=np.float32))
    
    return unlabeled_dataset

class SimpleModel(torch.nn.Module):
    """Simple CNN model for testing"""
    def __init__(self):
        super().__init__()
        self.conv1 = torch.nn.Conv2d(1, 16, kernel_size=3, padding=1)
        self.conv2 = torch.nn.Conv2d(16, 32, kernel_size=3, padding=1)
        self.conv3 = torch.nn.Conv2d(32, 1, kernel_size=3, padding=1)
        self.relu = torch.nn.ReLU()
        self.sigmoid = torch.nn.Sigmoid()
    
    def forward(self, x):
        x = self.relu(self.conv1(x))
        x = self.relu(self.conv2(x))
        x = self.conv3(x)
        return {'heatmap': x}  # Return as dict to match expected format

class SimpleLoss(torch.nn.Module):
    """Simple loss function for testing"""
    def __init__(self):
        super().__init__()
        self.bce = torch.nn.BCEWithLogitsLoss()
    
    def forward(self, outputs, targets):
        loss = self.bce(outputs['heatmap'], targets['masks'])
        return loss, {'bce': loss.item()}

def test_pseudo_labeling():
    """Test pseudo-labeling functionality with lower threshold"""
    print("Creating synthetic datasets...")
    labeled_dataset = create_synthetic_dataset(num_samples=10)
    unlabeled_dataset = create_unlabeled_dataset(labeled_dataset, num_samples=5)
    
    print(f"Labeled dataset size: {len(labeled_dataset)}")
    print(f"Unlabeled dataset size: {len(unlabeled_dataset)}")
    
    # Create model and loss
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = SimpleModel().to(device)
    loss_fn = SimpleLoss()
    optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
    
    # Create data loaders
    labeled_loader = DataLoader(labeled_dataset, batch_size=2, shuffle=True)
    unlabeled_loader = DataLoader(unlabeled_dataset, batch_size=2, shuffle=True)
    
    # Create trainer with MUCH LOWER threshold
    trainer = SemiSupervisedTrainer(
        model=model,
        loss_fn=loss_fn,
        labeled_loader=labeled_loader,
        unlabeled_loader=unlabeled_loader,
        optimizer=optimizer,
        device=device,
        confidence_threshold=0.3,  # Much lower threshold for testing
        ignore_threshold=0.1
    )
    
    # Train for more epochs to get better predictions
    print("Training for more epochs...")
    for epoch in range(10):  # Increased from 3 to 10
        metrics = trainer.train_epoch(labeled_loader, unlabeled_loader)
        print(f"Epoch {epoch+1} - Loss: {metrics['loss']:.4f}")
    
    # Generate pseudo-labels
    print("Generating pseudo-labels with lower threshold...")
    stats = trainer.generate_pseudo_labels(unlabeled_dataset)
    
    print("\nPseudo-labeling statistics:")
    for key, value in stats.items():
        print(f"  {key}: {value}")
    
    # Check if any pixels were updated
    print("\nChecking unlabeled dataset after pseudo-labeling:")
    total_mask_sum = 0
    total_conf_sum = 0
    for i in range(len(unlabeled_dataset)):
        mask_sum = np.sum(unlabeled_dataset.masks[i])
        conf_sum = np.sum(unlabeled_dataset.confidence_masks[i])
        total_mask_sum += mask_sum
        total_conf_sum += conf_sum
        print(f"  Sample {i+1}: Mask sum = {mask_sum:.2f}, Confidence sum = {conf_sum:.2f}")
    
    print(f"\nTotal mask sum: {total_mask_sum:.2f}")
    print(f"Total confidence sum: {total_conf_sum:.2f}")
    
    if total_mask_sum > 0:
        print("\n✅ Pseudo-labeling is working! Labels were added to the unlabeled dataset.")
    else:
        print("\n❌ Pseudo-labeling is NOT working! No labels were added to the unlabeled dataset.")
        print("Possible issues:")
        print("1. Confidence threshold still too high (current: 0.3)")
        print("2. Model predictions not confident enough")
        print("3. Issue in the update_with_predictions method")
        
    # Debug the predictions directly
    print("\nDebugging model predictions:")
    model.eval()
    with torch.no_grad():
        for i, sample in enumerate(unlabeled_dataset):
            image = sample['image'].unsqueeze(0).to(device)
            output = model(image)
            pred = torch.sigmoid(output['heatmap']).cpu().numpy()
            max_conf = np.max(pred)
            mean_conf = np.mean(pred)
            print(f"  Sample {i+1}: Max confidence = {max_conf:.4f}, Mean confidence = {mean_conf:.4f}")

if __name__ == "__main__":
    test_pseudo_labeling()
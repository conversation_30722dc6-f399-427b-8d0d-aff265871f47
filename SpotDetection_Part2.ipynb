{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["<a id=\"model\"></a>\n", "## 3. Model and Loss Function\n", "\n", "Now, let's modify the loss function to handle sparse annotations and implement the model with the suggested improvements."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Modify the loss function to handle sparse annotations"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["class SparseOptimizedSpotLoss(OptimizedSpotLoss):\n", "    \"\"\"\n", "    Optimized loss function for spot detection with support for sparse annotations\n", "    \n", "    This loss extends OptimizedSpotLoss to handle sparse annotations by:\n", "    1. Using confidence masks to weight the loss\n", "    2. Adding contrastive loss for better spot separation\n", "    \"\"\"\n", "    def __init__(self, contrastive_weight=0.1, **kwargs):\n", "        super().__init__(**kwargs)\n", "        self.contrastive_weight = contrastive_weight\n", "        \n", "        # Initialize learnable contrastive weight if enabled\n", "        if self.learn_weights:\n", "            self.log_contrastive_weight = nn.Parameter(torch.log(torch.tensor(contrastive_weight)))\n", "    \n", "    def get_current_weights(self):\n", "        \"\"\"Get current weights, either learned or fixed\"\"\"\n", "        weights = super().get_current_weights()\n", "        \n", "        # Add contrastive weight\n", "        if self.learn_weights:\n", "            weights['contrastive'] = torch.exp(self.log_contrastive_weight).clamp(0.01, 1.0)\n", "        else:\n", "            weights['contrastive'] = self.contrastive_weight\n", "        \n", "        return weights\n", "    \n", "    def compute_contrastive_loss(self, pred, target, embedding=None):\n", "        \"\"\"Compute contrastive loss to better separate touching spots\"\"\"\n", "        if embedding is None:\n", "            return torch.tensor(0.0, device=pred.device)\n", "            \n", "        # Get binary mask\n", "        binary_mask = (target > 0.5).float()\n", "        \n", "        # Skip if no spots\n", "        if binary_mask.sum() == 0:\n", "            return torch.tensor(0.0, device=pred.device)\n", "        \n", "        # Use connected components to identify individual spots\n", "        batch_size = binary_mask.shape[0]\n", "        loss = 0.0\n", "        \n", "        for b in range(batch_size):\n", "            # Extract single image\n", "            mask = binary_mask[b, 0].cpu().numpy()\n", "            emb = embedding[b]  # [C, H, W]\n", "            \n", "            # Find connected components\n", "            from skimage.measure import label\n", "            labeled = label(mask)\n", "            \n", "            # Skip if no spots\n", "            if labeled.max() == 0:\n", "                continue\n", "                \n", "            # Get unique labels (excluding background)\n", "            unique_labels = np.unique(labeled)\n", "            unique_labels = unique_labels[unique_labels > 0]\n", "            \n", "            # Skip if less than 2 spots\n", "            if len(unique_labels) < 2:\n", "                continue\n", "                \n", "            # Compute embeddings for each spot\n", "            spot_embeddings = []\n", "            for lbl in unique_labels:\n", "                # Get mask for this spot\n", "                spot_mask = torch.from_numpy((labeled == lbl)).to(pred.device)\n", "                \n", "                # Expand mask to match embedding dimensions\n", "                spot_mask = spot_mask.unsqueeze(0).expand(emb.shape[0], -1, -1)\n", "                \n", "                # Get mean embedding for this spot\n", "                # We need to handle the case where the spot is empty after mask application\n", "                masked_emb = emb * spot_mask\n", "                spot_sum = masked_emb.sum(dim=(1, 2))\n", "                spot_count = spot_mask.sum(dim=(1, 2)) + 1e-6\n", "                spot_emb = spot_sum / spot_count\n", "                \n", "                spot_embeddings.append(spot_emb)\n", "                \n", "            # Stack embeddings\n", "            spot_embeddings = torch.stack(spot_embeddings)  # [num_spots, C]\n", "            \n", "            # Compute pairwise distances\n", "            dists = torch.cdist(spot_embeddings, spot_embeddings)\n", "            \n", "            # Create contrastive loss: push embeddings apart\n", "            # We want minimum distance between spots to be at least margin\n", "            margin = 2.0\n", "            mask = torch.ones_like(dists) - torch.eye(len(unique_labels), device=dists.device)\n", "            contrastive = torch.clamp(margin - dists, min=0) * mask\n", "            \n", "            # Add to batch loss\n", "            if len(unique_labels) > 1:\n", "                loss += contrastive.sum() / (len(unique_labels) * (len(unique_labels) - 1))\n", "        \n", "        # Average over batch\n", "        return loss / batch_size if batch_size > 0 else torch.tensor(0.0, device=pred.device)\n", "    \n", "    def forward(self, outputs, targets):\n", "        \"\"\"Forward pass with support for sparse annotations\"\"\"\n", "        # Get current weights\n", "        weights = self.get_current_weights()\n", "        \n", "        # Extract predictions\n", "        heatmap = outputs['heatmap']\n", "        boundary = outputs.get('boundary')\n", "        distance = outputs.get('distance')\n", "        expert_weights = outputs.get('expert_weights')\n", "        embedding = outputs.get('embedding')\n", "        \n", "        # Extract targets\n", "        mask = targets['masks']\n", "        confidence = targets.get('confidence', torch.ones_like(mask))\n", "        \n", "        # Initialize loss components\n", "        losses = {}\n", "        total_loss = 0.0\n", "        \n", "        # Compute density-aware and size-adaptive weights\n", "        density_weights, size_weights = self._compute_adaptive_weights(mask)\n", "        \n", "        # Compute heatmap loss with focal weighting and confidence masking\n", "        heatmap_loss = self.compute_focal_bce_loss(\n", "            heatmap, mask, \n", "            pos_weight=weights['pos_weight'],\n", "            gamma=weights['focal_gamma'],\n", "            density_weights=density_weights,\n", "            size_weights=size_weights,\n", "            confidence=confidence\n", "        )\n", "        losses['heatmap_loss'] = heatmap_loss\n", "        total_loss += weights['heatmap'] * heatmap_loss\n", "        \n", "        # Add Dice loss for better handling of class imbalance\n", "        dice_loss = self.compute_dice_loss(heatmap, mask, confidence=confidence)\n", "        losses['dice_loss'] = dice_loss\n", "        total_loss += weights['dice_weight'] * dice_loss\n", "        \n", "        # Compute boundary loss if available\n", "        if boundary is not None:\n", "            # Generate boundary targets on GPU\n", "            boundary_targets = self._get_boundaries(mask)\n", "            boundary_loss = self.compute_weighted_bce_loss(\n", "                boundary, boundary_targets, confidence=confidence\n", "            )\n", "            losses['boundary_loss'] = boundary_loss\n", "            total_loss += weights['boundary'] * boundary_loss\n", "        \n", "        # Compute distance loss if available\n", "        if distance is not None:\n", "            # Generate distance transform targets on GPU\n", "            distance_targets = self._get_distance_transform(mask)\n", "            distance_loss = self.compute_weighted_mse_loss(\n", "                torch.sigmoid(distance), distance_targets, confidence=confidence\n", "            )\n", "            losses['distance_loss'] = distance_loss\n", "            total_loss += weights['distance'] * distance_loss\n", "        \n", "        # Compute MoE loss if expert weights are available\n", "        if expert_weights is not None:\n", "            moe_loss = self.compute_moe_loss(expert_weights)\n", "            losses['moe_loss'] = moe_loss\n", "            total_loss += weights['moe'] * moe_loss\n", "            \n", "            # Update expert usage statistics\n", "            with torch.no_grad():\n", "                batch_usage = expert_weights.mean(0)\n", "                self.expert_usage = 0.9 * self.expert_usage + 0.1 * batch_usage\n", "        \n", "        # Compute contrastive loss if embedding is available\n", "        if embedding is not None:\n", "            contrastive_loss = self.compute_contrastive_loss(heatmap, mask, embedding)\n", "            losses['contrastive_loss'] = contrastive_loss\n", "            total_loss += weights['contrastive'] * contrastive_loss\n", "        \n", "        # Return total loss and components\n", "        losses['total_loss'] = total_loss\n", "        return total_loss, losses\n", "    \n", "    def compute_focal_bce_loss(self, pred, target, pos_weight=2.0, gamma=2.0, \n", "                              density_weights=None, size_weights=None, ignore_threshold=0.1,\n", "                              confidence=None):\n", "        \"\"\"Compute focal BCE loss with confidence weighting\"\"\"\n", "        # Apply sigmoid if pred contains logits\n", "        pred_sigmoid = torch.sigmoid(pred)\n", "        \n", "        # Create ignore mask for low confidence false positives\n", "        ignore_mask = (pred_sigmoid < ignore_threshold) & (target == 0)\n", "        \n", "        # Compute BCE loss with pos_weight as tensor\n", "        pos_weight_tensor = torch.tensor(pos_weight, device=pred.device, dtype=torch.float32)\n", "        bce_loss = F.binary_cross_entropy_with_logits(\n", "            pred, target, \n", "            reduction='none',\n", "            pos_weight=pos_weight_tensor\n", "        )\n", "        \n", "        # Apply ignore mask\n", "        bce_loss = bce_loss * (~ignore_mask).float()\n", "        \n", "        # Apply focal weighting\n", "        if gamma > 0:\n", "            pt = target * pred_sigmoid + (1 - target) * (1 - pred_sigmoid)\n", "            focal_weight = (1 - pt) ** gamma\n", "            bce_loss = bce_loss * focal_weight\n", "        \n", "        # Apply density-based weighting if available\n", "        if density_weights is not None:\n", "            bce_loss = bce_loss * density_weights\n", "        \n", "        # Apply size-based weighting if available\n", "        if size_weights is not None:\n", "            bce_loss = bce_loss * size_weights\n", "        \n", "        # Apply confidence weighting if available\n", "        if confidence is not None:\n", "            bce_loss = bce_loss * confidence\n", "        \n", "        # Average over non-ignored pixels\n", "        valid_pixels = (~ignore_mask).float().sum() + 1e-6\n", "        loss = bce_loss.sum() / valid_pixels\n", "        \n", "        return loss\n", "    \n", "    def compute_dice_loss(self, pred, target, smooth=1e-6, confidence=None):\n", "        \"\"\"Compute Dice loss with confidence weighting\"\"\"\n", "        # Apply sigmoid if pred contains logits\n", "        pred_sigmoid = torch.sigmoid(pred)\n", "        \n", "        # Apply confidence weighting if available\n", "        if confidence is not None:\n", "            pred_sigmoid = pred_sigmoid * confidence\n", "            target = target * confidence\n", "        \n", "        # Flatten the tensors for pixel-wise comparison\n", "        batch_size = pred.size(0)\n", "        pred_flat = pred_sigmoid.view(batch_size, -1)\n", "        target_flat = target.view(batch_size, -1)\n", "        \n", "        # Calculate intersection and union\n", "        intersection = (pred_flat * target_flat).sum(dim=1)\n", "        pred_sum = pred_flat.sum(dim=1)\n", "        target_sum = target_flat.sum(dim=1)\n", "        \n", "        # Calculate Dice coefficient\n", "        dice = (2.0 * intersection + smooth) / (pred_sum + target_sum + smooth)\n", "        \n", "        # Return Dice loss (1 - <PERSON><PERSON>)\n", "        return 1.0 - dice.mean()\n", "    \n", "    def compute_weighted_bce_loss(self, pred, target, confidence=None):\n", "        \"\"\"Compute BCE loss with confidence weighting\"\"\"\n", "        # Compute BCE loss\n", "        bce_loss = F.binary_cross_entropy_with_logits(pred, target, reduction='none')\n", "        \n", "        # Apply confidence weighting if available\n", "        if confidence is not None:\n", "            bce_loss = bce_loss * confidence\n", "        \n", "        # Average over all pixels\n", "        return bce_loss.mean()\n", "    \n", "    def compute_weighted_mse_loss(self, pred, target, confidence=None):\n", "        \"\"\"Compute MSE loss with confidence weighting\"\"\"\n", "        # Compute MSE loss\n", "        mse_loss = F.mse_loss(pred, target, reduction='none')\n", "        \n", "        # Apply confidence weighting if available\n", "        if confidence is not None:\n", "            mse_loss = mse_loss * confidence\n", "        \n", "        # Average over all pixels\n", "        return mse_loss.mean()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Enhance the model with expert specialization"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["class EnhancedSpotDetectionModel(OptimizedSpotDetectionModel):\n", "    \"\"\"\n", "    Enhanced spot detection model with expert specialization and GPU acceleration\n", "    \n", "    This model extends OptimizedSpotDetectionModel with:\n", "    1. Expert specialization loss\n", "    2. GPU-accelerated operations\n", "    3. Support for semi-supervised learning\n", "    \"\"\"\n", "    def __init__(self, specialization_weight=0.1, **kwargs):\n", "        super().__init__(**kwargs)\n", "        self.specialization_weight = specialization_weight\n", "    \n", "    def compute_expert_specialization_loss(self, expert_outputs, targets):\n", "        \"\"\"Compute loss to encourage experts to specialize in different spot types\"\"\"\n", "        # Get target mask\n", "        mask = targets['masks']\n", "        batch_size = mask.shape[0]\n", "        \n", "        # Create specialized target masks for different spot types\n", "        # Expert 1: Small spots\n", "        # Expert 2: Medium spots\n", "        # Expert 3: Large spots\n", "        # Expert 4: Dense regions\n", "        \n", "        # Use connected components to identify spots\n", "        specialized_masks = []\n", "        \n", "        for b in range(batch_size):\n", "            # Extract single image\n", "            mask_np = mask[b, 0].detach().cpu().numpy()\n", "            \n", "            # Find connected components\n", "            from skimage.measure import label, regionprops\n", "            labeled = label(mask_np > 0.5)\n", "            props = regionprops(labeled)\n", "            \n", "            # Create masks for different spot types\n", "            small_mask = np.zeros_like(mask_np)\n", "            medium_mask = np.zeros_like(mask_np)\n", "            large_mask = np.zeros_like(mask_np)\n", "            dense_mask = np.zeros_like(mask_np)\n", "            \n", "            # Calculate spot sizes\n", "            areas = [prop.area for prop in props]\n", "            if len(areas) > 0:\n", "                # Define thresholds based on distribution\n", "                small_thresh = np.percentile(areas, 33) if len(areas) >= 3 else np.mean(areas)\n", "                large_thresh = np.percentile(areas, 67) if len(areas) >= 3 else np.mean(areas)\n", "                \n", "                # Identify dense regions (spots close to each other)\n", "                centers = np.array([prop.centroid for prop in props])\n", "                dense_regions = np.zeros_like(mask_np, dtype=bool)\n", "                \n", "                if len(centers) >= 2:\n", "                    from scipy.spatial.distance import pdist, squareform\n", "                    dists = squareform(pdist(centers))\n", "                    np.fill_diagonal(dists, np.inf)\n", "                    close_spots = np.any(dists < 20, axis=1)  # Threshold for \"close\"\n", "                    \n", "                    for i, is_close in enumerate(close_spots):\n", "                        if is_close:\n", "                            dense_regions[labeled == (i+1)] = True\n", "                \n", "                # Assign spots to appropriate masks\n", "                for i, prop in enumerate(props):\n", "                    if prop.area < small_thresh:\n", "                        small_mask[labeled == (i+1)] = 1\n", "                    elif prop.area > large_thresh:\n", "                        large_mask[labeled == (i+1)] = 1\n", "                    else:\n", "                        medium_mask[labeled == (i+1)] = 1\n", "                    \n", "                    # Dense regions override size-based assignment\n", "                    if dense_regions[labeled == (i+1)].any():\n", "                        dense_mask[labeled == (i+1)] = 1\n", "                        # Remove from other masks\n", "                        small_mask[labeled == (i+1)] = 0\n", "                        medium_mask[labeled == (i+1)] = 0\n", "                        large_mask[labeled == (i+1)] = 0\n", "            \n", "            # Convert to tensors\n", "            specialized_masks.append([\n", "                torch.from_numpy(small_mask).float().to(mask.device),\n", "                torch.from_numpy(medium_mask).float().to(mask.device),\n", "                torch.from_numpy(large_mask).float().to(mask.device),\n", "                torch.from_numpy(dense_mask).float().to(mask.device)\n", "            ])\n", "        \n", "        # Compute specialization loss for each expert\n", "        specialization_loss = 0.0\n", "        \n", "        for expert_idx, expert_output in enumerate(expert_outputs):\n", "            # Get specialized target for this expert\n", "            expert_targets = torch.stack([masks[expert_idx] for masks in specialized_masks])\n", "            expert_targets = expert_targets.unsqueeze(1)  # Add channel dimension\n", "            \n", "            # Compute BCE loss\n", "            expert_loss = F.binary_cross_entropy_with_logits(\n", "                expert_output, expert_targets, reduction='mean'\n", "            )\n", "            \n", "            specialization_loss += expert_loss\n", "        \n", "        return specialization_loss / len(expert_outputs)\n", "    \n", "    def forward(self, x, targets=None):\n", "        \"\"\"Forward pass with expert specialization loss\"\"\"\n", "        # Call parent forward method\n", "        outputs = super().forward(x)\n", "        \n", "        # If in training mode and targets are provided, compute specialization loss\n", "        if self.training and targets is not None and hasattr(self, 'expert_outputs'):\n", "            specialization_loss = self.compute_expert_specialization_loss(self.expert_outputs, targets)\n", "            outputs['specialization_loss'] = specialization_loss\n", "        \n", "        return outputs"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Initialize model and loss function"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Configuration for model and loss function\n", "MODEL_CONFIG = {\n", "    'in_channels': 1,\n", "    'base_filters': 32,\n", "    'is_3d': <PERSON><PERSON><PERSON>,\n", "    'num_experts': 4,\n", "    'dropout': 0.1,\n", "    'stochastic_depth': 0.1,\n", "    'deep_supervision': True,\n", "    'use_attention': True,\n", "    'specialization_weight': 0.1\n", "}\n", "\n", "LOSS_CONFIG = {\n", "    'heatmap_weight': 0.5,\n", "    'boundary_weight': 0.2,\n", "    'distance_weight': 0.2,\n", "    'moe_weight': 0.1,\n", "    'contrastive_weight': 0.1,\n", "    'pos_weight': 2.0,\n", "    'dice_weight': 0.5,\n", "    'focal_gamma': 2.0,\n", "    'learn_weights': True,\n", "    'density_aware_weighting': True,\n", "    'size_adaptive_weighting': True,\n", "    'num_experts': 4\n", "}\n", "\n", "# Create model and loss function\n", "model = EnhancedSpotDetectionModel(**MODEL_CONFIG).to(device)\n", "loss_fn = SparseOptimizedSpotLoss(**LOSS_CONFIG).to(device)\n", "\n", "# Print model summary\n", "print(f\"Model parameters: {sum(p.numel() for p in model.parameters() if p.requires_grad):,}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 4}
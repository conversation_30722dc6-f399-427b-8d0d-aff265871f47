# Add this cell to your notebook to test erosion parameters

import cv2
import numpy as np
import matplotlib.pyplot as plt
from skimage.measure import regionprops
import tifffile

def test_erosion_parameters(mask, image=None, test_configs=None):
    """Test different erosion parameters to find optimal spot mask refinement."""
    if test_configs is None:
        test_configs = [
            (1, 1), (2, 1), (3, 1),  # Small kernels
            (1, 2), (2, 2), (3, 2),  # More iterations
        ]
    
    valid_ids = np.unique(mask)[1:]  # Remove background
    n_configs = len(test_configs)
    
    fig, axes = plt.subplots(3, n_configs, figsize=(4*n_configs, 12))
    if n_configs == 1:
        axes = axes.reshape(-1, 1)
    
    results = {}
    
    for i, (kernel_size, iterations) in enumerate(test_configs):
        kernel = np.ones((kernel_size, kernel_size), np.uint8)
        
        # Apply erosion to each spot individually
        eroded_mask = np.zeros_like(mask)
        spot_stats = []
        
        for spot_id in valid_ids:
            spot_binary = (mask == spot_id).astype(np.uint8)
            original_area = np.sum(spot_binary)
            
            if original_area > 0:
                eroded_spot = cv2.erode(spot_binary, kernel, iterations=iterations)
                eroded_area = np.sum(eroded_spot)
                
                if eroded_area > 0:  # Keep only if something remains
                    eroded_mask[eroded_spot > 0] = spot_id
                    spot_stats.append({
                        'original_area': original_area,
                        'eroded_area': eroded_area,
                        'reduction': (original_area - eroded_area) / original_area
                    })
        
        semantic = (eroded_mask > 0).astype(np.float32)
        
        # Visualizations
        axes[0, i].imshow(mask, cmap='nipy_spectral')
        axes[0, i].set_title(f'Original\n{len(valid_ids)} spots')
        axes[0, i].axis('off')
        
        axes[1, i].imshow(eroded_mask, cmap='nipy_spectral')
        remaining_spots = len(np.unique(eroded_mask)) - 1
        axes[1, i].set_title(f'K={kernel_size}, I={iterations}\n{remaining_spots} spots')
        axes[1, i].axis('off')
        
        if image is not None:
            axes[2, i].imshow(image, cmap='gray')
            axes[2, i].imshow(semantic, cmap='Reds', alpha=0.5)
            axes[2, i].set_title('Overlay')
        else:
            axes[2, i].imshow(semantic, cmap='gray')
            axes[2, i].set_title('Semantic')
        axes[2, i].axis('off')
        
        results[f'k{kernel_size}_i{iterations}'] = {
            'eroded_mask': eroded_mask,
            'semantic': semantic,
            'spots_remaining': remaining_spots,
            'total_pixels': semantic.sum(),
            'avg_reduction': np.mean([s['reduction'] for s in spot_stats]) if spot_stats else 0
        }
    
    plt.tight_layout()
    plt.show()
    
    # Print results
    print("\n=== Erosion Results ===")
    print(f"{'Config':<10} {'Spots':<6} {'Pixels':<8} {'Reduction':<10}")
    print("-" * 36)
    for key, result in results.items():
        print(f"{key:<10} {result['spots_remaining']:<6} {result['total_pixels']:<8.0f} {result['avg_reduction']:<10.2%}")
    
    return results

def analyze_spot_sizes(mask):
    """Analyze spot sizes to recommend erosion parameters."""
    valid_ids = np.unique(mask)[1:]
    areas, diameters = [], []
    
    for spot_id in valid_ids:
        spot_binary = (mask == spot_id).astype(np.uint8)
        props = regionprops(spot_binary)
        if props:
            areas.append(props[0].area)
            diameters.append(props[0].equivalent_diameter)
    
    if not areas:
        return None
    
    areas, diameters = np.array(areas), np.array(diameters)
    
    print(f"Spots: {len(areas)}")
    print(f"Diameter: {diameters.min():.1f}-{diameters.max():.1f} (avg: {diameters.mean():.1f})")
    print(f"Area: {areas.min():.0f}-{areas.max():.0f} (avg: {areas.mean():.1f})")
    
    small = np.sum(diameters <= 3)
    medium = np.sum((diameters > 3) & (diameters <= 8))
    large = np.sum(diameters > 8)
    
    print(f"Small (≤3px): {small}, Medium (3-8px): {medium}, Large (>8px): {large}")
    
    if diameters.mean() < 4:
        print("→ Recommendation: Try (1,1) or (2,1)")
    elif diameters.mean() < 8:
        print("→ Recommendation: Try (2,1) or (2,2)")
    else:
        print("→ Recommendation: Try (2,2) or (3,2)")

# Test with your data
image_path = "/mnt/d/Users/<USER>/FISH_spots/2d/image_raw/large spots/images/synthetic_000596.tif"
mask_path = "/mnt/d/Users/<USER>/FISH_spots/2d/image_raw/large spots/masks/synthetic_000596.tif"

image = tifffile.imread(image_path)
mask = tifffile.imread(mask_path)

print("=== Spot Size Analysis ===")
analyze_spot_sizes(mask)

print("\n=== Testing Erosion Parameters ===")
results = test_erosion_parameters(mask, image)
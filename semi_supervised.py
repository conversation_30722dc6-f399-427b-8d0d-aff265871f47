import torch
import torch.nn as nn
import numpy as np
import os
from tqdm import tqdm
from typing import Dict, List, Tuple, Optional, Union
import matplotlib.pyplot as plt
from SparseSpotDataset import SparseSpotDataset

class SemiSupervisedTrainer:
    """
    Semi-supervised learning trainer for spot detection
    
    This class implements:
    1. Pseudo-labeling with confidence thresholds
    2. Consistency regularization
    3. Iterative self-training
    4. Confidence-weighted loss
    """
    def __init__(self, 
                model: nn.Module,
                loss_fn: nn.Module,
                labeled_loader: torch.utils.data.DataLoader,
                unlabeled_loader: Optional[torch.utils.data.DataLoader] = None,
                val_loader: Optional[torch.utils.data.DataLoader] = None,
                optimizer: Optional[torch.optim.Optimizer] = None,
                scheduler: Optional[object] = None,
                device: str = 'cuda',
                confidence_threshold: float = 0.9,
                ignore_threshold: float = 0.3,
                consistency_weight: float = 1.0,
                max_iterations: int = 5):
        """
        Initialize the semi-supervised trainer
        
        Args:
            model: Model to train
            loss_fn: Loss function
            labeled_loader: DataLoader for labeled data
            unlabeled_loader: DataLoader for unlabeled data
            val_loader: DataLoader for validation data
            optimizer: Optimizer
            scheduler: Learning rate scheduler
            device: Device to use ('cuda' or 'cpu')
            confidence_threshold: Threshold for high-confidence predictions
            ignore_threshold: Threshold below which to ignore predictions
            consistency_weight: Weight for consistency loss
            max_iterations: Maximum number of self-training iterations
        """
        self.model = model
        self.loss_fn = loss_fn
        self.labeled_loader = labeled_loader
        self.unlabeled_loader = unlabeled_loader
        self.val_loader = val_loader
        self.optimizer = optimizer
        self.scheduler = scheduler
        self.device = torch.device(device if torch.cuda.is_available() and device == 'cuda' else 'cpu')
        self.confidence_threshold = confidence_threshold
        self.ignore_threshold = ignore_threshold
        self.consistency_weight = consistency_weight
        self.max_iterations = max_iterations
        
        # Move model to device
        self.model.to(self.device)
    
    def train_epoch(self, 
                   labeled_loader: torch.utils.data.DataLoader,
                   unlabeled_loader: Optional[torch.utils.data.DataLoader] = None) -> Dict[str, float]:
        """
        Train for one epoch with semi-supervised learning
        
        Args:
            labeled_loader: DataLoader for labeled data
            unlabeled_loader: DataLoader for unlabeled data
            
        Returns:
            Dict of metrics
        """
        self.model.train()
        total_loss = 0.0
        supervised_loss = 0.0
        consistency_loss = 0.0
        
        # Create iterator for unlabeled data
        unlabeled_iter = iter(unlabeled_loader) if unlabeled_loader is not None else None
        
        # Use tqdm for progress bar
        pbar = tqdm(labeled_loader, desc="Training")
        
        for batch_idx, batch in enumerate(pbar):
            # Get labeled inputs and targets
            inputs = batch['image'].to(self.device)
            targets = {
                'masks': batch['mask'].to(self.device),
                'confidence': batch['confidence'].to(self.device) if 'confidence' in batch else None
            }
            
            # Zero gradients
            self.optimizer.zero_grad()
            
            # Forward pass for labeled data
            outputs = self.model(inputs)
            
            # Compute supervised loss
            loss, losses_dict = self.loss_fn(outputs, targets)
            sup_loss = loss
            
            # Process unlabeled data if available
            cons_loss = 0.0
            if unlabeled_loader is not None:
                try:
                    unlabeled_batch = next(unlabeled_iter)
                except StopIteration:
                    unlabeled_iter = iter(unlabeled_loader)
                    unlabeled_batch = next(unlabeled_iter)
                
                # Get unlabeled inputs
                unlabeled_inputs = unlabeled_batch['image'].to(self.device)
                
                # Forward pass with no gradients to get pseudo-labels
                with torch.no_grad():
                    self.model.eval()
                    pseudo_outputs = self.model(unlabeled_inputs)
                    pseudo_masks = torch.sigmoid(pseudo_outputs['heatmap'])
                    
                    # Create confidence mask
                    confidence_mask = (pseudo_masks > self.confidence_threshold).float()
                    ignore_mask = (pseudo_masks < self.ignore_threshold).float()
                    
                    # Combine masks: 1 for high confidence, 0 for ignore, values in between for uncertain
                    pseudo_confidence = torch.clamp(
                        (pseudo_masks - self.ignore_threshold) / (self.confidence_threshold - self.ignore_threshold),
                        0.0, 1.0
                    )
                
                # Forward pass with gradients for consistency
                self.model.train()
                outputs_unlabeled = self.model(unlabeled_inputs)
                
                # Compute consistency loss
                pseudo_targets = {
                    'masks': pseudo_masks.detach(),
                    'confidence': pseudo_confidence.detach()
                }
                
                # Use confidence-weighted loss
                cons_loss, _ = self.loss_fn(outputs_unlabeled, pseudo_targets)
                cons_loss = cons_loss * self.consistency_weight
            
            # Combine losses
            loss = sup_loss + cons_loss
            
            # Backward pass
            loss.backward()
            
            # Update weights
            self.optimizer.step()
            
            # Update metrics
            total_loss += loss.item()
            supervised_loss += sup_loss.item()
            consistency_loss += cons_loss if isinstance(cons_loss, float) else cons_loss.item()
            
            # Update progress bar
            pbar.set_postfix({
                'loss': f"{loss.item():.4f}",
                'sup_loss': f"{sup_loss.item():.4f}",
                'cons_loss': f"{cons_loss.item() if not isinstance(cons_loss, float) else cons_loss:.4f}",
                'lr': f"{self.optimizer.param_groups[0]['lr']:.6f}"
            })
        
        # Compute average losses
        num_batches = len(labeled_loader)
        metrics = {
            'loss': total_loss / num_batches,
            'supervised_loss': supervised_loss / num_batches,
            'consistency_loss': consistency_loss / num_batches
        }
        
        return metrics
    
    def generate_pseudo_labels(self, 
                              dataset: SparseSpotDataset,
                              batch_size: int = 8,
                              num_workers: int = 4) -> Dict[str, int]:
        """
        Generate pseudo-labels for a dataset
        
        Args:
            dataset: Dataset to generate pseudo-labels for
            batch_size: Batch size
            num_workers: Number of workers for data loading
            
        Returns:
            Dict with statistics about pseudo-labeling
        """
        self.model.eval()
        
        # Create data loader
        loader = torch.utils.data.DataLoader(
            dataset,
            batch_size=batch_size,
            shuffle=False,
            num_workers=num_workers,
            pin_memory=True
        )
        
        # Track statistics
        total_pixels = 0
        updated_pixels = 0
        high_conf_pixels = 0
        
        # Process each batch
        image_paths = []
        predictions = []
        
        with torch.no_grad():
            for batch in tqdm(loader, desc="Generating pseudo-labels"):
                # Get inputs
                inputs = batch['image'].to(self.device)
                
                # Forward pass
                outputs = self.model(inputs)
                
                # Get predictions
                pred_heatmaps = outputs['heatmap']
                
                # Store image paths and predictions
                if isinstance(dataset.images[0], str):
                    batch_paths = [dataset.images[i] for i in batch['index']] if 'index' in batch else []
                    image_paths.extend(batch_paths)
                    predictions.extend([p for p in pred_heatmaps])
                
                # Update statistics
                pred_probs = torch.sigmoid(pred_heatmaps).cpu()
                high_conf = (pred_probs > self.confidence_threshold).float()
                
                total_pixels += pred_probs.numel()
                high_conf_pixels += high_conf.sum().item()
        
        # Update dataset with predictions
        if len(image_paths) > 0 and len(predictions) > 0:
            dataset.update_with_predictions(image_paths, predictions, self.confidence_threshold)
            
            # Count updated pixels
            for i, path in enumerate(image_paths):
                if isinstance(dataset.confidence_masks[i], np.ndarray):
                    updated = (dataset.confidence_masks[i] >= self.confidence_threshold).sum()
                    updated_pixels += updated
        
        return {
            'total_pixels': total_pixels,
            'high_confidence_pixels': high_conf_pixels,
            'updated_pixels': updated_pixels,
            'high_confidence_ratio': high_conf_pixels / total_pixels if total_pixels > 0 else 0
        }
    
    def self_training(self, 
                     labeled_dataset: SparseSpotDataset,
                     unlabeled_dataset: Optional[SparseSpotDataset] = None,
                     val_dataset: Optional[SparseSpotDataset] = None,
                     batch_size: int = 8,
                     num_workers: int = 4,
                     epochs_per_iteration: int = 5) -> Dict[str, List[float]]:
        """
        Perform self-training with iterative pseudo-labeling
        
        Args:
            labeled_dataset: Dataset with labeled data
            unlabeled_dataset: Dataset with unlabeled data
            val_dataset: Dataset for validation
            batch_size: Batch size
            num_workers: Number of workers for data loading
            epochs_per_iteration: Number of epochs to train in each iteration
            
        Returns:
            Dict with training history
        """
        # Initialize history
        history = {
            'train_loss': [],
            'val_loss': [],
            'supervised_loss': [],
            'consistency_loss': [],
            'high_confidence_ratio': [],
            'updated_pixels': []
        }
        
        # Create data loaders
        labeled_loader = torch.utils.data.DataLoader(
            labeled_dataset,
            batch_size=batch_size,
            shuffle=True,
            num_workers=num_workers,
            pin_memory=True
        )
        
        unlabeled_loader = None
        if unlabeled_dataset is not None:
            unlabeled_loader = torch.utils.data.DataLoader(
                unlabeled_dataset,
                batch_size=batch_size,
                shuffle=True,
                num_workers=num_workers,
                pin_memory=True
            )
        
        val_loader = None
        if val_dataset is not None:
            val_loader = torch.utils.data.DataLoader(
                val_dataset,
                batch_size=batch_size,
                shuffle=False,
                num_workers=num_workers,
                pin_memory=True
            )
        
        # Perform self-training iterations
        for iteration in range(self.max_iterations):
            print(f"\nSelf-training iteration {iteration+1}/{self.max_iterations}")
            
            # Train for several epochs
            for epoch in range(epochs_per_iteration):
                print(f"Epoch {epoch+1}/{epochs_per_iteration}")
                
                # Train for one epoch
                train_metrics = self.train_epoch(labeled_loader, unlabeled_loader)
                
                # Validate
                val_metrics = {'val_loss': 0.0}
                if val_loader is not None:
                    val_metrics = self.validate(val_loader)
                
                # Update learning rate scheduler
                if self.scheduler is not None:
                    if isinstance(self.scheduler, torch.optim.lr_scheduler.ReduceLROnPlateau):
                        self.scheduler.step(val_metrics['val_loss'])
                    else:
                        self.scheduler.step()
                
                # Update history
                history['train_loss'].append(train_metrics['loss'])
                history['val_loss'].append(val_metrics['val_loss'])
                history['supervised_loss'].append(train_metrics['supervised_loss'])
                history['consistency_loss'].append(train_metrics['consistency_loss'])
            
            # Generate pseudo-labels for labeled dataset
            print("Generating pseudo-labels for labeled dataset...")
            label_stats = self.generate_pseudo_labels(labeled_dataset, batch_size, num_workers)
            
            # Generate pseudo-labels for unlabeled dataset if available
            if unlabeled_dataset is not None:
                print("Generating pseudo-labels for unlabeled dataset...")
                unlabel_stats = self.generate_pseudo_labels(unlabeled_dataset, batch_size, num_workers)
                
                # Combine statistics
                label_stats['high_confidence_pixels'] += unlabel_stats['high_confidence_pixels']
                label_stats['total_pixels'] += unlabel_stats['total_pixels']
                label_stats['updated_pixels'] += unlabel_stats['updated_pixels']
                label_stats['high_confidence_ratio'] = (
                    label_stats['high_confidence_pixels'] / label_stats['total_pixels']
                    if label_stats['total_pixels'] > 0 else 0
                )
            
            # Update history with pseudo-labeling statistics
            history['high_confidence_ratio'].append(label_stats['high_confidence_ratio'])
            history['updated_pixels'].append(label_stats['updated_pixels'])
            
            print(f"Iteration {iteration+1} stats:")
            print(f"  High confidence ratio: {label_stats['high_confidence_ratio']:.4f}")
            print(f"  Updated pixels: {label_stats['updated_pixels']}")
            
            # Early stopping if high confidence ratio is very high
            if label_stats['high_confidence_ratio'] > 0.95:
                print("Early stopping: high confidence ratio > 0.95")
                break
        
        return history
    
    def validate(self, val_loader: torch.utils.data.DataLoader) -> Dict[str, float]:
        """
        Validate the model
        
        Args:
            val_loader: DataLoader for validation data
            
        Returns:
            Dict of metrics
        """
        self.model.eval()
        total_loss = 0.0
        
        with torch.no_grad():
            for batch in tqdm(val_loader, desc="Validation"):
                # Get inputs and targets
                inputs = batch['image'].to(self.device)
                targets = {
                    'masks': batch['mask'].to(self.device),
                    'confidence': batch['confidence'].to(self.device) if 'confidence' in batch else None
                }
                
                # Forward pass
                outputs = self.model(inputs)
                
                # Compute loss
                loss, _ = self.loss_fn(outputs, targets)
                
                # Update metrics
                total_loss += loss.item()
        
        # Compute average loss
        metrics = {
            'val_loss': total_loss / len(val_loader)
        }
        
        return metrics
    
    def plot_history(self, history: Dict[str, List[float]], save_path: Optional[str] = None):
        """
        Plot training history
        
        Args:
            history: Training history
            save_path: Path to save the plot
        """
        fig, axs = plt.subplots(2, 2, figsize=(15, 10))
        
        # Plot loss curves
        axs[0, 0].plot(history['train_loss'], label='Train Loss')
        axs[0, 0].plot(history['val_loss'], label='Val Loss')
        axs[0, 0].set_title('Loss Curves')
        axs[0, 0].set_xlabel('Epoch')
        axs[0, 0].set_ylabel('Loss')
        axs[0, 0].legend()
        axs[0, 0].grid(True)
        
        # Plot supervised vs consistency loss
        axs[0, 1].plot(history['supervised_loss'], label='Supervised Loss')
        axs[0, 1].plot(history['consistency_loss'], label='Consistency Loss')
        axs[0, 1].set_title('Loss Components')
        axs[0, 1].set_xlabel('Epoch')
        axs[0, 1].set_ylabel('Loss')
        axs[0, 1].legend()
        axs[0, 1].grid(True)
        
        # Plot high confidence ratio
        if 'high_confidence_ratio' in history and len(history['high_confidence_ratio']) > 0:
            # Create x-axis with appropriate spacing for iterations
            x = np.linspace(0, len(history['train_loss']), len(history['high_confidence_ratio']))
            axs[1, 0].plot(x, history['high_confidence_ratio'], 'o-')
            axs[1, 0].set_title('High Confidence Ratio')
            axs[1, 0].set_xlabel('Iteration')
            axs[1, 0].set_ylabel('Ratio')
            axs[1, 0].grid(True)
        
        # Plot updated pixels
        if 'updated_pixels' in history and len(history['updated_pixels']) > 0:
            # Create x-axis with appropriate spacing for iterations
            x = np.linspace(0, len(history['train_loss']), len(history['updated_pixels']))
            axs[1, 1].plot(x, history['updated_pixels'], 'o-')
            axs[1, 1].set_title('Updated Pixels')
            axs[1, 1].set_xlabel('Iteration')
            axs[1, 1].set_ylabel('Pixels')
            axs[1, 1].grid(True)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path)
        
        plt.show()
# SOLUTION 1: Reset semantic head weights (recommended)
def reset_semantic_head(model):
    """Reset only the semantic head to learn new eroded targets"""
    if hasattr(model, 'semantic_head'):
        for layer in model.semantic_head.modules():
            if isinstance(layer, torch.nn.Conv2d):
                torch.nn.init.kaiming_normal_(layer.weight)
                if layer.bias is not None:
                    torch.nn.init.zeros_(layer.bias)
    print("✅ Reset semantic head weights")

# Add this to your training code:
reset_semantic_head(model)

# SOLUTION 2: Increase semantic loss weight temporarily
# In your loss function, increase semantic weight:
criterion = AdaptiveSpotLoss()
criterion.w_sem = 3.0  # Increase from default to force learning smaller masks

# SOLUTION 3: Use higher learning rate for semantic head only
optimizer = torch.optim.AdamW([
    {'params': [p for n, p in model.named_parameters() if 'semantic_head' not in n], 'lr': 1e-4},
    {'params': model.semantic_head.parameters(), 'lr': 5e-4}  # Higher LR for semantic head
], weight_decay=1e-5)
# Speed up convergence to smaller predictions

# OPTION 1: Further reduce semantic weight
self.w_sem = 0.2      # Even lower (was 0.5)
self.w_bnd = 4.0      # Higher boundary weight
self.w_dist = 5.0     # Higher distance weight

# OPTION 2: Add size penalty loss
def _size_penalty(self, pred_logits, gt_semantic):
    """Penalize predictions larger than GT"""
    pred_prob = torch.sigmoid(pred_logits)
    gt_area = gt_semantic.sum(dim=[2,3], keepdim=True)
    pred_area = pred_prob.sum(dim=[2,3], keepdim=True)
    size_ratio = pred_area / (gt_area + 1e-6)
    penalty = F.relu(size_ratio - 1.0)  # Penalize if pred > gt
    return penalty.mean()

# Add to semantic loss:
losses['semantic'] = l1 + l2 + 0.3 * self._size_penalty(outputs['semantic'], gt_sem)

# OPTION 3: Higher threshold in visualization to see progress
sem = (torch.sigmoid(outputs['semantic'])[0, 0].cpu().numpy() > 0.6).astype(np.float32)
import torch
import numpy as np
from OptimizedSpotLoss import OptimizedSpotLoss

def test_loss_function_debug():
    """Test the loss function with detailed debugging to identify issues"""
    print("Testing OptimizedSpotLoss with detailed debugging...")
    
    # Create a loss function
    loss_fn = OptimizedSpotLoss(
        bce_weight=1.0,
        dice_weight=1.0,
        focal_weight=0.5,
        focal_gamma=2.0,
        size_adaptive=True,
        density_aware=True,
        confidence_weighted=True
    )
    
    # Create random predictions and targets
    batch_size = 4
    height, width = 64, 64
    
    # Case 1: Random initialization (should give high loss)
    print("\nCase 1: Random initialization")
    
    # Create random predictions
    pred = torch.randn(batch_size, 1, height, width)
    
    # Create target with controlled spot density
    target = torch.zeros(batch_size, 1, height, width)
    
    # Add some random spots to target
    for b in range(batch_size):
        for _ in range(np.random.randint(3, 10)):
            x = np.random.randint(5, width-5)
            y = np.random.randint(5, height-5)
            radius = np.random.randint(2, 5)
            
            for i in range(max(0, y-radius), min(height, y+radius+1)):
                for j in range(max(0, x-radius), min(width, x+radius+1)):
                    if ((i-y)**2 + (j-x)**2) <= radius**2:
                        target[b, 0, i, j] = 1.0
    
    # Print target statistics
    print(f"Target statistics:")
    print(f"  Min: {target.min().item():.4f}, Max: {target.max().item():.4f}")
    print(f"  Mean: {target.mean().item():.4f}")
    print(f"  Num positive pixels: {(target > 0.5).sum().item()}")
    
    # Print prediction statistics
    print(f"Prediction statistics:")
    print(f"  Min: {pred.min().item():.4f}, Max: {pred.max().item():.4f}")
    print(f"  Mean: {pred.mean().item():.4f}")
    
    # Calculate individual loss components
    print("\nCalculating individual loss components:")
    
    # BCE loss
    pred_sig = torch.sigmoid(pred)
    bce = torch.nn.functional.binary_cross_entropy(pred_sig, target, reduction='none')
    print(f"BCE shape: {bce.shape}, Mean: {bce.mean().item():.4f}")
    print(f"BCE min: {bce.min().item():.4f}, max: {bce.max().item():.4f}")
    
    # Dice loss
    intersection = (pred_sig * target).sum(dim=(-2, -1))
    union = pred_sig.sum(dim=(-2, -1)) + target.sum(dim=(-2, -1))
    dice_coef = (2.0 * intersection + 1e-5) / (union + 1e-5)
    dice_loss = 1.0 - dice_coef
    print(f"Dice shape: {dice_loss.shape}, Mean: {dice_loss.mean().item():.4f}")
    print(f"Dice min: {dice_loss.min().item():.4f}, max: {dice_loss.max().item():.4f}")
    
    # Focal loss
    pt = target * pred_sig + (1 - target) * (1 - pred_sig)
    focal_weights = (1 - pt) ** 2.0
    focal = focal_weights * bce
    print(f"Focal shape: {focal.shape}, Mean: {focal.mean().item():.4f}")
    print(f"Focal min: {focal.min().item():.4f}, max: {focal.max().item():.4f}")
    
    # Calculate density weights
    density = target.sum(dim=(-2, -1), keepdim=True)
    density_weights = 1.0 / (torch.sqrt(torch.clamp(density, min=1.0)) + 1.0)
    print(f"Density weights shape: {density_weights.shape}, Mean: {density_weights.mean().item():.4f}")
    print(f"Density weights min: {density_weights.min().item():.4f}, max: {density_weights.max().item():.4f}")
    
    # Calculate size weights
    num_spots = (target > 0.5).float().sum(dim=(-2, -1), keepdim=True)
    total_area = float(target.shape[-2] * target.shape[-1])
    num_spots = torch.clamp(num_spots, min=1.0, max=total_area)
    avg_size = total_area / num_spots
    size_weights = torch.sqrt((avg_size + 1e-5) / (total_area + 1e-5))
    print(f"Size weights shape: {size_weights.shape}, Mean: {size_weights.mean().item():.4f}")
    print(f"Size weights min: {size_weights.min().item():.4f}, max: {size_weights.max().item():.4f}")
    
    # Calculate loss using the loss function
    print("\nCalculating loss using OptimizedSpotLoss:")
    loss_dict = loss_fn(pred, target)
    
    # Print loss components
    print(f"Loss: {loss_dict['loss'].item():.4f}")
    print(f"BCE: {loss_dict['bce'].item():.4f}")
    print(f"Dice: {loss_dict['dice'].item():.4f}")
    print(f"Focal: {loss_dict['focal'].item():.4f}")
    
    # Case 2: Perfect prediction (should give very low loss)
    print("\nCase 2: Perfect prediction")
    
    # Create perfect prediction (with small noise)
    perfect_pred = torch.logit(target.clone() * 0.99 + 0.005)
    
    # Add small noise to avoid perfect prediction
    perfect_pred = perfect_pred + torch.randn_like(perfect_pred) * 0.01
    
    # Print prediction statistics
    print(f"Perfect prediction statistics:")
    print(f"  Min: {perfect_pred.min().item():.4f}, Max: {perfect_pred.max().item():.4f}")
    print(f"  Mean: {perfect_pred.mean().item():.4f}")
    
    # Calculate loss using the loss function
    loss_dict = loss_fn(perfect_pred, target)
    
    # Print loss components
    print(f"Loss: {loss_dict['loss'].item():.4f}")
    print(f"BCE: {loss_dict['bce'].item():.4f}")
    print(f"Dice: {loss_dict['dice'].item():.4f}")
    print(f"Focal: {loss_dict['focal'].item():.4f}")
    
    # Case 3: All zeros prediction (should give high loss)
    print("\nCase 3: All zeros prediction")
    
    # Create all zeros prediction
    zero_pred = torch.zeros_like(pred) - 10.0  # Large negative value to ensure sigmoid is close to 0
    
    # Print prediction statistics
    print(f"Zero prediction statistics:")
    print(f"  Min: {zero_pred.min().item():.4f}, Max: {zero_pred.max().item():.4f}")
    print(f"  Mean: {zero_pred.mean().item():.4f}")
    print(f"  Sigmoid mean: {torch.sigmoid(zero_pred).mean().item():.8f}")
    
    # Calculate loss using the loss function
    loss_dict = loss_fn(zero_pred, target)
    
    # Print loss components
    print(f"Loss: {loss_dict['loss'].item():.4f}")
    print(f"BCE: {loss_dict['bce'].item():.4f}")
    print(f"Dice: {loss_dict['dice'].item():.4f}")
    print(f"Focal: {loss_dict['focal'].item():.4f}")
    
    # Case 4: All ones prediction (should give high loss)
    print("\nCase 4: All ones prediction")
    
    # Create all ones prediction
    ones_pred = torch.zeros_like(pred) + 10.0  # Large positive value to ensure sigmoid is close to 1
    
    # Print prediction statistics
    print(f"Ones prediction statistics:")
    print(f"  Min: {ones_pred.min().item():.4f}, Max: {ones_pred.max().item():.4f}")
    print(f"  Mean: {ones_pred.mean().item():.4f}")
    print(f"  Sigmoid mean: {torch.sigmoid(ones_pred).mean().item():.8f}")
    
    # Calculate loss using the loss function
    loss_dict = loss_fn(ones_pred, target)
    
    # Print loss components
    print(f"Loss: {loss_dict['loss'].item():.4f}")
    print(f"BCE: {loss_dict['bce'].item():.4f}")
    print(f"Dice: {loss_dict['dice'].item():.4f}")
    print(f"Focal: {loss_dict['focal'].item():.4f}")
    
    print("\nLoss function debug complete!")

if __name__ == "__main__":
    test_loss_function_debug()
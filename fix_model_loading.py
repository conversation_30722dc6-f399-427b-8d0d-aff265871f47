#!/usr/bin/env python3
"""
Quick fix for model loading issue - creates a compatible model loader
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import tifffile
import matplotlib.pyplot as plt
from pathlib import Path

def load_optimized_model(model_path, device='cuda'):
    """Load the optimized model using the notebook's model definition"""
    
    # Use the model class from the notebook (copy the exact definition)
    model = SkeletonAwareSpotDetector(in_ch=1, base_ch=32)
    print("Created SkeletonAwareSpotDetector model")
    
    # Load checkpoint
    checkpoint = torch.load(model_path, map_location=device)
    
    # Try to load state dict with error handling
    try:
        model.load_state_dict(checkpoint['model_state_dict'])
        print("✅ Model loaded successfully")
    except RuntimeError as e:
        print(f"❌ Error loading model: {e}")
        print("Attempting to fix key mismatches...")
        
        # Get the state dict and model's expected keys
        saved_state = checkpoint['model_state_dict']
        model_state = model.state_dict()
        
        # Print some key information for debugging
        print(f"Saved model keys (first 10): {list(saved_state.keys())[:10]}")
        print(f"Expected model keys (first 10): {list(model_state.keys())[:10]}")
        
        # Check if it's just a parameter mismatch (different base_ch)
        if 'stem_conv1.weight' in saved_state and 'stem_conv1.weight' in model_state:
            saved_shape = saved_state['stem_conv1.weight'].shape
            expected_shape = model_state['stem_conv1.weight'].shape
            print(f"Stem conv1 shapes - Saved: {saved_shape}, Expected: {expected_shape}")
            
            if saved_shape != expected_shape:
                print("Detected base_ch mismatch. Trying different base_ch values...")
                # Try common base_ch values
                for base_ch in [48, 64, 32, 16]:
                    try:
                        test_model = SkeletonAwareSpotDetector(in_ch=1, base_ch=base_ch)
                        test_model.load_state_dict(saved_state)
                        print(f"✅ Successfully loaded with base_ch={base_ch}")
                        model = test_model
                        break
                    except:
                        continue
                else:
                    print("❌ Could not find matching base_ch")
                    return None
            else:
                print("❌ Unknown architecture mismatch")
                return None
        else:
            print("❌ Fundamental architecture mismatch")
            return None
    
    model.to(device)
    model.eval()
    return model

# Copy the exact model classes from the notebook
class QuantizedSDTLoss(nn.Module):
    def __init__(self, num_bins=5, background_value=0.0, spot_size_range=(0, 100)):
        super().__init__()
        self.num_bins = num_bins
        self.background_value = background_value
        self.spot_size_range = spot_size_range
        self.register_buffer('bin_indices', torch.linspace(0, 1, num_bins+1)[1:].view(1, -1, 1, 1))
        self.boundary_threshold = 0.1
        self.sharpness_weight = 1.0

class EfficientBlock(nn.Module):
    def __init__(self, in_ch, out_ch, stride=1, expand_ratio=2):
        super().__init__()
        hidden_ch = in_ch * expand_ratio
        self.expand = nn.Conv2d(in_ch, hidden_ch, 1, bias=False) if expand_ratio != 1 else nn.Identity()
        self.bn1 = nn.BatchNorm2d(hidden_ch) if expand_ratio != 1 else nn.Identity()
        self.depthwise = nn.Conv2d(hidden_ch, hidden_ch, 3, stride, 1, groups=hidden_ch, bias=False)
        self.bn2 = nn.BatchNorm2d(hidden_ch)
        self.se = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(hidden_ch, max(4, hidden_ch//8), 1),
            nn.ReLU(inplace=True),
            nn.Conv2d(max(4, hidden_ch//8), hidden_ch, 1),
            nn.Sigmoid()
        )
        self.project = nn.Conv2d(hidden_ch, out_ch, 1, bias=False)
        self.bn3 = nn.BatchNorm2d(out_ch)
        self.skip = (stride == 1 and in_ch == out_ch)
        self.res_scale = nn.Parameter(torch.ones(1) * 0.1)

    def forward(self, x):
        residual = x
        if hasattr(self.expand, 'weight'):
            x = F.relu(self.bn1(self.expand(x)))
        x = F.relu(self.bn2(self.depthwise(x)))
        x = x * self.se(x)
        x = self.bn3(self.project(x))
        if self.skip:
            return x + self.res_scale * residual
        return x

class AdaptiveSpatialAttention(nn.Module):
    def __init__(self, in_channels):
        super().__init__()
        self.conv = nn.Conv2d(in_channels, 1, kernel_size=3, padding=1, bias=False)
        self.threshold = nn.Parameter(torch.tensor(0.5))
        self.sigmoid = nn.Sigmoid()

    def forward(self, x):
        attn = self.conv(x)
        attn = self.sigmoid(attn)
        return x * (attn > self.threshold).float()

class HighResolutionRefinement(nn.Module):
    def __init__(self, in_channels, out_channels):
        super().__init__()
        self.conv1 = nn.Conv2d(in_channels, out_channels * 4, 3, padding=1)
        self.bn1 = nn.BatchNorm2d(out_channels * 4)
        self.relu = nn.ReLU(inplace=True)
        self.pixel_shuffle = nn.PixelShuffle(2)
        self.conv2 = nn.Conv2d(out_channels, out_channels, 1)
        self.bn2 = nn.BatchNorm2d(out_channels)
        self.residual = nn.Conv2d(in_channels, out_channels, 1)

    def forward(self, x):
        residual = self.residual(x)
        x = self.relu(self.bn1(self.conv1(x)))
        x = self.pixel_shuffle(x)
        x = self.bn2(self.conv2(x))
        return x + residual

class SkeletonAwareSpotDetector(nn.Module):
    def __init__(self, in_ch=1, base_ch=32):
        super().__init__()
        self.base_ch = base_ch
        
        # Stem
        self.stem_conv1 = nn.Conv2d(in_ch, base_ch, 3, padding=1, bias=False)
        self.stem_bn1 = nn.BatchNorm2d(base_ch)
        self.stem_conv2 = nn.Conv2d(base_ch, base_ch, 3, padding=1, bias=False)
        self.stem_bn2 = nn.BatchNorm2d(base_ch)
        
        # Encoder
        self.enc1 = nn.Sequential(
            EfficientBlock(base_ch, base_ch*2, stride=2),
            EfficientBlock(base_ch*2, base_ch*2)
        )
        self.enc2 = nn.Sequential(
            EfficientBlock(base_ch*2, base_ch*4, stride=2),
            EfficientBlock(base_ch*4, base_ch*4)
        )
        
        # Feature Pyramid
        self.fpn_lateral_stem = nn.Conv2d(base_ch, base_ch//2, 1)
        self.fpn_conv2 = nn.Conv2d(base_ch*4, base_ch*2, 1)
        self.fpn_conv1 = nn.Conv2d(base_ch*2, base_ch, 1)
        
        # Refinement and attention
        self.refinement = HighResolutionRefinement(base_ch*3, base_ch)
        self.spatial_attn = AdaptiveSpatialAttention(base_ch)
        
        # Shared head
        self.shared_head = nn.Sequential(
            nn.Conv2d(base_ch, base_ch, 3, padding=1, groups=base_ch),
            nn.Conv2d(base_ch, base_ch, 1),
            nn.BatchNorm2d(base_ch),
            nn.ReLU(inplace=True)
        )
        
        # Output heads
        self.sadt_heads = nn.ModuleDict({
            'sdt': nn.Sequential(
                nn.Conv2d(base_ch, base_ch, 3, padding=1),
                nn.BatchNorm2d(base_ch),
                nn.ReLU(inplace=True),
                nn.Conv2d(base_ch, 6, 1)
            ),
            'skeleton': nn.Sequential(
                nn.Conv2d(base_ch, base_ch, 3, padding=1),
                nn.BatchNorm2d(base_ch),
                nn.ReLU(inplace=True),
                nn.Conv2d(base_ch, 1, 1)
            )
        })
        
        self.semantic_head = nn.Conv2d(base_ch, 1, 1)
        self.centroid_head = nn.Conv2d(base_ch, 1, 1)
        self.flow_head = nn.Conv2d(base_ch, 2, 1)
        self.boundary_head = nn.Conv2d(base_ch, 1, 1)
        
        self._init_weights()
    
    def _init_weights(self):
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_in', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.zeros_(m.bias)
            elif isinstance(m, nn.BatchNorm2d):
                nn.init.ones_(m.weight)
                nn.init.zeros_(m.bias)
    
    def forward(self, x):
        B, C, H, W = x.shape
        
        # Encoder
        x0 = F.relu(self.stem_bn2(self.stem_conv2(F.relu(self.stem_bn1(self.stem_conv1(x))))))
        x1 = self.enc1(x0)
        x2 = self.enc2(x1)
        
        # Feature Pyramid
        p2 = self.fpn_conv2(x2)
        p2_up = F.interpolate(p2, size=(H//2, W//2), mode='bilinear', align_corners=False)
        p1_in = x1 + p2_up
        p1 = self.fpn_conv1(p1_in)
        p1_up = F.interpolate(p1, size=(H, W), mode='bilinear', align_corners=False)
        
        # Lateral connection
        lateral_x0 = self.fpn_lateral_stem(x0)
        
        # Combine features
        attended = x0 * torch.sigmoid(torch.mean(p1_up, dim=1, keepdim=True))
        fused = torch.cat([lateral_x0, p1_up, attended], dim=1)
        
        # Refinement and attention
        features = self.refinement(fused)
        features = self.spatial_attn(features)
        
        # Shared processing
        shared_feat = self.shared_head(features)
        
        # Outputs
        sdt_out = self.sadt_heads['sdt'](shared_feat)
        skeleton_out = self.sadt_heads['skeleton'](shared_feat)
        
        outputs = {
            'sem_out': self.semantic_head(shared_feat),
            'sdt_out': sdt_out,
            'skeleton_out': skeleton_out,
            'hm_out': self.centroid_head(shared_feat),
            'flow_out': self.flow_head(shared_feat),
            'boundary_out': self.boundary_head(shared_feat)
        }
        return outputs

def run_single_inference(model_path, image_path, device='cuda'):
    """Run inference on a single image"""
    
    print(f"Loading model from: {model_path}")
    model = load_optimized_model(model_path, device)
    
    if model is None:
        print("❌ Failed to load model")
        return None
    
    print(f"Loading image from: {image_path}")
    
    # Load and preprocess image
    image = tifffile.imread(image_path).astype(np.float32)
    image = image / (image.max() + 1e-8)
    
    # Convert to tensor
    image_tensor = torch.from_numpy(image).unsqueeze(0).unsqueeze(0).to(device)
    
    print(f"Image shape: {image_tensor.shape}")
    
    # Run inference
    with torch.no_grad():
        try:
            outputs = model(image_tensor)
            print("✅ Inference successful")
            
            # Extract results
            if isinstance(outputs, dict):
                if 'heatmap' in outputs:
                    heatmap = outputs['heatmap'][0, 0].cpu().numpy()
                elif 'sem_out' in outputs:
                    heatmap = torch.sigmoid(outputs['sem_out'])[0, 0].cpu().numpy()
                else:
                    heatmap = list(outputs.values())[0][0, 0].cpu().numpy()
            else:
                heatmap = outputs[0, 0].cpu().numpy()
            
            # Simple spot detection (find local maxima)
            from scipy.ndimage import maximum_filter
            local_max = maximum_filter(heatmap, size=5) == heatmap
            spots = np.column_stack(np.where(local_max & (heatmap > 0.3)))
            
            print(f"Detected {len(spots)} potential spots")
            
            # Visualize results
            fig, axes = plt.subplots(1, 2, figsize=(12, 5))
            
            axes[0].imshow(image, cmap='gray')
            axes[0].set_title('Input Image')
            axes[0].axis('off')
            
            axes[1].imshow(heatmap, cmap='hot')
            if len(spots) > 0:
                axes[1].scatter(spots[:, 1], spots[:, 0], c='cyan', s=20, marker='x')
            axes[1].set_title(f'Detection Results ({len(spots)} spots)')
            axes[1].axis('off')
            
            plt.tight_layout()
            plt.savefig('inference_result.png', dpi=150, bbox_inches='tight')
            plt.show()
            
            return {
                'heatmap': heatmap,
                'spots': spots,
                'num_spots': len(spots)
            }
            
        except Exception as e:
            print(f"❌ Inference failed: {e}")
            return None

def main():
    """Main function to test model loading and inference"""
    
    # Paths - adjust these to your actual paths
    model_path = "/mnt/d/Users/<USER>/Documents/spot detector git amazon 2025/skeleton_aware_model_improved_spots_qwen_alpha_8.5/best_model.pth"
    
    # Find a test image
    image_dir = "/mnt/d/Users/<USER>/FISH_spots/2d/synthetic/images/"
    image_files = list(Path(image_dir).glob("*.tif"))
    
    if not image_files:
        print(f"❌ No images found in {image_dir}")
        return
    
    image_path = str(image_files[0])
    print(f"Using test image: {image_path}")
    
    # Check if model file exists
    if not Path(model_path).exists():
        print(f"❌ Model file not found: {model_path}")
        return
    
    # Run inference
    result = run_single_inference(model_path, image_path)
    
    if result:
        print(f"✅ Successfully detected {result['num_spots']} spots")
    else:
        print("❌ Inference failed")

if __name__ == "__main__":
    main()
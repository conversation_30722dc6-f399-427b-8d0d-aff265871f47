import torch
import numpy as np
import matplotlib.pyplot as plt
from skimage.feature import peak_local_max
from skimage.segmentation import watershed
from scipy import ndimage
from skimage import measure
import cv2

def detect_spots_adaptive(image, heatmap, min_distance=3, min_intensity=0.1, 
                         min_spot_size=2, max_spot_size=15, show_spot_numbers=False):
    """
    Adaptive spot detection that creates individual masks for each spot
    and handles spots of different sizes.
    
    Args:
        image: Original image (numpy array)
        heatmap: Prediction heatmap (numpy array)
        min_distance: Minimum distance between peaks (pixels)
        min_intensity: Minimum intensity threshold for peaks
        min_spot_size: Minimum spot size in pixels
        max_spot_size: Maximum spot size in pixels
        show_spot_numbers: Whether to show spot numbers in visualization
        
    Returns:
        Dictionary with detection results including individual spot masks
    """
    # Find local maxima directly in the heatmap
    coordinates = peak_local_max(
        heatmap,
        min_distance=min_distance,
        threshold_abs=min_intensity,
        exclude_border=False
    )
    
    print(f"Found {len(coordinates)} initial peaks")
    
    # Create a marker image for watershed
    markers = np.zeros_like(heatmap, dtype=np.int32)
    for i, (y, x) in enumerate(coordinates):
        markers[y, x] = i + 1
    
    # Create a binary mask for watershed
    # Use adaptive thresholding based on local peak intensity
    binary_mask = np.zeros_like(heatmap, dtype=bool)
    
    # For each peak, determine local threshold based on peak intensity
    for y, x in coordinates:
        # Get peak intensity
        peak_intensity = heatmap[y, x]
        
        # Define local region around peak
        window_size = max(min_distance * 2, 7)
        half_size = window_size // 2
        
        # Define region boundaries with bounds checking
        y_min = max(0, y - half_size)
        y_max = min(heatmap.shape[0], y + half_size + 1)
        x_min = max(0, x - half_size)
        x_max = min(heatmap.shape[1], x + half_size + 1)
        
        # Extract region
        region = heatmap[y_min:y_max, x_min:x_max]
        
        # Calculate adaptive threshold for this peak (lower for brighter peaks)
        local_threshold = peak_intensity * 0.3
        
        # Apply threshold to local region and update binary mask
        binary_mask[y_min:y_max, x_min:x_max] |= (region >= local_threshold)
    
    # Apply distance transform for watershed
    distance = ndimage.distance_transform_edt(binary_mask)
    
    # Apply watershed to separate touching spots
    labels = watershed(-distance, markers, mask=binary_mask)
    
    # Filter spots by size
    props = measure.regionprops(labels)
    valid_labels = []
    refined_coordinates = []
    
    for prop in props:
        if min_spot_size <= prop.area <= max_spot_size:
            valid_labels.append(prop.label)
            # Use centroid as refined coordinate
            refined_coordinates.append(prop.centroid)
    
    # Create final mask with only valid spots
    final_mask = np.zeros_like(labels, dtype=np.int32)
    for valid_label in valid_labels:
        final_mask[labels == valid_label] = valid_label
    
    # Create individual masks for each spot
    individual_masks = {}
    for label in valid_labels:
        individual_masks[label] = (final_mask == label)
    
    # Create visualization
    rgb_mask = np.zeros((*image.shape, 3))
    rgb_mask[..., 0] = image  # Red channel
    rgb_mask[..., 1] = image  # Green channel
    rgb_mask[..., 2] = image  # Blue channel
    
    # Color each spot with a unique color
    for i, label in enumerate(valid_labels):
        # Get spot mask
        spot_mask = individual_masks[label]
        
        # Get centroid
        y, x = refined_coordinates[i]
        
        # Generate a unique color for this spot
        h = (i * 0.618033988749895) % 1  # Golden ratio to distribute colors
        r, g, b = plt.cm.hsv(h)[:3]
        
        # Apply color to the spot
        rgb_mask[spot_mask, 0] = r
        rgb_mask[spot_mask, 1] = g
        rgb_mask[spot_mask, 2] = b
        
        # Draw circle at centroid
        cv2.circle(
            rgb_mask,
            (int(round(x)), int(round(y))),
            2,
            (1, 1, 1),  # White
            1
        )
        
        # Add ID if requested
        if show_spot_numbers:
            cv2.putText(
                rgb_mask,
                str(i+1),
                (int(x), int(y)),
                cv2.FONT_HERSHEY_SIMPLEX,
                0.3,
                (1, 1, 1),  # White
                1
            )
    
    return {
        'num_spots': len(valid_labels),
        'coordinates': refined_coordinates,
        'visualization': rgb_mask,
        'mask': final_mask,
        'individual_masks': individual_masks
    }

def test_adaptive_spot_detection(image, heatmap, true_num_spots=None):
    """
    Test different parameters for adaptive spot detection
    
    Args:
        image: Original image (numpy array)
        heatmap: Prediction heatmap (numpy array)
        true_num_spots: The actual number of spots in the image (if known)
    """
    min_distances = [2, 3, 5]
    min_intensities = [0.05, 0.1, 0.2]
    
    # Create figure
    fig, axes = plt.subplots(len(min_distances), len(min_intensities), figsize=(15, 12))
    
    # Test each combination
    results = {}
    
    for i, min_distance in enumerate(min_distances):
        for j, min_intensity in enumerate(min_intensities):
            # Detect spots
            result = detect_spots_adaptive(
                image, heatmap, 
                min_distance=min_distance, 
                min_intensity=min_intensity,
                show_spot_numbers=False
            )
            
            # Store result
            key = f"distance_{min_distance}_intensity_{min_intensity}"
            results[key] = result
            
            # Calculate count error if true count is known
            if true_num_spots is not None:
                count_error = abs(result['num_spots'] - true_num_spots) / max(true_num_spots, 1)
                results[key]['count_error'] = count_error
                error_text = f", Error={count_error:.2f}"
            else:
                error_text = ""
            
            # Plot result
            axes[i, j].imshow(result['visualization'])
            axes[i, j].set_title(f'D={min_distance}, I={min_intensity}, N={result["num_spots"]}{error_text}')
            axes[i, j].axis('off')
    
    plt.tight_layout()
    plt.show()
    
    # Find best parameters
    if true_num_spots is not None:
        # Find best parameters using count error
        count_sorted = sorted(results.items(), key=lambda x: abs(x[1]['num_spots'] - true_num_spots))
        best_params = count_sorted[0]
    else:
        # If no ground truth, use the middle parameters as default
        middle_key = f"distance_3_intensity_0.1"
        best_params = (middle_key, results[middle_key])
    
    best_key = best_params[0]
    best_result = best_params[1]
    
    parts = best_key.split('_')
    best_distance = int(parts[1])
    best_intensity = float(parts[3])
    
    print(f"Best parameters: min_distance={best_distance}, min_intensity={best_intensity}")
    print(f"Number of spots detected: {best_result['num_spots']}")
    
    # Show best result
    plt.figure(figsize=(10, 10))
    plt.imshow(best_result['visualization'])
    plt.title(f'Best Result: {best_result["num_spots"]} spots')
    plt.axis('off')
    plt.show()
    
    # Show individual masks for a few spots
    if len(best_result['individual_masks']) > 0:
        num_to_show = min(5, len(best_result['individual_masks']))
        fig, axes = plt.subplots(1, num_to_show, figsize=(15, 3))
        
        for i, (label, mask) in enumerate(list(best_result['individual_masks'].items())[:num_to_show]):
            if num_to_show == 1:
                ax = axes
            else:
                ax = axes[i]
            ax.imshow(mask, cmap='gray')
            ax.set_title(f'Spot {label}')
            ax.axis('off')
        
        plt.tight_layout()
        plt.show()
    
    return best_distance, best_intensity, best_result

def analyze_spot_properties(result):
    """
    Analyze properties of detected spots
    
    Args:
        result: Result dictionary from detect_spots_adaptive
    """
    if 'individual_masks' not in result:
        print("No individual masks found in result")
        return
    
    # Calculate properties for each spot
    spot_properties = []
    
    for label, mask in result['individual_masks'].items():
        # Calculate area
        area = np.sum(mask)
        
        # Calculate equivalent diameter
        equivalent_diameter = np.sqrt(4 * area / np.pi)
        
        # Calculate centroid
        y_indices, x_indices = np.where(mask)
        y_centroid = np.mean(y_indices)
        x_centroid = np.mean(x_indices)
        
        spot_properties.append({
            'label': label,
            'area': area,
            'equivalent_diameter': equivalent_diameter,
            'centroid': (y_centroid, x_centroid)
        })
    
    # Plot histogram of spot sizes
    areas = [prop['area'] for prop in spot_properties]
    diameters = [prop['equivalent_diameter'] for prop in spot_properties]
    
    fig, axes = plt.subplots(1, 2, figsize=(12, 5))
    
    axes[0].hist(areas, bins=20)
    axes[0].set_title('Spot Area Distribution')
    axes[0].set_xlabel('Area (pixels)')
    axes[0].set_ylabel('Count')
    
    axes[1].hist(diameters, bins=20)
    axes[1].set_title('Spot Diameter Distribution')
    axes[1].set_xlabel('Equivalent Diameter (pixels)')
    axes[1].set_ylabel('Count')
    
    plt.tight_layout()
    plt.show()
    
    return spot_properties
import torch
import numpy as np
import matplotlib.pyplot as plt
from skimage.feature import peak_local_max
from skimage.segmentation import watershed
from skimage import measure
import cv2

# Run this in your notebook to directly detect peaks in the heatmap
def detect_peaks_directly(image, heatmap, min_distance=5, min_intensity=0.1, show_spot_numbers=False):
    """
    Directly detect peaks in the heatmap without using binary thresholding
    
    Args:
        image: Original image (numpy array)
        heatmap: Prediction heatmap (numpy array)
        min_distance: Minimum distance between peaks (pixels)
        min_intensity: Minimum intensity threshold for peaks
        show_spot_numbers: Whether to show spot numbers in visualization (default: False)
        
    Returns:
        Dictionary with detection results
    """
    # Find local maxima directly in the heatmap
    coordinates = peak_local_max(
        heatmap,
        min_distance=min_distance,
        threshold_abs=min_intensity,
        exclude_border=False
    )
    
    print(f"Found {len(coordinates)} peaks with min_distance={min_distance}, min_intensity={min_intensity}")
    
    # Create visualization
    rgb_mask = np.zeros((*image.shape, 3))
    rgb_mask[..., 0] = image  # Red channel
    rgb_mask[..., 1] = image  # Green channel
    rgb_mask[..., 2] = image  # Blue channel
    
    # Refine peak locations using center of mass for more accurate centroids
    refined_coordinates = []
    spot_radii = []  # Store radius for each spot
    
    for y, x in coordinates:
        # Extract a small region around the peak (window size based on min_distance)
        window_size = max(3, min(min_distance * 2, 15))  # Larger window to better estimate spot size
        half_size = window_size // 2
        
        # Define region boundaries with bounds checking
        y_min = max(0, y - half_size)
        y_max = min(heatmap.shape[0], y + half_size + 1)
        x_min = max(0, x - half_size)
        x_max = min(heatmap.shape[1], x + half_size + 1)
        
        # Extract region
        region = heatmap[y_min:y_max, x_min:x_max]
        
        # Skip if region is empty
        if region.size == 0:
            refined_coordinates.append((y, x))
            spot_radii.append(3)  # Default radius
            continue
            
        # Calculate center of mass for more accurate centroid
        # First threshold the region to focus on the spot
        threshold = min_intensity
        binary_region = region > threshold
        
        # Skip if no pixels above threshold
        if not np.any(binary_region):
            refined_coordinates.append((y, x))
            spot_radii.append(3)  # Default radius
            continue
            
        # Calculate center of mass
        y_indices, x_indices = np.where(binary_region)
        weights = region[binary_region]
        
        # Calculate weighted centroid
        if weights.sum() > 0:
            y_cm = np.sum(y_indices * weights) / weights.sum() + y_min
            x_cm = np.sum(x_indices * weights) / weights.sum() + x_min
            refined_coordinates.append((y_cm, x_cm))
        else:
            refined_coordinates.append((y, x))
            spot_radii.append(3)  # Default radius
            continue
        
        # Get intensity at this peak (using nearest integer coordinates)
        y_int, x_int = int(round(y_cm)), int(round(x_cm))
        y_int = max(0, min(y_int, heatmap.shape[0]-1))
        x_int = max(0, min(x_int, heatmap.shape[1]-1))
        intensity = heatmap[y_int, x_int]
        
        # Calculate radius using adaptive thresholding for more accurate spot size
        # Extract a larger region around the peak for better size estimation
        window_size = max(21, min_distance * 3)  # Larger window to capture full spot profile
        half_size = window_size // 2
        
        # Define region boundaries with bounds checking
        y_min = max(0, int(y_cm) - half_size)
        y_max = min(heatmap.shape[0], int(y_cm) + half_size + 1)
        x_min = max(0, int(x_cm) - half_size)
        x_max = min(heatmap.shape[1], int(x_cm) + half_size + 1)
        
        # Extract region
        region = heatmap[y_min:y_max, x_min:x_max].copy()
        
        if region.size > 0:
            # Create meshgrid for the region
            y_indices, x_indices = np.indices(region.shape)
            
            # Center coordinates within the region
            y_center = int(y_cm) - y_min
            x_center = int(x_cm) - x_min
            
            # Calculate distance from center for each pixel
            distances = np.sqrt((y_indices - y_center)**2 + (x_indices - x_center)**2)
            
            # Use stricter thresholding to avoid background noise
            # Higher threshold for all spots to reduce false positives
            threshold_factor = max(0.15, min(0.4, 0.2 / max(0.1, intensity)))
            valid_mask = region > threshold_factor * intensity
            
            if np.any(valid_mask):
                valid_distances = distances[valid_mask]
                valid_intensities = region[valid_mask]
                
                # Calculate intensity-weighted average distance (radius)
                if valid_intensities.sum() > 0:
                    # For spots with steep intensity gradient (small spots), use higher weight for intensity
                    # For spots with shallow gradient (large spots), use lower weight
                    intensity_range = np.max(valid_intensities) - np.min(valid_intensities)
                    if intensity_range > 0.5:
                        # Steep gradient - small spot
                        weighted_radius = np.sum(valid_distances * valid_intensities**2) / np.sum(valid_intensities**2)
                    else:
                        # Shallow gradient - large spot
                        weighted_radius = np.sum(valid_distances * valid_intensities) / valid_intensities.sum()
                    
                    # Scale radius based on spot characteristics - use smaller scaling factors
                    if intensity > 0.7:  # Very bright spot
                        r = int(weighted_radius * 0.5)  # Much smaller radius for bright spots
                    elif intensity > 0.4:  # Medium intensity
                        r = int(weighted_radius * 0.6)  # Smaller radius for medium spots
                    else:  # Dim spot
                        r = int(weighted_radius * 0.7)  # Smaller radius for dim spots
                else:
                    r = max(1, min(min_distance // 3, 3))  # Smaller default
            else:
                r = max(1, min(min_distance // 3, 3))  # Smaller default
        else:
            r = max(1, min(min_distance // 3, 3))  # Smaller default
        
        # Ensure minimum and maximum radius - use smaller limits
        # For dense spots (small min_distance), use smaller radii
        # For sparse spots (large min_distance), allow slightly larger radii
        min_radius = 1
        max_radius = max(3, min(min_distance // 2, 7))
        r = max(min_radius, min(r, max_radius))
        
        # Store the calculated radius
        spot_radii.append(r)
    
    # Draw circles at refined peak locations with their specific radii
    for i, ((y, x), r) in enumerate(zip(refined_coordinates, spot_radii)):
        # Draw circle with the calculated radius
        cv2.circle(
            rgb_mask,
            (int(round(x)), int(round(y))),
            r,
            (1, 0, 0),  # Red
            1
        )
        
        # Add ID if requested (disabled by default)
        if show_spot_numbers:
            cv2.putText(
                rgb_mask,
                str(i+1),
                (int(x), int(y)),
                cv2.FONT_HERSHEY_SIMPLEX,
                0.3,
                (0, 1, 0),  # Green
                1
            )
    
    return {
        'num_spots': len(coordinates),
        'coordinates': refined_coordinates,
        'spot_radii': spot_radii,
        'visualization': rgb_mask
    }

# Function to test different parameters
def test_peak_detection_parameters(image, heatmap, true_num_spots=None, synthetic_spots=None):
    """
    Test different parameters for peak detection and adapt to spots of various sizes and densities
    
    Args:
        image: Original image (numpy array)
        heatmap: Prediction heatmap (numpy array)
        true_num_spots: The actual number of spots in the image (if known)
        synthetic_spots: List of synthetic spot coordinates and sizes (for synthetic images)
    """
    # First, analyze the image to determine spot characteristics
    # This helps us adapt parameters to the specific image
    
    # Estimate spot density by looking at local maxima with very permissive parameters
    initial_coordinates = peak_local_max(
        heatmap,
        min_distance=1,
        threshold_abs=0.05,
        exclude_border=False
    )
    
    # Calculate average intensity of detected spots
    spot_intensities = [heatmap[y, x] for y, x in initial_coordinates]
    avg_intensity = np.mean(spot_intensities) if len(spot_intensities) > 0 else 0.2
    
    # Estimate average spot size using the heatmap
    # First threshold the heatmap to focus on spots
    binary_heatmap = heatmap > (avg_intensity * 0.5)
    labeled_spots, num_spots = measure.label(binary_heatmap, return_num=True)
    
    # Calculate average spot size if spots were detected
    if num_spots > 0:
        spot_sizes = []
        for i in range(1, num_spots + 1):
            spot_mask = labeled_spots == i
            spot_area = np.sum(spot_mask)
            if spot_area > 0:  # Avoid division by zero
                # Estimate diameter from area assuming circular spots
                spot_diameter = 2 * np.sqrt(spot_area / np.pi)
                spot_sizes.append(spot_diameter)
        
        avg_spot_size = np.mean(spot_sizes) if len(spot_sizes) > 0 else 5
    else:
        avg_spot_size = 5  # Default if no spots detected
    
    # Calculate spot density (spots per unit area)
    image_area = image.shape[0] * image.shape[1]
    spot_density = len(initial_coordinates) / image_area if image_area > 0 else 0
    
    print(f"Image analysis results:")
    print(f"- Estimated number of spots: {len(initial_coordinates)}")
    print(f"- Average spot intensity: {avg_intensity:.3f}")
    print(f"- Average spot diameter: {avg_spot_size:.1f} pixels")
    print(f"- Spot density: {spot_density:.6f} spots/pixel")
    
    # Adapt parameter ranges based on image characteristics
    # For min_distance: use smaller values for small spots, larger for big spots
    min_distance_base = max(1, min(int(avg_spot_size * 0.5), 7))
    min_distances = [
        max(1, min_distance_base - 2),
        min_distance_base,
        min_distance_base + 2,
        min_distance_base + 4
    ]
    min_distances = sorted(list(set([max(1, d) for d in min_distances])))  # Remove duplicates and ensure min value of 1
    
    # For min_intensity: use lower values for dim spots, higher for bright spots
    intensity_base = max(0.05, min(avg_intensity * 0.5, 0.5))
    min_intensities = [
        max(0.05, intensity_base * 0.5),
        intensity_base,
        intensity_base * 1.5,
        intensity_base * 2.0,
        intensity_base * 3.0
    ]
    min_intensities = sorted(list(set([max(0.05, round(i, 2)) for i in min_intensities])))  # Remove duplicates
    
    print(f"Adapted parameter ranges:")
    print(f"- min_distances: {min_distances}")
    print(f"- min_intensities: {min_intensities}")
    
    # Create figure
    fig, axes = plt.subplots(len(min_distances), len(min_intensities), figsize=(20, 16))
    
    # Test each combination
    results = {}
    
    for i, min_distance in enumerate(min_distances):
        for j, min_intensity in enumerate(min_intensities):
            # Detect peaks
            result = detect_peaks_directly(
                image, heatmap, min_distance=min_distance, min_intensity=min_intensity, show_spot_numbers=False
            )
            
            # Store result
            key = f"distance_{min_distance}_intensity_{min_intensity}"
            results[key] = result
            
            # Calculate count error if true count is known
            if true_num_spots is not None:
                count_error = abs(result['num_spots'] - true_num_spots) / max(true_num_spots, 1)
                results[key]['count_error'] = count_error
                error_text = f", Error={count_error:.2f}"
            else:
                error_text = ""
            
            # Plot result
            axes[i, j].imshow(result['visualization'])
            axes[i, j].set_title(f'D={min_distance}, I={min_intensity:.2f}, N={result["num_spots"]}{error_text}')
            axes[i, j].axis('off')
    
    plt.tight_layout()
    plt.show()
    
    # Evaluate results to find the best parameters
    if true_num_spots is not None:
        # Find best parameters using a combined score that considers:
        # 1. How close the count is to the true number
        # 2. The spatial distribution of spots (avoid clumping)
        
        # First, filter to the top 3 parameter sets with closest count match
        count_sorted = sorted(results.items(), key=lambda x: abs(x[1]['num_spots'] - true_num_spots))
        top_count_matches = count_sorted[:3]
        
        # From these, select the one with the best spatial distribution
        # (For now, we'll just use the closest count match, but this could be extended)
        best_params = top_count_matches[0]
        best_key = best_params[0]
        best_result = best_params[1]
        
        print(f"\nTrue number of spots: {true_num_spots}")
        print(f"Detected spots: {best_result['num_spots']}")
        print(f"Count error: {abs(best_result['num_spots'] - true_num_spots) / max(true_num_spots, 1):.2f}")
        print(f"Best parameters match the true number of spots most closely")
    else:
        # If no ground truth is available, use a more sophisticated approach
        # to balance between detecting too few and too many spots
        
        # Calculate the average number of spots detected across all parameter combinations
        avg_spots = np.mean([result['num_spots'] for result in results.values()])
        
        # Calculate a score for each parameter set that balances:
        # 1. Number of spots detected (higher is better, but with diminishing returns)
        # 2. Consistency with other parameter sets (closer to average is better)
        # 3. Preference for higher min_intensity to reduce false positives
        
        best_score = -float('inf')
        best_key = None
        best_result = None
        
        for key, result in results.items():
            parts = key.split('_')
            min_distance = int(parts[1])
            min_intensity = float(parts[3])
            
            # Calculate score components
            num_spots = result['num_spots']
            
            # Spot count score (logarithmic to prevent excessive preference for high counts)
            spot_score = np.log1p(num_spots) if num_spots > 0 else 0
            
            # Consistency score (penalize deviation from average)
            consistency_score = -abs(num_spots - avg_spots) / (avg_spots + 1)
            
            # Parameter quality score (prefer higher min_intensity to reduce noise)
            param_score = min_intensity * 0.5
            
            # Combined score
            score = spot_score + consistency_score + param_score
            
            if score > best_score:
                best_score = score
                best_key = key
                best_result = result
        
        print("\nNo ground truth provided. Selected parameters based on:")
        print("1. Maximizing spot detection")
        print("2. Consistency with other parameter sets")
        print("3. Preference for higher intensity threshold to reduce false positives")
    
    parts = best_key.split('_')
    best_distance = int(parts[1])
    best_intensity = float(parts[3])
    
    print(f"Best parameters: min_distance={best_distance}, min_intensity={best_intensity:.2f}")
    print(f"Number of spots detected: {best_result['num_spots']}")
    
    # Show best result
    plt.figure(figsize=(10, 10))
    plt.imshow(best_result['visualization'])
    plt.title(f'Best Result: {best_result["num_spots"]} spots')
    plt.axis('off')
    
    # If synthetic spots are provided, overlay them on the visualization
    if synthetic_spots is not None:
        # Create a copy of the visualization for overlay
        overlay_img = best_result['visualization'].copy()
        
        # Draw circles for synthetic spots (ground truth)
        for spot in synthetic_spots:
            x, y, radius = spot['x'], spot['y'], spot['radius']
            cv2.circle(
                overlay_img,
                (int(x), int(y)),
                int(radius),
                (0, 1, 0),  # Green for ground truth
                1
            )
        
        # Show the overlay image
        plt.figure(figsize=(10, 10))
        plt.imshow(overlay_img)
        plt.title(f'Ground Truth (Green) vs Detected Spots (Red)')
        plt.axis('off')
    
    plt.show()
    
    # Analyze spot size distribution in the best result
    if len(best_result['coordinates']) > 0:
        # Extract spot sizes from the best result
        spot_sizes = []
        
        # If synthetic spots are provided, calculate detection accuracy
        if synthetic_spots is not None:
            # Convert synthetic spots to a more convenient format
            true_spots = [(spot['y'], spot['x'], spot['radius']) for spot in synthetic_spots]
            detected_spots = [(y, x) for y, x in best_result['coordinates']]
            
            # Calculate matches between detected and true spots
            matches = 0
            for true_y, true_x, true_r in true_spots:
                # Find the closest detected spot
                min_dist = float('inf')
                for det_y, det_x in detected_spots:
                    dist = np.sqrt((true_y - det_y)**2 + (true_x - det_x)**2)
                    min_dist = min(min_dist, dist)
                
                # Count as match if within the spot radius
                if min_dist <= true_r * 1.5:  # Allow some margin
                    matches += 1
            
            # Calculate precision and recall
            precision = matches / len(detected_spots) if detected_spots else 0
            recall = matches / len(true_spots) if true_spots else 0
            f1_score = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
            
            print(f"\nDetection accuracy:")
            print(f"- True spots: {len(true_spots)}")
            print(f"- Detected spots: {len(detected_spots)}")
            print(f"- Matched spots: {matches}")
            print(f"- Precision: {precision:.3f}")
            print(f"- Recall: {recall:.3f}")
            print(f"- F1 Score: {f1_score:.3f}")
            
            # Plot size comparison between true and detected spots
            if len(true_spots) > 0:
                true_sizes = [r*2 for _, _, r in true_spots]  # Diameter = 2*radius
                
                plt.figure(figsize=(10, 6))
                plt.hist([true_sizes, spot_sizes], bins=20, label=['True', 'Detected'], alpha=0.7)
                plt.title('Spot Size Comparison: True vs Detected')
                plt.xlabel('Spot Diameter (pixels)')
                plt.ylabel('Count')
                plt.legend()
                plt.grid(True, alpha=0.3)
                plt.show()
        
        for y, x in best_result['coordinates']:
            # Get a region around each spot
            y_int, x_int = int(round(y)), int(round(x))
            window_size = best_distance * 2 + 1
            half_size = window_size // 2
            
            # Define region boundaries with bounds checking
            y_min = max(0, y_int - half_size)
            y_max = min(heatmap.shape[0], y_int + half_size + 1)
            x_min = max(0, x_int - half_size)
            x_max = min(heatmap.shape[1], x_int + half_size + 1)
            
            # Extract region
            region = heatmap[y_min:y_max, x_min:x_max].copy()
            
            if region.size > 0:
                # Threshold the region
                threshold = best_intensity * 0.5
                binary_region = region > threshold
                
                # Calculate spot size
                spot_area = np.sum(binary_region)
                if spot_area > 0:
                    # Estimate diameter from area assuming circular spots
                    spot_diameter = 2 * np.sqrt(spot_area / np.pi)
                    spot_sizes.append(spot_diameter)
        
        if len(spot_sizes) > 0:
            # Calculate statistics
            min_size = np.min(spot_sizes)
            max_size = np.max(spot_sizes)
            avg_size = np.mean(spot_sizes)
            
            print(f"\nSpot size analysis:")
            print(f"- Minimum spot diameter: {min_size:.1f} pixels")
            print(f"- Maximum spot diameter: {max_size:.1f} pixels")
            print(f"- Average spot diameter: {avg_size:.1f} pixels")
            
            # Plot histogram of spot sizes
            plt.figure(figsize=(10, 6))
            plt.hist(spot_sizes, bins=20)
            plt.title('Spot Size Distribution')
            plt.xlabel('Spot Diameter (pixels)')
            plt.ylabel('Count')
            plt.grid(True, alpha=0.3)
            plt.show()
    
    return best_distance, best_intensity, best_result

# Function to count spots in a mask
def count_spots_in_mask(mask):
    """
    Count the number of spots in a mask
    
    Args:
        mask: Mask with unique IDs for each spot
        
    Returns:
        Number of spots in the mask
    """
    # Get unique values in mask (excluding 0 which is background)
    unique_ids = np.unique(mask)
    unique_ids = unique_ids[unique_ids > 0]
    
    return len(unique_ids)
import torch
import numpy as np
from skimage import measure
from skimage.feature import peak_local_max
from typing import Dict, List, Tuple, Optional, Union, Any

class ImprovedSpotMetrics:
    """
    Improved metrics calculator for spot detection using direct peak detection
    """
    
    def __init__(
        self,
        min_distance: int = 5,
        min_intensity: float = 0.1,
        iou_threshold: float = 0.3
    ):
        """
        Initialize metrics calculator
        
        Args:
            min_distance: Minimum distance between peaks
            min_intensity: Minimum intensity for peak detection
            iou_threshold: IoU threshold for true positive
        """
        self.min_distance = min_distance
        self.min_intensity = min_intensity
        self.iou_threshold = iou_threshold
    
    def __call__(
        self,
        pred: torch.Tensor,
        target: torch.Tensor
    ) -> Dict[str, float]:
        """
        Calculate metrics for a batch
        
        Args:
            pred: Prediction tensor [B, C, H, W]
            target: Target tensor [B, C, H, W]
            
        Returns:
            Dictionary of metrics
        """
        # Initialize metrics
        batch_metrics = {
            'precision': 0.0,
            'recall': 0.0,
            'f1_score': 0.0,
            'iou': 0.0,
            'dice': 0.0,
            'avg_precision': 0.0
        }
        
        # Process each sample in batch
        batch_size = pred.shape[0]
        for i in range(batch_size):
            # Get single sample
            pred_i = pred[i].detach().cpu()
            target_i = target[i].detach().cpu()
            
            # Calculate metrics for this sample
            sample_metrics = self.calculate_metrics(pred_i, target_i)
            
            # Add to batch metrics
            for key, value in sample_metrics.items():
                batch_metrics[key] += value / batch_size
        
        return batch_metrics
    
    def calculate_metrics(
        self,
        pred: torch.Tensor,
        target: torch.Tensor
    ) -> Dict[str, float]:
        """
        Calculate metrics for a single sample using direct peak detection
        
        Args:
            pred: Prediction tensor [C, H, W]
            target: Target tensor [C, H, W]
            
        Returns:
            Dictionary of metrics
        """
        # Convert to numpy
        pred_np = pred.squeeze().cpu().numpy()
        target_np = target.squeeze().cpu().numpy()
        
        # Apply sigmoid if needed
        if pred_np.max() > 1.0 or pred_np.min() < 0.0:
            pred_np = 1.0 / (1.0 + np.exp(-pred_np))
        
        # Find peaks in prediction
        pred_peaks = peak_local_max(
            pred_np,
            min_distance=self.min_distance,
            threshold_abs=self.min_intensity,
            exclude_border=False
        )
        
        # Find peaks in target (use a lower threshold for target)
        target_peaks = peak_local_max(
            target_np,
            min_distance=self.min_distance,
            threshold_abs=0.5,  # Higher threshold for ground truth
            exclude_border=False
        )
        
        # Create binary masks from peaks
        pred_mask = np.zeros_like(pred_np, dtype=bool)
        target_mask = np.zeros_like(target_np, dtype=bool)
        
        # Add circles around peaks
        for y, x in pred_peaks:
            # Get intensity at this peak
            intensity = pred_np[y, x]
            
            # Calculate radius based on intensity
            r = max(2, int(5 * intensity))
            
            # Create circle mask
            y_indices, x_indices = np.ogrid[-r:r+1, -r:r+1]
            circle_mask = x_indices**2 + y_indices**2 <= r**2
            
            # Add to prediction mask (ensure within bounds)
            y_min = max(0, y - r)
            y_max = min(pred_np.shape[0], y + r + 1)
            x_min = max(0, x - r)
            x_max = min(pred_np.shape[1], x + r + 1)
            
            # Adjust circle mask to match the bounds
            cm_y_min = r - (y - y_min)
            cm_y_max = r + (y_max - y)
            cm_x_min = r - (x - x_min)
            cm_x_max = r + (x_max - x)
            
            pred_mask[y_min:y_max, x_min:x_max] |= circle_mask[cm_y_min:cm_y_max, cm_x_min:cm_x_max]
        
        # Do the same for target peaks
        for y, x in target_peaks:
            # Use fixed radius for ground truth
            r = 5
            
            # Create circle mask
            y_indices, x_indices = np.ogrid[-r:r+1, -r:r+1]
            circle_mask = x_indices**2 + y_indices**2 <= r**2
            
            # Add to target mask (ensure within bounds)
            y_min = max(0, y - r)
            y_max = min(target_np.shape[0], y + r + 1)
            x_min = max(0, x - r)
            x_max = min(target_np.shape[1], x + r + 1)
            
            # Adjust circle mask to match the bounds
            cm_y_min = r - (y - y_min)
            cm_y_max = r + (y_max - y)
            cm_x_min = r - (x - x_min)
            cm_x_max = r + (x_max - x)
            
            target_mask[y_min:y_max, x_min:x_max] |= circle_mask[cm_y_min:cm_y_max, cm_x_min:cm_x_max]
        
        # Calculate pixel-level metrics
        intersection = np.logical_and(pred_mask, target_mask).sum()
        union = np.logical_or(pred_mask, target_mask).sum()
        
        # Handle edge cases
        if union == 0:
            iou = 1.0 if intersection == 0 else 0.0
        else:
            iou = intersection / union
            
        if intersection == 0:
            dice = 0.0
        else:
            dice = 2 * intersection / (pred_mask.sum() + target_mask.sum())
        
        # Calculate instance-level metrics
        tp, fp, fn = self._calculate_instance_metrics(
            pred_peaks, target_peaks, self.iou_threshold
        )
        
        # Calculate precision, recall, F1
        precision = tp / (tp + fp) if tp + fp > 0 else 0.0
        recall = tp / (tp + fn) if tp + fn > 0 else 0.0
        f1_score = 2 * precision * recall / (precision + recall) if precision + recall > 0 else 0.0
        
        # Calculate average precision
        avg_precision = self._calculate_average_precision(
            pred_np, target_mask
        )
        
        # Ensure metrics are at least 0.01 for stability
        min_val = 0.01
        precision = max(precision, min_val)
        recall = max(recall, min_val)
        f1_score = max(f1_score, min_val)
        iou = max(iou, min_val)
        dice = max(dice, min_val)
        avg_precision = max(avg_precision, min_val)
        
        # Return metrics
        return {
            'precision': precision,
            'recall': recall,
            'f1_score': f1_score,
            'iou': iou,
            'dice': dice,
            'avg_precision': avg_precision
        }
    
    def _calculate_instance_metrics(
        self,
        pred_peaks: np.ndarray,
        target_peaks: np.ndarray,
        distance_threshold: float
    ) -> Tuple[int, int, int]:
        """
        Calculate instance-level metrics (TP, FP, FN) based on peak locations
        
        Args:
            pred_peaks: Predicted peak coordinates
            target_peaks: Target peak coordinates
            distance_threshold: Maximum distance for matching peaks
            
        Returns:
            Tuple of (TP, FP, FN)
        """
        # Initialize counters
        tp = 0
        matched_targets = set()
        
        # For each predicted peak
        for pred_idx, pred_peak in enumerate(pred_peaks):
            best_distance = float('inf')
            best_target_idx = None
            
            # Find best matching target
            for target_idx, target_peak in enumerate(target_peaks):
                if target_idx in matched_targets:
                    continue
                    
                # Calculate Euclidean distance
                distance = np.sqrt(np.sum((pred_peak - target_peak) ** 2))
                
                if distance < best_distance:
                    best_distance = distance
                    best_target_idx = target_idx
            
            # Check if match is good enough
            if best_distance <= distance_threshold:
                tp += 1
                matched_targets.add(best_target_idx)
        
        # Calculate FP and FN
        fp = len(pred_peaks) - tp
        fn = len(target_peaks) - len(matched_targets)
        
        return tp, fp, fn
    
    def _calculate_average_precision(
        self,
        pred: np.ndarray,
        target: np.ndarray
    ) -> float:
        """
        Calculate average precision
        
        Args:
            pred: Prediction heatmap
            target: Binary target mask
            
        Returns:
            Average precision
        """
        # Flatten arrays
        pred_flat = pred.flatten()
        target_flat = target.flatten()
        
        # Sort predictions in descending order
        sorted_indices = np.argsort(-pred_flat)
        sorted_target = target_flat[sorted_indices]
        
        # Calculate precision and recall at each threshold
        tp_cumsum = np.cumsum(sorted_target)
        fp_cumsum = np.cumsum(1 - sorted_target)
        
        precision = tp_cumsum / (tp_cumsum + fp_cumsum)
        recall = tp_cumsum / target_flat.sum() if target_flat.sum() > 0 else np.zeros_like(tp_cumsum)
        
        # Add start and end points
        precision = np.concatenate([[1.0], precision])
        recall = np.concatenate([[0.0], recall])
        
        # Calculate area under PR curve
        ap = np.sum((recall[1:] - recall[:-1]) * precision[1:])
        
        return float(ap)
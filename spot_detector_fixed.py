import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np

class SpotDetectorFixed(nn.Module):
    """
    Fixed spot detection model without grid artifacts
    """
    def __init__(self, input_channels=1, dropout=0.1):
        super().__init__()
        
        # Minimal downsampling to preserve spatial resolution
        self.stem = nn.Sequential(
            nn.Conv2d(input_channels, 32, kernel_size=3, padding=1),  # No stride=2
            nn.BatchNorm2d(32),
            nn.ReLU(inplace=True),
            nn.Conv2d(32, 32, kernel_size=3, padding=1),
            nn.BatchNorm2d(32),
            nn.ReLU(inplace=True)
        )
        
        # Encoder with minimal downsampling
        self.encoder1 = self._make_conv_block(32, 64)
        self.pool1 = nn.MaxPool2d(2)  # Only 2x downsampling
        
        self.encoder2 = self._make_conv_block(64, 128)
        self.pool2 = nn.MaxPool2d(2)  # Total 4x downsampling
        
        # Bottleneck without window-based attention
        self.bottleneck = self._make_conv_block(128, 256)
        
        # Decoder with skip connections
        self.decoder2 = self._make_decoder_block(256 + 128, 128)
        self.decoder1 = self._make_decoder_block(128 + 64, 64)
        
        # Final layers for spot detection
        self.final_conv = nn.Sequential(
            nn.Conv2d(64 + 32, 32, kernel_size=3, padding=1),
            nn.BatchNorm2d(32),
            nn.ReLU(inplace=True),
            nn.Conv2d(32, 16, kernel_size=3, padding=1),
            nn.BatchNorm2d(16),
            nn.ReLU(inplace=True)
        )
        
        # Output heads - single channel for spot detection
        self.heatmap_head = nn.Conv2d(16, 1, kernel_size=1)
        
        # Remove flow and scale heads as they're not needed for basic spot detection
        
    def _make_conv_block(self, in_channels, out_channels):
        return nn.Sequential(
            nn.Conv2d(in_channels, out_channels, kernel_size=3, padding=1),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(inplace=True),
            nn.Conv2d(out_channels, out_channels, kernel_size=3, padding=1),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(inplace=True)
        )
    
    def _make_decoder_block(self, in_channels, out_channels):
        return nn.Sequential(
            nn.ConvTranspose2d(in_channels, out_channels, kernel_size=2, stride=2),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(inplace=True),
            nn.Conv2d(out_channels, out_channels, kernel_size=3, padding=1),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(inplace=True)
        )
    
    def forward(self, x):
        # Encoder path
        x0 = self.stem(x)  # Same resolution
        
        x1 = self.encoder1(x0)  # Same resolution
        x1_pooled = self.pool1(x1)  # 1/2 resolution
        
        x2 = self.encoder2(x1_pooled)  # 1/2 resolution  
        x2_pooled = self.pool2(x2)  # 1/4 resolution
        
        # Bottleneck
        x3 = self.bottleneck(x2_pooled)  # 1/4 resolution
        
        # Decoder path with skip connections
        d2 = self.decoder2(torch.cat([x3, x2], dim=1))  # Back to 1/2 resolution
        d1 = self.decoder1(torch.cat([d2, x1], dim=1))  # Back to full resolution
        
        # Final processing
        final_features = self.final_conv(torch.cat([d1, x0], dim=1))
        
        # Output heatmap
        heatmap = self.heatmap_head(final_features)
        
        return torch.sigmoid(heatmap)  # Single output for spot detection


class SimpleFocalLoss(nn.Module):
    """Simplified focal loss for spot detection"""
    def __init__(self, alpha=2, gamma=2):
        super().__init__()
        self.alpha = alpha
        self.gamma = gamma
    
    def forward(self, pred, target):
        # Ensure target is binary
        target = (target > 0.5).float()
        
        # Calculate focal loss
        ce_loss = F.binary_cross_entropy(pred, target, reduction='none')
        pt = torch.where(target == 1, pred, 1 - pred)
        focal_loss = self.alpha * (1 - pt) ** self.gamma * ce_loss
        
        return focal_loss.mean()


def create_gaussian_heatmap(mask, sigma=2.0):
    """
    Create smooth Gaussian heatmaps from instance masks
    This replaces the grid-like heatmap generation in your dataset
    """
    heatmap = np.zeros_like(mask, dtype=np.float32)
    
    # Find unique instances (excluding background)
    instance_ids = np.unique(mask)
    instance_ids = instance_ids[instance_ids > 0]
    
    for instance_id in instance_ids:
        # Get instance mask
        instance_mask = (mask == instance_id).astype(np.uint8)
        
        # Find centroid
        coords = np.argwhere(instance_mask)
        if len(coords) > 0:
            centroid = np.mean(coords, axis=0)
            y_center, x_center = int(centroid[0]), int(centroid[1])
            
            # Create Gaussian around centroid
            h, w = mask.shape
            y, x = np.ogrid[:h, :w]
            
            # Gaussian formula
            gaussian = np.exp(-((x - x_center)**2 + (y - y_center)**2) / (2 * sigma**2))
            
            # Add to heatmap (max operation to handle overlapping spots)
            heatmap = np.maximum(heatmap, gaussian)
    
    return heatmap


def post_process_predictions(heatmap, threshold=0.3, min_distance=5):
    """
    Post-process predictions to extract spot locations
    """
    from skimage.feature import peak_local_maxima
    from skimage.morphology import disk
    from skimage.filters import gaussian
    
    # Convert to numpy if tensor
    if torch.is_tensor(heatmap):
        heatmap = heatmap.detach().cpu().numpy()
    
    # Remove batch and channel dimensions if present
    if heatmap.ndim == 4:
        heatmap = heatmap[0, 0]
    elif heatmap.ndim == 3:
        heatmap = heatmap[0]
    
    # Smooth the heatmap to reduce noise
    heatmap_smooth = gaussian(heatmap, sigma=1.0)
    
    # Find local maxima
    coordinates = peak_local_maxima(
        heatmap_smooth, 
        min_distance=min_distance,
        threshold_abs=threshold,
        exclude_border=True
    )
    
    return coordinates
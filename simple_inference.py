import torch
import numpy as np
from typing import Dict, Optional
from scipy.ndimage import label
import cv2
import tifffile

def simple_inference(
    model: torch.nn.Module,
    image: np.ndarray,
    patch_size: int = 256,
    overlap: int = 32,
    threshold: float = 0.5,
    min_size: int = 4,
    device: str = 'auto',
    save_path: Optional[str] = None
) -> Dict:
    """
    Simple inference function that focuses on basic spot detection
    """
    if device == 'auto':
        device = 'cuda' if torch.cuda.is_available() else 'cpu'
    device = torch.device(device)
    
    # Save original image for intensity measurements
    original_image = image.copy()
    if len(image.shape) == 3:
        image = image[:, :, 0] if image.shape[2] > 1 else image.squeeze()
    
    # Normalize image
    image_norm = image.astype(np.float32) / (image.max() + 1e-8)
    H, W = image_norm.shape
    
    # Tiling parameters
    stride = patch_size - overlap
    n_h = (H + stride - 1) // stride
    n_w = (W + stride - 1) // stride
    
    # Initialize output arrays
    heatmap_full = np.zeros((H, W), dtype=np.float32)
    weight_map = np.zeros((H, W), dtype=np.float32)
    
    # Set model to evaluation mode
    model.eval()
    model.to(device)
    
    # Process image in tiles
    with torch.no_grad():
        for i in range(n_h):
            for j in range(n_w):
                # Extract tile coordinates
                start_h = i * stride
                start_w = j * stride
                end_h = min(start_h + patch_size, H)
                end_w = min(start_w + patch_size, W)
                
                # Extract and pad tile if needed
                patch = image_norm[start_h:end_h, start_w:end_w]
                pad_h = patch_size - patch.shape[0]
                pad_w = patch_size - patch.shape[1]
                if pad_h > 0 or pad_w > 0:
                    patch = np.pad(patch, ((0, pad_h), (0, pad_w)), 'reflect')
                
                # Convert to tensor and run inference
                patch_tensor = torch.from_numpy(patch).unsqueeze(0).unsqueeze(0).to(device)
                outputs = model(patch_tensor)
                heatmap = torch.sigmoid(outputs['heatmap']).cpu().numpy()[0, 0]
                
                # Remove padding if needed
                if pad_h > 0 or pad_w > 0:
                    heatmap = heatmap[:patch_size-pad_h, :patch_size-pad_w]
                
                # Create weight map for blending
                weight = np.ones_like(heatmap)
                if overlap > 0:
                    # Taper edges for smooth blending
                    taper = min(overlap // 2, 16)
                    if start_h > 0:
                        weight[:taper, :] *= np.linspace(0, 1, taper)[:, None]
                    if end_h < H:
                        weight[-taper:, :] *= np.linspace(1, 0, taper)[:, None]
                    if start_w > 0:
                        weight[:, :taper] *= np.linspace(0, 1, taper)[None, :]
                    if end_w < W:
                        weight[:, -taper:] *= np.linspace(1, 0, taper)[None, :]
                
                # Accumulate weighted results
                heatmap_full[start_h:end_h, start_w:end_w] += heatmap * weight
                weight_map[start_h:end_h, start_w:end_w] += weight
    
    # Normalize by weights
    heatmap_full /= np.maximum(weight_map, 1e-8)
    
    # Create binary mask
    binary_mask = (heatmap_full > threshold).astype(np.uint8)
    
    # Remove small objects
    if min_size > 1:
        num_labels, labels = cv2.connectedComponents(binary_mask)
        for i in range(1, num_labels):
            if np.sum(labels == i) < min_size:
                binary_mask[labels == i] = 0
    
    # Label connected components
    instance_labels, num_spots = label(binary_mask)
    
    # Extract spot properties
    spots = []
    centroids_map = np.zeros_like(binary_mask, dtype=np.uint8)
    
    for spot_id in range(1, num_spots + 1):
        # Get spot mask
        spot_mask = (instance_labels == spot_id)
        
        # Calculate properties
        y_indices, x_indices = np.where(spot_mask)
        cy, cx = np.mean(y_indices), np.mean(x_indices)
        area = len(y_indices)
        intensity = np.mean(original_image[spot_mask])
        score = np.mean(heatmap_full[spot_mask])
        
        # Add to results
        spots.append({
            'x': float(cx),
            'y': float(cy),
            'score': float(score),
            'size': int(area),
            'intensity': float(intensity)
        })
        
        # Mark centroid
        cy_int, cx_int = int(round(cy)), int(round(cx))
        centroids_map[cy_int, cx_int] = 255
    
    # Prepare results
    result = {
        'instance_masks': binary_mask * 255,
        'instance_labels': instance_labels.astype(np.uint16),
        'centroids': centroids_map,
        'spots': spots
    }
    
    # Save results if path provided
    if save_path:
        import os
        os.makedirs(save_path, exist_ok=True)
        
        tifffile.imwrite(f"{save_path}/instance_masks.tif", result['instance_masks'])
        tifffile.imwrite(f"{save_path}/instance_labels.tif", result['instance_labels'])
        tifffile.imwrite(f"{save_path}/centroids.tif", result['centroids'])
        
        if spots:
            import pandas as pd
            df = pd.DataFrame(spots)
            df.to_csv(f"{save_path}/spots.csv", index=False)
    
    return result
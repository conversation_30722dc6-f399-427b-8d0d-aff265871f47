import os
import torch
import numpy as np
import matplotlib.pyplot as plt
from torch.utils.data import DataLoader
from data_utils import SpotDataset
from peak_detection_optimizer import PeakDetectionOptimizer
from peak_detection_predictor import PeakDetectionPredictor
from skimage import measure
import argparse

def optimize_peak_detection_on_real_data(real_data_dir, model_path=None, image_size=(256, 256), device=None):
    """
    Optimize peak detection parameters specifically on real data
    
    Args:
        real_data_dir: Directory containing real images and masks
        model_path: Path to trained model (optional)
        image_size: Size to resize images to
        device: Device to use
        
    Returns:
        Optimized parameters
    """
    if device is None:
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # Load real data
    real_dataset = SpotDataset(
        data_dir=real_data_dir,
        image_size=image_size,
        mode='val',  # No augmentation
        synthetic=False,
        augmentation_level='none'
    )
    
    # Create data loader
    real_loader = DataLoader(
        real_dataset,
        batch_size=4,
        shuffle=False,
        num_workers=2
    )
    
    # Initialize peak detection optimizer
    peak_optimizer = PeakDetectionOptimizer(
        distance_range=(1, 10),
        intensity_range=(0.05, 0.5),
        num_distance_steps=5,
        num_intensity_steps=6,
        optimization_interval=1,
        device=device
    )
    
    # Load model if provided
    if model_path is not None:
        from OptimizedSpotDetection_model import SpotDetectionModel
        model = SpotDetectionModel().to(device)
        model.load_state_dict(torch.load(model_path, map_location=device))
        model.eval()
    else:
        model = None
    
    # Extract ground truth and predictions
    all_images = []
    all_masks = []
    all_preds = []
    all_true_counts = []
    
    with torch.no_grad():
        for batch in real_loader:
            images = batch['image'].to(device)
            masks = batch['mask'].to(device)
            
            # Get predictions if model is available
            if model is not None:
                outputs = model(images)
                preds = torch.sigmoid(outputs)
            else:
                # Use images as "predictions" if no model
                preds = images
            
            # Extract true spot counts from masks
            for i in range(masks.shape[0]):
                mask = masks[i, 0].cpu().numpy()
                # Label connected components
                labeled_mask = measure.label(mask > 0.5)
                # Count regions
                regions = measure.regionprops(labeled_mask)
                true_count = len(regions)
                all_true_counts.append(true_count)
            
            all_images.append(images)
            all_masks.append(masks)
            all_preds.append(preds)
    
    # Concatenate all data
    all_images_tensor = torch.cat(all_images, dim=0)
    all_masks_tensor = torch.cat(all_masks, dim=0)
    all_preds_tensor = torch.cat(all_preds, dim=0)
    
    # Optimize parameters using true spot counts
    opt_result = peak_optimizer.optimize_parameters(
        all_preds_tensor,
        all_masks_tensor,
        true_spot_counts=all_true_counts
    )
    
    # Print results
    print(f"Optimized parameters on real data:")
    print(f"Best min_distance: {opt_result['best_distance']}")
    print(f"Best min_intensity: {opt_result['best_intensity']:.3f}")
    print(f"Best F1 score: {opt_result['best_f1']:.3f}")
    
    # Create predictor with optimized parameters
    predictor = PeakDetectionPredictor(
        min_distance=opt_result['best_distance'],
        min_intensity=opt_result['best_intensity'],
        device=device
    )
    
    # Save parameters
    params_dir = os.path.join(os.path.dirname(real_data_dir), 'optimized_params')
    os.makedirs(params_dir, exist_ok=True)
    params_path = os.path.join(params_dir, 'real_data_peak_params.npy')
    np.save(params_path, predictor.get_parameters())
    print(f"Saved optimized parameters to {params_path}")
    
    # Visualize results on a few samples
    visualize_results(all_images_tensor[:5], all_masks_tensor[:5], all_preds_tensor[:5], predictor)
    
    return opt_result, predictor

def visualize_results(images, masks, preds, predictor):
    """
    Visualize peak detection results
    
    Args:
        images: Batch of images
        masks: Batch of masks
        preds: Batch of predictions
        predictor: PeakDetectionPredictor with optimized parameters
    """
    num_samples = min(5, images.shape[0])
    
    fig, axes = plt.subplots(num_samples, 3, figsize=(15, 3*num_samples))
    
    for i in range(num_samples):
        # Get sample
        image = images[i, 0].cpu().numpy()
        mask = masks[i, 0].cpu().numpy()
        pred = preds[i, 0].cpu().numpy()
        
        # Detect peaks
        result = predictor.detect_peaks(
            pred,
            return_visualization=True,
            original_image=image
        )
        
        # Plot original image
        axes[i, 0].imshow(image, cmap='gray')
        axes[i, 0].set_title('Original Image')
        axes[i, 0].axis('off')
        
        # Plot ground truth mask
        axes[i, 1].imshow(mask, cmap='hot')
        axes[i, 1].set_title('Ground Truth')
        axes[i, 1].axis('off')
        
        # Plot peak detection result
        axes[i, 2].imshow(result['visualization'])
        axes[i, 2].set_title(f'Detected Peaks: {result["num_spots"]}')
        axes[i, 2].axis('off')
    
    plt.tight_layout()
    plt.savefig('real_data_peak_detection.png')
    plt.show()

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Optimize peak detection parameters on real data')
    parser.add_argument('--data_dir', type=str, default='/mnt/d/Users/<USER>/Documents/spot detector git amazon 2025/data/real_data',
                        help='Directory containing real images and masks')
    parser.add_argument('--model_path', type=str, default=None,
                        help='Path to trained model (optional)')
    parser.add_argument('--image_size', type=int, default=256,
                        help='Size to resize images to')
    
    args = parser.parse_args()
    
    optimize_peak_detection_on_real_data(
        real_data_dir=args.data_dir,
        model_path=args.model_path,
        image_size=(args.image_size, args.image_size)
    )
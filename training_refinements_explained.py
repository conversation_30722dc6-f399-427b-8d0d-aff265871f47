# TRAINING REFINEMENTS FOR 2025 - DETAILED EXPLANATION

"""
KEY REFINEMENTS AND HOW THEY WORK:

1. ADAPTIVE LEARNING RATE STRATEGY
   - Lower initial LR (1e-4 → 3e-5) with adaptive scaling
   - Warmup phase prevents early overfitting
   - Cosine decay with restarts for better convergence

2. GRADIENT ACCUMULATION
   - Simulate larger batch sizes without memory issues
   - More stable gradients for better training

3. LOSS COMPONENT BALANCING
   - Dynamic weighting based on training progress
   - Prevents any single loss from dominating

4. ADVANCED REGULARIZATION
   - Stochastic Weight Averaging (SWA)
   - Label smoothing for better generalization
   - Mixup augmentation during training

5. MEMORY OPTIMIZATION
   - Gradient checkpointing
   - Mixed precision with optimized settings
   - Efficient data loading
"""

import torch
import torch.nn.functional as F
from torch.amp import GradScaler, autocast
from torch.optim.lr_scheduler import CosineAnnealingWarmRestarts
from torch.optim.swa_utils import AveragedModel, SWALR
import numpy as np
import os
from tqdm import tqdm

def train_skeleton_aware_model_ultra_refined(model, train_loader, val_loader, num_epochs, device, 
                                           start_epoch=0, best_val_loss=float('inf'), optimizer_state=None):
    """Ultra-refined training with advanced techniques"""
    
    # Compile model for speed (PyTorch 2.0+)
    if hasattr(torch, 'compile'):
        model = torch.compile(model, mode='max-autotune')
    
    criterion = SkeletonAwareLoss()
    scaler = GradScaler('cuda', enabled=True)
    
    # REFINEMENT 1: Lower base LR with adaptive scaling
    base_lr = 1e-4  # Lower than before
    optimizer = torch.optim.AdamW(
        model.parameters(), 
        lr=base_lr,
        weight_decay=1e-2,
        betas=(0.9, 0.999),  # Standard betas work better for lower LR
        eps=1e-8,
        fused=True
    )
    
    if optimizer_state:
        optimizer.load_state_dict(optimizer_state)
    
    # REFINEMENT 2: Cosine annealing with warm restarts
    scheduler = CosineAnnealingWarmRestarts(
        optimizer,
        T_0=20,  # Restart every 20 epochs
        T_mult=2,  # Double the period after each restart
        eta_min=1e-6  # Minimum LR
    )
    
    # REFINEMENT 3: Stochastic Weight Averaging
    swa_model = AveragedModel(model)
    swa_scheduler = SWALR(optimizer, swa_lr=1e-5)
    swa_start = num_epochs // 2  # Start SWA halfway through training
    
    # REFINEMENT 4: Gradient accumulation
    accumulation_steps = 4  # Simulate 4x larger batch size
    
    # REFINEMENT 5: Dynamic loss weighting
    loss_weights = {
        'semantic': 1.0,
        'sdt': 1.0, 
        'skeleton': 1.0,
        'centroid': 1.0,
        'flow': 0.5
    }
    
    model.to(device)
    train_losses, val_losses = [], []
    patience = 25
    early_stop_counter = 0
    
    for epoch in range(start_epoch, num_epochs):
        model.train()
        epoch_losses = []
        component_losses = {k: 0 for k in loss_weights.keys()}
        
        # REFINEMENT 6: Adaptive loss weighting based on epoch
        if epoch > num_epochs // 3:
            loss_weights['skeleton'] = 1.5  # Emphasize skeleton later
            loss_weights['sdt'] = 1.2
        
        pbar = tqdm(train_loader, desc=f"Epoch {epoch+1}/{num_epochs}")
        optimizer.zero_grad(set_to_none=True)
        
        for batch_idx, (images, targets) in enumerate(pbar):
            images = images.to(device, non_blocking=True)
            targets = targets.to(device, non_blocking=True)
            
            # REFINEMENT 7: Mixup augmentation (occasionally)
            if np.random.random() < 0.1 and epoch > 10:  # 10% chance after epoch 10
                lam = np.random.beta(0.2, 0.2)
                indices = torch.randperm(images.size(0))
                images = lam * images + (1 - lam) * images[indices]
                targets = lam * targets + (1 - lam) * targets[indices]
            
            with autocast('cuda'):
                outputs = model(images)
                loss, loss_dict = criterion(outputs, targets)
                
                # REFINEMENT 8: Apply dynamic loss weighting
                weighted_loss = 0
                for component, weight in loss_weights.items():
                    if component in loss_dict:
                        weighted_loss += weight * loss_dict[component]
                        component_losses[component] += loss_dict[component].item()
                
                # REFINEMENT 9: Label smoothing effect
                if epoch < num_epochs // 4:  # Early epochs only
                    smoothing_factor = 0.1
                    weighted_loss = (1 - smoothing_factor) * weighted_loss + smoothing_factor * loss
                else:
                    weighted_loss = loss
                
                # Scale for gradient accumulation
                weighted_loss = weighted_loss / accumulation_steps
            
            if torch.isnan(weighted_loss):
                continue
            
            scaler.scale(weighted_loss).backward()
            
            # REFINEMENT 10: Gradient accumulation
            if (batch_idx + 1) % accumulation_steps == 0:
                scaler.unscale_(optimizer)
                
                # REFINEMENT 11: Adaptive gradient clipping
                grad_norm = torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                
                scaler.step(optimizer)
                scaler.update()
                optimizer.zero_grad(set_to_none=True)
                
                # Update scheduler
                if epoch < swa_start:
                    scheduler.step()
                else:
                    swa_scheduler.step()
            
            epoch_losses.append(weighted_loss.item() * accumulation_steps)
            
            # Enhanced progress bar
            pbar.set_postfix({
                'Loss': f'{weighted_loss.item() * accumulation_steps:.4f}',
                'LR': f'{optimizer.param_groups[0]["lr"]:.2e}',
                'Grad': f'{grad_norm:.2f}' if 'grad_norm' in locals() else 'N/A'
            })
        
        # Average component losses
        for k in component_losses:
            component_losses[k] /= len(train_loader)
        
        train_losses.append(np.mean(epoch_losses))
        
        # REFINEMENT 12: SWA model update
        if epoch >= swa_start:
            swa_model.update_parameters(model)
        
        # Validation with appropriate model
        eval_model = swa_model if epoch >= swa_start else model
        eval_model.eval()
        val_epoch_losses = []
        
        with torch.no_grad():
            for images, targets in val_loader:
                images = images.to(device, non_blocking=True)
                targets = targets.to(device, non_blocking=True)
                
                with autocast('cuda'):
                    outputs = eval_model(images)
                    loss, _ = criterion(outputs, targets)
                
                if not torch.isnan(loss):
                    val_epoch_losses.append(loss.item())
        
        val_losses.append(np.mean(val_epoch_losses))
        
        # Enhanced logging
        components_str = ", ".join([f"{k}: {v:.4f}" for k, v in component_losses.items()])
        print(f"Epoch {epoch+1}: Train={train_losses[-1]:.4f}, Val={val_losses[-1]:.4f}")
        print(f"Components: {components_str}")
        print(f"LR: {optimizer.param_groups[0]['lr']:.2e}")
        
        # Save best model
        if val_losses[-1] < best_val_loss:
            best_val_loss = val_losses[-1]
            
            # Save the appropriate model
            save_model = swa_model if epoch >= swa_start else model
            
            torch.save({
                'model_state_dict': save_model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'scheduler_state_dict': scheduler.state_dict(),
                'swa_model_state_dict': swa_model.state_dict() if epoch >= swa_start else None,
                'epoch': epoch,
                'best_val_loss': best_val_loss,
                'train_losses': train_losses,
                'val_losses': val_losses,
                'loss_weights': loss_weights
            }, os.path.join(model_dir, 'best_model_ultra_refined.pth'))
            
            print(f"✅ Best model saved (val_loss={best_val_loss:.4f})")
            early_stop_counter = 0
        else:
            early_stop_counter += 1
            if early_stop_counter >= patience:
                print(f"⚠️ Early stopping after {patience} epochs")
                break
    
    # REFINEMENT 13: Final SWA model preparation
    if num_epochs >= swa_start:
        print("Updating SWA batch norm statistics...")
        torch.optim.swa_utils.update_bn(train_loader, swa_model, device)
        return swa_model, train_losses, val_losses
    
    return model, train_losses, val_losses

# REFINEMENT 14: Advanced data augmentation
class AdvancedAugmentation:
    def __init__(self, p=0.5):
        self.p = p
    
    def __call__(self, image, target):
        if np.random.random() < self.p:
            # Elastic deformation
            if np.random.random() < 0.3:
                image, target = self.elastic_transform(image, target)
            
            # Intensity variations
            if np.random.random() < 0.4:
                image = self.intensity_variation(image)
            
            # Gaussian noise
            if np.random.random() < 0.2:
                image = self.add_gaussian_noise(image)
        
        return image, target
    
    def elastic_transform(self, image, target, alpha=1, sigma=50):
        """Elastic deformation for data augmentation"""
        from scipy.ndimage import gaussian_filter, map_coordinates
        
        shape = image.shape
        dx = gaussian_filter((np.random.random(shape) * 2 - 1), sigma) * alpha
        dy = gaussian_filter((np.random.random(shape) * 2 - 1), sigma) * alpha
        
        x, y = np.meshgrid(np.arange(shape[1]), np.arange(shape[0]))
        indices = np.reshape(y + dy, (-1, 1)), np.reshape(x + dx, (-1, 1))
        
        image = map_coordinates(image, indices, order=1, mode='reflect').reshape(shape)
        target = map_coordinates(target, indices, order=0, mode='reflect').reshape(target.shape)
        
        return image, target
    
    def intensity_variation(self, image, factor=0.2):
        """Random intensity scaling"""
        scale = 1 + np.random.uniform(-factor, factor)
        return np.clip(image * scale, 0, 1)
    
    def add_gaussian_noise(self, image, std=0.05):
        """Add Gaussian noise"""
        noise = np.random.normal(0, std, image.shape)
        return np.clip(image + noise, 0, 1)

# REFINEMENT 15: Learning rate finder
def find_optimal_lr(model, train_loader, device, start_lr=1e-7, end_lr=1e-1, num_iter=100):
    """Find optimal learning rate using LR range test"""
    model.train()
    criterion = SkeletonAwareLoss()
    optimizer = torch.optim.AdamW(model.parameters(), lr=start_lr)
    
    lr_mult = (end_lr / start_lr) ** (1 / num_iter)
    lrs, losses = [], []
    
    for i, (images, targets) in enumerate(train_loader):
        if i >= num_iter:
            break
            
        images = images.to(device)
        targets = targets.to(device)
        
        optimizer.zero_grad()
        outputs = model(images)
        loss, _ = criterion(outputs, targets)
        loss.backward()
        optimizer.step()
        
        lrs.append(optimizer.param_groups[0]['lr'])
        losses.append(loss.item())
        
        # Update learning rate
        optimizer.param_groups[0]['lr'] *= lr_mult
    
    # Find optimal LR (steepest descent)
    gradients = np.gradient(losses)
    optimal_idx = np.argmin(gradients)
    optimal_lr = lrs[optimal_idx]
    
    return optimal_lr, lrs, losses

# Usage example:
"""
# Find optimal learning rate
optimal_lr, lrs, losses = find_optimal_lr(model, train_loader, device)
print(f"Optimal LR: {optimal_lr:.2e}")

# Train with ultra-refined method
model, start_epoch, best_val_loss, optimizer_state = initialize_model_refined(resume_checkpoint)
trained_model, train_losses, val_losses = train_skeleton_aware_model_ultra_refined(
    model, train_loader, val_loader, 
    num_epochs=300, device=device,
    start_epoch=start_epoch, best_val_loss=best_val_loss, 
    optimizer_state=optimizer_state
)
"""
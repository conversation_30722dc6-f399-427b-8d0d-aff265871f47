import torch
import numpy as np
import matplotlib.pyplot as plt
from enhanced_peak_detection import EnhancedPeakDetector
from enhanced_synthetic_data_generator import EnhancedSyntheticSpotGenerator
from enhanced_spot_dataset import EnhancedSpotDataset
import cv2

def visualize_enhanced_detection():
    """
    Visualize the enhanced peak detection approach
    
    This function demonstrates the combo strategy:
    1. Generate synthetic data with spot centroids and masks
    2. Use peak detection to find spot centroids
    3. Use actual masks for spot representation
    """
    # Create synthetic data generator
    generator = EnhancedSyntheticSpotGenerator(
        image_size=(256, 256),
        min_spots=10,
        max_spots=30,
        min_radius=3,
        max_radius=8,
        density_factor=1.0,
        mask_threshold=0.15,
        shape_variation=0.3,
        add_gradients=True,
        realistic_noise=True
    )
    
    # Generate a few samples
    num_samples = 3
    images = []
    masks = []
    spot_data_list = []
    
    for _ in range(num_samples):
        image, mask, spot_data = generator.generate_sample()
        images.append(image)
        masks.append(mask)
        spot_data_list.append(spot_data)
    
    # Initialize peak detector
    peak_detector = EnhancedPeakDetector(
        min_distance=5,
        min_intensity=0.1
    )
    
    # Create figure for visualization
    fig, axes = plt.subplots(num_samples, 4, figsize=(16, 4*num_samples))
    
    for i in range(num_samples):
        image = images[i]
        mask = masks[i]
        spot_data = spot_data_list[i]
        
        # Create a simulated prediction (for demonstration)
        # In a real scenario, this would come from your model
        # Here we'll just add some noise to the ground truth
        pred = np.zeros_like(image)
        for spot in spot_data:
            x, y = spot['x'], spot['y']
            radius = spot['radius']
            intensity = spot['intensity']
            
            # Create spot in prediction
            y_grid, x_grid = np.ogrid[-y:image.shape[0]-y, -x:image.shape[1]-x]
            dist = np.sqrt(x_grid**2 + y_grid**2)
            
            # Add some random offset to simulate prediction error
            offset_x = np.random.normal(0, 1)
            offset_y = np.random.normal(0, 1)
            y_grid_offset = np.ogrid[-(y+offset_y):image.shape[0]-(y+offset_y)]
            x_grid_offset = np.ogrid[-(x+offset_x):image.shape[1]-(x+offset_x)]
            dist_offset = np.sqrt(x_grid_offset**2 + y_grid_offset**2)
            
            # Create spot with some noise
            spot = np.exp(-(dist_offset**2) / (2 * ((radius+np.random.normal(0, 0.5))/2)**2)) * intensity
            
            # Add spot to prediction
            pred += spot
        
        # Add noise to prediction
        pred += np.random.normal(0, 0.05, pred.shape)
        pred = np.clip(pred, 0, 1)
        
        # Detect peaks with masks
        result = peak_detector.detect_peaks_with_masks(
            pred,
            mask=mask,
            return_visualization=True,
            original_image=image
        )
        
        # Original image
        axes[i, 0].imshow(image, cmap='gray')
        axes[i, 0].set_title('Original Image')
        axes[i, 0].axis('off')
        
        # Ground truth mask
        axes[i, 1].imshow(mask, cmap='hot')
        axes[i, 1].set_title('Ground Truth Mask')
        axes[i, 1].axis('off')
        
        # Simulated prediction
        axes[i, 2].imshow(pred, cmap='hot')
        axes[i, 2].set_title('Simulated Prediction')
        axes[i, 2].axis('off')
        
        # Enhanced detection visualization
        axes[i, 3].imshow(result['visualization'])
        axes[i, 3].set_title(f'Enhanced Detection: {result["num_spots"]} spots')
        axes[i, 3].axis('off')
    
    plt.tight_layout()
    plt.savefig('enhanced_detection_visualization.png')
    plt.show()
    
    # Create a comparison visualization
    fig, axes = plt.subplots(num_samples, 3, figsize=(15, 5*num_samples))
    
    for i in range(num_samples):
        image = images[i]
        mask = masks[i]
        spot_data = spot_data_list[i]
        
        # Simulated prediction (same as before)
        pred = np.zeros_like(image)
        for spot in spot_data:
            x, y = spot['x'], spot['y']
            radius = spot['radius']
            intensity = spot['intensity']
            
            # Create spot in prediction with offset
            y_grid, x_grid = np.ogrid[-y:image.shape[0]-y, -x:image.shape[1]-x]
            dist = np.sqrt(x_grid**2 + y_grid**2)
            
            # Add some random offset to simulate prediction error
            offset_x = np.random.normal(0, 1)
            offset_y = np.random.normal(0, 1)
            y_grid_offset = np.ogrid[-(y+offset_y):image.shape[0]-(y+offset_y)]
            x_grid_offset = np.ogrid[-(x+offset_x):image.shape[1]-(x+offset_x)]
            dist_offset = np.sqrt(x_grid_offset**2 + y_grid_offset**2)
            
            # Create spot with some noise
            spot = np.exp(-(dist_offset**2) / (2 * ((radius+np.random.normal(0, 0.5))/2)**2)) * intensity
            
            # Add spot to prediction
            pred += spot
        
        # Add noise to prediction
        pred += np.random.normal(0, 0.05, pred.shape)
        pred = np.clip(pred, 0, 1)
        
        # 1. Standard peak detection (without using masks)
        # Find local maxima directly in the heatmap
        from skimage.feature import peak_local_max
        coordinates = peak_local_max(
            pred,
            min_distance=5,
            threshold_abs=0.1,
            exclude_border=False
        )
        
        # Create visualization for standard peak detection
        standard_vis = np.zeros((*image.shape, 3))
        standard_vis[..., 0] = image  # Red channel
        standard_vis[..., 1] = image  # Green channel
        standard_vis[..., 2] = image  # Blue channel
        
        # Draw circles at peak locations
        for j, (y, x) in enumerate(coordinates):
            # Draw circle
            cv2.circle(
                standard_vis,
                (int(x), int(y)),
                3,  # Fixed radius
                (1, 0, 0),  # Red
                1
            )
            
            # Add ID
            cv2.putText(
                standard_vis,
                str(j+1),
                (int(x), int(y)),
                cv2.FONT_HERSHEY_SIMPLEX,
                0.3,
                (0, 1, 0),  # Green
                1
            )
        
        # 2. Enhanced peak detection (using masks)
        result = peak_detector.detect_peaks_with_masks(
            pred,
            mask=mask,
            return_visualization=True,
            original_image=image
        )
        
        # Original image with ground truth
        gt_vis = np.zeros((*image.shape, 3))
        gt_vis[..., 0] = image  # Red channel
        gt_vis[..., 1] = image  # Green channel
        gt_vis[..., 2] = image  # Blue channel
        
        # Draw ground truth spots
        for j, spot in enumerate(spot_data):
            x, y = spot['x'], spot['y']
            radius = spot['radius']
            
            # Draw circle
            cv2.circle(
                gt_vis,
                (int(x), int(y)),
                radius,
                (0, 1, 0),  # Green for ground truth
                1
            )
            
            # Add ID
            cv2.putText(
                gt_vis,
                str(j+1),
                (int(x), int(y)),
                cv2.FONT_HERSHEY_SIMPLEX,
                0.3,
                (0, 1, 0),  # Green
                1
            )
        
        # Display visualizations
        axes[i, 0].imshow(gt_vis)
        axes[i, 0].set_title('Ground Truth')
        axes[i, 0].axis('off')
        
        axes[i, 1].imshow(standard_vis)
        axes[i, 1].set_title(f'Standard Peak Detection: {len(coordinates)} spots')
        axes[i, 1].axis('off')
        
        axes[i, 2].imshow(result['visualization'])
        axes[i, 2].set_title(f'Enhanced Detection: {result["num_spots"]} spots')
        axes[i, 2].axis('off')
    
    plt.tight_layout()
    plt.savefig('peak_detection_comparison.png')
    plt.show()


if __name__ == "__main__":
    visualize_enhanced_detection()
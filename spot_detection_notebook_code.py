# SpotDetection_Part1 Notebook Code
# Copy and paste these code blocks into your notebook

# Import necessary libraries
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import os
import matplotlib.pyplot as plt
import numpy as np
from tqdm.notebook import tqdm
import time
from typing import Dict, List, Tuple, Optional, Union, Callable

# Fix the prediction visualization by removing the vertical flip
# This code should replace the visualize method in your SpotDetectionPredictor class
def visualize(self, 
             image: Union[str, np.ndarray, torch.Tensor],
             result: Optional[Dict] = None,
             show_spots: bool = True,
             show_heatmap: bool = True,
             show_experts: bool = False,
             figsize: Tuple[int, int] = (15, 10)) -> None:
    """Visualize prediction results with consistent orientation"""
    # Load and normalize image
    if isinstance(image, str):
        image_np = io.imread(image)
        if len(image_np.shape) == 3 and image_np.shape[2] > 1:
            image_np = cv2.cvtColor(image_np, cv2.COLOR_RGB2GRAY)
        image_np = image_np.astype(np.float32) / 255.0
    elif isinstance(image, np.ndarray):
        image_np = image.copy()
        if len(image_np.shape) == 3 and image_np.shape[2] > 1:
            image_np = cv2.cvtColor(image_np, cv2.COLOR_RGB2GRAY)
        if image_np.max() > 1.0:
            image_np = image_np.astype(np.float32) / 255.0
    else:
        # Convert tensor to numpy
        image_np = image.squeeze().cpu().numpy()
        if len(image_np.shape) == 3 and image_np.shape[0] == 1:
            image_np = image_np[0]

    # Get prediction if not provided
    if result is None:
        result = self.predict(image_np, return_heatmap=True)

    # Create figure
    num_plots = 1  # Original image
    if show_spots:
        num_plots += 1
    if show_heatmap:
        num_plots += 1
    if show_experts and 'expert_heatmaps' in result:
        num_plots += len(result['expert_heatmaps'])

    fig, axes = plt.subplots(1, num_plots, figsize=figsize)
    if num_plots == 1:
        axes = [axes]

    # Plot original image with consistent orientation
    axes[0].imshow(image_np, cmap='gray')
    axes[0].set_title('Original Image')
    axes[0].axis('off')

    plot_idx = 1

    # Plot spots
    if show_spots and 'spot_props' in result:
        # Create RGB image for visualization
        rgb_mask = np.zeros((*image_np.shape, 3))
        rgb_mask[..., 0] = image_np  # Red channel
        rgb_mask[..., 1] = image_np  # Green channel
        rgb_mask[..., 2] = image_np  # Blue channel

        # Overlay spots
        for prop in result['spot_props']:
            y, x = prop['centroid']
            r = int(np.sqrt(prop['area'] / np.pi))

            # Draw circle
            cv2.circle(
                rgb_mask,
                (int(x), int(y)),
                r,
                (1, 0, 0),  # Red
                1
            )

            # Add ID
            cv2.putText(
                rgb_mask,
                str(prop['id']),
                (int(x), int(y)),
                cv2.FONT_HERSHEY_SIMPLEX,
                0.3,
                (0, 1, 0),  # Green
                1
            )

        axes[plot_idx].imshow(rgb_mask)
        axes[plot_idx].set_title(f'Detected Spots: {result["num_spots"]}')
        axes[plot_idx].axis('off')
        plot_idx += 1

    # Plot heatmap with consistent orientation - FIXED: removed the vertical flip
    if show_heatmap and 'heatmap' in result:
        # Use the heatmap directly without flipping
        heatmap = result['heatmap']
        axes[plot_idx].imshow(heatmap, cmap='hot')
        axes[plot_idx].set_title('Prediction Heatmap')
        axes[plot_idx].axis('off')
        plot_idx += 1

    # Plot expert outputs with consistent orientation - FIXED: removed the vertical flip
    if show_experts and 'expert_heatmaps' in result:
        for i, expert_heatmap in enumerate(result['expert_heatmaps']):
            weight = result['gating_weights'][i] if 'gating_weights' in result else None
            weight_str = f" (w={weight:.2f})" if weight is not None else ""
            
            # Use the expert heatmap directly without flipping
            axes[plot_idx].imshow(expert_heatmap, cmap='hot')
            axes[plot_idx].set_title(f'Expert {i+1}{weight_str}')
            axes[plot_idx].axis('off')
            plot_idx += 1

    plt.tight_layout()
    plt.show()

# Create a class for visualizing predictions during training
class TrainingVisualizer:
    def __init__(self, model, device, save_dir='./training_visualizations'):
        self.model = model
        self.device = device
        self.save_dir = save_dir
        os.makedirs(save_dir, exist_ok=True)
        
    def visualize_epoch(self, val_loader, epoch):
        # Skip if not in first 100 epochs
        if epoch > 100:
            return
            
        # Get a batch from validation set
        batch = next(iter(val_loader))
        images = batch['image'].to(self.device)
        masks = batch['mask'].to(self.device)
        
        # Select a single image for visualization
        image = images[0:1]
        mask = masks[0:1]
        
        # Get prediction
        self.model.eval()
        with torch.no_grad():
            outputs = self.model(image)
            
            # Get main output
            if isinstance(outputs, dict):
                pred = outputs.get('output', outputs.get('combined_output', outputs.get('heatmap', None)))
            else:
                pred = outputs
        
        # Convert to numpy
        image_np = image.squeeze().cpu().numpy()
        mask_np = mask.squeeze().cpu().numpy()
        pred_np = torch.sigmoid(pred).squeeze().cpu().numpy()
        
        # Create figure
        fig, axes = plt.subplots(1, 3, figsize=(15, 5))
        
        # Plot original image
        axes[0].imshow(image_np, cmap='gray')
        axes[0].set_title('Original Image')
        axes[0].axis('off')
        
        # Plot ground truth mask
        axes[1].imshow(mask_np, cmap='gray')
        axes[1].set_title('Ground Truth')
        axes[1].axis('off')
        
        # Plot prediction
        axes[2].imshow(pred_np, cmap='hot')
        axes[2].set_title(f'Prediction (Epoch {epoch+1})')
        axes[2].axis('off')
        
        # Save figure
        plt.tight_layout()
        plt.savefig(f"{self.save_dir}/epoch_{epoch+1:03d}.png")
        plt.close()

# Modified training loop with AdamW optimizer and visualization
def train_model(model, train_loader, val_loader, device, num_epochs=200, save_dir='./training_visualizations'):
    # Create save directory
    os.makedirs(save_dir, exist_ok=True)
    
    # Initialize loss function
    loss_fn = OptimizedSpotLoss(
        bce_weight=1.0,
        dice_weight=1.0,
        focal_weight=0.5,
        focal_gamma=2.0,
        size_adaptive=True,
        density_aware=True,
        confidence_weighted=True
    )
    
    # Initialize metrics calculator
    metrics_calculator = SpotDetectionMetrics(threshold=0.5, iou_threshold=0.5)
    
    # Initialize optimizer - AdamW instead of Adam
    optimizer = optim.AdamW(
        model.parameters(),
        lr=0.0001,  # Lower learning rate for AdamW
        weight_decay=0.01,  # Weight decay parameter
        betas=(0.9, 0.999),
        eps=1e-8
    )
    
    # Initialize learning rate scheduler
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(
        optimizer,
        mode='min',
        factor=0.5,
        patience=5,
        verbose=True
    )
    
    # Initialize trainer
    trainer = SpotDetectionTrainer(
        model=model,
        loss_fn=loss_fn,
        optimizer=optimizer,
        device=device,
        metrics_calculator=metrics_calculator,
        scheduler=scheduler
    )
    
    # Initialize visualizer
    visualizer = TrainingVisualizer(model, device, save_dir)
    
    # Initialize variables for early stopping
    best_val_loss = float('inf')
    patience_counter = 0
    early_stopping_patience = 10
    
    # Initialize history
    history = {
        'train_loss': [],
        'val_loss': [],
        'train_metrics': [],
        'val_metrics': [],
        'learning_rates': []
    }
    
    # Train for specified number of epochs
    for epoch in range(num_epochs):
        start_time = time.time()

        # Train for one epoch
        train_metrics = trainer.train_epoch(train_loader)

        # Validate
        val_metrics = trainer.validate(val_loader)
        
        # Visualize predictions (only for first 100 epochs)
        visualizer.visualize_epoch(val_loader, epoch)

        # Update learning rate if scheduler is available
        if scheduler is not None:
            scheduler.step(val_metrics['loss'])

        # Get current learning rate
        current_lr = optimizer.param_groups[0]['lr']

        # Update history
        history['train_loss'].append(train_metrics['loss'])
        history['val_loss'].append(val_metrics['loss'])
        history['train_metrics'].append(train_metrics)
        history['val_metrics'].append(val_metrics)
        history['learning_rates'].append(current_lr)

        # Calculate epoch time
        epoch_time = time.time() - start_time

        # Print epoch summary
        print(f"Epoch {epoch+1}/{num_epochs} - {epoch_time:.1f}s - "
              f"Train Loss: {train_metrics['loss']:.4f} - "
              f"Val Loss: {val_metrics['loss']:.4f} - "
              f"LR: {current_lr:.6f}")

        # Print additional metrics if available
        if 'precision' in val_metrics:
            f1_key = 'f1_score' if 'f1_score' in val_metrics else 'f1'
            print(f"Val Precision: {val_metrics['precision']:.4f} - "
                  f"Val Recall: {val_metrics['recall']:.4f} - "
                  f"Val F1: {val_metrics[f1_key]:.4f} - "
                  f"Val IoU: {val_metrics['iou']:.4f}")

        # Check for best model
        if val_metrics['loss'] < best_val_loss:
            best_val_loss = val_metrics['loss']
            patience_counter = 0

            # Save best model
            torch.save(model.state_dict(), f"{save_dir}/best_model.pth")
            print(f"Saved best model to {save_dir}/best_model.pth")
        else:
            patience_counter += 1

        # Check for early stopping
        if early_stopping_patience > 0 and patience_counter >= early_stopping_patience:
            print(f"Early stopping after {epoch+1} epochs")
            break
    
    # Load best model
    model.load_state_dict(torch.load(f"{save_dir}/best_model.pth"))
    
    # Plot training history
    plot_training_history(history)
    
    return history

# Function to plot training history
def plot_training_history(history):
    # Create figure with subplots
    fig, axes = plt.subplots(1, 2, figsize=(15, 5))

    # Plot loss
    axes[0].plot(history['train_loss'], label='Train Loss')
    axes[0].plot(history['val_loss'], label='Val Loss')
    axes[0].set_xlabel('Epoch')
    axes[0].set_ylabel('Loss')
    axes[0].set_title('Training and Validation Loss')
    axes[0].legend()
    axes[0].grid(True)

    # Plot metrics if available
    if len(history['val_metrics']) > 0 and 'precision' in history['val_metrics'][0]:
        # Extract metrics
        val_precision = [m['precision'] for m in history['val_metrics']]
        val_recall = [m['recall'] for m in history['val_metrics']]
        # Handle both f1 and f1_score keys
        f1_key = 'f1_score' if 'f1_score' in history['val_metrics'][0] else 'f1'
        val_f1 = [m[f1_key] for m in history['val_metrics']]
        val_iou = [m['iou'] for m in history['val_metrics']]

        # Plot metrics
        axes[1].plot(val_precision, label='Precision')
        axes[1].plot(val_recall, label='Recall')
        axes[1].plot(val_f1, label='F1')
        axes[1].plot(val_iou, label='IoU')
        axes[1].set_xlabel('Epoch')
        axes[1].set_ylabel('Metric')
        axes[1].set_title('Validation Metrics')
        axes[1].legend()
        axes[1].grid(True)
    else:
        # Plot learning rate
        axes[1].plot(history['learning_rates'], label='Learning Rate')
        axes[1].set_xlabel('Epoch')
        axes[1].set_ylabel('Learning Rate')
        axes[1].set_title('Learning Rate Schedule')
        axes[1].legend()
        axes[1].grid(True)

    plt.tight_layout()
    plt.savefig("./training_visualizations/training_history.png")
    plt.show()

# Example usage in your notebook:
"""
# Fix the visualization method in your predictor
from predict import SpotDetectionPredictor
# Monkey patch the visualize method
SpotDetectionPredictor.visualize = visualize

# Train your model with AdamW and visualization
history = train_model(
    model=model,
    train_loader=train_loader,
    val_loader=val_loader,
    device=device,
    num_epochs=200,
    save_dir='./training_visualizations'
)
"""
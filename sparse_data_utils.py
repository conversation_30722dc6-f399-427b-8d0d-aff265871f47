import os
import numpy as np
import torch
import cv2
from skimage import io
from data_utils import SpotDataset
from torch.utils.data import DataLoader
from typing import Tuple, Dict
import albumentations as A
from albumentations.pytorch import ToTensorV2

# Define a global constant for image size
IMAGE_SIZE = 256

def normalize_orientation(arr):
    """Ensure consistent orientation for all arrays"""
    if torch.is_tensor(arr):
        arr = arr.squeeze().cpu().numpy()
    arr = np.asarray(arr)
    if arr.ndim > 2:
        if arr.shape[0] in [1, 3, 4]:  # Channel-first format
            arr = arr.transpose(1, 2, 0)
        if arr.shape[-1] in [3, 4]:  # RGB/RGBA to grayscale
            arr = cv2.cvtColor(arr, cv2.COLOR_RGB2GRAY)
    # Ensure values are in [0, 1] range
    if arr.max() > 1.0:
        arr = arr / 255.0
    return arr.astype(np.float32)

class SparseSpotDataset(SpotDataset):
    """
    Dataset for spot detection with sparse annotations
    
    This dataset extends SpotDataset to handle sparse annotations by:
    1. Creating confidence masks for partially annotated data
    2. Supporting semi-supervised learning with high-confidence predictions
    """
    def __init__(self, 
                 confidence_threshold=0.9,
                 ignore_threshold=0.3,
                 synthetic_params=None,
                 **kwargs):
        """
        Initialize the dataset
        
        Args:
            confidence_threshold: Threshold for high-confidence predictions
            ignore_threshold: Threshold below which to ignore predictions
            **kwargs: Arguments to pass to SpotDataset
        """
        # Pass synthetic_params to parent class
        if synthetic_params:
            kwargs['synthetic_params'] = synthetic_params
        super().__init__(**kwargs)
        self.confidence_threshold = confidence_threshold
        self.ignore_threshold = ignore_threshold
        self.synthetic_params = synthetic_params
        
        # Create confidence masks for each sample
        self.confidence_masks = []
        for i in range(len(self.masks)):
            if isinstance(self.masks[i], str):
                # For real data, check if confidence mask exists
                conf_path = self.masks[i].replace('.png', '_conf.png').replace('.tif', '_conf.png')
                if os.path.exists(conf_path):
                    conf_mask = io.imread(conf_path).astype(np.float32) / 255.0
                else:
                    # If no confidence mask, assume full confidence in annotations
                    mask = io.imread(self.masks[i])
                    if len(mask.shape) == 3 and mask.shape[2] > 1:
                        mask = cv2.cvtColor(mask, cv2.COLOR_RGB2GRAY)
                    mask = (mask > 0).astype(np.float32)
                    conf_mask = mask.copy()
                self.confidence_masks.append(conf_path if os.path.exists(conf_path) else conf_mask)
            else:
                # For synthetic data, confidence is 1.0 everywhere
                self.confidence_masks.append(np.ones_like(self.masks[i]))
                
        # Ensure confidence_masks has the same length as masks
        if len(self.confidence_masks) < len(self.masks):
            for i in range(len(self.confidence_masks), len(self.masks)):
                if isinstance(self.masks[i], str):
                    # For real data
                    mask = io.imread(self.masks[i])
                    if len(mask.shape) == 3 and mask.shape[2] > 1:
                        mask = cv2.cvtColor(mask, cv2.COLOR_RGB2GRAY)
                    mask = (mask > 0).astype(np.float32)
                    self.confidence_masks.append(mask.copy())
                else:
                    # For synthetic data
                    self.confidence_masks.append(np.ones_like(self.masks[i]))
    
    def update_with_predictions(self, image_paths, predictions, threshold=None):
        """
        Update dataset with high-confidence predictions
        
        Args:
            image_paths: Paths to images
            predictions: Predicted heatmaps
            threshold: Confidence threshold (overrides self.confidence_threshold)
        """
        if threshold is None:
            threshold = self.confidence_threshold
        
        # Ensure confidence_masks is properly initialized
        if len(self.confidence_masks) < len(self.masks):
            # Add missing confidence masks
            for i in range(len(self.confidence_masks), len(self.masks)):
                if isinstance(self.masks[i], str):
                    mask = io.imread(self.masks[i])
                    if len(mask.shape) == 3 and mask.shape[2] > 1:
                        mask = cv2.cvtColor(mask, cv2.COLOR_RGB2GRAY)
                    mask = (mask > 0).astype(np.float32)
                    self.confidence_masks.append(mask.copy())
                else:
                    self.confidence_masks.append(np.ones_like(self.masks[i]))
        
        # Process each prediction
        for path, pred in zip(image_paths, predictions):
            # Find index of this image in the dataset
            if isinstance(path, str):
                idx = self.images.index(path) if path in self.images else -1
            else:
                # For tensor images, find by comparing content
                idx = -1
                for i, img in enumerate(self.images):
                    if not isinstance(img, str) and torch.all(img == path):
                        idx = i
                        break
            
            if idx >= 0:
                # Convert prediction to numpy
                pred_np = torch.sigmoid(pred).squeeze().cpu().numpy()
                
                # Get current mask and confidence
                if isinstance(self.masks[idx], str):
                    mask = io.imread(self.masks[idx])
                    if len(mask.shape) == 3 and mask.shape[2] > 1:
                        mask = cv2.cvtColor(mask, cv2.COLOR_RGB2GRAY)
                    mask = (mask > 0).astype(np.float32)
                else:
                    mask = self.masks[idx].copy()
                
                # Ensure confidence mask exists for this index
                if idx >= len(self.confidence_masks):
                    self.confidence_masks.append(np.ones_like(mask))
                
                if isinstance(self.confidence_masks[idx], str):
                    conf = io.imread(self.confidence_masks[idx]).astype(np.float32) / 255.0
                else:
                    conf = self.confidence_masks[idx].copy()
                
                # Update mask with high-confidence predictions
                high_conf = pred_np > threshold
                low_conf = (conf < 0.5) & (pred_np > self.ignore_threshold)
                
                # Update mask where we have high confidence predictions
                # or where we have low confidence annotations but reasonable predictions
                mask[high_conf | low_conf] = 1.0
                
                # Update confidence mask
                conf[high_conf] = 1.0
                
                # Save updated mask and confidence
                self.masks[idx] = mask
                self.confidence_masks[idx] = conf
    
    # Update __getitem__ method to fix orientation issues
    def __getitem__(self, idx):
        """Get a sample from the dataset with confidence mask"""
        # Ensure confidence_masks has the same length as masks
        if len(self.confidence_masks) <= idx:
            # Add missing confidence masks
            for i in range(len(self.confidence_masks), idx + 1):
                if i < len(self.masks):
                    if isinstance(self.masks[i], str):
                        mask = io.imread(self.masks[i])
                        if len(mask.shape) == 3 and mask.shape[2] > 1:
                            mask = cv2.cvtColor(mask, cv2.COLOR_RGB2GRAY)
                        mask = (mask > 0).astype(np.float32)
                        self.confidence_masks.append(mask.copy())
                    else:
                        self.confidence_masks.append(np.ones_like(self.masks[i]))
        
        # Get image and mask paths/data
        image_source = self.images[idx]
        mask_source = self.masks[idx]
        conf_source = self.confidence_masks[idx]
        
        # Get target image size
        target_size = self.image_size[0] if isinstance(self.image_size, tuple) else self.image_size
        
        # Load and normalize image
        if isinstance(image_source, str):
            image = io.imread(image_source)
            if len(image.shape) == 3 and image.shape[-1] > 1:
                image = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
            image = image.astype(np.float32)
            image = (image - np.min(image)) / (np.max(image) - np.min(image) + 1e-8)
            
            # Resize image to target size
            if image.shape[0] != target_size or image.shape[1] != target_size:
                image = cv2.resize(image, (target_size, target_size), interpolation=cv2.INTER_AREA)
        else:
            image = image_source.copy()
            # Resize if needed
            if image.shape[0] != target_size or image.shape[1] != target_size:
                image = cv2.resize(image, (target_size, target_size), interpolation=cv2.INTER_AREA)
        
        # Load and normalize mask
        if isinstance(mask_source, str):
            mask = io.imread(mask_source)
            if len(mask.shape) == 3 and mask.shape[-1] > 1:
                mask = cv2.cvtColor(mask, cv2.COLOR_RGB2GRAY)
            mask = (mask > 0).astype(np.float32)
            
            # Resize mask to target size
            if mask.shape[0] != target_size or mask.shape[1] != target_size:
                mask = cv2.resize(mask, (target_size, target_size), interpolation=cv2.INTER_NEAREST)
        else:
            mask = mask_source.copy()
            if isinstance(mask, np.ndarray) and np.max(mask) > 1:
                mask = (mask > 0).astype(np.float32)
            
            # Resize if needed
            if isinstance(mask, np.ndarray) and (mask.shape[0] != target_size or mask.shape[1] != target_size):
                mask = cv2.resize(mask, (target_size, target_size), interpolation=cv2.INTER_NEAREST)
        
        # Load and normalize confidence mask
        if isinstance(conf_source, str):
            conf_mask = io.imread(conf_source)
            if len(conf_mask.shape) == 3 and conf_mask.shape[-1] > 1:
                conf_mask = cv2.cvtColor(conf_mask, cv2.COLOR_RGB2GRAY)
            conf_mask = conf_mask.astype(np.float32) / 255.0
            
            # Resize confidence mask to target size
            if conf_mask.shape[0] != target_size or conf_mask.shape[1] != target_size:
                conf_mask = cv2.resize(conf_mask, (target_size, target_size), interpolation=cv2.INTER_NEAREST)
        else:
            # Handle case where conf_source might be None or not properly initialized
            if conf_source is None:
                # Create a default confidence mask matching the mask
                conf_mask = np.ones((target_size, target_size), dtype=np.float32)
            else:
                conf_mask = conf_source.copy()
                if isinstance(conf_mask, np.ndarray) and np.max(conf_mask) > 1:
                    conf_mask = conf_mask / 255.0
                
                # Resize if needed
                if isinstance(conf_mask, np.ndarray) and (conf_mask.shape[0] != target_size or conf_mask.shape[1] != target_size):
                    conf_mask = cv2.resize(conf_mask, (target_size, target_size), interpolation=cv2.INTER_NEAREST)
        
        # Ensure all arrays have the same shape before augmentation
        # Resize confidence mask to match mask if needed
        if isinstance(conf_mask, np.ndarray) and isinstance(mask, np.ndarray) and conf_mask.shape != mask.shape:
            try:
                conf_mask = cv2.resize(conf_mask, (mask.shape[1], mask.shape[0]))
            except Exception:
                # If resize fails, create a new confidence mask
                conf_mask = np.ones_like(mask)
        
        # Fix orientation issues - ensure confidence mask has same orientation as mask
        # Try different orientations and pick the one that best matches the mask
        try:
            best_conf_mask = conf_mask.copy()
            best_diff = np.sum(np.abs(mask - conf_mask))
        except Exception:
            # If comparison fails, create a new confidence mask
            conf_mask = np.ones_like(mask)
            best_conf_mask = conf_mask.copy()
            best_diff = 0

        # Check vertical flip
        flipped_v = np.flipud(conf_mask)
        diff_v = np.sum(np.abs(mask - flipped_v))
        if diff_v < best_diff:
            best_conf_mask = flipped_v
            best_diff = diff_v

        # Check horizontal flip
        flipped_h = np.fliplr(conf_mask)
        diff_h = np.sum(np.abs(mask - flipped_h))
        if diff_h < best_diff:
            best_conf_mask = flipped_h
            best_diff = diff_h

        # Check both flips (180 degree rotation)
        flipped_both = np.fliplr(np.flipud(conf_mask))
        diff_both = np.sum(np.abs(mask - flipped_both))
        if diff_both < best_diff:
            best_conf_mask = flipped_both
            best_diff = diff_both

        # Use the best orientation
        conf_mask = best_conf_mask
        
        # Apply the same augmentation to all three components together
        if self.transform is not None:
            # Make sure all inputs are 2D for albumentations
            if image.ndim > 2:
                image = image.squeeze()
            if mask.ndim > 2:
                mask = mask.squeeze()
            if conf_mask.ndim > 2:
                conf_mask = conf_mask.squeeze()
            
            # Create a copy of the transform inputs to avoid modifying originals
            # Ensure all inputs have the same data type (float32)
            image_copy = image.copy().astype(np.float32)
            mask_copy = mask.copy().astype(np.float32)
            conf_copy = conf_mask.copy().astype(np.float32)
            
            transform_inputs = {
                'image': image_copy,
                'mask': mask_copy,
                'mask1': conf_copy  # mask1 is the confidence mask
            }
            
            try:
                # Apply transforms
                transformed = self.transform(**transform_inputs)
            except Exception as e:
                # If transform fails, return unaugmented data
                print(f"Warning: Augmentation failed, using unaugmented data: {str(e)}")
                transformed = {
                    'image': torch.from_numpy(image_copy).float(),
                    'mask': torch.from_numpy(mask_copy).float(),
                    'mask1': torch.from_numpy(conf_copy).float()
                }
            
            # Extract results
            image = transformed['image']
            mask = transformed['mask']
            conf_mask = transformed['mask1']
            
            # CRITICAL FIX: Force confidence mask to match mask's orientation after augmentation
            # Convert to numpy for processing if they're tensors
            if isinstance(mask, torch.Tensor):
                mask_np = mask.cpu().numpy()
            else:
                mask_np = mask
                
            if isinstance(conf_mask, torch.Tensor):
                conf_np = conf_mask.cpu().numpy()
            else:
                conf_np = conf_mask
            
            # Ensure they're 2D for comparison
            if mask_np.ndim > 2:
                mask_np = mask_np.squeeze()
            if conf_np.ndim > 2:
                conf_np = conf_np.squeeze()
            
            # Try all possible orientations and pick the best match
            best_conf = conf_np.copy()
            best_diff = np.sum(np.abs(mask_np - conf_np))
            
            # Check vertical flip
            flipped_v = np.flipud(conf_np)
            diff_v = np.sum(np.abs(mask_np - flipped_v))
            if diff_v < best_diff:
                best_conf = flipped_v
                best_diff = diff_v
            
            # Check horizontal flip
            flipped_h = np.fliplr(conf_np)
            diff_h = np.sum(np.abs(mask_np - flipped_h))
            if diff_h < best_diff:
                best_conf = flipped_h
                best_diff = diff_h
            
            # Check both flips (180 degree rotation)
            flipped_both = np.fliplr(np.flipud(conf_np))
            diff_both = np.sum(np.abs(mask_np - flipped_both))
            if diff_both < best_diff:
                best_conf = flipped_both
            
            # Use the best orientation for confidence mask
            conf_mask = best_conf
            
            # Convert to proper tensor format
            if isinstance(image, np.ndarray):
                try:
                    image = torch.from_numpy(image).float()
                    mask = torch.from_numpy(mask).float()
                    conf_mask = torch.from_numpy(conf_mask).float()
                except Exception as e:
                    print(f"Warning: Tensor conversion failed: {str(e)}")
                    # Create tensors with consistent shape
                    image = torch.zeros((1, IMAGE_SIZE, IMAGE_SIZE), dtype=torch.float32)
                    mask = torch.zeros((1, IMAGE_SIZE, IMAGE_SIZE), dtype=torch.float32)
                    conf_mask = torch.zeros((1, IMAGE_SIZE, IMAGE_SIZE), dtype=torch.float32)
        else:
            try:
                # Ensure consistent channel dimension
                if image.ndim == 2:
                    image = np.expand_dims(image, 0)
                if mask.ndim == 2:
                    mask = np.expand_dims(mask, 0)
                if conf_mask.ndim == 2:
                    conf_mask = np.expand_dims(conf_mask, 0)
                    
                # Convert to tensors
                image = torch.from_numpy(image).float()
                mask = torch.from_numpy(mask).float()
                conf_mask = torch.from_numpy(conf_mask).float()
            except Exception as e:
                print(f"Warning: Tensor conversion failed: {str(e)}")
                # Create tensors with consistent shape
                image = torch.zeros((1, IMAGE_SIZE, IMAGE_SIZE), dtype=torch.float32)
                mask = torch.zeros((1, IMAGE_SIZE, IMAGE_SIZE), dtype=torch.float32)
                conf_mask = torch.zeros((1, IMAGE_SIZE, IMAGE_SIZE), dtype=torch.float32)
        
        # Create sample dictionary
        sample = {
            'image': image,
            'mask': mask,
            'confidence': conf_mask
        }
        
        return sample


def create_sparse_data_loaders(data_dir=None, 
                              batch_size=8,
                              image_size=(256, 256),
                              train_val_split=0.8,
                              synthetic=True,
                              synthetic_size=1000,
                              augmentation_level='strong',
                              num_workers=4,
                              confidence_threshold=0.9,
                              ignore_threshold=0.3,
                              synthetic_params=None,
                              ensure_size=True):
    """
    Create train and validation data loaders with support for sparse annotations
    
    Args:
        data_dir: Directory containing images and masks
        batch_size: Batch size
        image_size: Size to resize images to
        train_val_split: Fraction of data to use for training
        synthetic: Whether to generate synthetic data
        synthetic_size: Number of synthetic samples to generate
        augmentation_level: Level of augmentation ('none', 'light', 'medium', 'strong')
        num_workers: Number of workers for data loading
        confidence_threshold: Threshold for high-confidence predictions
        ignore_threshold: Threshold below which to ignore predictions
        ensure_size: Whether to ensure all images are the same size using custom collate function
        
    Returns:
        train_loader, val_loader, train_dataset, val_dataset
    """
    # Create datasets
    if data_dir is not None:
        # Load real data and split into train/val
        full_dataset = SparseSpotDataset(
            data_dir=data_dir,
            image_size=image_size,
            mode='train',  # Will be overridden for validation split
            synthetic=False,
            augmentation_level='none',  # No augmentation yet
            confidence_threshold=confidence_threshold,
            ignore_threshold=ignore_threshold
        )
        
        # Calculate split sizes
        train_size = int(train_val_split * len(full_dataset))
        val_size = len(full_dataset) - train_size
        
        # Split dataset
        train_indices, val_indices = torch.utils.data.random_split(
            range(len(full_dataset)), 
            [train_size, val_size],
            generator=torch.Generator().manual_seed(42)
        )
        
        # Create new datasets with appropriate augmentation
        train_dataset = SparseSpotDataset(
            data_dir=None,  # No need to load data again
            image_size=image_size,
            mode='train',
            synthetic=synthetic,
            synthetic_size=synthetic_size,
            augmentation_level=augmentation_level,
            confidence_threshold=confidence_threshold,
            ignore_threshold=ignore_threshold,
            synthetic_params=synthetic_params
        )
        # Start with real data from the training split
        train_dataset.images = [full_dataset.images[i] for i in train_indices]
        train_dataset.masks = [full_dataset.masks[i] for i in train_indices]
        train_dataset.confidence_masks = [full_dataset.confidence_masks[i] for i in train_indices]
        
        # Ensure confidence_masks has the same length as masks
        if len(train_dataset.confidence_masks) < len(train_dataset.masks):
            for i in range(len(train_dataset.confidence_masks), len(train_dataset.masks)):
                if isinstance(train_dataset.masks[i], str):
                    mask = io.imread(train_dataset.masks[i])
                    if len(mask.shape) == 3 and mask.shape[2] > 1:
                        mask = cv2.cvtColor(mask, cv2.COLOR_RGB2GRAY)
                    mask = (mask > 0).astype(np.float32)
                    train_dataset.confidence_masks.append(mask.copy())
                else:
                    train_dataset.confidence_masks.append(np.ones_like(train_dataset.masks[i]))

        # If synthetic data is requested, generate it and add to the dataset
        if synthetic:
            # Store the current number of samples
            original_size = len(train_dataset.images)
            
            # Generate synthetic data
            train_dataset._generate_synthetic_data()
            
            # Print the number of samples added
            print(f"Added {len(train_dataset.images) - original_size} synthetic samples to training dataset")

        
        val_dataset = SparseSpotDataset(
            data_dir=None,  # No need to load data again
            image_size=image_size,
            mode='val',
            synthetic=False,  # No synthetic data for validation
            augmentation_level='none',  # No augmentation for validation
            confidence_threshold=confidence_threshold,
            ignore_threshold=ignore_threshold,
            synthetic_params=synthetic_params
        )
        val_dataset.images = [full_dataset.images[i] for i in val_indices]
        val_dataset.masks = [full_dataset.masks[i] for i in val_indices]
        val_dataset.confidence_masks = [full_dataset.confidence_masks[i] for i in val_indices]
        
        # Ensure confidence_masks has the same length as masks
        if len(val_dataset.confidence_masks) < len(val_dataset.masks):
            for i in range(len(val_dataset.confidence_masks), len(val_dataset.masks)):
                if isinstance(val_dataset.masks[i], str):
                    mask = io.imread(val_dataset.masks[i])
                    if len(mask.shape) == 3 and mask.shape[2] > 1:
                        mask = cv2.cvtColor(mask, cv2.COLOR_RGB2GRAY)
                    mask = (mask > 0).astype(np.float32)
                    val_dataset.confidence_masks.append(mask.copy())
                else:
                    val_dataset.confidence_masks.append(np.ones_like(val_dataset.masks[i]))
        
    else:
        # Create synthetic datasets
        train_dataset = SparseSpotDataset(
            data_dir=None,
            image_size=image_size,
            mode='train',
            synthetic=True,
            synthetic_size=synthetic_size,
            augmentation_level=augmentation_level,
            confidence_threshold=confidence_threshold,
            ignore_threshold=ignore_threshold,
            synthetic_params=synthetic_params
        )
        
        val_dataset = SparseSpotDataset(
            data_dir=None,
            image_size=image_size,
            mode='val',
            synthetic=True,
            synthetic_size=int(synthetic_size * (1 - train_val_split)),
            augmentation_level='none',  # No augmentation for validation
            confidence_threshold=confidence_threshold,
            ignore_threshold=ignore_threshold,
            synthetic_params=synthetic_params
        )
    
    # Create data loaders with optional custom collate function
    if ensure_size:
        # Import custom collate function
        from custom_collate import ensure_size_collate_fn
        
        # Get target size from image_size parameter
        target_size = image_size[0] if isinstance(image_size, tuple) else image_size
        
        # Create data loaders with custom collate function
        train_loader = DataLoader(
            train_dataset,
            batch_size=batch_size,
            shuffle=True,
            num_workers=num_workers,
            pin_memory=True,
            collate_fn=lambda batch: ensure_size_collate_fn(batch, target_size=target_size)
        )
        
        val_loader = DataLoader(
            val_dataset,
            batch_size=batch_size,
            shuffle=False,
            num_workers=num_workers,
            pin_memory=True,
            collate_fn=lambda batch: ensure_size_collate_fn(batch, target_size=target_size)
        )
    else:
        # Create data loaders without custom collate function
        train_loader = DataLoader(
            train_dataset,
            batch_size=batch_size,
            shuffle=True,
            num_workers=num_workers,
            pin_memory=True
        )
        
        val_loader = DataLoader(
            val_dataset,
            batch_size=batch_size,
            shuffle=False,
            num_workers=num_workers,
            pin_memory=True
        )
    
    return train_loader, val_loader, train_dataset, val_dataset


def load_3d_stack(dir_path: str) -> Tuple[np.ndarray, np.ndarray]:
    """Load 3D image and mask stacks with consistent orientation"""
    # Get sorted filenames to maintain z-order
    image_files = sorted([f for f in os.listdir(os.path.join(dir_path, 'images')) 
                         if f.endswith(('.tif', '.tiff'))])
    mask_files = sorted([f for f in os.listdir(os.path.join(dir_path, 'masks'))
                        if f.endswith(('.tif', '.tiff'))])

    # Load stacks maintaining orientation
    image_stack = []
    mask_stack = []
    
    for img_file, mask_file in zip(image_files, mask_files):
        # Load image slice
        img = io.imread(os.path.join(dir_path, 'images', img_file))
        if len(img.shape) == 3 and img.shape[2] > 1:
            img = cv2.cvtColor(img, cv2.COLOR_RGB2GRAY)
        img = img.astype(np.float32) / 255.0
        
        # Load mask slice
        mask = io.imread(os.path.join(dir_path, 'masks', mask_file))
        if len(mask.shape) == 3 and mask.shape[2] > 1:
            mask = cv2.cvtColor(mask, cv2.COLOR_RGB2GRAY)
        mask = (mask > 0).astype(np.float32)
        
        image_stack.append(img)
        mask_stack.append(mask)
    
    # Stack slices with consistent orientation [D, H, W]
    image_stack = np.stack(image_stack, axis=0)
    mask_stack = np.stack(mask_stack, axis=0)
    
    return image_stack, mask_stack

def create_3d_augmentation_pipeline():
    """Create augmentation pipeline for 3D data that preserves orientation"""
    return A.Compose([
        A.RandomRotate90(p=0.5),
        A.HorizontalFlip(p=0.5),
        A.VerticalFlip(p=0.5),
        A.OneOf([
            A.GaussNoise(p=1),
            A.RandomBrightnessContrast(p=1),
        ], p=0.3),
        ToTensorV2()
    ])

def process_3d_batch(batch: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
    """Process 3D batch maintaining consistent orientation"""
    # Ensure input has shape [B, C, D, H, W]
    if len(batch['image'].shape) == 4:  # [B, D, H, W]
        batch['image'] = batch['image'].unsqueeze(1)
    if len(batch['mask'].shape) == 4:
        batch['mask'] = batch['mask'].unsqueeze(1)
    
    if 'confidence' in batch and len(batch['confidence'].shape) == 4:
        batch['confidence'] = batch['confidence'].unsqueeze(1)
    
    return batch
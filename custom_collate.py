import torch
import numpy as np

def ensure_size_collate_fn(batch, target_size=256):
    """
    Custom collate function that ensures all images are the same size
    """
    # Filter out None samples
    batch = [b for b in batch if b is not None]
    if len(batch) == 0:
        return {}
    
    # Process each sample to ensure consistent size
    processed_batch = []
    for sample in batch:
        processed_sample = {}
        for key, value in sample.items():
            if isinstance(value, torch.Tensor) and value.dim() >= 2:
                # Ensure tensor has correct shape
                if value.shape[-1] != target_size or value.shape[-2] != target_size:
                    # Handle different tensor dimensions
                    if value.dim() == 2:
                        value = torch.nn.functional.interpolate(
                            value.unsqueeze(0).unsqueeze(0), 
                            size=(target_size, target_size), 
                            mode='nearest'
                        ).squeeze(0).squeeze(0)
                    elif value.dim() == 3:
                        value = torch.nn.functional.interpolate(
                            value.unsqueeze(0), 
                            size=(target_size, target_size), 
                            mode='nearest'
                        ).squeeze(0)
                    else:
                        value = torch.nn.functional.interpolate(
                            value, 
                            size=(target_size, target_size), 
                            mode='nearest'
                        )
            processed_sample[key] = value
        processed_batch.append(processed_sample)
    
    # Use default collate
    return torch.utils.data.default_collate(processed_batch)
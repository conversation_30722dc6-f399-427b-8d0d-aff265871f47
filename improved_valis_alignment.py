# Improved VALIS registration parameters for better alignment

# Replace your existing registrar initialization with these optimized parameters:
registrar = registration.Valis(
    clahe_padded_dir,
    dst_dir, 
    reference_img_f=clahe_padded_reference,
    align_to_reference=True,
    micro_rigid_registrar_cls=MicroRigidRegistrar,
    
    # Key parameters for better alignment:
    max_processed_image_dim_px=4000,  # Increased from 2000 for higher resolution
    max_non_rigid_registration_dim_px=3000,  # Increased from 1500
    
    # Feature detection parameters
    feature_detector_cls=registration.SuperGlueDetector,  # Better than default SIFT
    matcher_cls=registration.SuperGlueMatcher,
    
    # Rigid registration improvements
    rigid_registrar_cls=registration.OpticalFlowRigidRegistrar,  # More accurate than default
    
    # Non-rigid registration improvements
    non_rigid_registrar_cls=registration.OpticalFlowNonRigidRegistrar,
    
    # Quality parameters
    norm_method='img_stats',
    thumbnail_size=850,  # Increased for better feature detection
    
    # Alignment strictness
    align_to_reference=True,
    crop_to_overlap=False,  # Keep full images
    
    # Processing parameters
    do_rigid=True,
    do_non_rigid=True,
    
    # Feature matching parameters
    max_keypoints=10000,  # Increased from default 5000
    match_filter_method='ransac',
    ransac_reproj_threshold=2.0,  # Tighter threshold
    
    # Overlap requirements
    min_overlap_ratio=0.1,  # Minimum overlap required
)

# Enhanced micro-registration parameters
micro_reg, micro_error = registrar.register_micro(
    max_non_rigid_registration_dim_px=4000,  # Increased resolution
    align_to_reference=True,
    
    # Micro-registration specific improvements
    micro_rigid_registrar_kwargs={
        'max_keypoints': 15000,
        'match_filter_method': 'ransac',
        'ransac_reproj_threshold': 1.5,  # Even tighter for micro-registration
        'min_matches': 50,  # Require more matches
    }
)

# Alternative: If you have good initial alignment, try SimpleElastix for sub-pixel accuracy
# registrar = registration.Valis(
#     clahe_padded_dir,
#     dst_dir,
#     reference_img_f=clahe_padded_reference,
#     non_rigid_registrar_cls=registration.SimpleElastixNonRigidRegistrar,
#     max_processed_image_dim_px=4000,
#     max_non_rigid_registration_dim_px=4000,
# )
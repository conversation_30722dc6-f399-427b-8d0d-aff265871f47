# UltraSegNet 2025: Advanced Unsupervised Instance Segmentation for Microscopy
# Combines: SAM2, DINOv2, Segment Anything, Contrastive Learning, and Hybrid SDT

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import numpy as np
import cv2
from scipy.ndimage import distance_transform_edt, label
from skimage.segmentation import watershed, find_boundaries
from skimage.feature import peak_local_maxima
from skimage.morphology import remove_small_objects, skeletonize
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple, Optional
import os
import tifffile
from tqdm import tqdm

class ConvNeXtBlock(nn.Module):
    """ConvNeXt block for modern CNN architecture"""
    def __init__(self, dim, drop_path=0.):
        super().__init__()
        self.dwconv = nn.Conv2d(dim, dim, kernel_size=7, padding=3, groups=dim)
        self.norm = nn.LayerNorm(dim, eps=1e-6)
        self.pwconv1 = nn.Linear(dim, 4 * dim)
        self.act = nn.GELU()
        self.pwconv2 = nn.Linear(4 * dim, dim)
        self.drop_path = nn.Identity() if drop_path == 0. else nn.Dropout(drop_path)

    def forward(self, x):
        input = x
        x = self.dwconv(x)
        x = x.permute(0, 2, 3, 1)  # (N, C, H, W) -> (N, H, W, C)
        x = self.norm(x)
        x = self.pwconv1(x)
        x = self.act(x)
        x = self.pwconv2(x)
        x = x.permute(0, 3, 1, 2)  # (N, H, W, C) -> (N, C, H, W)
        x = input + self.drop_path(x)
        return x

class MultiScaleFeatureExtractor(nn.Module):
    """Multi-scale feature extraction with ConvNeXt backbone"""
    def __init__(self, in_channels=1, base_dim=96):
        super().__init__()
        
        # Stem
        self.stem = nn.Sequential(
            nn.Conv2d(in_channels, base_dim, kernel_size=4, stride=4),
            nn.LayerNorm(base_dim, eps=1e-6)
        )
        
        # 4 stages with different scales
        self.stages = nn.ModuleList([
            nn.Sequential(*[ConvNeXtBlock(base_dim) for _ in range(3)]),
            nn.Sequential(
                nn.LayerNorm(base_dim, eps=1e-6),
                nn.Conv2d(base_dim, base_dim*2, kernel_size=2, stride=2),
                *[ConvNeXtBlock(base_dim*2) for _ in range(3)]
            ),
            nn.Sequential(
                nn.LayerNorm(base_dim*2, eps=1e-6),
                nn.Conv2d(base_dim*2, base_dim*4, kernel_size=2, stride=2),
                *[ConvNeXtBlock(base_dim*4) for _ in range(9)]
            ),
            nn.Sequential(
                nn.LayerNorm(base_dim*4, eps=1e-6),
                nn.Conv2d(base_dim*4, base_dim*8, kernel_size=2, stride=2),
                *[ConvNeXtBlock(base_dim*8) for _ in range(3)]
            )
        ])
        
    def forward(self, x):
        features = []
        x = self.stem(x)
        
        for stage in self.stages:
            x = stage(x)
            features.append(x)
            
        return features

class HybridSDTHead(nn.Module):
    """Hybrid Skeleton-aware Distance Transform + Edge Transform"""
    def __init__(self, in_dim, out_dim=1):
        super().__init__()
        self.conv1 = nn.Conv2d(in_dim, in_dim//2, 3, padding=1)
        self.conv2 = nn.Conv2d(in_dim//2, in_dim//4, 3, padding=1)
        self.sdt_head = nn.Conv2d(in_dim//4, out_dim, 1)
        self.edge_head = nn.Conv2d(in_dim//4, out_dim, 1)
        self.skeleton_head = nn.Conv2d(in_dim//4, out_dim, 1)
        
    def forward(self, x):
        x = F.relu(self.conv1(x))
        x = F.relu(self.conv2(x))
        
        sdt = torch.sigmoid(self.sdt_head(x))
        edge = torch.sigmoid(self.edge_head(x))
        skeleton = torch.sigmoid(self.skeleton_head(x))
        
        # Hybrid combination: α*SDT + β*Edge + γ*Skeleton
        hybrid_sdt = 0.6 * sdt + 0.3 * edge + 0.1 * skeleton
        
        return {
            'sdt': sdt,
            'edge': edge,
            'skeleton': skeleton,
            'hybrid_sdt': hybrid_sdt
        }

class ContrastiveEmbeddingHead(nn.Module):
    """Contrastive learning for instance discrimination"""
    def __init__(self, in_dim, embed_dim=128):
        super().__init__()
        self.conv = nn.Sequential(
            nn.Conv2d(in_dim, in_dim//2, 3, padding=1),
            nn.ReLU(),
            nn.Conv2d(in_dim//2, embed_dim, 1)
        )
        
    def forward(self, x):
        return F.normalize(self.conv(x), dim=1)

class UltraSegNet(nn.Module):
    """Ultra-advanced instance segmentation network"""
    def __init__(self, in_channels=1, base_dim=96, num_classes=1, embed_dim=128):
        super().__init__()
        
        # Feature extractor
        self.backbone = MultiScaleFeatureExtractor(in_channels, base_dim)
        
        # Feature Pyramid Network
        self.fpn = nn.ModuleList([
            nn.Conv2d(base_dim*8, base_dim*2, 1),
            nn.Conv2d(base_dim*4, base_dim*2, 1),
            nn.Conv2d(base_dim*2, base_dim*2, 1),
            nn.Conv2d(base_dim, base_dim*2, 1)
        ])
        
        # Task-specific heads
        self.semantic_head = nn.Sequential(
            nn.Conv2d(base_dim*2, base_dim, 3, padding=1),
            nn.ReLU(),
            nn.Conv2d(base_dim, num_classes, 1)
        )
        
        self.centroid_head = nn.Sequential(
            nn.Conv2d(base_dim*2, base_dim, 3, padding=1),
            nn.ReLU(),
            nn.Conv2d(base_dim, 1, 1)
        )
        
        self.hybrid_sdt_head = HybridSDTHead(base_dim*2)
        self.embedding_head = ContrastiveEmbeddingHead(base_dim*2, embed_dim)
        
        # Boundary refinement
        self.boundary_head = nn.Sequential(
            nn.Conv2d(base_dim*2, base_dim//2, 3, padding=1),
            nn.ReLU(),
            nn.Conv2d(base_dim//2, 1, 1)
        )
        
    def forward(self, x):
        # Multi-scale features
        features = self.backbone(x)
        
        # FPN upsampling
        fpn_features = []
        for i, (feat, conv) in enumerate(zip(features, self.fpn)):
            if i == 0:
                fpn_feat = conv(feat)
            else:
                fpn_feat = conv(feat) + F.interpolate(fpn_features[-1], scale_factor=2)
            fpn_features.append(fpn_feat)
        
        # Use highest resolution FPN feature
        final_feat = F.interpolate(fpn_features[-1], scale_factor=4, mode='bilinear')
        
        # Task predictions
        semantic = torch.sigmoid(self.semantic_head(final_feat))
        centroid = torch.sigmoid(self.centroid_head(final_feat))
        sdt_outputs = self.hybrid_sdt_head(final_feat)
        embeddings = self.embedding_head(final_feat)
        boundary = torch.sigmoid(self.boundary_head(final_feat))
        
        return {
            'semantic': semantic,
            'centroid': centroid,
            'embeddings': embeddings,
            'boundary': boundary,
            **sdt_outputs
        }

class UnsupervisedDataset(Dataset):
    """Dataset for unsupervised learning from images only"""
    def __init__(self, image_paths, patch_size=512, augment=True):
        self.image_paths = image_paths
        self.patch_size = patch_size
        self.augment = augment
        
    def __len__(self):
        return len(self.image_paths) * 4  # 4 patches per image
    
    def __getitem__(self, idx):
        img_idx = idx // 4
        patch_idx = idx % 4
        
        # Load image
        image = tifffile.imread(self.image_paths[img_idx]).astype(np.float32)
        if len(image.shape) == 3:
            image = image[0]  # Take first channel if multi-channel
            
        # Normalize
        image = (image - image.min()) / (image.max() - image.min() + 1e-8)
        
        # Extract patch
        h, w = image.shape
        if h < self.patch_size or w < self.patch_size:
            # Pad if too small
            pad_h = max(0, self.patch_size - h)
            pad_w = max(0, self.patch_size - w)
            image = np.pad(image, ((0, pad_h), (0, pad_w)), mode='reflect')
            h, w = image.shape
        
        # Random crop
        start_h = np.random.randint(0, max(1, h - self.patch_size))
        start_w = np.random.randint(0, max(1, w - self.patch_size))
        patch = image[start_h:start_h+self.patch_size, start_w:start_w+self.patch_size]
        
        # Augmentation
        if self.augment:
            patch = self.augment_patch(patch)
        
        return torch.from_numpy(patch[None, ...]).float()  # Add channel dim
    
    def augment_patch(self, patch):
        """Data augmentation"""
        # Random rotation
        if np.random.random() < 0.5:
            k = np.random.randint(1, 4)
            patch = np.rot90(patch, k)
        
        # Random flip
        if np.random.random() < 0.5:
            patch = np.fliplr(patch)
        if np.random.random() < 0.5:
            patch = np.flipud(patch)
        
        # Intensity augmentation
        if np.random.random() < 0.3:
            patch = patch * np.random.uniform(0.8, 1.2)
            patch = np.clip(patch, 0, 1)
        
        # Gaussian noise
        if np.random.random() < 0.2:
            noise = np.random.normal(0, 0.02, patch.shape)
            patch = np.clip(patch + noise, 0, 1)
        
        return patch

class UnsupervisedLoss(nn.Module):
    """Advanced unsupervised loss combining multiple objectives"""
    def __init__(self):
        super().__init__()
        
    def compute_pseudo_labels(self, image):
        """Generate pseudo labels from image using classical methods"""
        # Convert to numpy
        img_np = image.cpu().numpy()[0, 0]  # Remove batch and channel dims
        
        # Otsu thresholding for semantic mask
        from skimage.filters import threshold_otsu
        thresh = threshold_otsu(img_np)
        semantic_pseudo = (img_np > thresh).astype(np.float32)
        
        # Distance transform for SDT
        if semantic_pseudo.sum() > 0:
            sdt_pseudo = distance_transform_edt(semantic_pseudo)
            sdt_pseudo = sdt_pseudo / (sdt_pseudo.max() + 1e-8)
        else:
            sdt_pseudo = np.zeros_like(semantic_pseudo)
        
        # Skeleton
        if semantic_pseudo.sum() > 0:
            skeleton_pseudo = skeletonize(semantic_pseudo > 0.5).astype(np.float32)
        else:
            skeleton_pseudo = np.zeros_like(semantic_pseudo)
        
        # Boundaries
        boundary_pseudo = find_boundaries(semantic_pseudo > 0.5).astype(np.float32)
        
        # Centroids using local maxima
        if sdt_pseudo.max() > 0:
            peaks = peak_local_maxima(sdt_pseudo, min_distance=10, threshold_abs=0.3)
            centroid_pseudo = np.zeros_like(sdt_pseudo)
            if len(peaks) > 0:
                centroid_pseudo[peaks[:, 0], peaks[:, 1]] = 1.0
        else:
            centroid_pseudo = np.zeros_like(sdt_pseudo)
        
        return {
            'semantic': torch.from_numpy(semantic_pseudo[None, None, ...]).to(image.device),
            'sdt': torch.from_numpy(sdt_pseudo[None, None, ...]).to(image.device),
            'skeleton': torch.from_numpy(skeleton_pseudo[None, None, ...]).to(image.device),
            'boundary': torch.from_numpy(boundary_pseudo[None, None, ...]).to(image.device),
            'centroid': torch.from_numpy(centroid_pseudo[None, None, ...]).to(image.device)
        }
    
    def contrastive_loss(self, embeddings, semantic_mask):
        """Contrastive loss for instance discrimination"""
        B, C, H, W = embeddings.shape
        
        # Sample positive and negative pairs
        mask = semantic_mask > 0.5
        if mask.sum() == 0:
            return torch.tensor(0.0, device=embeddings.device)
        
        # Flatten embeddings and masks
        embeddings_flat = embeddings.view(B, C, -1).permute(0, 2, 1)  # B, HW, C
        mask_flat = mask.view(B, -1)  # B, HW
        
        loss = 0.0
        for b in range(B):
            pos_indices = torch.where(mask_flat[b])[0]
            neg_indices = torch.where(~mask_flat[b])[0]
            
            if len(pos_indices) < 2 or len(neg_indices) < 1:
                continue
            
            # Sample pairs
            pos_pairs = torch.combinations(pos_indices[:min(100, len(pos_indices))], 2)
            neg_samples = neg_indices[torch.randperm(len(neg_indices))[:min(100, len(neg_indices))]]
            
            # Positive similarity
            if len(pos_pairs) > 0:
                pos_sim = F.cosine_similarity(
                    embeddings_flat[b][pos_pairs[:, 0]], 
                    embeddings_flat[b][pos_pairs[:, 1]], dim=1
                )
                pos_loss = -torch.log(torch.sigmoid(pos_sim * 10)).mean()
            else:
                pos_loss = 0
            
            # Negative similarity
            if len(pos_indices) > 0 and len(neg_samples) > 0:
                pos_anchor = pos_indices[torch.randperm(len(pos_indices))[:min(50, len(pos_indices))]]
                neg_sim = F.cosine_similarity(
                    embeddings_flat[b][pos_anchor[:, None]], 
                    embeddings_flat[b][neg_samples[None, :]], dim=2
                )
                neg_loss = -torch.log(torch.sigmoid(-neg_sim * 10)).mean()
            else:
                neg_loss = 0
            
            loss += pos_loss + neg_loss
        
        return loss / B
    
    def forward(self, outputs, images):
        batch_size = images.shape[0]
        total_loss = 0.0
        loss_dict = {}
        
        for i in range(batch_size):
            # Generate pseudo labels
            pseudo_labels = self.compute_pseudo_labels(images[i:i+1])
            
            # Semantic loss
            semantic_loss = F.binary_cross_entropy(
                outputs['semantic'][i:i+1], 
                pseudo_labels['semantic']
            )
            
            # SDT loss
            sdt_loss = F.mse_loss(
                outputs['hybrid_sdt'][i:i+1], 
                pseudo_labels['sdt']
            )
            
            # Skeleton loss
            skeleton_loss = F.binary_cross_entropy(
                outputs['skeleton'][i:i+1], 
                pseudo_labels['skeleton']
            )
            
            # Boundary loss
            boundary_loss = F.binary_cross_entropy(
                outputs['boundary'][i:i+1], 
                pseudo_labels['boundary']
            )
            
            # Centroid loss
            centroid_loss = F.binary_cross_entropy(
                outputs['centroid'][i:i+1], 
                pseudo_labels['centroid']
            )
            
            batch_loss = (semantic_loss + sdt_loss + skeleton_loss + 
                         boundary_loss + centroid_loss)
            total_loss += batch_loss
        
        # Contrastive loss
        contrastive_loss = self.contrastive_loss(
            outputs['embeddings'], 
            outputs['semantic']
        )
        
        total_loss = total_loss / batch_size + 0.1 * contrastive_loss
        
        loss_dict = {
            'total': total_loss,
            'contrastive': contrastive_loss
        }
        
        return total_loss, loss_dict

def train_ultrasegnet(model, train_loader, val_loader, num_epochs=100, device='cuda'):
    """Training loop for UltraSegNet"""
    model = model.to(device)
    if hasattr(torch, 'compile'):
        model = torch.compile(model)
    
    optimizer = torch.optim.AdamW(model.parameters(), lr=1e-4, weight_decay=1e-2)
    scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, num_epochs)
    criterion = UnsupervisedLoss()
    scaler = torch.amp.GradScaler('cuda')
    
    best_loss = float('inf')
    
    for epoch in range(num_epochs):
        model.train()
        epoch_losses = []
        
        pbar = tqdm(train_loader, desc=f"Epoch {epoch+1}/{num_epochs}")
        for images in pbar:
            images = images.to(device)
            
            optimizer.zero_grad()
            
            with torch.amp.autocast('cuda'):
                outputs = model(images)
                loss, loss_dict = criterion(outputs, images)
            
            scaler.scale(loss).backward()
            scaler.step(optimizer)
            scaler.update()
            
            epoch_losses.append(loss.item())
            pbar.set_postfix({'Loss': f'{loss.item():.4f}'})
        
        scheduler.step()
        
        # Validation
        if val_loader:
            model.eval()
            val_losses = []
            with torch.no_grad():
                for images in val_loader:
                    images = images.to(device)
                    outputs = model(images)
                    loss, _ = criterion(outputs, images)
                    val_losses.append(loss.item())
            
            val_loss = np.mean(val_losses)
            print(f"Epoch {epoch+1}: Train={np.mean(epoch_losses):.4f}, Val={val_loss:.4f}")
            
            if val_loss < best_loss:
                best_loss = val_loss
                torch.save(model.state_dict(), 'ultrasegnet_best.pth')
        else:
            print(f"Epoch {epoch+1}: Train={np.mean(epoch_losses):.4f}")
    
    return model

def inference_ultrasegnet(model, image, device='cuda', min_size=50):
    """Fast inference with instance segmentation"""
    model.eval()
    
    # Prepare image
    if isinstance(image, np.ndarray):
        if len(image.shape) == 2:
            image = torch.from_numpy(image[None, None, ...]).float()
        else:
            image = torch.from_numpy(image).float()
    
    image = image.to(device)
    
    with torch.no_grad():
        outputs = model(image)
    
    # Extract predictions
    semantic = outputs['semantic'][0, 0].cpu().numpy()
    centroid = outputs['centroid'][0, 0].cpu().numpy()
    hybrid_sdt = outputs['hybrid_sdt'][0, 0].cpu().numpy()
    boundary = outputs['boundary'][0, 0].cpu().numpy()
    
    # Instance segmentation using watershed
    # Find peaks for markers
    peaks = peak_local_maxima(
        centroid, 
        min_distance=5, 
        threshold_abs=0.3,
        exclude_border=True
    )
    
    markers = np.zeros_like(semantic, dtype=int)
    for i, (y, x) in enumerate(peaks):
        markers[y, x] = i + 1
    
    # Watershed segmentation
    instance_labels = watershed(
        -hybrid_sdt, 
        markers, 
        mask=semantic > 0.5,
        watershed_line=True
    )
    
    # Remove small objects
    instance_labels = remove_small_objects(instance_labels, min_size=min_size)
    
    # Extract instance information
    instances = []
    for region_id in np.unique(instance_labels):
        if region_id == 0:
            continue
        
        mask = instance_labels == region_id
        props = cv2.moments(mask.astype(np.uint8))
        
        if props['m00'] > 0:
            cx = int(props['m10'] / props['m00'])
            cy = int(props['m01'] / props['m00'])
            area = props['m00']
            
            instances.append({
                'id': region_id,
                'centroid': (cy, cx),
                'area': area,
                'mask': mask
            })
    
    return {
        'instances': instances,
        'instance_labels': instance_labels,
        'semantic': semantic,
        'centroid': centroid,
        'hybrid_sdt': hybrid_sdt,
        'boundary': boundary
    }

# Usage example
if __name__ == "__main__":
    # Initialize model
    model = UltraSegNet(in_channels=1, base_dim=96)
    
    # Create dataset (replace with your image paths)
    image_paths = ['/path/to/your/images/*.tif']  # Add your paths
    train_dataset = UnsupervisedDataset(image_paths, patch_size=512)
    train_loader = DataLoader(train_dataset, batch_size=4, shuffle=True, num_workers=4)
    
    # Train model
    trained_model = train_ultrasegnet(model, train_loader, None, num_epochs=100)
    
    # Inference example
    test_image = np.random.random((512, 512)).astype(np.float32)  # Replace with real image
    results = inference_ultrasegnet(trained_model, test_image)
    
    print(f"Detected {len(results['instances'])} instances")
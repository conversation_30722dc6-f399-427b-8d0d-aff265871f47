import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import torch.nn.functional as F
import numpy as np
import os
import time
from typing import Dict, List, Tuple, Optional, Union

# Import the model and loss function
# In a real project, you would use:
# from .model import OptimizedSpotDetectionModel
# from .loss import OptimizedSpotLoss

class SpotDetectionSystem:
    """
    Complete system for spot detection that integrates the model and loss function
    
    This class handles:
    - Model initialization
    - Loss function configuration
    - Training and validation loops
    - Inference with instance segmentation
    - Model saving and loading
    """
    def __init__(self, 
                 in_channels: int = 1,
                 base_filters: int = 32,
                 is_3d: bool = False,
                 num_experts: int = 4,
                 dropout: float = 0.1,
                 stochastic_depth: float = 0.1,
                 deep_supervision: bool = True,
                 use_attention: bool = True,
                 learn_loss_weights: bool = True,
                 density_aware_weighting: bool = True,
                 size_adaptive_weighting: bool = True,
                 device: str = 'cuda'):
        """
        Initialize the SpotDetectionSystem
        
        Args:
            in_channels: Number of input channels
            base_filters: Number of base filters in the first layer
            is_3d: Whether to use 3D convolutions
            num_experts: Number of experts in the MoE
            dropout: Dropout rate
            stochastic_depth: Stochastic depth rate
            deep_supervision: Whether to use deep supervision
            use_attention: Whether to use attention gates
            learn_loss_weights: Whether to learn loss weights
            density_aware_weighting: Whether to use density-aware weighting
            size_adaptive_weighting: Whether to use size-adaptive weighting
            device: Device to use ('cuda' or 'cpu')
        """
        self.device = torch.device(device if torch.cuda.is_available() and device == 'cuda' else 'cpu')
        
        # Initialize model
        self.model = OptimizedSpotDetectionModel(
            in_channels=in_channels,
            base_filters=base_filters,
            is_3d=is_3d,
            num_experts=num_experts,
            dropout=dropout,
            stochastic_depth=stochastic_depth,
            deep_supervision=deep_supervision,
            use_attention=use_attention
        ).to(self.device)
        
        # Initialize loss function
        self.loss_fn = OptimizedSpotLoss(
            heatmap_weight=0.5,
            boundary_weight=0.2,
            distance_weight=0.2,
            moe_weight=0.1,
            pos_weight=2.0,
            dice_weight=0.5,
            focal_gamma=1.0,
            learn_weights=learn_loss_weights,
            density_aware_weighting=density_aware_weighting,
            size_adaptive_weighting=size_adaptive_weighting,
            num_experts=num_experts
        ).to(self.device)
        
        # Initialize optimizer
        self.optimizer = None
        self.scheduler = None
        
    def configure_optimizer(self, 
                           learning_rate: float = 1e-3,
                           weight_decay: float = 1e-4,
                           scheduler_type: str = 'cosine'):
        """
        Configure optimizer and learning rate scheduler
        
        Args:
            learning_rate: Initial learning rate
            weight_decay: Weight decay for regularization
            scheduler_type: Type of scheduler ('cosine', 'step', or 'plateau')
        """
        # Create optimizer with weight decay
        self.optimizer = optim.AdamW(
            self.model.parameters(),
            lr=learning_rate,
            weight_decay=weight_decay
        )
        
        # Create learning rate scheduler
        if scheduler_type == 'cosine':
            self.scheduler = optim.lr_scheduler.CosineAnnealingLR(
                self.optimizer, T_max=100, eta_min=learning_rate/100
            )
        elif scheduler_type == 'step':
            self.scheduler = optim.lr_scheduler.StepLR(
                self.optimizer, step_size=30, gamma=0.1
            )
        elif scheduler_type == 'plateau':
            self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
                self.optimizer, mode='min', factor=0.1, patience=10
            )
        else:
            raise ValueError(f"Unknown scheduler type: {scheduler_type}")
    
    def train_epoch(self, dataloader: DataLoader) -> Dict[str, float]:
        """
        Train for one epoch
        
        Args:
            dataloader: DataLoader for training data
            
        Returns:
            Dict of metrics
        """
        self.model.train()
        total_loss = 0.0
        component_losses = {
            'heatmap_loss': 0.0,
            'dice_loss': 0.0,
            'boundary_loss': 0.0,
            'distance_loss': 0.0,
            'moe_loss': 0.0
        }
        
        start_time = time.time()
        num_batches = len(dataloader)
        
        for batch_idx, batch in enumerate(dataloader):
            # Get inputs and targets
            inputs = batch['image'].to(self.device)
            targets = {
                'masks': batch['mask'].to(self.device)
            }
            
            # Zero gradients
            self.optimizer.zero_grad()
            
            # Forward pass
            outputs = self.model(inputs)
            
            # Compute loss
            loss, losses_dict = self.loss_fn(outputs, targets)
            
            # Backward pass
            loss.backward()
            
            # Update weights
            self.optimizer.step()
            
            # Update metrics
            total_loss += loss.item()
            for key, value in losses_dict.items():
                if key in component_losses:
                    component_losses[key] += value.item()
            
            # Print progress
            if (batch_idx + 1) % 10 == 0 or (batch_idx + 1) == num_batches:
                elapsed = time.time() - start_time
                print(f"Batch {batch_idx+1}/{num_batches}, Loss: {loss.item():.4f}, "
                      f"Time: {elapsed:.2f}s, ETA: {elapsed/(batch_idx+1)*(num_batches-batch_idx-1):.2f}s")
        
        # Compute average losses
        metrics = {
            'loss': total_loss / num_batches
        }
        for key, value in component_losses.items():
            metrics[key] = value / num_batches
        
        # Get current loss weights
        if hasattr(self.loss_fn, 'get_current_weights'):
            weights = self.loss_fn.get_current_weights()
            for key, value in weights.items():
                if isinstance(value, torch.Tensor):
                    metrics[f'weight_{key}'] = value.item()
                else:
                    metrics[f'weight_{key}'] = value
        
        # Get expert usage statistics
        if hasattr(self.model, 'expert_usage'):
            expert_usage = self.model.expert_usage.cpu().numpy()
            for i, usage in enumerate(expert_usage):
                metrics[f'expert_{i}_usage'] = usage
        
        return metrics
    
    def validate(self, dataloader: DataLoader) -> Dict[str, float]:
        """
        Validate the model
        
        Args:
            dataloader: DataLoader for validation data
            
        Returns:
            Dict of metrics
        """
        self.model.eval()
        # Enable fast validation mode to skip instance segmentation
        self.model.configure_for_validation(fast_mode=True)
        
        total_loss = 0.0
        component_losses = {
            'heatmap_loss': 0.0,
            'dice_loss': 0.0,
            'boundary_loss': 0.0,
            'distance_loss': 0.0,
            'moe_loss': 0.0
        }
        
        num_batches = len(dataloader)
        
        with torch.no_grad():
            for batch in dataloader:
                # Get inputs and targets
                inputs = batch['image'].to(self.device)
                targets = {
                    'masks': batch['mask'].to(self.device)
                }
                
                # Forward pass
                outputs = self.model(inputs)
                
                # Compute loss
                loss, losses_dict = self.loss_fn(outputs, targets)
                
                # Update metrics
                total_loss += loss.item()
                for key, value in losses_dict.items():
                    if key in component_losses:
                        component_losses[key] += value.item()
        
        # Restore normal mode
        self.model.configure_for_validation(fast_mode=False)
        
        # Compute average losses
        metrics = {
            'val_loss': total_loss / num_batches
        }
        for key, value in component_losses.items():
            metrics[f'val_{key}'] = value / num_batches
        
        return metrics
    
    def train(self, 
             train_dataloader: DataLoader,
             val_dataloader: DataLoader,
             num_epochs: int = 100,
             save_dir: str = './checkpoints',
             save_freq: int = 10):
        """
        Train the model
        
        Args:
            train_dataloader: DataLoader for training data
            val_dataloader: DataLoader for validation data
            num_epochs: Number of epochs to train
            save_dir: Directory to save checkpoints
            save_freq: Frequency of saving checkpoints (in epochs)
        """
        # Create save directory if it doesn't exist
        os.makedirs(save_dir, exist_ok=True)
        
        # Initialize best validation loss
        best_val_loss = float('inf')
        
        # Train for specified number of epochs
        for epoch in range(num_epochs):
            print(f"Epoch {epoch+1}/{num_epochs}")
            
            # Train for one epoch
            train_metrics = self.train_epoch(train_dataloader)
            
            # Validate
            val_metrics = self.validate(val_dataloader)
            
            # Update learning rate scheduler
            if isinstance(self.scheduler, optim.lr_scheduler.ReduceLROnPlateau):
                self.scheduler.step(val_metrics['val_loss'])
            else:
                self.scheduler.step()
            
            # Print metrics
            print(f"Train Loss: {train_metrics['loss']:.4f}, "
                  f"Val Loss: {val_metrics['val_loss']:.4f}")
            
            # Save checkpoint if validation loss improved
            if val_metrics['val_loss'] < best_val_loss:
                best_val_loss = val_metrics['val_loss']
                self.save_model(os.path.join(save_dir, 'best_model.pth'))
                print(f"Saved best model with validation loss: {best_val_loss:.4f}")
            
            # Save checkpoint periodically
            if (epoch + 1) % save_freq == 0:
                self.save_model(os.path.join(save_dir, f'model_epoch_{epoch+1}.pth'))
                print(f"Saved checkpoint at epoch {epoch+1}")
    
    def save_model(self, path: str):
        """
        Save model and loss function
        
        Args:
            path: Path to save the model
        """
        torch.save({
            'model_state_dict': self.model.state_dict(),
            'loss_state_dict': self.loss_fn.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict() if self.optimizer else None,
            'scheduler_state_dict': self.scheduler.state_dict() if self.scheduler else None
        }, path)
    
    def load_model(self, path: str):
        """
        Load model and loss function
        
        Args:
            path: Path to load the model from
        """
        checkpoint = torch.load(path, map_location=self.device)
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.loss_fn.load_state_dict(checkpoint['loss_state_dict'])
        
        if self.optimizer and 'optimizer_state_dict' in checkpoint:
            self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        
        if self.scheduler and 'scheduler_state_dict' in checkpoint:
            self.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
    
    def predict(self, 
               image: torch.Tensor,
               threshold: float = 0.5,
               min_size: int = 1,
               min_distance: int = 1) -> Dict[str, torch.Tensor]:
        """
        Run inference on a single image
        
        Args:
            image: Input image tensor [1, C, H, W] or [1, C, D, H, W]
            threshold: Threshold for peak detection
            min_size: Minimum spot size in pixels
            min_distance: Minimum distance between spots
            
        Returns:
            Dict of outputs including instance segmentation
        """
        self.model.eval()
        
        # Set instance segmentation parameters
        self.model.min_distance = min_distance
        self.model.peak_threshold = threshold
        self.model.spot_min_size = min_size
        
        with torch.no_grad():
            # Add batch dimension if needed
            if image.dim() == 3:  # [C, H, W]
                image = image.unsqueeze(0)
            elif image.dim() == 4 and self.model.is_3d:  # [C, D, H, W]
                image = image.unsqueeze(0)
            
            # Move to device
            image = image.to(self.device)
            
            # Forward pass
            outputs = self.model(image)
        
        return outputs


# Example usage
def example_usage():
    """Example of how to use the SpotDetectionSystem"""
    # Create system
    system = SpotDetectionSystem(
        in_channels=1,
        base_filters=32,
        is_3d=False,
        num_experts=4,
        dropout=0.1,
        stochastic_depth=0.1,
        deep_supervision=True,
        use_attention=True,
        learn_loss_weights=True,
        density_aware_weighting=True,
        size_adaptive_weighting=True,
        device='cuda'
    )
    
    # Configure optimizer
    system.configure_optimizer(
        learning_rate=1e-3,
        weight_decay=1e-4,
        scheduler_type='cosine'
    )
    
    # Create dummy data
    class DummyDataset(torch.utils.data.Dataset):
        def __init__(self, size=100):
            self.size = size
        
        def __len__(self):
            return self.size
        
        def __getitem__(self, idx):
            # Create random image and mask
            image = torch.randn(1, 256, 256)
            mask = torch.zeros(1, 256, 256)
            
            # Add random spots
            for _ in range(np.random.randint(5, 15)):
                x = np.random.randint(20, 236)
                y = np.random.randint(20, 236)
                r = np.random.randint(3, 10)
                
                for i in range(-r, r+1):
                    for j in range(-r, r+1):
                        if i*i + j*j <= r*r:
                            mask[0, y+i, x+j] = 1.0
            
            return {'image': image, 'mask': mask}
    
    # Create dataloaders
    train_dataset = DummyDataset(size=100)
    val_dataset = DummyDataset(size=20)
    
    train_dataloader = DataLoader(train_dataset, batch_size=4, shuffle=True)
    val_dataloader = DataLoader(val_dataset, batch_size=4, shuffle=False)
    
    # Train model
    system.train(
        train_dataloader=train_dataloader,
        val_dataloader=val_dataloader,
        num_epochs=5,
        save_dir='./checkpoints',
        save_freq=1
    )
    
    # Run inference
    image = torch.randn(1, 256, 256)
    outputs = system.predict(image)
    
    # Print outputs
    print("Output keys:", outputs.keys())
    print("Heatmap shape:", outputs['heatmap'].shape)
    if 'instance_labels' in outputs:
        print("Instance labels shape:", outputs['instance_labels'].shape)
        print("Number of detected spots:", len(outputs['spot_coords'][0]))


if __name__ == "__main__":
    example_usage()
# Debug the binary mask issue
from debug_binary_mask import debug_binary_mask, fix_spot_detection

# Run the debugging function
debug_results = debug_binary_mask(test_image, model, device)

# Apply the fixed spot detection
fixed_results = fix_spot_detection(
    test_image, 
    heatmap,
    threshold=debug_results['best_threshold'],
    min_spot_size=3,
    min_distance=debug_results.get('best_watershed_min_distance', 5)
)

# Display the results
plt.figure(figsize=(15, 5))

plt.subplot(131)
plt.imshow(test_image, cmap='gray')
plt.title('Original Image')
plt.axis('off')

plt.subplot(132)
plt.imshow(debug_results['best_binary_mask'], cmap='gray')
plt.title(f'Binary Mask (t={debug_results["best_threshold"]})')
plt.axis('off')

plt.subplot(133)
plt.imshow(fixed_results['visualization'])
plt.title(f'Fixed Detection: {fixed_results["num_spots"]} spots')
plt.axis('off')

plt.tight_layout()
plt.show()

print(f"\nOptimized Parameters:")
print(f"threshold = {debug_results['best_threshold']}")
print(f"min_distance = {debug_results.get('best_watershed_min_distance', 5)}")
print(f"min_spot_size = 3")
print(f"Number of spots detected: {fixed_results['num_spots']}")
import os
import cv2
import numpy as np
import tifffile
import torch
from torch.utils.data import Dataset
from skimage.measure import regionprops
from scipy.spatial.distance import cdist
from scipy.ndimage import distance_transform_edt
from albumentations.pytorch import ToTensorV2

class SimpleSpotDataset(Dataset):
    """
    Simplified dataset without random patch extraction - uses center crops only
    """
    def __init__(self, image_paths, mask_paths, transform=None, patch_size=128):
        assert len(image_paths) == len(mask_paths)
        self.image_paths = image_paths
        self.mask_paths = mask_paths
        self.transform = transform
        self.patch_size = patch_size

    @staticmethod
    def get_valid_ids(mask):
        valid_ids = np.unique(mask)
        valid_ids = valid_ids[valid_ids > 0]
        return valid_ids

    @staticmethod
    def get_centroids_and_sizes(mask, valid_ids):
        centroids = []
        areas = []
        spot_masks = []
        for iid in valid_ids:
            spot_mask = (mask == iid).astype(np.uint8)
            props = regionprops(spot_mask)
            if props:
                prop = props[0]
                centroids.append(prop.centroid)  # (row, col) = (y, x)
                areas.append(prop.area)
                spot_masks.append(spot_mask)
        return centroids, areas, spot_masks

    @staticmethod
    def generate_instance_mask(mask_shape, spot_masks):
        instance_mask = np.zeros(mask_shape, dtype=np.float32)
        for spot_mask in spot_masks:
            instance_mask = np.maximum(instance_mask, spot_mask.astype(np.float32))
        return instance_mask

    @staticmethod
    def generate_centroid_map(mask_shape, centroids):
        centroid_map = np.zeros(mask_shape, dtype=np.float32)
        for cy, cx in centroids:  # cy=row, cx=col
            cy = int(round(cy))
            cx = int(round(cx))
            if 0 <= cy < mask_shape[0] and 0 <= cx < mask_shape[1]:
                centroid_map[cy, cx] = 1.0
        return centroid_map

    def _generate_distance_transform(self, mask):
        semantic = (mask > 0).astype(np.uint8)
        distance = distance_transform_edt(semantic)
        max_possible_distance = np.sqrt(self.patch_size ** 2 + self.patch_size ** 2)
        distance = np.clip(distance / max_possible_distance, 0, 1)
        return distance.astype(np.float32)

    def _generate_flow_field(self, mask, centroids):
        h, w = mask.shape
        flow_y = np.zeros((h, w), dtype=np.float32)
        flow_x = np.zeros((h, w), dtype=np.float32)
        if len(centroids) == 0:
            return np.stack([flow_y, flow_x], axis=0)
        centroids_arr = np.array(centroids)
        spot_mask = (mask > 0)
        if not spot_mask.any():
            return np.stack([flow_y, flow_x], axis=0)
        spot_pixels = np.column_stack(np.where(spot_mask))
        distances = cdist(spot_pixels, centroids_arr)
        nearest_centroid_idx = np.argmin(distances, axis=1)
        for i, (py, px) in enumerate(spot_pixels):
            nearest_centroid = centroids_arr[nearest_centroid_idx[i]]
            cy, cx = nearest_centroid
            dy = cy - py
            dx = cx - px
            norm = np.sqrt(dy**2 + dx**2) + 1e-8
            flow_y[py, px] = dy / norm
            flow_x[py, px] = dx / norm
        return np.stack([flow_y, flow_x], axis=0)

    def __len__(self):
        return len(self.image_paths)

    def __getitem__(self, idx):
        try:
            # Load image and mask
            img = tifffile.imread(self.image_paths[idx]).astype(np.float32)
            msk = tifffile.imread(self.mask_paths[idx]).astype(np.int32)
            
            # Normalize image
            img /= (img.max() + 1e-8)
            img = np.clip(img, 0, 1)
            
            H, W = img.shape[:2]
            
            # Simple center crop
            start_h = max(0, (H - self.patch_size) // 2)
            start_w = max(0, (W - self.patch_size) // 2)
            
            patch_img = img[start_h:start_h+self.patch_size, start_w:start_w+self.patch_size]
            patch_msk = msk[start_h:start_h+self.patch_size, start_w:start_w+self.patch_size]
            
            # Pad if needed
            ph = self.patch_size - patch_img.shape[0]
            pw = self.patch_size - patch_img.shape[1]
            if ph > 0 or pw > 0:
                patch_img = np.pad(patch_img, ((0, ph), (0, pw)), 'reflect')
                patch_msk = np.pad(patch_msk, ((0, ph), (0, pw)), 'constant')
            
            # Generate semantic mask (no erosion for testing)
            sem = (patch_msk > 0).astype(np.float32)
            
            # Generate boundary mask
            bnd = np.zeros_like(sem, dtype=np.float32)
            for iid in np.unique(patch_msk):
                if iid == 0: continue
                im = (patch_msk == iid).astype(np.uint8)
                kernel = np.ones((2,2), np.uint8)
                er = cv2.erode(im, kernel, iterations=1)
                bnd += ((im - er) > 0).astype(np.float32)
            bnd = np.clip(bnd, 0, 1)
            
            # Get spot properties
            centroids, areas, spot_masks = self.get_centroids_and_sizes(
                patch_msk, self.get_valid_ids(patch_msk))
            instance_mask = self.generate_instance_mask(patch_msk.shape, spot_masks)
            centroid_map = self.generate_centroid_map(patch_msk.shape, centroids)
            distance_map = self._generate_distance_transform(patch_msk)
            flow_field = self._generate_flow_field(patch_msk, centroids)
            
            # Stack: [semantic, boundary, distance, instance mask, centroid map]
            mask_stack = np.stack([sem, bnd, distance_map, instance_mask, centroid_map], axis=0)
            
            # Apply augmentation if provided
            if self.transform is not None:
                img_u8 = (patch_img * 255).astype(np.uint8)
                mask_hwc = np.moveaxis(mask_stack, 0, -1)
                flow_hwc = np.moveaxis(flow_field, 0, -1)
                aug = self.transform(image=img_u8, mask=mask_hwc, flow=flow_hwc)
                patch_img = aug['image'].astype(np.float32) / 255.0
                mask_stack = np.moveaxis(aug['mask'], -1, 0).astype(np.float32)
                flow_field = np.moveaxis(aug.get('flow', flow_hwc), -1, 0).astype(np.float32)
                mask_stack[0] = (mask_stack[0] > 0.5).astype(np.float32)
                mask_stack[1] = (mask_stack[1] > 0.5).astype(np.float32)
            
            # Convert to tensors
            img_t = ToTensorV2()(image=patch_img)['image']
            msk_t = torch.from_numpy(mask_stack)
            flow_t = torch.from_numpy(flow_field)
            
            # Simple confidence mask
            semantic = mask_stack[0]
            confidence = np.ones_like(semantic, dtype=np.float32)
            confidence_t = torch.from_numpy(confidence).unsqueeze(0)
            
            return img_t, msk_t, flow_t, confidence_t
            
        except Exception as e:
            print(f"Error processing sample {idx}: {e}")
            # Return empty tensors
            empty_img = torch.zeros((1, self.patch_size, self.patch_size), dtype=torch.float32)
            empty_mask = torch.zeros((5, self.patch_size, self.patch_size), dtype=torch.float32)
            empty_flow = torch.zeros((2, self.patch_size, self.patch_size), dtype=torch.float32)
            empty_conf = torch.zeros((1, self.patch_size, self.patch_size), dtype=torch.float32)
            return empty_img, empty_mask, empty_flow, empty_conf
import torch
import numpy as np
from sklearn.metrics import precision_recall_curve, average_precision_score
from typing import Dict, List, Tuple, Optional, Union
from skimage import measure

class FixedSpotMetrics:
    """Calculate metrics for spot detection with improved stability"""
    
    def __init__(self, threshold: float = 0.5, iou_threshold: float = 0.5):
        self.threshold = threshold
        self.iou_threshold = iou_threshold

    def calculate_metrics(self, pred: torch.Tensor, target: torch.Tensor) -> Dict[str, float]:
        """Calculate metrics with consistent orientation handling"""
        # Ensure tensors have shape [B, C, H, W] or [B, C, D, H, W]
        if len(pred.shape) == 3:
            pred = pred.unsqueeze(1)
        if len(target.shape) == 3:
            target = target.unsqueeze(1)
        
        # Convert to numpy with consistent orientation
        pred_np = pred.detach().cpu().numpy()
        target_np = target.detach().cpu().numpy()
        
        # Calculate metrics for each sample
        batch_size = pred.shape[0]
        metrics_dict = {
            'precision': 0.0,
            'recall': 0.0,
            'f1_score': 0.0,
            'iou': 0.0,
            'dice': 0.0,
            'avg_precision': 0.0
        }
        
        for i in range(batch_size):
            # Handle 2D and 3D data
            if len(pred.shape) == 4:  # 2D
                sample_metrics = self._calculate_2d_metrics(
                    pred_np[i, 0], target_np[i, 0]
                )
            else:  # 3D
                sample_metrics = self._calculate_3d_metrics(
                    pred_np[i, 0], target_np[i, 0]
                )
            
            # Accumulate metrics
            for k, v in sample_metrics.items():
                metrics_dict[k] += v
        
        # Average across batch
        for k in metrics_dict:
            metrics_dict[k] /= max(batch_size, 1)  # Avoid division by zero
            
        return metrics_dict
    
    def _calculate_2d_metrics(self, pred: np.ndarray, target: np.ndarray) -> Dict[str, float]:
        """Calculate 2D metrics with improved stability"""
        # Create binary masks
        pred_binary = (pred > self.threshold).astype(np.uint8)
        target_binary = (target > 0).astype(np.uint8)
        
        # Get instance labels with consistent orientation
        pred_labels = measure.label(pred_binary)
        target_labels = measure.label(target_binary)
        
        # Get region properties
        pred_props = measure.regionprops(pred_labels)
        target_props = measure.regionprops(target_labels)
        
        # Calculate instance-level metrics
        tp, fp, fn = 0, 0, 0
        matched_pairs = self._find_matching_pairs(
            pred_props, target_props, pred_labels, target_labels
        )
        
        tp = len(matched_pairs)
        fp = len(pred_props) - tp
        fn = len(target_props) - tp
        
        # Calculate metrics with improved stability
        precision = tp / max(tp + fp, 1)  # Avoid division by zero
        recall = tp / max(tp + fn, 1)     # Avoid division by zero
        f1 = 2 * precision * recall / max(precision + recall, 1e-6)  # Avoid division by zero
        
        # Calculate IoU and Dice with improved stability
        intersection = np.logical_and(pred_binary, target_binary).sum()
        union = np.logical_or(pred_binary, target_binary).sum()
        iou = intersection / max(union, 1)  # Avoid division by zero
        dice = 2 * intersection / max(pred_binary.sum() + target_binary.sum(), 1)  # Avoid division by zero
        
        # Calculate average precision
        try:
            # Flatten arrays for precision-recall calculation
            pred_flat = pred.flatten()
            target_flat = target_binary.flatten()
            
            # Calculate average precision only if there are positive examples
            if target_flat.sum() > 0:
                avg_precision = average_precision_score(target_flat, pred_flat)
            else:
                avg_precision = 0.0
        except Exception as e:
            print(f"Warning: Error calculating average precision: {e}")
            avg_precision = 0.0
        
        return {
            'precision': precision,
            'recall': recall,
            'f1_score': f1,
            'iou': iou,
            'dice': dice,
            'avg_precision': avg_precision
        }
    
    def _calculate_3d_metrics(self, pred: np.ndarray, target: np.ndarray) -> Dict[str, float]:
        """Calculate 3D metrics with improved stability"""
        metrics_3d = {
            'precision': 0.0,
            'recall': 0.0,
            'f1_score': 0.0,
            'iou': 0.0,
            'dice': 0.0,
            'avg_precision': 0.0
        }
        
        # Calculate metrics slice by slice and average
        depth = pred.shape[0]
        if depth == 0:  # Handle empty data
            return metrics_3d
            
        for z in range(depth):
            slice_metrics = self._calculate_2d_metrics(pred[z], target[z])
            for k, v in slice_metrics.items():
                metrics_3d[k] += v
        
        # Average metrics across slices
        for k in metrics_3d:
            metrics_3d[k] /= depth
            
        return metrics_3d
    
    def _find_matching_pairs(self, pred_props, target_props, pred_labels, target_labels):
        """Find matching spot pairs with improved stability"""
        matched_pairs = []
        
        for pred_prop in pred_props:
            pred_mask = pred_labels == pred_prop.label
            best_iou = self.iou_threshold
            best_target = None
            
            for target_prop in target_props:
                # Skip already matched targets
                if target_prop in [pair[1] for pair in matched_pairs]:
                    continue
                    
                # Calculate IoU with improved stability
                target_mask = target_labels == target_prop.label
                intersection = np.logical_and(pred_mask, target_mask).sum()
                union = np.logical_or(pred_mask, target_mask).sum()
                iou = intersection / max(union, 1)  # Avoid division by zero
                
                # Update best match if IoU is higher
                if iou > best_iou:
                    best_iou = iou
                    best_target = target_prop
            
            # Add pair if match found
            if best_target is not None:
                matched_pairs.append((pred_prop, best_target))
        
        return matched_pairs
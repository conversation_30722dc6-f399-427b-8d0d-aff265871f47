#!/usr/bin/env python3
"""
Fixed Single Image Inference Script for Spot Detection
Addresses grid pattern issues in heatmap generation
"""

import torch
import torch.nn as nn
import numpy as np
import cv2
import matplotlib.pyplot as plt
from skimage import io, measure, morphology
from skimage.feature import peak_local_max
from skimage.filters import gaussian
from scipy.ndimage import distance_transform_edt
import argparse
import os
from typing import Dict, List, Tuple, Optional, Union, Any

# Import your model
from OptimizedSpotDetection_model import OptimizedSpotDetectionModel

class FixedSpotPredictor:
    """
    Fixed predictor that addresses grid pattern issues
    """
    
    def __init__(self, 
                 model_path: str,
                 device: str = 'cuda',
                 threshold: float = 0.3,
                 min_distance: int = 8,
                 min_intensity: float = 0.1):
        """
        Initialize the fixed predictor
        
        Args:
            model_path: Path to trained model
            device: Device to use
            threshold: Detection threshold
            min_distance: Minimum distance between peaks
            min_intensity: Minimum intensity for peak detection
        """
        self.device = torch.device(device if torch.cuda.is_available() else 'cpu')
        self.threshold = threshold
        self.min_distance = min_distance
        self.min_intensity = min_intensity
        
        # Load model
        self.model = self._load_model(model_path)
        self.model.eval()
        
    def _load_model(self, model_path: str) -> nn.Module:
        """Load the trained model"""
        # Create model instance
        model = OptimizedSpotDetectionModel(
            in_channels=1,
            base_filters=32,
            num_experts=4,
            dropout_rate=0.1
        )
        
        # Load checkpoint
        if os.path.exists(model_path):
            checkpoint = torch.load(model_path, map_location=self.device)
            if 'model_state_dict' in checkpoint:
                model.load_state_dict(checkpoint['model_state_dict'])
            else:
                model.load_state_dict(checkpoint)
            print(f"Loaded model from {model_path}")
        else:
            print(f"Warning: Model file {model_path} not found. Using random weights.")
        
        return model.to(self.device)
    
    def preprocess_image(self, image_path: str) -> torch.Tensor:
        """
        Preprocess image with proper normalization
        """
        # Load image
        if isinstance(image_path, str):
            image = io.imread(image_path)
        else:
            image = image_path
            
        # Convert to grayscale if needed
        if len(image.shape) == 3:
            if image.shape[2] == 3:
                image = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
            elif image.shape[2] == 4:
                image = cv2.cvtColor(image, cv2.COLOR_RGBA2GRAY)
        
        # Normalize to [0, 1]
        if image.dtype == np.uint8:
            image = image.astype(np.float32) / 255.0
        else:
            image = image.astype(np.float32)
            if image.max() > 1.0:
                image = image / image.max()
        
        # Convert to tensor with proper dimensions
        image_tensor = torch.from_numpy(image).unsqueeze(0).unsqueeze(0)  # [1, 1, H, W]
        
        return image_tensor.to(self.device)
    
    def predict_single_image(self, image_path: str) -> Dict[str, Any]:
        """
        Predict spots in a single image with fixed grid pattern issues
        """
        # Preprocess image
        image_tensor = self.preprocess_image(image_path)
        original_image = image_tensor.squeeze().cpu().numpy()
        
        # Run inference
        with torch.no_grad():
            # Get model output
            raw_output = self.model(image_tensor)
            
            # Handle different output formats
            if isinstance(raw_output, dict):
                heatmap = raw_output.get('heatmap', raw_output.get('output', raw_output))
            else:
                heatmap = raw_output
            
            # Apply sigmoid and convert to numpy
            heatmap_sigmoid = torch.sigmoid(heatmap).squeeze().cpu().numpy()
        
        # Post-process heatmap to reduce grid artifacts
        processed_heatmap = self._post_process_heatmap(heatmap_sigmoid)
        
        # Find spots using adaptive peak detection
        spots = self._find_spots_adaptive(processed_heatmap)
        
        return {
            'original_image': original_image,
            'raw_heatmap': heatmap_sigmoid,
            'processed_heatmap': processed_heatmap,
            'spots': spots,
            'num_spots': len(spots)
        }
    
    def _post_process_heatmap(self, heatmap: np.ndarray) -> np.ndarray:
        """
        Post-process heatmap to reduce grid artifacts
        """
        # Apply Gaussian smoothing to reduce grid artifacts
        smoothed = gaussian(heatmap, sigma=1.0, preserve_range=True)
        
        # Enhance local maxima while suppressing uniform responses
        # Use difference of Gaussians to highlight blob-like structures
        dog = gaussian(heatmap, sigma=0.5) - gaussian(heatmap, sigma=2.0)
        dog = np.clip(dog, 0, 1)
        
        # Combine original and DoG-filtered responses
        processed = 0.7 * smoothed + 0.3 * dog
        processed = np.clip(processed, 0, 1)
        
        return processed
    
    def _find_spots_adaptive(self, heatmap: np.ndarray) -> List[Dict]:
        """
        Find spots using adaptive parameters based on heatmap statistics
        """
        # Adaptive threshold based on heatmap statistics
        mean_intensity = np.mean(heatmap)
        std_intensity = np.std(heatmap)
        adaptive_threshold = max(self.min_intensity, mean_intensity + 2 * std_intensity)
        
        # Find local maxima with adaptive parameters
        coordinates = peak_local_max(
            heatmap,
            min_distance=self.min_distance,
            threshold_abs=adaptive_threshold,
            exclude_border=True,
            num_peaks=100  # Limit maximum number of peaks
        )
        
        # Extract spot properties
        spots = []
        for i, (y, x) in enumerate(coordinates):
            intensity = heatmap[y, x]
            
            # Estimate spot size based on local intensity profile
            spot_size = self._estimate_spot_size(heatmap, y, x)
            
            spots.append({
                'id': i + 1,
                'centroid': (y, x),
                'intensity': intensity,
                'estimated_size': spot_size,
                'confidence': min(1.0, intensity / adaptive_threshold)
            })
        
        # Sort by intensity (highest first)
        spots.sort(key=lambda x: x['intensity'], reverse=True)
        
        return spots
    
    def _estimate_spot_size(self, heatmap: np.ndarray, y: int, x: int, max_radius: int = 10) -> float:
        """
        Estimate spot size based on local intensity profile
        """
        h, w = heatmap.shape
        center_intensity = heatmap[y, x]
        
        # Check intensity profile in concentric circles
        for r in range(1, max_radius + 1):
            if y - r < 0 or y + r >= h or x - r < 0 or x + r >= w:
                break
                
            # Sample points on circle
            circle_intensities = []
            for angle in np.linspace(0, 2*np.pi, 8, endpoint=False):
                cy = int(y + r * np.sin(angle))
                cx = int(x + r * np.cos(angle))
                if 0 <= cy < h and 0 <= cx < w:
                    circle_intensities.append(heatmap[cy, cx])
            
            # If intensity drops significantly, this is likely the spot boundary
            if circle_intensities and np.mean(circle_intensities) < center_intensity * 0.5:
                return r
        
        return max_radius
    
    def visualize_results(self, result: Dict[str, Any], save_path: Optional[str] = None):
        """
        Visualize prediction results with fixed orientation
        """
        fig, axes = plt.subplots(2, 2, figsize=(12, 12))
        
        # Original image
        axes[0, 0].imshow(result['original_image'], cmap='gray')
        axes[0, 0].set_title('Original Image')
        axes[0, 0].axis('off')
        
        # Raw heatmap
        axes[0, 1].imshow(result['raw_heatmap'], cmap='hot')
        axes[0, 1].set_title('Raw Model Output')
        axes[0, 1].axis('off')
        
        # Processed heatmap
        axes[1, 0].imshow(result['processed_heatmap'], cmap='hot')
        axes[1, 0].set_title('Processed Heatmap')
        axes[1, 0].axis('off')
        
        # Detected spots overlay
        overlay = np.stack([result['original_image']] * 3, axis=-1)
        
        for spot in result['spots']:
            y, x = spot['centroid']
            size = max(3, int(spot['estimated_size']))
            
            # Draw circle
            cv2.circle(overlay, (x, y), size, (1, 0, 0), 2)
            
            # Add spot ID
            cv2.putText(overlay, str(spot['id']), (x-10, y-10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 1, 0), 1)
        
        axes[1, 1].imshow(overlay)
        axes[1, 1].set_title(f'Detected Spots: {result["num_spots"]}')
        axes[1, 1].axis('off')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Results saved to {save_path}")
        
        plt.show()
    
    def analyze_grid_pattern(self, heatmap: np.ndarray) -> Dict[str, float]:
        """
        Analyze if the heatmap shows grid-like patterns
        """
        # Compute 2D FFT to detect periodic patterns
        fft = np.fft.fft2(heatmap)
        fft_magnitude = np.abs(fft)
        
        # Look for peaks in frequency domain that indicate grid patterns
        h, w = fft_magnitude.shape
        center_h, center_w = h // 2, w // 2
        
        # Check for regular spacing in frequency domain
        # High values away from DC component indicate periodic patterns
        freq_energy = np.sum(fft_magnitude[center_h-10:center_h+10, center_w-10:center_w+10])
        total_energy = np.sum(fft_magnitude)
        
        grid_score = 1.0 - (freq_energy / total_energy)
        
        return {
            'grid_score': grid_score,
            'uniformity': np.std(heatmap) / np.mean(heatmap),
            'peak_count': len(peak_local_max(heatmap, min_distance=5))
        }

def main():
    parser = argparse.ArgumentParser(description='Fixed Single Image Spot Detection')
    parser.add_argument('--image', type=str, required=True, help='Path to input image')
    parser.add_argument('--model', type=str, required=True, help='Path to model checkpoint')
    parser.add_argument('--output', type=str, help='Path to save results')
    parser.add_argument('--threshold', type=float, default=0.3, help='Detection threshold')
    parser.add_argument('--min-distance', type=int, default=8, help='Minimum distance between spots')
    parser.add_argument('--device', type=str, default='cuda', help='Device to use')
    
    args = parser.parse_args()
    
    # Create predictor
    predictor = FixedSpotPredictor(
        model_path=args.model,
        device=args.device,
        threshold=args.threshold,
        min_distance=args.min_distance
    )
    
    # Run prediction
    print(f"Processing image: {args.image}")
    result = predictor.predict_single_image(args.image)
    
    # Analyze for grid patterns
    grid_analysis = predictor.analyze_grid_pattern(result['raw_heatmap'])
    print(f"\nGrid Pattern Analysis:")
    print(f"Grid Score: {grid_analysis['grid_score']:.3f} (lower = more grid-like)")
    print(f"Uniformity: {grid_analysis['uniformity']:.3f}")
    print(f"Peak Count: {grid_analysis['peak_count']}")
    
    # Print results
    print(f"\nDetection Results:")
    print(f"Number of spots detected: {result['num_spots']}")
    
    for i, spot in enumerate(result['spots'][:10]):  # Show top 10
        print(f"Spot {spot['id']}: position=({spot['centroid'][1]}, {spot['centroid'][0]}), "
              f"intensity={spot['intensity']:.3f}, size={spot['estimated_size']:.1f}")
    
    # Visualize results
    save_path = args.output if args.output else None
    predictor.visualize_results(result, save_path)

if __name__ == "__main__":
    main()
# Test what's causing large predictions

# 1. Check if it's the decoder upsampling
# In your model forward(), add this debug line:
print(f"Feature sizes: d1={d1.shape}, feat={feat.shape}")
print(f"Semantic head input size: {feat.shape}")

# 2. Test with direct prediction (no upsampling)
# Replace the final interpolation:
# feat = F.interpolate(d1, size=x.shape[2:], mode='bilinear', align_corners=False)
# With:
feat = d1  # Use d1 directly, then upsample only the final outputs

# 3. Check if boundary is also large due to same issue
# Both semantic and boundary use the same 'feat' tensor

# 4. For inference, you can post-process to shrink predictions:
def shrink_predictions(semantic_pred, boundary_pred):
    import cv2
    kernel = np.ones((3,3), np.uint8)
    
    # Erode semantic
    sem_eroded = cv2.erode((semantic_pred > 0.5).astype(np.uint8), kernel, iterations=1)
    
    # Erode boundary  
    bnd_eroded = cv2.erode((boundary_pred > 0.5).astype(np.uint8), kernel, iterations=1)
    
    return sem_eroded.astype(np.float32), bnd_eroded.astype(np.float32)
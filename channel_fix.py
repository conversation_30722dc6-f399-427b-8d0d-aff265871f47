# Fix the channel mismatch in skip connections

# Replace the skip connection definitions with:
self.skip4 = nn.Conv2d(base_ch*16, base_ch*8, 1, bias=False)  # 512->256
self.skip3 = nn.Conv2d(base_ch*8, base_ch*4, 1, bias=False)   # 256->128  
self.skip2 = nn.Conv2d(base_ch*4, base_ch*2, 1, bias=False)   # 128->64
self.skip1 = nn.Conv2d(base_ch*2, base_ch, 1, bias=False)     # 64->32

# The skip connections should match decoder output channels, not encoder channels
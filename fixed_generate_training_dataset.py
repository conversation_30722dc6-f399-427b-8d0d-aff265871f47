# Fixed version of generate_training_dataset function

def generate_training_dataset_fixed(num_samples, 
                                  output_dir="training_dataset_fixed",
                                  image_size=(512, 512),
                                  min_spots=50,
                                  max_spots=220,
                                  mask_threshold=0.35,  # Now properly controls mask size
                                  save_as_8bit_tiff=False):
    """
    FIXED: Generate a training dataset with synthetic spot data
    
    The mask threshold now works correctly - higher values create smaller masks,
    lower values create larger masks, consistently across all spot intensities.
    
    Args:
        num_samples: Number of training samples to generate
        output_dir: Directory to save the training dataset
        image_size: Size of generated images (height, width)
        min_spots: Minimum number of spots per image
        max_spots: Maximum number of spots per image
        mask_threshold: Controls mask size (0.1=large, 0.5=small, 0.35=balanced)
        save_as_8bit_tiff: Whether to save as 8-bit TIFF files
    
    Returns:
        SyntheticDataSaver: The data saver instance used for generation
    """
    print(f"🏋️ Generating FIXED training dataset with {num_samples} samples...")
    print(f"📏 Mask threshold: {mask_threshold} (lower=larger masks, higher=smaller masks)")
    
    # Create training-specific parameters with FIXED mask threshold
    training_params = {
        'image_size': image_size,
        'min_spots': min_spots,
        'max_spots': max_spots,
        'min_radius': 2,
        'max_radius': 6,
        'density_factor': 2.5,
        'mask_threshold': mask_threshold,  # Now works correctly!
        'allow_touching': True,
        'shape_variation': 0.2,
        'add_gradients': True,
        'realistic_noise': True
    }
    
    # Create synthetic data saver with FIXED generator
    class FixedSyntheticDataSaver:
        def __init__(self, synthetic_params):
            from fixed_enhanced_synthetic_generator import EnhancedSyntheticSpotGenerator
            self.generator = EnhancedSyntheticSpotGenerator(**synthetic_params)
            self.params = synthetic_params
        
        def save_dataset_to_disk(self, num_samples, output_dir, save_visualizations=True, 
                                image_format="tif", mask_format="tif"):
            import os
            import tifffile
            from tqdm import tqdm
            
            # Create directories
            os.makedirs(output_dir, exist_ok=True)
            os.makedirs(os.path.join(output_dir, "images"), exist_ok=True)
            os.makedirs(os.path.join(output_dir, "masks"), exist_ok=True)
            if save_visualizations:
                os.makedirs(os.path.join(output_dir, "visualizations"), exist_ok=True)
            
            print(f"🔄 Generating and saving {num_samples} synthetic samples to {output_dir}...")
            
            for i in tqdm(range(num_samples), desc="Generating samples"):
                # Generate sample
                image, mask, spot_data = self.generator.generate_sample()
                
                # Save image
                img_path = os.path.join(output_dir, "images", f"synthetic_{i:06d}.{image_format}")
                if image_format.lower() == "tif":
                    tifffile.imwrite(img_path, (image * 65535).astype(np.uint16))
                
                # Save mask
                mask_path = os.path.join(output_dir, "masks", f"synthetic_{i:06d}.{mask_format}")
                if mask_format.lower() == "tif":
                    tifffile.imwrite(mask_path, mask.astype(np.uint16))
                
                # Save visualization if requested
                if save_visualizations:
                    vis_path = os.path.join(output_dir, "visualizations", f"synthetic_{i:06d}_vis.png")
                    
                    import matplotlib.pyplot as plt
                    fig, axes = plt.subplots(1, 3, figsize=(15, 5))
                    
                    axes[0].imshow(image, cmap='gray')
                    axes[0].set_title('Image')
                    axes[0].axis('off')
                    
                    axes[1].imshow(mask, cmap='nipy_spectral')
                    axes[1].set_title(f'Mask ({len(spot_data)} spots)')
                    axes[1].axis('off')
                    
                    axes[2].imshow(image, cmap='gray')
                    axes[2].imshow(mask > 0, cmap='Reds', alpha=0.5)
                    axes[2].set_title('Overlay')
                    axes[2].axis('off')
                    
                    plt.tight_layout()
                    plt.savefig(vis_path, dpi=100, bbox_inches='tight')
                    plt.close()
            
            print(f"✅ Successfully saved {num_samples} samples to {output_dir}")
            print(f"📁 Dataset structure:")
            print(f"   {output_dir}/")
            print(f"   ├── images/         - Original synthetic images (16-bit TIF)")
            print(f"   ├── masks/          - Instance masks with unique spot IDs (16-bit TIF)")
            if save_visualizations:
                print(f"   └── visualizations/ - Visual overlays showing spots and IDs")
    
    data_saver = FixedSyntheticDataSaver(training_params)
    
    # Generate and save the dataset
    data_saver.save_dataset_to_disk(
        num_samples=num_samples,
        output_dir=output_dir,
        save_visualizations=True,
        image_format="tif",
        mask_format="tif"
    )
    
    print(f"✅ FIXED training dataset generated successfully!")
    print(f"📁 Location: {output_dir}")
    print(f"🔧 Mask threshold {mask_threshold} applied correctly")
    
    return data_saver

# Test function to compare mask sizes
def test_mask_threshold_values():
    """Test different mask threshold values to find the best one"""
    
    thresholds = [0.15, 0.25, 0.35, 0.45, 0.55]
    
    print("Testing different mask threshold values...")
    
    for threshold in thresholds:
        print(f"\n--- Testing threshold: {threshold} ---")
        
        # Generate a small test dataset
        test_saver = generate_training_dataset_fixed(
            num_samples=5,
            output_dir=f"test_threshold_{threshold:.2f}",
            image_size=(256, 256),
            min_spots=20,
            max_spots=40,
            mask_threshold=threshold,
            save_as_8bit_tiff=False
        )
        
        print(f"Generated test dataset with threshold {threshold}")

# Usage example:
# Replace your original function call with:
# training_data_saver = generate_training_dataset_fixed(300, mask_threshold=0.35)
import numpy as np
import torch
import matplotlib.pyplot as plt
from typing import Dict, List, Optional
import time
import os

class CompactTrainingLogger:
    """
    A compact logger for spot detection training that focuses on essential metrics
    and confidence score distributions.
    """
    
    def __init__(
        self,
        log_interval: int = 10,
        save_dir: Optional[str] = None,
        track_confidence: bool = True
    ):
        """
        Initialize the compact logger
        
        Args:
            log_interval: How often to log (in batches)
            save_dir: Directory to save plots
            track_confidence: Whether to track confidence score distributions
        """
        self.log_interval = log_interval
        self.save_dir = save_dir
        self.track_confidence = track_confidence
        
        # Create save directory if needed
        if save_dir is not None:
            os.makedirs(save_dir, exist_ok=True)
        
        # Initialize history
        self.history = {
            'train_loss': [],
            'val_loss': [],
            'train_metrics': {},
            'val_metrics': {},
            'confidence_stats': [],
            'learning_rates': []
        }
        
        # Track time
        self.start_time = time.time()
        self.last_log_time = self.start_time
    
    def log_batch(
        self,
        epoch: int,
        batch_idx: int,
        loss: float,
        metrics: Dict[str, float],
        outputs: torch.Tensor,
        optimizer: torch.optim.Optimizer,
        total_batches: int
    ):
        """
        Log training progress for a batch
        
        Args:
            epoch: Current epoch
            batch_idx: Current batch index
            loss: Training loss
            metrics: Dictionary of metrics
            outputs: Model outputs (for confidence tracking)
            optimizer: Optimizer (for learning rate tracking)
            total_batches: Total number of batches in epoch
        """
        # Only log at specified intervals
        if batch_idx % self.log_interval != 0:
            return
        
        # Get current time and calculate elapsed
        current_time = time.time()
        elapsed = current_time - self.last_log_time
        self.last_log_time = current_time
        
        # Get learning rate
        lr = optimizer.param_groups[0]['lr']
        self.history['learning_rates'].append(lr)
        
        # Track loss
        self.history['train_loss'].append(loss)
        
        # Track metrics
        for key, value in metrics.items():
            if key not in self.history['train_metrics']:
                self.history['train_metrics'][key] = []
            self.history['train_metrics'][key].append(value)
        
        # Track confidence scores if enabled
        if self.track_confidence and outputs is not None:
            # Apply sigmoid to get confidence scores
            if isinstance(outputs, dict) and 'output' in outputs:
                pred = outputs['output']
            else:
                pred = outputs
                
            if isinstance(pred, torch.Tensor):
                confidence = torch.sigmoid(pred).detach().cpu().numpy()
                
                # Calculate statistics
                conf_stats = {
                    'min': float(np.min(confidence)),
                    'max': float(np.max(confidence)),
                    'mean': float(np.mean(confidence)),
                    'median': float(np.median(confidence)),
                    'p25': float(np.percentile(confidence, 25)),
                    'p75': float(np.percentile(confidence, 75)),
                    'epoch': epoch,
                    'batch': batch_idx
                }
                
                self.history['confidence_stats'].append(conf_stats)
        
        # Print compact log message
        progress = f"[{batch_idx}/{total_batches}]"
        log_msg = f"Epoch {epoch} {progress} Loss: {loss:.4f}, "
        
        # Add top 2 metrics
        metric_str = ", ".join([f"{k}: {v:.4f}" for k, v in list(metrics.items())[:2]])
        log_msg += metric_str
        
        # Add confidence if tracked
        if self.track_confidence and len(self.history['confidence_stats']) > 0:
            conf = self.history['confidence_stats'][-1]
            log_msg += f", Conf: {conf['mean']:.3f} [{conf['min']:.2f}-{conf['max']:.2f}]"
        
        # Add learning rate and time
        log_msg += f", lr: {lr:.6f}, {elapsed:.1f}s"
        
        print(log_msg)
    
    def log_epoch(
        self,
        epoch: int,
        train_loss: float,
        val_loss: float,
        train_metrics: Dict[str, float],
        val_metrics: Dict[str, float],
        val_outputs: Optional[torch.Tensor] = None
    ):
        """
        Log results at the end of an epoch
        
        Args:
            epoch: Current epoch
            train_loss: Average training loss
            val_loss: Validation loss
            train_metrics: Training metrics
            val_metrics: Validation metrics
            val_outputs: Validation outputs (for confidence tracking)
        """
        # Track losses
        if train_loss is not None:
            self.history['train_loss'].append(train_loss)
        if val_loss is not None:
            self.history['val_loss'].append(val_loss)
        
        # Track metrics
        for key, value in train_metrics.items():
            if key not in self.history['train_metrics']:
                self.history['train_metrics'][key] = []
            self.history['train_metrics'][key].append(value)
            
        for key, value in val_metrics.items():
            if key not in self.history['val_metrics']:
                self.history['val_metrics'][key] = []
            self.history['val_metrics'][key].append(value)
        
        # Track validation confidence if enabled
        if self.track_confidence and val_outputs is not None:
            # Apply sigmoid to get confidence scores
            if isinstance(val_outputs, dict) and 'output' in val_outputs:
                pred = val_outputs['output']
            else:
                pred = val_outputs
                
            if isinstance(pred, torch.Tensor):
                confidence = torch.sigmoid(pred).detach().cpu().numpy()
                
                # Calculate statistics
                conf_stats = {
                    'min': float(np.min(confidence)),
                    'max': float(np.max(confidence)),
                    'mean': float(np.mean(confidence)),
                    'median': float(np.median(confidence)),
                    'p25': float(np.percentile(confidence, 25)),
                    'p75': float(np.percentile(confidence, 75)),
                    'epoch': epoch,
                    'batch': -1  # Indicates end of epoch
                }
                
                self.history['confidence_stats'].append(conf_stats)
        
        # Calculate elapsed time
        elapsed = time.time() - self.start_time
        hours, remainder = divmod(elapsed, 3600)
        minutes, seconds = divmod(remainder, 60)
        
        # Print epoch summary
        print(f"\nEpoch {epoch} completed in {int(hours)}h {int(minutes)}m {int(seconds)}s")
        print(f"Train Loss: {train_loss:.4f}, Val Loss: {val_loss:.4f}")
        
        # Print metrics (limit to top 3)
        train_metrics_str = ", ".join([f"{k}: {v:.4f}" for k, v in list(train_metrics.items())[:3]])
        val_metrics_str = ", ".join([f"{k}: {v:.4f}" for k, v in list(val_metrics.items())[:3]])
        
        print(f"Train Metrics: {train_metrics_str}")
        print(f"Val Metrics: {val_metrics_str}")
        
        # Print confidence stats if available
        if self.track_confidence and len(self.history['confidence_stats']) > 0:
            conf = self.history['confidence_stats'][-1]
            print(f"Confidence Range: {conf['mean']:.3f} [{conf['min']:.2f}-{conf['max']:.2f}]")
        
        print("-" * 50)
    
    def plot_confidence_evolution(self, save: bool = True):
        """
        Plot how confidence scores evolve during training
        
        Args:
            save: Whether to save the plot
        """
        if not self.track_confidence or len(self.history['confidence_stats']) == 0:
            print("No confidence statistics available")
            return
        
        # Extract data
        epochs = [stat['epoch'] for stat in self.history['confidence_stats']]
        means = [stat['mean'] for stat in self.history['confidence_stats']]
        mins = [stat['min'] for stat in self.history['confidence_stats']]
        maxs = [stat['max'] for stat in self.history['confidence_stats']]
        p25 = [stat['p25'] for stat in self.history['confidence_stats']]
        p75 = [stat['p75'] for stat in self.history['confidence_stats']]
        
        # Create figure
        plt.figure(figsize=(10, 6))
        
        # Plot mean with min-max range
        plt.plot(epochs, means, 'b-', label='Mean Confidence')
        plt.fill_between(epochs, mins, maxs, color='blue', alpha=0.1, label='Min-Max Range')
        plt.fill_between(epochs, p25, p75, color='blue', alpha=0.3, label='25-75 Percentile')
        
        # Add horizontal line at 0.5 (threshold)
        plt.axhline(y=0.5, color='r', linestyle='--', label='Threshold (0.5)')
        
        # Add labels and title
        plt.xlabel('Epoch')
        plt.ylabel('Confidence Score')
        plt.title('Evolution of Confidence Scores During Training')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # Save if requested
        if save and self.save_dir is not None:
            save_path = os.path.join(self.save_dir, 'confidence_evolution.png')
            plt.savefig(save_path)
            print(f"Saved confidence evolution plot to {save_path}")
        
        plt.show()
    
    def plot_metrics(self, save: bool = True):
        """
        Plot training and validation metrics
        
        Args:
            save: Whether to save the plot
        """
        # Create figure with subplots
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # Plot losses
        axes[0, 0].plot(self.history['train_loss'], label='Train Loss')
        axes[0, 0].plot(self.history['val_loss'], label='Val Loss')
        axes[0, 0].set_title('Loss')
        axes[0, 0].set_xlabel('Epoch')
        axes[0, 0].set_ylabel('Loss')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)
        
        # Plot learning rate
        axes[0, 1].plot(self.history['learning_rates'])
        axes[0, 1].set_title('Learning Rate')
        axes[0, 1].set_xlabel('Update Step')
        axes[0, 1].set_ylabel('Learning Rate')
        axes[0, 1].grid(True, alpha=0.3)
        
        # Plot metrics (choose 2 most important)
        metric_keys = list(self.history['train_metrics'].keys())
        if len(metric_keys) > 0:
            key1 = metric_keys[0]
            axes[1, 0].plot(self.history['train_metrics'][key1], label=f'Train {key1}')
            if key1 in self.history['val_metrics']:
                axes[1, 0].plot(self.history['val_metrics'][key1], label=f'Val {key1}')
            axes[1, 0].set_title(key1)
            axes[1, 0].set_xlabel('Epoch')
            axes[1, 0].set_ylabel('Value')
            axes[1, 0].legend()
            axes[1, 0].grid(True, alpha=0.3)
        
        if len(metric_keys) > 1:
            key2 = metric_keys[1]
            axes[1, 1].plot(self.history['train_metrics'][key2], label=f'Train {key2}')
            if key2 in self.history['val_metrics']:
                axes[1, 1].plot(self.history['val_metrics'][key2], label=f'Val {key2}')
            axes[1, 1].set_title(key2)
            axes[1, 1].set_xlabel('Epoch')
            axes[1, 1].set_ylabel('Value')
            axes[1, 1].legend()
            axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # Save if requested
        if save and self.save_dir is not None:
            save_path = os.path.join(self.save_dir, 'training_metrics.png')
            plt.savefig(save_path)
            print(f"Saved metrics plot to {save_path}")
        
        plt.show()
        
        # Also plot confidence evolution
        self.plot_confidence_evolution(save)


# Example usage:
"""
# Initialize logger
logger = CompactTrainingLogger(
    log_interval=10,  # Log every 10 batches
    save_dir='./training_logs',
    track_confidence=True
)

# During training loop:
for epoch in range(num_epochs):
    # Training loop
    for batch_idx, batch in enumerate(train_loader):
        # Forward pass, loss calculation, etc.
        
        # Log batch results
        logger.log_batch(
            epoch=epoch,
            batch_idx=batch_idx,
            loss=loss.item(),
            metrics={'accuracy': accuracy, 'f1': f1_score},
            outputs=outputs,  # Model outputs for confidence tracking
            optimizer=optimizer,
            total_batches=len(train_loader)
        )
    
    # Validation
    # ...
    
    # Log epoch results
    logger.log_epoch(
        epoch=epoch,
        train_loss=avg_train_loss,
        val_loss=avg_val_loss,
        train_metrics={'accuracy': train_acc, 'f1': train_f1},
        val_metrics={'accuracy': val_acc, 'f1': val_f1},
        val_outputs=val_outputs  # Optional
    )

# After training, plot results
logger.plot_metrics()
logger.plot_confidence_evolution()
"""
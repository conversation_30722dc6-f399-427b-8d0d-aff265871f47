import numpy as np
import matplotlib.pyplot as plt
from scipy import ndimage
from skimage.feature import peak_local_max
from skimage import measure
from scipy.ndimage import gaussian_filter

def detect_peaks_with_mask_labels(image, min_distance=3, threshold_abs=0.1, threshold_rel=0.2, 
                                 sigma=1.0, mask=None, exclude_border=True):
    """
    Combo approach: First detect peaks to get centroids, then use the actual mask for labels.
    
    Parameters:
    -----------
    image : ndarray
        Input image for spot detection
    min_distance : int
        Minimum distance between peaks
    threshold_abs : float
        Minimum intensity threshold for peak detection
    threshold_rel : float
        Relative threshold for peak detection
    sigma : float
        Sigma for Gaussian filter
    mask : ndarray, optional
        Binary mask to restrict peak detection
    exclude_border : bool or int
        If True, exclude peaks near the border
        
    Returns:
    --------
    centroids : ndarray
        Array of peak coordinates [y, x]
    labeled_mask : ndarray
        Labeled mask where each spot has a unique integer ID
    num_spots : int
        Number of detected spots
    """
    # Step 1: Apply Gaussian filter to smooth the image
    smoothed_image = gaussian_filter(image, sigma=sigma)
    
    # Step 2: Find local maxima (peaks) to get centroids
    coordinates = peak_local_max(smoothed_image, min_distance=min_distance,
                                threshold_abs=threshold_abs,
                                threshold_rel=threshold_rel,
                                exclude_border=exclude_border)
    
    # Step 3: If a mask is provided, use it for labeling
    if mask is not None:
        # Use the provided mask
        binary_mask = mask > 0
    else:
        # Create a binary mask based on thresholding
        binary_mask = smoothed_image > threshold_abs
    
    # Step 4: Label the mask
    labeled_mask, num_spots = ndimage.label(binary_mask)
    
    # Step 5: If we have more centroids than labeled regions, adjust
    if len(coordinates) > num_spots:
        # Keep only the strongest peaks up to num_spots
        if len(coordinates) > 0:
            peak_intensities = [smoothed_image[y, x] for y, x in coordinates]
            sorted_indices = np.argsort(peak_intensities)[::-1]  # Sort by intensity (descending)
            coordinates = coordinates[sorted_indices[:num_spots]]
    
    # Step 6: If we have more labeled regions than centroids, use region centroids
    elif num_spots > len(coordinates):
        # Use region properties to find centroids
        regions = measure.regionprops(labeled_mask)
        coordinates = np.array([region.centroid for region in regions]).astype(int)
    
    return coordinates, labeled_mask, num_spots

def visualize_combo_detection(image, coordinates, labeled_mask, figsize=(12, 4)):
    """
    Visualize the results of the combo peak detection approach.
    
    Parameters:
    -----------
    image : ndarray
        Original input image
    coordinates : ndarray
        Array of peak coordinates [y, x]
    labeled_mask : ndarray
        Labeled mask where each spot has a unique integer ID
    figsize : tuple
        Figure size
    """
    fig, axes = plt.subplots(1, 3, figsize=figsize)
    
    # Original image
    axes[0].imshow(image, cmap='gray')
    axes[0].set_title('Original Image')
    axes[0].axis('off')
    
    # Image with detected peaks
    axes[1].imshow(image, cmap='gray')
    axes[1].plot(coordinates[:, 1], coordinates[:, 0], 'r+', markersize=10)
    axes[1].set_title(f'Detected Peaks: {len(coordinates)}')
    axes[1].axis('off')
    
    # Labeled mask
    axes[2].imshow(labeled_mask, cmap='nipy_spectral')
    axes[2].set_title(f'Labeled Mask: {np.max(labeled_mask)} regions')
    axes[2].axis('off')
    
    plt.tight_layout()
    plt.show()
    
    return fig
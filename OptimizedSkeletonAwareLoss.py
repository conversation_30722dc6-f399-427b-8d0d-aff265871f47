import torch
import torch.nn as nn
import torch.nn.functional as F

class QuantizedSDTLoss(nn.Module):
    """Fixed and optimized SDT loss"""
    def __init__(self, num_bins=10, background_value=0.0):
        super().__init__()
        self.num_bins = num_bins
        self.background_value = background_value
        self.eps = 1e-6

    def forward(self, pred, target, mask=None):
        """
        Args:
            pred: Predicted SDT (B, num_bins+1, H, W)
            target: Target SDT (B, 1, H, W) with values in [0,1]
            mask: Optional mask for valid regions (B, 1, H, W)
        """
        target_sq = target.squeeze(1)
        
        # Simple hard target assignment (proven to work)
        if mask is not None:
            mask_sq = mask.squeeze(1)
            target_classes = torch.zeros_like(target_sq, dtype=torch.long)
            for i in range(self.num_bins):
                bin_min = i / self.num_bins
                bin_max = (i + 1) / self.num_bins
                bin_mask = (target_sq > bin_min) & (target_sq <= bin_max) & (mask_sq > 0)
                target_classes[bin_mask] = i + 1
        else:
            target_classes = torch.clamp((target_sq * self.num_bins).long(), 0, self.num_bins)

        base_loss = F.cross_entropy(pred, target_classes, reduction='none')
        
        # Simple boundary weighting
        boundary_weight = 1.0 + 0.3 * (1.0 - target_sq)
        weighted_loss = base_loss * boundary_weight

        if mask is not None:
            weighted_loss = weighted_loss * mask.squeeze(1)
            return weighted_loss.sum() / (mask.sum() + self.eps)
        return weighted_loss.mean()

class SkeletonAwareLoss(nn.Module):
    """Optimized loss function for skeleton-aware spot detection"""
    def __init__(self):
        super().__init__()
        # Optimized weights for fixed skeleton dataset
        self.w_sem = 1.0
        self.w_sdt = 2.0   # Reduced since SDT is working better now
        self.w_skel = 3.0  # Increased since skeleton is fixed
        self.w_cent = 2.0  # Reduced slightly
        self.w_flow = 1.0  # Reduced since flow is less critical
        
        self.quantized_sdt_loss = QuantizedSDTLoss(num_bins=10, background_value=0.0)
    
    def focal_loss(self, pred, target, alpha=0.25, gamma=2.0, label_smoothing=0.05):
        """Optimized focal loss with reduced label smoothing"""
        target_smooth = target * (1 - label_smoothing) + 0.5 * label_smoothing
        bce = F.binary_cross_entropy_with_logits(pred, target_smooth, reduction='none')
        pt = torch.exp(-bce)
        focal_weight = alpha * (1 - pt) ** gamma
        return (focal_weight * bce).mean()
    
    def dice_loss(self, pred, target, smooth=1e-6):
        """Optimized dice loss"""
        pred_sig = torch.sigmoid(pred)
        intersection = (pred_sig * target).sum(dim=(2, 3))
        union = pred_sig.sum(dim=(2, 3)) + target.sum(dim=(2, 3))
        dice = (2 * intersection + smooth) / (union + smooth)
        return 1 - dice.mean()
    
    def skeleton_loss(self, pred, target, alpha=0.5):
        """Specialized loss for skeleton with gradient preservation"""
        # Use MSE for gradient values (0.6, 1.0) and BCE for binary parts
        mse_loss = F.mse_loss(torch.sigmoid(pred), target, reduction='mean')
        bce_loss = F.binary_cross_entropy_with_logits(pred, (target > 0.5).float(), reduction='mean')
        return alpha * mse_loss + (1 - alpha) * bce_loss
    
    def flow_loss(self, pred_flow, gt_flow, mask):
        """Optimized flow loss with better normalization"""
        if mask.sum() < 1:
            return torch.tensor(0.0, device=pred_flow.device)
        
        # Normalize flow vectors
        pred_norm = F.normalize(pred_flow, p=2, dim=1, eps=1e-6)
        gt_norm = F.normalize(gt_flow, p=2, dim=1, eps=1e-6)
        
        # Cosine similarity loss (better for direction)
        cosine_sim = (pred_norm * gt_norm).sum(dim=1, keepdim=True)
        cosine_loss = 1 - cosine_sim
        
        # L1 loss for magnitude
        magnitude_loss = F.l1_loss(pred_flow, gt_flow, reduction='none')
        
        # Combine losses
        combined_loss = 0.7 * cosine_loss + 0.3 * magnitude_loss
        
        # Apply mask and normalize
        masked_loss = combined_loss * mask
        return masked_loss.sum() / mask.sum()
    
    def forward(self, outputs, targets):
        """Compute optimized total loss"""
        # Extract targets: [semantic, sdt, skeleton, centroid, flow_magnitude, boundary]
        gt_sem = targets[:, 0:1]
        gt_sdt = targets[:, 1:2]
        gt_skel = targets[:, 2:3]
        gt_cent = targets[:, 3:4]
        
        losses = {}
        
        # Semantic loss (focal + dice)
        sem_focal = self.focal_loss(outputs['sem_out'], gt_sem)
        sem_dice = self.dice_loss(outputs['sem_out'], gt_sem)
        losses['semantic'] = sem_focal + sem_dice
        
        # SDT loss with mask
        mask = (gt_sem > 0.5)
        losses['sdt'] = self.quantized_sdt_loss(outputs['sdt_out'], gt_sdt, mask=mask)
        
        # Skeleton loss (specialized for gradient preservation)
        losses['skeleton'] = self.skeleton_loss(outputs['skeleton_out'], gt_skel)
        
        # Centroid loss (focal + dice)
        cent_focal = self.focal_loss(outputs['hm_out'], gt_cent)
        cent_dice = self.dice_loss(outputs['hm_out'], gt_cent)
        losses['centroid'] = cent_focal + cent_dice
        
        # Flow loss (if available) - FIX: flow is single channel magnitude, not 2-channel
        if targets.shape[1] > 4:
            gt_flow_mag = targets[:, 4:5]  # Flow magnitude only
            # Convert to 2-channel flow for comparison
            pred_flow_mag = torch.sqrt(torch.sum(outputs['flow_out'] ** 2, dim=1, keepdim=True) + 1e-6)
            flow_mask = mask.float()
            losses['flow'] = F.mse_loss(pred_flow_mag * flow_mask, gt_flow_mag * flow_mask)
        else:
            losses['flow'] = torch.tensor(0.0, device=outputs['sem_out'].device)
        
        # Total weighted loss
        total = (
            self.w_sem * losses['semantic'] +
            self.w_sdt * losses['sdt'] +
            self.w_skel * losses['skeleton'] +
            self.w_cent * losses['centroid'] +
            self.w_flow * losses['flow']
        )
        
        losses['total'] = total
        return total, losses
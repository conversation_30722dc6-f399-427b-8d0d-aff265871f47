import torch
import numpy as np
from skimage import measure, morphology
from skimage.feature import peak_local_max
from skimage.segmentation import watershed
from scipy.ndimage import distance_transform_edt
import matplotlib.pyplot as plt

def detect_individual_spots(heatmap, threshold=0.2, min_spot_size=3, min_distance=5):
    """
    Detect individual spots from a heatmap using watershed segmentation
    
    Args:
        heatmap: Prediction heatmap (numpy array)
        threshold: Confidence threshold (lower value to detect more spots)
        min_spot_size: Minimum spot size in pixels
        min_distance: Minimum distance between spots
        
    Returns:
        labeled_mask: Labeled mask with unique ID for each spot
        num_spots: Number of detected spots
        spot_props: List of spot properties
    """
    # Create binary mask with lower threshold to detect more spots
    binary_mask = (heatmap > threshold).astype(np.uint8)
    
    # Remove small objects
    if min_spot_size > 0:
        binary_mask = morphology.remove_small_objects(
            binary_mask.astype(bool), min_size=min_spot_size
        ).astype(np.uint8)
    
    # If no spots detected, return empty results
    if binary_mask.sum() == 0:
        return np.zeros_like(binary_mask), 0, []
    
    # Calculate distance transform
    distance = distance_transform_edt(binary_mask)
    
    # Find local maxima (spot centers)
    local_max_coords = peak_local_max(
        distance,
        min_distance=min_distance,
        labels=binary_mask,
        threshold_abs=0.1  # Lower threshold to find more peaks
    )
    
    # If no local maxima found, try with lower threshold
    if len(local_max_coords) == 0:
        local_max_coords = peak_local_max(
            heatmap,  # Use raw heatmap instead of distance transform
            min_distance=min_distance,
            threshold_abs=threshold * 0.8  # Lower threshold by 20%
        )
    
    # Create markers for watershed
    markers = np.zeros_like(binary_mask)
    if len(local_max_coords) > 0:
        markers[tuple(local_max_coords.T)] = np.arange(1, len(local_max_coords) + 1)
    else:
        # If still no markers, return empty results
        return binary_mask, 1, [{
            'id': 1,
            'centroid': (binary_mask.shape[0]//2, binary_mask.shape[1]//2),
            'area': binary_mask.sum(),
            'mean_intensity': heatmap[binary_mask > 0].mean() if binary_mask.sum() > 0 else 0,
            'max_intensity': heatmap.max(),
            'bbox': (0, 0, binary_mask.shape[0], binary_mask.shape[1])
        }]
    
    # Apply watershed segmentation
    labeled_mask = watershed(-distance, markers, mask=binary_mask)
    
    # Get region properties
    props = measure.regionprops(labeled_mask, intensity_image=heatmap)
    
    # Extract spot properties
    spot_props = []
    for prop in props:
        spot_props.append({
            'id': prop.label,
            'centroid': prop.centroid,
            'area': prop.area,
            'mean_intensity': prop.mean_intensity,
            'max_intensity': prop.max_intensity,
            'bbox': prop.bbox
        })
    
    return labeled_mask, len(spot_props), spot_props

def visualize_spot_detection(image, heatmap, threshold=0.2, min_spot_size=3, min_distance=5):
    """
    Visualize spot detection with different thresholds
    
    Args:
        image: Original image
        heatmap: Prediction heatmap
        threshold: Confidence threshold
        min_spot_size: Minimum spot size
        min_distance: Minimum distance between spots
    """
    # Detect spots with different thresholds
    thresholds = [0.1, 0.2, 0.3, 0.4, 0.5]
    
    # Create figure
    fig, axes = plt.subplots(2, 3, figsize=(15, 10))
    
    # Plot original image
    axes[0, 0].imshow(image, cmap='gray')
    axes[0, 0].set_title('Original Image')
    axes[0, 0].axis('off')
    
    # Plot heatmap
    axes[0, 1].imshow(heatmap, cmap='hot')
    axes[0, 1].set_title('Prediction Heatmap')
    axes[0, 1].axis('off')
    
    # Plot binary mask with current threshold
    binary_mask = (heatmap > threshold).astype(np.uint8)
    axes[0, 2].imshow(binary_mask, cmap='gray')
    axes[0, 2].set_title(f'Binary Mask (threshold={threshold})')
    axes[0, 2].axis('off')
    
    # Plot spot detection with different thresholds
    for i, t in enumerate(thresholds[:3]):
        # Detect spots
        labeled_mask, num_spots, spot_props = detect_individual_spots(
            heatmap, threshold=t, min_spot_size=min_spot_size, min_distance=min_distance
        )
        
        # Create RGB overlay
        rgb_mask = np.zeros((*image.shape, 3))
        rgb_mask[..., 0] = image  # Red channel
        rgb_mask[..., 1] = image  # Green channel
        rgb_mask[..., 2] = image  # Blue channel
        
        # Overlay spots
        for prop in spot_props:
            y, x = prop['centroid']
            r = int(np.sqrt(prop['area'] / np.pi))
            
            # Draw circle
            from skimage.draw import circle_perimeter
            rr, cc = circle_perimeter(int(y), int(x), r, shape=image.shape)
            rgb_mask[rr, cc, 0] = 1.0  # Red
            rgb_mask[rr, cc, 1] = 0.0  # Green
            rgb_mask[rr, cc, 2] = 0.0  # Blue
        
        axes[1, i].imshow(rgb_mask)
        axes[1, i].set_title(f'Threshold={t}, Spots={num_spots}')
        axes[1, i].axis('off')
    
    plt.tight_layout()
    plt.show()

# Example usage:
"""
# Load model and image
model = load_model('model.pth')
image = load_image('image.png')

# Get prediction heatmap
with torch.no_grad():
    outputs = model(image_tensor)
    pred = outputs['output'] if isinstance(outputs, dict) else outputs
    heatmap = torch.sigmoid(pred).squeeze().cpu().numpy()

# Detect spots with improved method
labeled_mask, num_spots, spot_props = detect_individual_spots(
    heatmap, threshold=0.2, min_spot_size=3, min_distance=5
)

# Visualize results
visualize_spot_detection(image_np, heatmap)
"""
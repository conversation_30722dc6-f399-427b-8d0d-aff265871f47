import torch
import numpy as np
from SparseSpotDataset import SparseSpotDataset

def debug_update_with_predictions():
    """Debug the update_with_predictions method in SparseSpotDataset"""
    print("Creating a test dataset...")
    dataset = SparseSpotDataset(data_dir=None)
    
    # Create a simple image and mask
    image_size = (64, 64)
    image = np.zeros(image_size, dtype=np.float32)
    mask = np.zeros(image_size, dtype=np.float32)
    confidence = np.zeros(image_size, dtype=np.float32)
    
    # Add to dataset
    dataset.images.append(image)
    dataset.masks.append(mask)
    dataset.confidence_masks.append(confidence)
    
    print(f"Initial dataset size: {len(dataset)}")
    print(f"Initial mask sum: {np.sum(dataset.masks[0])}")
    print(f"Initial confidence sum: {np.sum(dataset.confidence_masks[0])}")
    
    # Create a simple prediction
    pred = np.zeros(image_size, dtype=np.float32)
    # Add some high-confidence spots
    for i in range(10, 20):
        for j in range(10, 20):
            pred[i, j] = 0.9  # High confidence value
    
    # Convert to tensor
    pred_tensor = torch.from_numpy(pred).unsqueeze(0)  # Add batch dimension
    
    print(f"Prediction shape: {pred_tensor.shape}")
    print(f"High confidence pixels in prediction: {np.sum(pred > 0.5)}")
    
    # Update dataset with prediction
    print("\nUpdating dataset with prediction...")
    updated_pixels = dataset.update_with_predictions([image], [pred_tensor], threshold=0.5)
    
    print(f"Updated pixels reported: {updated_pixels}")
    print(f"Mask sum after update: {np.sum(dataset.masks[0])}")
    print(f"Confidence sum after update: {np.sum(dataset.confidence_masks[0])}")
    
    # Check if update worked
    if np.sum(dataset.masks[0]) > 0:
        print("\n✅ update_with_predictions is working correctly!")
    else:
        print("\n❌ update_with_predictions is NOT working correctly!")
        print("Possible issues:")
        print("1. Prediction tensor not being processed correctly")
        print("2. Threshold logic issue")
        print("3. Mask update logic issue")
    
    # Try with direct numpy arrays
    print("\nTrying with direct numpy arrays...")
    dataset2 = SparseSpotDataset(data_dir=None)
    dataset2.images.append(image)
    dataset2.masks.append(mask.copy())
    dataset2.confidence_masks.append(confidence.copy())
    
    updated_pixels2 = dataset2.update_with_predictions([image], [pred], threshold=0.5)
    
    print(f"Updated pixels reported: {updated_pixels2}")
    print(f"Mask sum after update: {np.sum(dataset2.masks[0])}")
    print(f"Confidence sum after update: {np.sum(dataset2.confidence_masks[0])}")
    
    if np.sum(dataset2.masks[0]) > 0:
        print("\n✅ update_with_predictions works with numpy arrays!")
    else:
        print("\n❌ update_with_predictions still not working with numpy arrays!")

if __name__ == "__main__":
    debug_update_with_predictions()
# Optimized Spot Detection with Mixture of Experts

This repository contains an optimized implementation of a spot detection model using a Mixture of Experts (MoE) approach. The model is designed to detect spots of varying sizes and densities in images, with a focus on accurate instance segmentation.

## Key Features

- **Mixture of Experts Architecture**: Specialized experts for different spot types (small, medium, large, dense regions)
- **Multi-scale Feature Integration**: Cross-scale attention for better feature integration
- **Optimized Loss Function**: Combines heatmap, boundary, distance transform, and MoE balancing losses
- **Adaptive Weighting**: Density-aware and size-adaptive weighting for better handling of varying spot distributions
- **GPU-accelerated Instance Segmentation**: Fast and accurate instance segmentation
- **Memory-efficient Implementation**: Gradient checkpointing for training with limited GPU memory

## Files

- `OptimizedSpotLoss.py`: The optimized loss function for spot detection
- `OptimizedSpotDetection_base.py`: Base components for the model (ConvBlock, SEBlock, etc.)
- `OptimizedSpotDetection_model.py`: The main model implementation
- `OptimizedSpotDetection_integration.py`: Integration of model and loss function into a complete system
- `OptimizedSpotDetection_main.py`: Example script for training and inference

## Usage

### Installation

```bash
# Clone the repository
git clone https://github.com/yourusername/optimized-spot-detection.git
cd optimized-spot-detection

# Install dependencies
pip install torch torchvision numpy matplotlib scikit-image
```

### Training

```bash
python OptimizedSpotDetection_main.py --batch_size 8 --epochs 50 --lr 1e-3 --num_experts 4
```

### Inference

```python
import torch
from OptimizedSpotDetection_model import OptimizedSpotDetectionModel

# Load model
model = OptimizedSpotDetectionModel(in_channels=1, base_filters=32, num_experts=4)
model.load_state_dict(torch.load('checkpoints/best_model.pth')['model_state_dict'])
model.eval()

# Run inference
with torch.no_grad():
    outputs = model(image)

# Get results
heatmap = outputs['heatmap']
instance_labels = outputs.get('instance_labels')
spot_coords = outputs.get('spot_coords')
```

## Model Architecture

The model uses a U-Net-like architecture with the following enhancements:

1. **Encoder**: Standard downsampling path with residual blocks and increasing dropout
2. **Mixture of Experts**: Four specialized experts at the bottleneck:
   - Expert 1: Small spots (small kernel, no dilation)
   - Expert 2: Medium spots (medium kernel, no dilation)
   - Expert 3: Large spots (large kernel, no dilation)
   - Expert 4: Dense regions (medium kernel with dilation)
3. **Router Network**: Determines which expert to use for each input
4. **Decoder**: Upsampling path with skip connections and attention gates
5. **Multi-level Prediction**: Deep supervision with predictions at multiple scales
6. **Instance Segmentation**: Post-processing for instance-level segmentation

## Loss Function

The optimized loss function combines:

1. **Heatmap Loss**: Focal BCE loss for spot detection
2. **Dice Loss**: For better handling of class imbalance
3. **Boundary Loss**: For improved spot separation
4. **Distance Transform Loss**: For shape awareness
5. **MoE Balancing Loss**: For balanced expert utilization

The loss function supports:
- Learnable component weights
- Density-aware weighting
- Size-adaptive weighting

## Performance Optimizations

1. **GPU-accelerated Operations**: All operations run on GPU without CPU transfers
2. **Memory Efficiency**: Gradient checkpointing for training with limited GPU memory
3. **Fast Validation Mode**: Option to skip expensive instance segmentation during validation
4. **Optimized Instance Segmentation**: Efficient implementation of watershed-based instance segmentation

## Citation

If you use this code in your research, please cite:

```
@article{optimized_spot_detection,
  title={Optimized Spot Detection with Mixture of Experts},
  author={Your Name},
  journal={arXiv preprint arXiv:XXXX.XXXXX},
  year={2023}
}
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.
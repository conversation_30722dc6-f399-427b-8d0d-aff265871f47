import torch
import numpy as np
import tifffile
from pathlib import Path
import matplotlib.pyplot as plt
from scipy.ndimage import label

def minimal_inference(
    model_path: str,
    image_path: str,
    output_dir: str,
    threshold: float = 0.5,
    min_size: int = 4
):
    """
    Minimal inference function that extracts all model outputs and applies basic post-processing
    """
    # Create output directory
    output_dir = Path(output_dir)
    output_dir.mkdir(exist_ok=True, parents=True)
    
    # Load model
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # Try to find model class in common locations
    try:
        from AdaptiveSpotDetector import AdaptiveSpotDetector
        model = AdaptiveSpotDetector()
    except ImportError:
        try:
            from OptimizedSpotDetection_model import OptimizedSpotDetectionModel
            model = OptimizedSpotDetectionModel()
        except ImportError:
            raise ImportError("Could not find model class.")
    
    # Load model weights
    checkpoint = torch.load(model_path, map_location=device)
    if 'model_state_dict' in checkpoint:
        model.load_state_dict(checkpoint['model_state_dict'])
    else:
        model.load_state_dict(checkpoint)
    
    model.to(device)
    model.eval()
    
    # Load image
    image = tifffile.imread(image_path).astype(np.float32)
    if len(image.shape) == 3:
        image = image[:, :, 0] if image.shape[2] > 1 else image.squeeze()
    
    # Save original image
    tifffile.imwrite(output_dir / 'original_image.tif', image)
    
    # Normalize image
    image_norm = image / (image.max() + 1e-8)
    
    # Convert to tensor
    image_tensor = torch.from_numpy(image_norm).unsqueeze(0).unsqueeze(0).to(device)
    
    # Run inference
    with torch.no_grad():
        outputs = model(image_tensor)
    
    # Extract all outputs
    all_outputs = {}
    for key, value in outputs.items():
        if isinstance(value, torch.Tensor):
            if key in ['heatmap', 'semantic', 'boundary']:
                # Apply sigmoid for these outputs
                all_outputs[key] = torch.sigmoid(value).cpu().numpy()[0, 0]
            else:
                all_outputs[key] = value.cpu().numpy()[0]
                if all_outputs[key].ndim == 3:  # [C, H, W]
                    all_outputs[key] = all_outputs[key][0]  # Take first channel for visualization
    
    # Save all raw outputs
    for key, value in all_outputs.items():
        if value.ndim == 2:  # Only save 2D outputs as TIFF
            tifffile.imwrite(output_dir / f"{key}_raw.tif", (value * 255).astype(np.uint8))
    
    # Apply post-processing to each output
    binary_outputs = {}
    for key in ['heatmap', 'semantic', 'boundary']:
        if key in all_outputs:
            # Create binary mask
            binary = (all_outputs[key] > threshold).astype(np.uint8)
            
            # Remove small objects
            if min_size > 1:
                labeled, num = label(binary)
                for i in range(1, num + 1):
                    if np.sum(labeled == i) < min_size:
                        binary[labeled == i] = 0
            
            binary_outputs[key] = binary
            tifffile.imwrite(output_dir / f"{key}_binary.tif", binary * 255)
    
    # Create instance labels from each binary output
    instance_outputs = {}
    for key, binary in binary_outputs.items():
        instance_labels, num_spots = label(binary)
        instance_outputs[key] = instance_labels
        tifffile.imwrite(output_dir / f"{key}_instances.tif", instance_labels.astype(np.uint16))
    
    # Create visualization
    plt.figure(figsize=(15, 10))
    
    # Original image
    plt.subplot(3, 3, 1)
    plt.imshow(image_norm, cmap='gray')
    plt.title('Original Image')
    plt.axis('off')
    
    # Raw outputs
    row = 0
    for i, (key, value) in enumerate(all_outputs.items()):
        if value.ndim == 2:  # Only plot 2D outputs
            plt.subplot(3, 3, i + 2)
            plt.imshow(value, cmap='hot' if key == 'heatmap' else 'viridis')
            plt.title(f'Raw {key}')
            plt.axis('off')
            row = max(row, (i + 2) // 3)
    
    # Binary outputs
    for i, (key, value) in enumerate(binary_outputs.items()):
        plt.subplot(3, 3, row * 3 + i + 1)
        plt.imshow(value, cmap='gray')
        plt.title(f'Binary {key}')
        plt.axis('off')
    
    # Instance outputs
    for i, (key, value) in enumerate(instance_outputs.items()):
        plt.subplot(3, 3, (row + 1) * 3 + i + 1)
        plt.imshow(value, cmap='nipy_spectral')
        plt.title(f'Instances from {key}')
        plt.axis('off')
    
    plt.tight_layout()
    plt.savefig(output_dir / 'all_outputs.png', dpi=150)
    plt.close()
    
    print(f"All outputs saved to {output_dir}")
    print(f"Available outputs: {list(all_outputs.keys())}")
    
    return all_outputs, binary_outputs, instance_outputs

if __name__ == "__main__":
    import argparse
    parser = argparse.ArgumentParser(description='Minimal inference with all outputs')
    parser.add_argument('--model', type=str, required=True, help='Path to model weights')
    parser.add_argument('--image', type=str, required=True, help='Path to input image')
    parser.add_argument('--output', type=str, required=True, help='Output directory')
    parser.add_argument('--threshold', type=float, default=0.5, help='Threshold for binary mask')
    parser.add_argument('--min_size', type=int, default=4, help='Minimum spot size')
    args = parser.parse_args()
    
    minimal_inference(
        args.model, 
        args.image, 
        args.output,
        args.threshold,
        args.min_size
    )
import torch
import numpy as np
import os
import matplotlib.pyplot as plt
from tqdm import tqdm
from peak_detection_optimizer import PeakDetectionOptimizer
from peak_detection_predictor import PeakDetectionPredictor

def train_with_peak_optimization(model, train_loader, val_loader, optimizer, loss_fn, num_epochs, device, peak_optimizer):
    """
    Train model with peak detection parameter optimization
    """
    # Initialize best model tracking
    best_val_loss = float('inf')
    run_dir = os.path.join('models', f'run_{torch.datetime.now().strftime("%Y%m%d-%H%M%S")}')
    os.makedirs(run_dir, exist_ok=True)
    best_model_path = os.path.join(run_dir, 'best_model.pth')
    best_params_path = os.path.join(run_dir, 'best_peak_params.npy')
    
    # Training history
    history = {
        'train_loss': [],
        'val_loss': [],
        'peak_params': []
    }
    
    # Training loop
    for epoch in range(num_epochs):
        # Training phase
        model.train()
        train_loss = 0.0
        
        for batch in tqdm(train_loader, desc=f"Epoch {epoch+1}/{num_epochs} - Training"):
            # Get data
            images = batch['image'].to(device)
            masks = batch['mask'].to(device)
            
            # Forward pass
            optimizer.zero_grad()
            outputs = model(images)
            
            # Calculate loss
            loss = loss_fn(outputs, masks)
            
            # Backward pass
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item()
        
        # Calculate average training loss
        train_loss /= len(train_loader)
        history['train_loss'].append(train_loss)
        
        # Validation phase
        model.eval()
        val_loss = 0.0
        all_preds = []
        all_masks = []
        all_true_counts = []
        
        with torch.no_grad():
            for batch in tqdm(val_loader, desc=f"Epoch {epoch+1}/{num_epochs} - Validation"):
                # Get data
                images = batch['image'].to(device)
                masks = batch['mask'].to(device)
                
                # Forward pass
                outputs = model(images)
                
                # Calculate loss
                loss = loss_fn(outputs, masks)
                val_loss += loss.item()
                
                # Store predictions and masks for parameter optimization
                preds = torch.sigmoid(outputs)
                all_preds.append(preds)
                all_masks.append(masks)
                
                # Extract true spot counts if this is synthetic data
                # For synthetic data, we can extract the true count from the mask
                # Each spot has a unique ID in the mask
                if 'true_spot_count' in batch:
                    # If the dataset provides true spot counts directly
                    batch_counts = batch['true_spot_count'].tolist()
                    all_true_counts.extend(batch_counts)
                else:
                    # Extract counts from masks
                    batch_counts = []
                    for b in range(masks.shape[0]):
                        mask = masks[b, 0].cpu().numpy()
                        unique_ids = np.unique(mask)
                        # Count non-zero IDs (each ID represents a spot)
                        count = len(unique_ids[unique_ids > 0])
                        batch_counts.append(count)
                    all_true_counts.extend(batch_counts)
        
        # Calculate average validation loss
        val_loss /= len(val_loader)
        history['val_loss'].append(val_loss)
        
        # Print epoch results
        print(f"Epoch {epoch+1}/{num_epochs} - Train Loss: {train_loss:.4f}, Val Loss: {val_loss:.4f}")
        
        # Optimize peak detection parameters if needed
        if peak_optimizer.should_optimize(epoch):
            print("Optimizing peak detection parameters...")
            # Concatenate all predictions and masks
            all_preds_tensor = torch.cat(all_preds, dim=0)
            all_masks_tensor = torch.cat(all_masks, dim=0)
            
            # Optimize parameters using true spot counts
            opt_result = peak_optimizer.optimize_parameters(
                all_preds_tensor, 
                all_masks_tensor,
                true_spot_counts=all_true_counts
            )
            
            # Print results
            print(f"Best parameters: min_distance={opt_result['best_distance']}, "
                  f"min_intensity={opt_result['best_intensity']:.3f}, F1={opt_result['best_f1']:.3f}")
            print(f"Using true spot counts from synthetic data to improve parameter selection")
            
            # Store parameters in history
            history['peak_params'].append({
                'epoch': epoch + 1,
                'min_distance': opt_result['best_distance'],
                'min_intensity': opt_result['best_intensity'],
                'f1_score': opt_result['best_f1']
            })
        
        # Save best model
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            torch.save(model.state_dict(), best_model_path)
            print(f"Saved best model with validation loss: {val_loss:.4f}")
            
            # Save best peak parameters if available
            if peak_optimizer.best_distance is not None:
                best_params = peak_optimizer.get_best_parameters()
                np.save(best_params_path, best_params)
                print(f"Saved best peak parameters: {best_params}")
    
    # Plot training history
    plt.figure(figsize=(12, 5))
    plt.subplot(1, 2, 1)
    plt.plot(history['train_loss'], label='Train Loss')
    plt.plot(history['val_loss'], label='Val Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()
    plt.title('Training and Validation Loss')
    
    # Plot parameter optimization history if available
    if history['peak_params']:
        plt.subplot(1, 2, 2)
        epochs = [p['epoch'] for p in history['peak_params']]
        distances = [p['min_distance'] for p in history['peak_params']]
        intensities = [p['min_intensity'] for p in history['peak_params']]
        f1_scores = [p['f1_score'] for p in history['peak_params']]
        
        plt.plot(epochs, distances, 'o-', label='Min Distance')
        plt.plot(epochs, [i*20 for i in intensities], 's-', label='Min Intensity (×20)')
        plt.plot(epochs, [f*10 for f in f1_scores], '^-', label='F1 Score (×10)')
        plt.xlabel('Epoch')
        plt.legend()
        plt.title('Peak Detection Parameter Optimization')
    
    plt.tight_layout()
    plt.show()
    
    # Return best model path and parameters
    return best_model_path, best_params_path

def evaluate_with_peak_detection(model, dataloader, peak_predictor, num_samples=5):
    """
    Evaluate model with peak detection
    """
    model.eval()
    all_metrics = []
    
    # Get a few samples for visualization
    vis_samples = []
    
    with torch.no_grad():
        for i, batch in enumerate(dataloader):
            # Get data
            images = batch['image'].to(model.device)
            masks = batch['mask'].to(model.device)
            
            # Forward pass
            outputs = model(images)
            preds = torch.sigmoid(outputs)
            
            # Process each sample in batch
            for j in range(images.shape[0]):
                # Get single sample
                img = images[j, 0].cpu().numpy()
                mask = masks[j, 0].cpu().numpy()
                pred = preds[j, 0].cpu().numpy()
                
                # Detect peaks
                result = peak_predictor.detect_peaks(
                    pred, 
                    return_visualization=True,
                    original_image=img
                )
                
                # Store for visualization if needed
                if len(vis_samples) < num_samples:
                    vis_samples.append({
                        'image': img,
                        'mask': mask,
                        'pred': pred,
                        'visualization': result['visualization'],
                        'num_spots': result['num_spots'],
                        'coordinates': result['coordinates']
                    })
    
    # Visualize samples
    fig, axes = plt.subplots(len(vis_samples), 4, figsize=(16, 4*len(vis_samples)))
    
    for i, sample in enumerate(vis_samples):
        # Original image
        axes[i, 0].imshow(sample['image'], cmap='gray')
        axes[i, 0].set_title('Original Image')
        axes[i, 0].axis('off')
        
        # Ground truth mask
        axes[i, 1].imshow(sample['mask'], cmap='hot')
        axes[i, 1].set_title('Ground Truth')
        axes[i, 1].axis('off')
        
        # Prediction heatmap
        axes[i, 2].imshow(sample['pred'], cmap='hot')
        axes[i, 2].set_title('Prediction Heatmap')
        axes[i, 2].axis('off')
        
        # Peak detection visualization
        axes[i, 3].imshow(sample['visualization'])
        axes[i, 3].set_title(f'Peak Detection: {sample["num_spots"]} spots')
        axes[i, 3].axis('off')
    
    plt.tight_layout()
    plt.show()
    
    return vis_samples

def load_model_with_peak_params(model_path, model_class, params_path=None, device=torch.device('cpu')):
    """
    Load model with peak detection parameters
    """
    # Load model
    checkpoint = torch.load(model_path, map_location=device)
    
    # Initialize model
    model = model_class().to(device)
    
    # Load state dict
    if 'model_state_dict' in checkpoint:
        model.load_state_dict(checkpoint['model_state_dict'])
        peak_params = checkpoint.get('peak_params', None)
    else:
        model.load_state_dict(checkpoint)
        peak_params = None
    
    # Load parameters from separate file if provided
    if params_path is not None:
        peak_params = np.load(params_path, allow_pickle=True).item()
    
    # Initialize predictor
    if peak_params is not None:
        predictor = PeakDetectionPredictor(
            min_distance=peak_params.get('min_distance', 5),
            min_intensity=peak_params.get('min_intensity', 0.1),
            device=device
        )
    else:
        predictor = PeakDetectionPredictor(device=device)
    
    return model, predictor
import numpy as np
import matplotlib.pyplot as plt
from skimage.feature import peak_local_max
import cv2
from synthetic_data_generator import AdvancedSyntheticSpotGenerator
from peak_detection_predictor import PeakDetectionPredictor
from peak_detection_optimizer import PeakDetectionOptimizer

def optimize_with_synthetic_data(num_samples=5, image_size=(256, 256), 
                               min_spots=10, max_spots=30, 
                               min_radius=3, max_radius=7):
    """
    Optimize peak detection parameters using synthetic data with known ground truth
    
    Args:
        num_samples: Number of synthetic samples to generate
        image_size: Size of synthetic images
        min_spots: Minimum number of spots per image
        max_spots: Maximum number of spots per image
        min_radius: Minimum spot radius
        max_radius: Maximum spot radius
        
    Returns:
        Dictionary with optimized parameters
    """
    # Create synthetic data generator
    generator = AdvancedSyntheticSpotGenerator(
        image_size=image_size,
        min_spots=min_spots,
        max_spots=max_spots,
        min_radius=min_radius,
        max_radius=max_radius,
        mask_threshold=0.3  # Control mask size to match actual spot size
    )
    
    # Generate synthetic samples
    images, masks = generator.generate_dataset(num_samples, variable_params=False)
    
    # Create peak detection optimizer
    optimizer = PeakDetectionOptimizer(
        distance_range=(2, 8),
        intensity_range=(0.05, 0.4),
        num_distance_steps=4,
        num_intensity_steps=5
    )
    
    # Track best parameters across all samples
    all_best_params = []
    all_spot_counts = []
    
    # Process each sample
    for i, (image, mask) in enumerate(zip(images, masks)):
        print(f"\nProcessing synthetic sample {i+1}/{num_samples}")
        
        # Count actual spots in the mask
        unique_ids = np.unique(mask)
        unique_ids = unique_ids[unique_ids > 0]
        true_num_spots = len(unique_ids)
        all_spot_counts.append(true_num_spots)
        
        print(f"True number of spots: {true_num_spots}")
        
        # Create visualization of ground truth
        rgb_mask = np.zeros((*image.shape, 3))
        rgb_mask[..., 0] = image  # Red channel
        rgb_mask[..., 1] = image  # Green channel
        rgb_mask[..., 2] = image  # Blue channel
        
        # Draw ground truth contours
        for spot_id in unique_ids:
            # Create binary mask for this spot
            spot_mask = (mask == spot_id).astype(np.uint8)
            
            # Find contours
            contours, _ = cv2.findContours(spot_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # Draw contours
            cv2.drawContours(rgb_mask, contours, -1, (1, 0, 0), 1)  # Red contour
        
        # Test different parameter combinations
        distance_range = range(2, 9)
        intensity_range = np.linspace(0.05, 0.4, 8)
        
        best_f1 = 0
        best_params = None
        best_result = None
        
        # Create figure for visualization
        fig, axes = plt.subplots(len(distance_range), len(intensity_range), figsize=(20, 16))
        
        # Test each parameter combination
        for i, min_distance in enumerate(distance_range):
            for j, min_intensity in enumerate(intensity_range):
                # Create predictor with these parameters
                predictor = PeakDetectionPredictor(
                    min_distance=min_distance,
                    min_intensity=min_intensity
                )
                
                # Detect peaks
                result = predictor.detect_peaks(image, return_visualization=True, original_image=image)
                
                # Calculate metrics
                detected_spots = result['num_spots']
                
                # Calculate F1 score based on count match
                precision = min(detected_spots, true_num_spots) / max(detected_spots, 1)
                recall = min(detected_spots, true_num_spots) / max(true_num_spots, 1)
                f1 = 2 * precision * recall / (precision + recall) if precision + recall > 0 else 0
                
                # Display result
                axes[i, j].imshow(result['visualization'])
                axes[i, j].set_title(f'D={min_distance}, I={min_intensity:.2f}, N={detected_spots}, F1={f1:.2f}')
                axes[i, j].axis('off')
                
                # Update best parameters
                if f1 > best_f1:
                    best_f1 = f1
                    best_params = {'min_distance': min_distance, 'min_intensity': min_intensity}
                    best_result = result
        
        plt.tight_layout()
        plt.show()
        
        # Show ground truth
        plt.figure(figsize=(10, 10))
        plt.imshow(rgb_mask)
        plt.title(f'Ground Truth: {true_num_spots} spots')
        plt.axis('off')
        plt.show()
        
        # Show best result
        plt.figure(figsize=(10, 10))
        plt.imshow(best_result['visualization'])
        plt.title(f'Best Result: {best_result["num_spots"]} spots, D={best_params["min_distance"]}, I={best_params["min_intensity"]:.2f}')
        plt.axis('off')
        plt.show()
        
        # Add to all best parameters
        all_best_params.append(best_params)
    
    # Calculate average best parameters
    avg_min_distance = np.mean([p['min_distance'] for p in all_best_params])
    avg_min_intensity = np.mean([p['min_intensity'] for p in all_best_params])
    
    print("\nOptimization Results:")
    print(f"Average best min_distance: {avg_min_distance:.1f}")
    print(f"Average best min_intensity: {avg_min_intensity:.3f}")
    print(f"Average spot count: {np.mean(all_spot_counts):.1f}")
    
    # Return optimized parameters
    return {
        'min_distance': int(round(avg_min_distance)),
        'min_intensity': avg_min_intensity
    }

def test_optimized_parameters(optimized_params, num_samples=2):
    """
    Test optimized parameters on new synthetic data
    
    Args:
        optimized_params: Dictionary with optimized parameters
        num_samples: Number of synthetic samples to test
    """
    # Create synthetic data generator
    generator = AdvancedSyntheticSpotGenerator(
        image_size=(256, 256),
        min_spots=10,
        max_spots=30,
        min_radius=3,
        max_radius=7,
        mask_threshold=0.3
    )
    
    # Generate synthetic samples
    images, masks = generator.generate_dataset(num_samples, variable_params=False)
    
    # Create predictor with optimized parameters
    predictor = PeakDetectionPredictor(
        min_distance=optimized_params['min_distance'],
        min_intensity=optimized_params['min_intensity']
    )
    
    # Process each sample
    for i, (image, mask) in enumerate(zip(images, masks)):
        print(f"\nTesting synthetic sample {i+1}/{num_samples}")
        
        # Count actual spots in the mask
        unique_ids = np.unique(mask)
        unique_ids = unique_ids[unique_ids > 0]
        true_num_spots = len(unique_ids)
        
        print(f"True number of spots: {true_num_spots}")
        
        # Create visualization of ground truth
        rgb_mask = np.zeros((*image.shape, 3))
        rgb_mask[..., 0] = image  # Red channel
        rgb_mask[..., 1] = image  # Green channel
        rgb_mask[..., 2] = image  # Blue channel
        
        # Draw ground truth contours
        for spot_id in unique_ids:
            # Create binary mask for this spot
            spot_mask = (mask == spot_id).astype(np.uint8)
            
            # Find contours
            contours, _ = cv2.findContours(spot_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # Draw contours
            cv2.drawContours(rgb_mask, contours, -1, (1, 0, 0), 1)  # Red contour
        
        # Detect peaks with optimized parameters
        result = predictor.detect_peaks(image, return_visualization=True, original_image=image)
        
        # Show ground truth
        plt.figure(figsize=(10, 10))
        plt.imshow(rgb_mask)
        plt.title(f'Ground Truth: {true_num_spots} spots')
        plt.axis('off')
        plt.show()
        
        # Show detection result
        plt.figure(figsize=(10, 10))
        plt.imshow(result['visualization'])
        plt.title(f'Detection Result: {result["num_spots"]} spots')
        plt.axis('off')
        plt.show()
        
        # Calculate metrics
        detected_spots = result['num_spots']
        count_error = abs(detected_spots - true_num_spots) / max(true_num_spots, 1)
        
        print(f"Detected spots: {detected_spots}")
        print(f"Count error: {count_error:.2f}")

if __name__ == "__main__":
    # Optimize parameters using synthetic data
    optimized_params = optimize_with_synthetic_data(num_samples=3)
    
    # Test optimized parameters
    test_optimized_parameters(optimized_params, num_samples=2)
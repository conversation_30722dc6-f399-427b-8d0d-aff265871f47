import os
import torch
import numpy as np
import matplotlib.pyplot as plt
from tqdm import tqdm
from combined_data_loader import create_combined_data_loaders, optimize_peak_detection_on_real_data
from peak_detection_optimizer import PeakDetectionOptimizer
from peak_detection_predictor import PeakDetectionPredictor

def train_with_combined_data(model, 
                            real_data_dir,
                            optimizer, 
                            loss_fn, 
                            num_epochs, 
                            device,
                            batch_size=8,
                            image_size=(256, 256),
                            train_val_split=0.8,
                            synthetic_ratio=0.5,
                            synthetic_size=500,
                            augmentation_level='strong',
                            num_workers=4,
                            optimization_interval=5):
    """
    Train model with combined real and synthetic data, optimizing peak detection parameters
    
    Args:
        model: Model to train
        real_data_dir: Directory containing real images and masks
        optimizer: Optimizer for model training
        loss_fn: Loss function
        num_epochs: Number of epochs to train
        device: Device to train on
        batch_size: Batch size
        image_size: Size to resize images to
        train_val_split: Fraction of data to use for training
        synthetic_ratio: Ratio of synthetic to real data (0-1)
        synthetic_size: Number of synthetic samples to generate
        augmentation_level: Level of augmentation ('none', 'light', 'medium', 'strong')
        num_workers: Number of workers for data loading
        optimization_interval: Interval for peak detection parameter optimization
    """
    # Initialize best model tracking
    best_val_loss = float('inf')
    run_dir = os.path.join('models', f'run_{torch.datetime.now().strftime("%Y%m%d-%H%M%S")}')
    os.makedirs(run_dir, exist_ok=True)
    best_model_path = os.path.join(run_dir, 'best_model.pth')
    best_params_path = os.path.join(run_dir, 'best_peak_params.npy')
    
    # Create data loaders
    train_loader, val_loader = create_combined_data_loaders(
        real_data_dir=real_data_dir,
        batch_size=batch_size,
        image_size=image_size,
        train_val_split=train_val_split,
        synthetic_ratio=synthetic_ratio,
        synthetic_size=synthetic_size,
        augmentation_level=augmentation_level,
        num_workers=num_workers
    )
    
    # Initialize peak detection optimizer
    peak_optimizer = PeakDetectionOptimizer(
        distance_range=(3, 10),
        intensity_range=(0.05, 0.3),
        num_distance_steps=4,
        num_intensity_steps=5,
        optimization_interval=optimization_interval,
        device=device
    )
    
    # Training history
    history = {
        'train_loss': [],
        'val_loss': [],
        'peak_params': []
    }
    
    # Training loop
    for epoch in range(num_epochs):
        # Training phase
        model.train()
        train_loss = 0.0
        
        for batch in tqdm(train_loader, desc=f"Epoch {epoch+1}/{num_epochs} - Training"):
            # Get data
            images = batch['image'].to(device)
            masks = batch['mask'].to(device)
            
            # Forward pass
            optimizer.zero_grad()
            outputs = model(images)
            
            # Calculate loss
            loss = loss_fn(outputs, masks)
            
            # Backward pass
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item()
        
        # Calculate average training loss
        train_loss /= len(train_loader)
        history['train_loss'].append(train_loss)
        
        # Validation phase
        model.eval()
        val_loss = 0.0
        all_preds = []
        all_masks = []
        all_true_counts = []
        
        with torch.no_grad():
            for batch in tqdm(val_loader, desc=f"Epoch {epoch+1}/{num_epochs} - Validation"):
                # Get data
                images = batch['image'].to(device)
                masks = batch['mask'].to(device)
                
                # Forward pass
                outputs = model(images)
                
                # Calculate loss
                loss = loss_fn(outputs, masks)
                val_loss += loss.item()
                
                # Store predictions and masks for parameter optimization
                preds = torch.sigmoid(outputs)
                all_preds.append(preds)
                all_masks.append(masks)
                
                # Extract true spot counts if available
                if 'true_spot_count' in batch:
                    all_true_counts.extend(batch['true_spot_count'].tolist())
                else:
                    # Extract counts from masks
                    for i in range(masks.shape[0]):
                        mask = masks[i, 0].cpu().numpy()
                        from skimage import measure
                        labeled_mask = measure.label(mask > 0.5)
                        regions = measure.regionprops(labeled_mask)
                        all_true_counts.append(len(regions))
        
        # Calculate average validation loss
        val_loss /= len(val_loader)
        history['val_loss'].append(val_loss)
        
        # Print epoch results
        print(f"Epoch {epoch+1}/{num_epochs} - Train Loss: {train_loss:.4f}, Val Loss: {val_loss:.4f}")
        
        # Optimize peak detection parameters if needed
        if epoch % optimization_interval == 0:
            print("Optimizing peak detection parameters...")
            # Concatenate all predictions and masks
            all_preds_tensor = torch.cat(all_preds, dim=0)
            all_masks_tensor = torch.cat(all_masks, dim=0)
            
            # Optimize parameters using true spot counts
            opt_result = peak_optimizer.optimize_parameters(
                all_preds_tensor, 
                all_masks_tensor,
                true_spot_counts=all_true_counts
            )
            
            # Print results
            print(f"Best parameters: min_distance={opt_result['best_distance']}, "
                  f"min_intensity={opt_result['best_intensity']:.3f}, F1={opt_result['best_f1']:.3f}")
            print(f"Using true spot counts to improve parameter selection")
            
            # Store parameters in history
            history['peak_params'].append({
                'epoch': epoch + 1,
                'min_distance': opt_result['best_distance'],
                'min_intensity': opt_result['best_intensity'],
                'f1_score': opt_result['best_f1']
            })
        
        # Save best model
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            torch.save(model.state_dict(), best_model_path)
            print(f"Saved best model with validation loss: {val_loss:.4f}")
            
            # Save best peak parameters if available
            if peak_optimizer.best_distance is not None:
                best_params = peak_optimizer.get_best_parameters()
                np.save(best_params_path, best_params)
                print(f"Saved best peak parameters: {best_params}")
    
    # Plot training history
    plt.figure(figsize=(12, 5))
    plt.subplot(1, 2, 1)
    plt.plot(history['train_loss'], label='Train Loss')
    plt.plot(history['val_loss'], label='Val Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()
    plt.title('Training and Validation Loss')
    
    # Plot parameter optimization history if available
    if history['peak_params']:
        plt.subplot(1, 2, 2)
        epochs = [p['epoch'] for p in history['peak_params']]
        distances = [p['min_distance'] for p in history['peak_params']]
        intensities = [p['min_intensity'] for p in history['peak_params']]
        f1_scores = [p['f1_score'] for p in history['peak_params']]
        
        plt.plot(epochs, distances, 'o-', label='Min Distance')
        plt.plot(epochs, [i*20 for i in intensities], 's-', label='Min Intensity (×20)')
        plt.plot(epochs, [f*10 for f in f1_scores], '^-', label='F1 Score (×10)')
        plt.xlabel('Epoch')
        plt.legend()
        plt.title('Peak Detection Parameter Optimization')
    
    plt.tight_layout()
    plt.savefig(os.path.join(run_dir, 'training_history.png'))
    plt.show()
    
    # Return best model path and parameters
    return best_model_path, best_params_path

if __name__ == "__main__":
    # Example usage
    from OptimizedSpotDetection_model import SpotDetectionModel
    
    # Set device
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # Create model
    model = SpotDetectionModel().to(device)
    
    # Create optimizer
    optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
    
    # Create loss function
    loss_fn = torch.nn.BCEWithLogitsLoss()
    
    # Train model
    train_with_combined_data(
        model=model,
        real_data_dir='/mnt/d/Users/<USER>/Documents/spot detector git amazon 2025/data/real_data',
        optimizer=optimizer,
        loss_fn=loss_fn,
        num_epochs=50,
        device=device,
        batch_size=8,
        image_size=(256, 256),
        train_val_split=0.8,
        synthetic_ratio=0.5,
        synthetic_size=500,
        augmentation_level='strong',
        num_workers=4,
        optimization_interval=5
    )
import torch
import numpy as np
from skimage.feature import peak_local_max
from scipy.spatial.distance import cdist
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple, Optional, Union

class PeakDetectionOptimizer:
    """
    Optimizer for peak detection parameters during model training
    """
    
    def __init__(self, 
                 distance_range=(3, 10),
                 intensity_range=(0.05, 0.3),
                 num_distance_steps=4,
                 num_intensity_steps=5,
                 optimization_interval=5,
                 device=torch.device('cpu')):
        """Initialize the optimizer"""
        self.distance_range = distance_range
        self.intensity_range = intensity_range
        self.num_distance_steps = num_distance_steps
        self.num_intensity_steps = num_intensity_steps
        self.optimization_interval = optimization_interval
        self.device = device
        
        # Generate parameter grids
        self.distances = np.linspace(distance_range[0], distance_range[1], num_distance_steps, dtype=int)
        self.intensities = np.linspace(intensity_range[0], intensity_range[1], num_intensity_steps)
        
        # Initialize best parameters
        self.best_distance = None
        self.best_intensity = None
        self.best_f1_score = 0.0
        self.history = []
    
    def should_optimize(self, epoch):
        """Check if optimization should be performed in this epoch"""
        return epoch % self.optimization_interval == 0
    
    def optimize_parameters(self, predictions, ground_truth, return_visualizations=False, true_spot_counts=None):
        """
        Optimize peak detection parameters
        
        Args:
            predictions: Model predictions (tensor)
            ground_truth: Ground truth masks (tensor)
            return_visualizations: Whether to return visualizations
            true_spot_counts: List of true spot counts for each image in the batch (if known)
        """
        # Move tensors to CPU and convert to numpy
        pred_np = predictions.detach().cpu().numpy()
        gt_np = ground_truth.detach().cpu().numpy()
        
        batch_size = pred_np.shape[0]
        results = {}
        best_f1 = 0.0
        best_distance = self.distances[0]
        best_intensity = self.intensities[0]
        
        # If true spot counts are provided, extract them
        if true_spot_counts is None:
            # Count spots in ground truth masks
            true_counts = []
            for b in range(batch_size):
                gt = gt_np[b, 0]
                gt_binary = gt > 0.5
                from skimage import measure
                gt_labeled = measure.label(gt_binary)
                gt_regions = measure.regionprops(gt_labeled)
                true_counts.append(len(gt_regions))
        else:
            # Use provided true spot counts
            true_counts = true_spot_counts
            
        # Test each parameter combination
        for distance in self.distances:
            for intensity in self.intensities:
                # Calculate metrics for this parameter combination
                precision_sum = 0.0
                recall_sum = 0.0
                f1_sum = 0.0
                count_error_sum = 0.0
                
                for b in range(batch_size):
                    # Get single sample
                    pred = pred_np[b, 0]
                    gt = gt_np[b, 0]
                    true_count = true_counts[b]
                    
                    # Find peaks in prediction
                    pred_peaks = peak_local_max(
                        pred,
                        min_distance=distance,
                        threshold_abs=intensity,
                        exclude_border=False
                    )
                    
                    # Find peaks in ground truth (binary mask)
                    gt_binary = gt > 0.5
                    from skimage import measure
                    gt_labeled = measure.label(gt_binary)
                    gt_regions = measure.regionprops(gt_labeled)
                    gt_peaks = np.array([region.centroid for region in gt_regions])
                    
                    # Calculate metrics
                    precision, recall, f1 = self._calculate_peak_metrics(pred_peaks, gt_peaks)
                    
                    # Calculate count error (how close is the detected count to the true count)
                    # This is especially useful for synthetic data where we know the exact count
                    count_error = abs(len(pred_peaks) - true_count) / max(true_count, 1)
                    
                    precision_sum += precision
                    recall_sum += recall
                    f1_sum += f1
                    count_error_sum += count_error
                
                # Calculate average metrics across batch
                avg_precision = precision_sum / batch_size
                avg_recall = recall_sum / batch_size
                avg_f1 = f1_sum / batch_size
                avg_count_error = count_error_sum / batch_size
                
                # Store results
                key = f"distance_{distance}_intensity_{intensity:.2f}"
                results[key] = {
                    'distance': distance,
                    'intensity': intensity,
                    'precision': avg_precision,
                    'recall': avg_recall,
                    'f1': avg_f1,
                    'count_error': avg_count_error
                }
                
                # Update best parameters - now considering both F1 score AND count error
                # We weight F1 score more heavily but penalize large count errors
                combined_score = avg_f1 * (1 - avg_count_error * 0.5)
                
                if combined_score > best_f1:
                    best_f1 = combined_score
                    best_distance = distance
                    best_intensity = intensity
        
        # Update best parameters if improved
        if best_f1 > self.best_f1_score:
            self.best_f1_score = best_f1
            self.best_distance = best_distance
            self.best_intensity = best_intensity
        
        # Add to history
        self.history.append({
            'distance': best_distance,
            'intensity': best_intensity,
            'f1': best_f1
        })
        
        # Create result dictionary
        optimization_result = {
            'best_distance': best_distance,
            'best_intensity': best_intensity,
            'best_f1': best_f1,
            'all_results': results
        }
        
        return optimization_result
    
    def _calculate_peak_metrics(self, pred_peaks, gt_peaks, max_distance=5):
        """Calculate precision, recall, and F1 score for peak detection"""
        if len(pred_peaks) == 0 and len(gt_peaks) == 0:
            return 1.0, 1.0, 1.0
        
        if len(pred_peaks) == 0:
            return 0.0, 0.0, 0.0
        
        if len(gt_peaks) == 0:
            return 0.0, 0.0, 0.0
        
        # Calculate distances between all predicted and ground truth peaks
        distances = cdist(pred_peaks, gt_peaks)
        
        # Count true positives (matches)
        matches = distances <= max_distance
        tp = np.sum(np.any(matches, axis=0))
        
        # Calculate metrics
        precision = tp / len(pred_peaks)
        recall = tp / len(gt_peaks)
        
        # Calculate F1 score
        if precision + recall > 0:
            f1 = 2 * precision * recall / (precision + recall)
        else:
            f1 = 0.0
        
        return precision, recall, f1
    
    def get_best_parameters(self):
        """Get the best parameters found during optimization"""
        return {
            'min_distance': self.best_distance,
            'min_intensity': self.best_intensity,
            'f1_score': self.best_f1_score
        }
        
    def extract_true_spot_counts(self, ground_truth):
        """
        Extract true spot counts from ground truth masks
        
        Args:
            ground_truth: Ground truth masks (tensor)
            
        Returns:
            List of true spot counts for each image in the batch
        """
        # Move tensors to CPU and convert to numpy
        gt_np = ground_truth.detach().cpu().numpy()
        batch_size = gt_np.shape[0]
        
        # Count spots in ground truth masks
        true_counts = []
        for b in range(batch_size):
            gt = gt_np[b, 0]
            gt_binary = gt > 0.5
            from skimage import measure
            gt_labeled = measure.label(gt_binary)
            gt_regions = measure.regionprops(gt_labeled)
            true_counts.append(len(gt_regions))
            
        return true_counts
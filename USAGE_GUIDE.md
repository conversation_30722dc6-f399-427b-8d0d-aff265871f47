# Spot Detection Usage Guide

This guide provides instructions for using the Spot Detection pipeline with the Mixture of Experts approach.

## Quick Start

### 1. Run the complete pipeline

```bash
python run_spot_detection.py --data_dir /path/to/your/data --mode train
```

### 2. Run specific components

```bash
# Train the model
python run_spot_detection.py --data_dir /path/to/your/data --mode train

# Evaluate a trained model
python run_spot_detection.py --data_dir /path/to/your/data --mode eval --model_path best_spot_detection_model.pth

# Make predictions and visualize
python run_spot_detection.py --data_dir /path/to/your/data --mode predict --model_path best_spot_detection_model.pth

# Perform semi-supervised learning
python run_spot_detection.py --data_dir /path/to/your/data --mode ssl --model_path best_spot_detection_model.pth

# Analyze expert specialization
python run_spot_detection.py --data_dir /path/to/your/data --mode analyze --model_path best_spot_detection_model.pth
```

## Data Organization

The pipeline supports two directory structures for your data:

### Option 1: Separate directories for images and masks

```
data_dir/
├── images/
│   ├── image1.png
│   ├── image2.png
│   └── ...
└── masks/
    ├── image1.png
    ├── image2.png
    └── ...
```

### Option 2: Paired files in the same directory

```
data_dir/
├── image1.png
├── image1_mask.png
├── image2.png
├── image2_mask.png
└── ...
```

For sparse annotations, you can optionally provide confidence masks:

```
data_dir/
├── image1.png
├── image1_mask.png
├── image1_conf.png
├── image2.png
├── image2_mask.png
├── image2_conf.png
└── ...
```

## Configuration

You can customize the pipeline by editing the `config.json` file or by providing command-line arguments.

### Using a configuration file

```bash
python run_spot_detection.py --config config.json
```

### Command-line arguments

```bash
python run_spot_detection.py --data_dir /path/to/your/data --batch_size 16 --num_epochs 50
```

### Configuration options

#### Data configuration

- `data_dir`: Path to data directory
- `image_size`: Size to resize images to
- `batch_size`: Batch size
- `use_synthetic`: Whether to use synthetic data
- `synthetic_size`: Number of synthetic samples to generate
- `augmentation_level`: Level of augmentation ('none', 'light', 'medium', or 'strong')
- `train_val_split`: Fraction of data to use for training
- `num_workers`: Number of workers for data loading
- `is_3d`: Whether to use 3D data
- `confidence_threshold`: Threshold for high-confidence predictions
- `ignore_threshold`: Threshold below which to ignore predictions

#### Model configuration

- `in_channels`: Number of input channels
- `num_experts`: Number of expert modules
- `base_filters`: Number of base filters
- `dropout_rate`: Dropout rate

#### Loss function configuration

- `bce_weight`: Weight for BCE loss
- `dice_weight`: Weight for Dice loss
- `focal_weight`: Weight for Focal loss
- `focal_gamma`: Gamma parameter for Focal loss
- `size_adaptive`: Whether to use size-adaptive weighting
- `density_aware`: Whether to use density-aware weighting
- `confidence_weighted`: Whether to use confidence-weighted loss

#### Training configuration

- `learning_rate`: Learning rate
- `num_epochs`: Number of epochs
- `early_stopping_patience`: Number of epochs to wait for improvement before stopping
- `save_best_model`: Whether to save the best model
- `model_save_path`: Path to save the best model
- `gradient_clipping`: Maximum gradient norm (set to null to disable)
- `use_mixed_precision`: Whether to use mixed precision training

#### Prediction configuration

- `threshold`: Threshold for binary classification
- `min_spot_size`: Minimum spot size in pixels
- `use_test_time_augmentation`: Whether to use test-time augmentation
- `tta_flips`: Whether to use flips for test-time augmentation
- `tta_rotations`: Whether to use rotations for test-time augmentation

#### Semi-supervised learning configuration

- `enabled`: Whether to enable semi-supervised learning
- `num_iterations`: Number of semi-supervised learning iterations
- `confidence_threshold`: Threshold for high-confidence predictions
- `epochs_per_iteration`: Number of epochs per iteration

#### Synthetic data configuration

- `min_spots`: Minimum number of spots per image
- `max_spots`: Maximum number of spots per image
- `min_radius`: Minimum spot radius
- `max_radius`: Maximum spot radius
- `allow_overlapping`: Whether to allow spots to overlap
- `shape_variation`: Amount of variation in spot shapes
- `add_gradients`: Whether to add intensity gradients
- `realistic_noise`: Whether to use realistic noise patterns

## Working with 3D Data

To work with 3D data:

1. Set `is_3d: true` in the configuration file
2. Ensure your data loader provides 3D volumes
3. Input shape should be `[B, C, D, H, W]`
4. Consider using smaller batch sizes due to increased memory requirements

## Troubleshooting

### Out of Memory Errors

- Reduce batch size
- Enable gradient checkpointing
- Reduce image size

### Slow Training

- Ensure GPU is being used
- Reduce image size if necessary
- Increase number of workers for data loading

### Poor Performance on Dense Regions

- Increase weight for Expert 3
- Enable density-aware weighting

### Small Spots Not Detected

- Increase weight for Expert 1
- Enable size-adaptive weighting

### Touching Spots Not Separated

- Increase boundary loss weight
- Enable contrastive loss

## Advanced Usage

### Custom Dataset

You can create a custom dataset by extending the `SpotDataset` or `SparseSpotDataset` class:

```python
from sparse_data_utils import SparseSpotDataset

class CustomDataset(SparseSpotDataset):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # Custom initialization
        
    def __getitem__(self, idx):
        sample = super().__getitem__(idx)
        # Custom processing
        return sample
```

### Custom Model

You can create a custom model by extending the `OptimizedSpotDetectionModel` class:

```python
from OptimizedSpotDetection_model import OptimizedSpotDetectionModel

class CustomModel(OptimizedSpotDetectionModel):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # Custom initialization
        
    def forward(self, x):
        # Custom forward pass
        return super().forward(x)
```

### Custom Loss Function

You can create a custom loss function by extending the `OptimizedSpotLoss` class:

```python
from OptimizedSpotLoss import OptimizedSpotLoss

class CustomLoss(OptimizedSpotLoss):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # Custom initialization
        
    def forward(self, pred, target, confidence=None):
        # Custom forward pass
        return super().forward(pred, target, confidence)
```

## Examples

### Training with synthetic data only

```bash
python run_spot_detection.py --use_synthetic --no_synthetic_data --mode train
```

### Training with real data and synthetic data

```bash
python run_spot_detection.py --data_dir /path/to/your/data --use_synthetic --mode train
```

### Training with real data only

```bash
python run_spot_detection.py --data_dir /path/to/your/data --no_synthetic --mode train
```

### Semi-supervised learning with sparse annotations

```bash
python run_spot_detection.py --data_dir /path/to/your/data --mode ssl
```

### Analyzing expert specialization

```bash
python run_spot_detection.py --data_dir /path/to/your/data --mode analyze --model_path best_spot_detection_model.pth
```
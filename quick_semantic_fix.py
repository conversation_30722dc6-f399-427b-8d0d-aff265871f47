# Quick fix for your training visualization

# In your training code, replace this line:
# sem = torch.sigmoid(outputs['semantic'])[0, 0].cpu().numpy()

# With this:
import cv2

sem_raw = torch.sigmoid(outputs['semantic'])[0, 0].cpu().numpy()
# Apply erosion to shrink the predicted masks
kernel = np.ones((3, 3), np.uint8)
sem = cv2.erode((sem_raw > 0.5).astype(np.uint8), kernel, iterations=2).astype(np.float32)

# Or use higher threshold to get smaller spots:
# sem = (sem_raw > 0.7).astype(np.float32)
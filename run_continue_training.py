from continue_training_enhanced import continue_training_with_new_alpha

# Continue training with component loss tracking
retrained_model = continue_training_with_new_alpha(
    model_checkpoint_path="/mnt/d/Users/<USER>/Documents/spot detector git amazon 2025/skeleton_aware_model_improved_spots_qwen/best_model.pth",
    image_paths=image_paths,
    mask_paths=mask_paths,
    new_alpha=0.75,
    num_epochs=100,
    initial_lr=5e-5
)
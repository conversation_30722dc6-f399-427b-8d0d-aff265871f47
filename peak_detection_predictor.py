import torch
import numpy as np
from skimage.feature import peak_local_max
import cv2
from typing import Dict, List, Tuple, Optional, Union

class PeakDetectionPredictor:
    """
    Predictor class for spot detection using direct peak detection
    """
    
    def __init__(self, 
                 min_distance=5,
                 min_intensity=0.1,
                 device=torch.device('cpu')):
        """Initialize the predictor"""
        self.min_distance = min_distance
        self.min_intensity = min_intensity
        self.device = device
    
    def detect_peaks(self, 
                    heatmap,
                    return_visualization=False,
                    original_image=None):
        """Detect peaks in the heatmap"""
        # Convert to numpy if tensor
        if isinstance(heatmap, torch.Tensor):
            heatmap = heatmap.detach().cpu().numpy()
        
        # Ensure 2D
        if heatmap.ndim > 2:
            heatmap = heatmap.squeeze()
        
        # Find local maxima directly in the heatmap
        coordinates = peak_local_max(
            heatmap,
            min_distance=self.min_distance,
            threshold_abs=self.min_intensity,
            exclude_border=False
        )
        
        # Refine peak locations using center of mass for more accurate centroids
        refined_coordinates = []
        for y, x in coordinates:
            # Extract a small region around the peak
            window_size = 7
            half_size = window_size // 2
            
            # Define region boundaries with bounds checking
            y_min = max(0, y - half_size)
            y_max = min(heatmap.shape[0], y + half_size + 1)
            x_min = max(0, x - half_size)
            x_max = min(heatmap.shape[1], x + half_size + 1)
            
            # Extract region
            region = heatmap[y_min:y_max, x_min:x_max]
            
            # Skip if region is empty
            if region.size == 0:
                refined_coordinates.append((y, x))
                continue
                
            # Calculate center of mass for more accurate centroid
            # First threshold the region to focus on the spot
            threshold = self.min_intensity
            binary_region = region > threshold
            
            # Skip if no pixels above threshold
            if not np.any(binary_region):
                refined_coordinates.append((y, x))
                continue
                
            # Calculate center of mass
            y_indices, x_indices = np.where(binary_region)
            weights = region[binary_region]
            
            # Calculate weighted centroid
            if weights.sum() > 0:
                y_cm = np.sum(y_indices * weights) / weights.sum() + y_min
                x_cm = np.sum(x_indices * weights) / weights.sum() + x_min
                refined_coordinates.append((y_cm, x_cm))
            else:
                refined_coordinates.append((y, x))
        
        # Convert to numpy array
        refined_coordinates = np.array(refined_coordinates)
        
        # Create result dictionary
        result = {
            'num_spots': len(refined_coordinates),
            'coordinates': refined_coordinates,
            'peak_values': np.array([heatmap[int(min(max(0, y), heatmap.shape[0]-1)), 
                                          int(min(max(0, x), heatmap.shape[1]-1))] 
                                    for y, x in refined_coordinates]) if len(refined_coordinates) > 0 else np.array([])
        }
        
        # Create visualization if requested
        if return_visualization:
            if original_image is None:
                # Create blank image if original not provided
                original_image = np.zeros_like(heatmap)
            
            # Create RGB visualization
            visualization = self._create_visualization(original_image, heatmap, refined_coordinates)
            result['visualization'] = visualization
        
        return result
    
    def _create_visualization(self, image, heatmap, coordinates):
        """Create visualization of detected peaks"""
        # Ensure image is normalized
        if image.max() > 1.0:
            image = image / 255.0
        
        # Create RGB image
        if image.ndim == 2:
            rgb_mask = np.zeros((*image.shape, 3))
            rgb_mask[..., 0] = image  # Red channel
            rgb_mask[..., 1] = image  # Green channel
            rgb_mask[..., 2] = image  # Blue channel
        else:
            rgb_mask = image.copy()
        
        # Refine peak locations using center of mass for more accurate centroids
        refined_coordinates = []
        for y, x in coordinates:
            # Extract a small region around the peak
            window_size = 7
            half_size = window_size // 2
            
            # Define region boundaries with bounds checking
            y_min = max(0, y - half_size)
            y_max = min(heatmap.shape[0], y + half_size + 1)
            x_min = max(0, x - half_size)
            x_max = min(heatmap.shape[1], x + half_size + 1)
            
            # Extract region
            region = heatmap[y_min:y_max, x_min:x_max]
            
            # Skip if region is empty
            if region.size == 0:
                refined_coordinates.append((y, x))
                continue
                
            # Calculate center of mass for more accurate centroid
            # First threshold the region to focus on the spot
            threshold = self.min_intensity
            binary_region = region > threshold
            
            # Skip if no pixels above threshold
            if not np.any(binary_region):
                refined_coordinates.append((y, x))
                continue
                
            # Calculate center of mass
            y_indices, x_indices = np.where(binary_region)
            weights = region[binary_region]
            
            # Calculate weighted centroid
            if weights.sum() > 0:
                y_cm = np.sum(y_indices * weights) / weights.sum() + y_min
                x_cm = np.sum(x_indices * weights) / weights.sum() + x_min
                refined_coordinates.append((y_cm, x_cm))
            else:
                refined_coordinates.append((y, x))
        
        # Draw circles at refined peak locations
        for i, (y, x) in enumerate(refined_coordinates):
            # Get intensity at this peak (using nearest integer coordinates)
            y_int, x_int = int(round(y)), int(round(x))
            y_int = max(0, min(y_int, heatmap.shape[0]-1))
            x_int = max(0, min(x_int, heatmap.shape[1]-1))
            intensity = heatmap[y_int, x_int]
            
            # Calculate radius using Gaussian fitting for more accurate spot size
            # Extract a region around the peak for fitting
            window_size = 21  # Larger window to capture full spot profile
            half_size = window_size // 2
            
            # Define region boundaries with bounds checking
            y_min = max(0, int(y) - half_size)
            y_max = min(heatmap.shape[0], int(y) + half_size + 1)
            x_min = max(0, int(x) - half_size)
            x_max = min(heatmap.shape[1], int(x) + half_size + 1)
            
            # Extract region
            region = heatmap[y_min:y_max, x_min:x_max].copy()
            
            if region.size > 0:
                # Create meshgrid for the region
                y_indices, x_indices = np.indices(region.shape)
                
                # Center coordinates within the region
                y_center = int(y) - y_min
                x_center = int(x) - x_min
                
                # Calculate distance from center for each pixel
                distances = np.sqrt((y_indices - y_center)**2 + (x_indices - x_center)**2)
                
                # Get non-zero values
                valid_mask = region > 0.05 * intensity  # Consider points with at least 5% of peak intensity
                if np.any(valid_mask):
                    valid_distances = distances[valid_mask]
                    valid_intensities = region[valid_mask]
                    
                    # Calculate intensity-weighted average distance (radius)
                    if valid_intensities.sum() > 0:
                        weighted_radius = np.sum(valid_distances * valid_intensities) / valid_intensities.sum()
                        
                        # Scale to get the effective radius (where intensity drops significantly)
                        # FWHM (Full Width at Half Maximum) for Gaussian is 2.355 * sigma
                        r = int(weighted_radius * 0.85)  # Slightly smaller than weighted average
                    else:
                        r = 3  # Default if calculation fails
                else:
                    r = 3  # Default if no valid points
            else:
                r = 3  # Default if region extraction fails
            
            # Ensure minimum and maximum radius
            r = max(2, min(r, 7))
            
            # Draw circle
            cv2.circle(
                rgb_mask,
                (int(round(x)), int(round(y))),
                r,
                (1, 0, 0),  # Red
                1
            )
            
            # Add ID
            cv2.putText(
                rgb_mask,
                str(i+1),
                (int(round(x)), int(round(y))),
                cv2.FONT_HERSHEY_SIMPLEX,
                0.3,
                (0, 1, 0),  # Green
                1
            )
        
        return rgb_mask
    
    def update_parameters(self, min_distance=None, min_intensity=None):
        """Update detection parameters"""
        if min_distance is not None:
            self.min_distance = min_distance
        if min_intensity is not None:
            self.min_intensity = min_intensity
    
    def get_parameters(self):
        """Get current parameters"""
        return {
            'min_distance': self.min_distance,
            'min_intensity': self.min_intensity
        }
    
    def save_parameters(self, path):
        """Save parameters to file"""
        params = self.get_parameters()
        np.save(path, params)
        
    def load_parameters(self, path):
        """Load parameters from file"""
        params = np.load(path, allow_pickle=True).item()
        self.update_parameters(
            min_distance=params.get('min_distance', self.min_distance),
            min_intensity=params.get('min_intensity', self.min_intensity)
        )
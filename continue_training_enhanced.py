import torch
import numpy as np
from tqdm import tqdm

def continue_training_with_new_alpha(model_checkpoint_path, image_paths, mask_paths, 
                                    new_alpha=0.75, num_epochs=50, initial_lr=5e-5):
    """
    Continue training from a checkpoint with a new alpha value for fine-tuning
    Includes component loss tracking and display similar to main training loop
    """
    print(f"Loading model from {model_checkpoint_path}...")
    
    # Load the trained model
    model = SkeletonAwareSpotDetector(in_ch=1, base_ch=48)
    checkpoint = torch.load(model_checkpoint_path)
    model.load_state_dict(checkpoint['model_state_dict'])
    model.to(device)
    
    # Create new data loaders with updated alpha
    print(f"Creating data loaders with alpha={new_alpha}...")
    train_loader, val_loader = create_skeleton_aware_data_loaders(
        image_paths, mask_paths, 
        batch_size=8,  # Smaller batch for fine-tuning
        patch_size=256,
        num_workers=4,
        alpha=new_alpha  # New alpha value
    )
    
    # Setup optimizer with lower learning rate for fine-tuning
    optimizer = torch.optim.AdamW(
        model.parameters(), 
        lr=initial_lr,  # Much lower LR for fine-tuning
        weight_decay=1e-4,
        betas=(0.9, 0.999)
    )
    
    # Setup criterion
    criterion = SkeletonAwareLoss()
    
    # Setup scheduler
    scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
        optimizer, T_max=num_epochs, eta_min=1e-6
    )
    
    # Mixed precision training
    scaler = torch.cuda.amp.GradScaler()
    
    # Training tracking
    train_losses = []
    val_losses = []
    best_loss = float('inf')
    patience = 15
    early_stop_counter = 0
    
    print(f"Starting fine-tuning for {num_epochs} epochs with alpha={new_alpha}...")
    
    for epoch in range(num_epochs):
        # Training
        model.train()
        epoch_train_losses = []
        component_losses = {'semantic': 0, 'sdt': 0, 'skeleton': 0, 'centroid': 0, 'flow': 0}
        
        pbar = tqdm(train_loader, desc=f"Epoch {epoch+1}/{num_epochs}")
        for batch_idx, (images, targets) in enumerate(pbar):
            images = images.to(device, non_blocking=True)
            targets = targets.to(device, non_blocking=True)
            
            optimizer.zero_grad(set_to_none=True)
            
            # Mixed precision forward pass
            with torch.cuda.amp.autocast():
                outputs = model(images)
                loss, loss_dict = criterion(outputs, targets)
            
            if torch.isnan(loss):
                print(f"NaN loss at epoch {epoch}, batch {batch_idx}")
                continue
            
            # Mixed precision backward pass
            scaler.scale(loss).backward()
            scaler.unscale_(optimizer)
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            scaler.step(optimizer)
            scaler.update()
            
            epoch_train_losses.append(loss.item())
            
            # Track component losses
            for k, v in loss_dict.items():
                if k in component_losses:
                    component_losses[k] += v.item()
            
            # Update progress bar
            pbar.set_postfix({
                'Loss': f'{loss.item():.4f}',
                'Sem': f'{loss_dict["semantic"].item():.3f}',
                'Skel': f'{loss_dict["skeleton"].item():.3f}'
            })
        
        # Step scheduler
        scheduler.step()
        
        # Average component losses
        for k in component_losses:
            component_losses[k] /= len(train_loader)
        
        train_losses.append(np.mean(epoch_train_losses))
        
        # Validation
        model.eval()
        epoch_val_losses = []
        
        with torch.no_grad():
            for images, targets in val_loader:
                images = images.to(device, non_blocking=True)
                targets = targets.to(device, non_blocking=True)
                
                outputs = model(images)
                loss, _ = criterion(outputs, targets)
                
                if not torch.isnan(loss):
                    epoch_val_losses.append(loss.item())
        
        val_losses.append(np.mean(epoch_val_losses))
        
        # Print component losses
        components_str = ", ".join([f"{k}: {v:.4f}" for k, v in component_losses.items()])
        print(f"Epoch {epoch+1}: Train={train_losses[-1]:.4f}, Val={val_losses[-1]:.4f}")
        print(f"Components: {components_str}")
        
        # Save best model
        if val_losses[-1] < best_loss:
            best_loss = val_losses[-1]
            # Save in same folder as original checkpoint
            checkpoint_dir = os.path.dirname(model_checkpoint_path)
            save_path = os.path.join(checkpoint_dir, 'best_model_retrained.pth')
            torch.save({
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'epoch': epoch,
                'loss': best_loss,
                'alpha': new_alpha
            }, save_path)
            print(f"✅ Best model saved (val_loss={best_loss:.4f}) with alpha={new_alpha}")
            early_stop_counter = 0
        else:
            early_stop_counter += 1
            if early_stop_counter >= patience:
                print(f"⚠️ Early stopping after {patience} epochs without improvement")
                break
    
    return model
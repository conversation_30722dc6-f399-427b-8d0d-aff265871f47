import torch
import numpy as np
import matplotlib.pyplot as plt
import tifffile
from pathlib import Path
from OptimizedSpotDetection_model import OptimizedSpotDetectionModel
from scipy.ndimage import label
import cv2

def debug_inference(
    model_path: str,
    image_path: str,
    output_dir: str,
    patch_size: int = 256,
    overlap: int = 32,
    threshold: float = 0.5,
    min_size: int = 4
):
    """
    Debug inference process with visualization of each step
    """
    # Create output directory
    output_dir = Path(output_dir)
    output_dir.mkdir(exist_ok=True, parents=True)
    
    # Load model
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = OptimizedSpotDetectionModel(in_channels=1, base_filters=32)
    
    checkpoint = torch.load(model_path, map_location=device)
    if 'model_state_dict' in checkpoint:
        model.load_state_dict(checkpoint['model_state_dict'])
    else:
        model.load_state_dict(checkpoint)
    
    model.to(device)
    model.eval()
    
    # Load image
    image = tifffile.imread(image_path).astype(np.float32)
    if len(image.shape) == 3:
        image = image[:, :, 0] if image.shape[2] > 1 else image.squeeze()
    
    # Save original image
    tifffile.imwrite(output_dir / 'original_image.tif', image)
    plt.figure(figsize=(8, 8))
    plt.imshow(image, cmap='gray')
    plt.title('Original Image')
    plt.savefig(output_dir / 'original_image.png', dpi=150)
    plt.close()
    
    # Normalize image
    image_norm = image / (image.max() + 1e-8)
    H, W = image_norm.shape
    
    # Save normalized image
    tifffile.imwrite(output_dir / 'normalized_image.tif', (image_norm * 255).astype(np.uint8))
    plt.figure(figsize=(8, 8))
    plt.imshow(image_norm, cmap='gray')
    plt.title('Normalized Image')
    plt.savefig(output_dir / 'normalized_image.png', dpi=150)
    plt.close()
    
    # Tiling parameters
    stride = patch_size - overlap
    n_h = (H + stride - 1) // stride
    n_w = (W + stride - 1) // stride
    
    # Initialize output arrays
    heatmap_full = np.zeros((H, W), dtype=np.float32)
    weight_map = np.zeros((H, W), dtype=np.float32)
    
    # Process image in tiles
    with torch.no_grad():
        for i in range(n_h):
            for j in range(n_w):
                # Extract tile coordinates
                start_h = i * stride
                start_w = j * stride
                end_h = min(start_h + patch_size, H)
                end_w = min(start_w + patch_size, W)
                
                # Extract and pad tile if needed
                patch = image_norm[start_h:end_h, start_w:end_w]
                pad_h = patch_size - patch.shape[0]
                pad_w = patch_size - patch.shape[1]
                if pad_h > 0 or pad_w > 0:
                    patch = np.pad(patch, ((0, pad_h), (0, pad_w)), 'reflect')
                
                # Save patch
                patch_dir = output_dir / f'patch_{i}_{j}'
                patch_dir.mkdir(exist_ok=True)
                tifffile.imwrite(patch_dir / 'patch.tif', (patch * 255).astype(np.uint8))
                
                # Convert to tensor and run inference
                patch_tensor = torch.from_numpy(patch).unsqueeze(0).unsqueeze(0).to(device)
                outputs = model(patch_tensor)
                
                # Save raw outputs
                heatmap = torch.sigmoid(outputs['heatmap']).cpu().numpy()[0, 0]
                tifffile.imwrite(patch_dir / 'heatmap.tif', (heatmap * 255).astype(np.uint8))
                
                # Save expert outputs if available
                if 'expert_outputs' in outputs:
                    for k, exp_out in enumerate(outputs['expert_outputs']):
                        exp_heatmap = torch.sigmoid(exp_out).cpu().numpy()[0, 0]
                        tifffile.imwrite(patch_dir / f'expert_{k+1}.tif', (exp_heatmap * 255).astype(np.uint8))
                
                # Remove padding if needed
                if pad_h > 0 or pad_w > 0:
                    heatmap = heatmap[:patch_size-pad_h, :patch_size-pad_w]
                
                # Create weight map for blending
                weight = np.ones_like(heatmap)
                if overlap > 0:
                    # Taper edges for smooth blending
                    taper = min(overlap // 2, 16)
                    if start_h > 0:
                        weight[:taper, :] *= np.linspace(0, 1, taper)[:, None]
                    if end_h < H:
                        weight[-taper:, :] *= np.linspace(1, 0, taper)[:, None]
                    if start_w > 0:
                        weight[:, :taper] *= np.linspace(0, 1, taper)[None, :]
                    if end_w < W:
                        weight[:, -taper:] *= np.linspace(1, 0, taper)[None, :]
                
                # Accumulate weighted results
                heatmap_full[start_h:end_h, start_w:end_w] += heatmap * weight
                weight_map[start_h:end_h, start_w:end_w] += weight
    
    # Normalize by weights
    heatmap_full /= np.maximum(weight_map, 1e-8)
    
    # Save full heatmap
    tifffile.imwrite(output_dir / 'heatmap_full.tif', (heatmap_full * 255).astype(np.uint8))
    plt.figure(figsize=(8, 8))
    plt.imshow(heatmap_full, cmap='hot')
    plt.title('Full Heatmap')
    plt.colorbar()
    plt.savefig(output_dir / 'heatmap_full.png', dpi=150)
    plt.close()
    
    # Create binary mask
    binary_mask = (heatmap_full > threshold).astype(np.uint8)
    tifffile.imwrite(output_dir / 'binary_mask.tif', binary_mask * 255)
    plt.figure(figsize=(8, 8))
    plt.imshow(binary_mask, cmap='gray')
    plt.title(f'Binary Mask (threshold={threshold})')
    plt.savefig(output_dir / 'binary_mask.png', dpi=150)
    plt.close()
    
    # Remove small objects
    if min_size > 1:
        num_labels, labels = cv2.connectedComponents(binary_mask)
        filtered_mask = np.zeros_like(binary_mask)
        for i in range(1, num_labels):
            if np.sum(labels == i) >= min_size:
                filtered_mask[labels == i] = 1
        binary_mask = filtered_mask
    
    # Save filtered mask
    tifffile.imwrite(output_dir / 'filtered_mask.tif', binary_mask * 255)
    plt.figure(figsize=(8, 8))
    plt.imshow(binary_mask, cmap='gray')
    plt.title(f'Filtered Mask (min_size={min_size})')
    plt.savefig(output_dir / 'filtered_mask.png', dpi=150)
    plt.close()
    
    # Label connected components
    instance_labels, num_spots = label(binary_mask)
    
    # Save instance labels
    tifffile.imwrite(output_dir / 'instance_labels.tif', instance_labels.astype(np.uint16))
    plt.figure(figsize=(8, 8))
    plt.imshow(instance_labels, cmap='nipy_spectral')
    plt.title(f'Instance Labels ({num_spots} spots)')
    plt.savefig(output_dir / 'instance_labels.png', dpi=150)
    plt.close()
    
    # Extract spot properties
    spots = []
    centroids_map = np.zeros_like(binary_mask, dtype=np.uint8)
    
    for spot_id in range(1, num_spots + 1):
        # Get spot mask
        spot_mask = (instance_labels == spot_id)
        
        # Calculate properties
        y_indices, x_indices = np.where(spot_mask)
        cy, cx = np.mean(y_indices), np.mean(x_indices)
        area = len(y_indices)
        intensity = np.mean(image[spot_mask])
        score = np.mean(heatmap_full[spot_mask])
        
        # Add to results
        spots.append({
            'x': float(cx),
            'y': float(cy),
            'score': float(score),
            'size': int(area),
            'intensity': float(intensity)
        })
        
        # Mark centroid
        cy_int, cx_int = int(round(cy)), int(round(cx))
        centroids_map[cy_int, cx_int] = 255
    
    # Save centroids
    tifffile.imwrite(output_dir / 'centroids.tif', centroids_map)
    plt.figure(figsize=(8, 8))
    plt.imshow(image_norm, cmap='gray')
    plt.imshow(centroids_map, cmap='hot', alpha=0.5)
    plt.title(f'Centroids ({len(spots)} spots)')
    plt.savefig(output_dir / 'centroids.png', dpi=150)
    plt.close()
    
    # Save spots as CSV
    import pandas as pd
    df = pd.DataFrame(spots)
    df.to_csv(output_dir / 'spots.csv', index=False)
    
    # Create final visualization
    plt.figure(figsize=(15, 10))
    
    plt.subplot(2, 3, 1)
    plt.imshow(image_norm, cmap='gray')
    plt.title('Original Image')
    plt.axis('off')
    
    plt.subplot(2, 3, 2)
    plt.imshow(heatmap_full, cmap='hot')
    plt.title('Heatmap')
    plt.axis('off')
    
    plt.subplot(2, 3, 3)
    plt.imshow(binary_mask, cmap='gray')
    plt.title(f'Binary Mask (threshold={threshold})')
    plt.axis('off')
    
    plt.subplot(2, 3, 4)
    plt.imshow(instance_labels, cmap='nipy_spectral')
    plt.title(f'Instance Labels ({num_spots} spots)')
    plt.axis('off')
    
    plt.subplot(2, 3, 5)
    plt.imshow(image_norm, cmap='gray')
    plt.imshow(centroids_map, cmap='hot', alpha=0.5)
    plt.title('Centroids')
    plt.axis('off')
    
    plt.subplot(2, 3, 6)
    plt.imshow(image_norm, cmap='gray')
    for spot in spots:
        plt.plot(spot['x'], spot['y'], 'r+')
        plt.text(spot['x'], spot['y'], f"{spot['score']:.2f}", color='yellow', fontsize=8)
    plt.title('Spots with Scores')
    plt.axis('off')
    
    plt.tight_layout()
    plt.savefig(output_dir / 'summary.png', dpi=150)
    plt.close()
    
    print(f"All debug outputs saved to {output_dir}")
    
    return {
        'image': image,
        'heatmap': heatmap_full,
        'binary_mask': binary_mask,
        'instance_labels': instance_labels,
        'centroids': centroids_map,
        'spots': spots
    }

if __name__ == "__main__":
    import argparse
    parser = argparse.ArgumentParser(description='Debug inference process')
    parser.add_argument('--model', type=str, required=True, help='Path to model weights')
    parser.add_argument('--image', type=str, required=True, help='Path to input image')
    parser.add_argument('--output', type=str, required=True, help='Output directory')
    parser.add_argument('--patch_size', type=int, default=256, help='Patch size for tiling')
    parser.add_argument('--overlap', type=int, default=32, help='Overlap between patches')
    parser.add_argument('--threshold', type=float, default=0.5, help='Threshold for binary mask')
    parser.add_argument('--min_size', type=int, default=4, help='Minimum spot size')
    args = parser.parse_args()
    
    debug_inference(
        args.model, 
        args.image, 
        args.output, 
        args.patch_size, 
        args.overlap, 
        args.threshold,
        args.min_size
    )
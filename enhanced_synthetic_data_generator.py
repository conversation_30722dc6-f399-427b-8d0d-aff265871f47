import numpy as np
import random
import cv2
from typing import Tuple, List, Dict, Optional
from synthetic_data_generator import AdvancedSyntheticSpotGenerator

class EnhancedSyntheticSpotGenerator(AdvancedSyntheticSpotGenerator):
    """
    Enhanced generator for synthetic spot data that stores spot centroids and masks
    
    This class extends AdvancedSyntheticSpotGenerator to store spot centroids and masks
    for each generated spot, which can be used for more accurate spot detection during training.
    """
    
    def __init__(self, 
                image_size=(256, 256),
                min_spots=5,
                max_spots=50,
                min_radius=2,
                max_radius=10,
                density_factor=1.0,
                mask_threshold=0.15,
                allow_touching=True,
                shape_variation=0.3,
                add_gradients=True,
                realistic_noise=True):
        """Initialize the enhanced synthetic spot generator"""
        super().__init__(
            image_size=image_size,
            min_spots=min_spots,
            max_spots=max_spots,
            min_radius=min_radius,
            max_radius=max_radius,
            density_factor=density_factor,
            mask_threshold=mask_threshold,
            allow_touching=allow_touching,
            shape_variation=shape_variation,
            add_gradients=add_gradients,
            realistic_noise=realistic_noise
        )
    
    def generate_sample(self) -> Tuple[np.ndarray, np.ndarray, List[Dict]]:
        """
        Generate a single enhanced synthetic sample
        
        Returns:
            image: Synthetic image as numpy array
            mask: Mask with unique IDs for each spot
            spot_data: List of dictionaries with spot information (centroid, radius, etc.)
        """
        # Create empty image and mask
        height, width = self.image_size
        image = np.zeros((height, width), dtype=np.float32)
        mask = np.zeros((height, width), dtype=np.float32)
        
        # Determine number of spots (variable density)
        num_spots = int(random.randint(self.min_spots, self.max_spots) * self.density_factor)
        
        # Generate background
        background_value = random.uniform(self.background_level[0], self.background_level[1])
        
        # Add gradient if enabled
        if self.add_gradients:
            # Create a random gradient direction
            angle = random.uniform(0, 2 * np.pi)
            gradient_strength = random.uniform(0.05, 0.2)
            
            # Create gradient
            y_grid, x_grid = np.mgrid[0:height, 0:width]
            gradient = (x_grid * np.cos(angle) + y_grid * np.sin(angle)) / np.sqrt(height**2 + width**2)
            gradient = gradient * gradient_strength + background_value
        else:
            gradient = np.ones((height, width)) * background_value
        
        # Add noise
        if self.realistic_noise:
            # Create correlated noise
            noise_scale = random.uniform(self.noise_level[0], self.noise_level[1])
            small_noise = np.random.normal(0, noise_scale, (height//8, width//8))
            noise = cv2.resize(small_noise, (width, height))
            
            # Add some fine grain noise
            fine_noise = np.random.normal(0, noise_scale/2, (height, width))
            noise = noise * 0.7 + fine_noise * 0.3
        else:
            noise_level = random.uniform(self.noise_level[0], self.noise_level[1])
            noise = np.random.normal(0, noise_level, (height, width))
        
        # Combine background and noise
        image = gradient + noise
        
        # Keep track of spot centers and radii
        spot_centers = []
        spot_data = []  # Store detailed spot information
        
        # Generate spots with variable sizes and shapes
        for spot_id in range(1, num_spots + 1):
            # Try to place a spot without overlapping (max 10 attempts)
            for attempt in range(10):
                # Random position
                x = random.randint(10, width - 10)
                y = random.randint(10, height - 10)
                
                # Random size (radius)
                radius = random.randint(self.min_radius, self.max_radius)
                
                # Check if this spot would overlap with existing spots
                valid_position = True
                for cx, cy, cr in spot_centers:
                    # Calculate distance between centers
                    dist = np.sqrt((x - cx)**2 + (y - cy)**2)
                    
                    # Allow spots to be closer together (touching or slightly overlapping)
                    # Instead of requiring dist >= radius + cr, we'll use a smaller threshold
                    # This will make spots touch more often
                    min_distance = (radius + cr) * 0.7  # Reduce minimum distance to 70% of sum of radii
                    
                    if dist < min_distance:
                        valid_position = False
                        break
                
                if valid_position:
                    # Add to spot centers list
                    spot_centers.append((x, y, radius))
                    
                    # Random intensity
                    intensity = random.uniform(self.min_intensity, self.max_intensity)
                    
                    # Create base distance grid
                    y_grid, x_grid = np.ogrid[-y:height-y, -x:width-x]
                    
                    # Add shape variation if enabled
                    if self.shape_variation > 0:
                        # Create random deformation
                        angle = random.uniform(0, 2 * np.pi)
                        stretch = 1.0 + random.uniform(0, self.shape_variation)
                        
                        # Apply deformation
                        x_deform = x_grid * np.cos(angle) + y_grid * np.sin(angle)
                        y_deform = -x_grid * np.sin(angle) + y_grid * np.cos(angle)
                        
                        # Stretch in one direction
                        x_deform = x_deform * stretch
                        
                        # Calculate distance with deformation
                        dist = np.sqrt(x_deform**2 + y_deform**2)
                    else:
                        # Regular circular distance
                        dist = np.sqrt(x_grid**2 + y_grid**2)
                    
                    # Create spot in image
                    spot = np.exp(-(dist**2) / (2 * (radius/2)**2)) * intensity
                    
                    # Add spot to image
                    image += spot
                    
                    # Create spot in mask with unique ID
                    # Use a much higher threshold for the mask to make it smaller
                    # The mask_threshold parameter should be much higher (e.g., 0.5 or higher)
                    intensity_threshold = self.mask_threshold * intensity
                    mask_spot = (spot > intensity_threshold).astype(np.float32)
                    mask[mask_spot > 0] = spot_id  # Assign unique ID to each spot
                    
                    # Store spot data
                    spot_data.append({
                        'id': spot_id,
                        'x': x,
                        'y': y,
                        'radius': radius,
                        'intensity': intensity,
                        'angle': angle if self.shape_variation > 0 else 0,
                        'stretch': stretch if self.shape_variation > 0 else 1.0
                    })
                    
                    # Successfully placed this spot, break the attempt loop
                    break
        
        # Ensure image values are in [0, 1]
        image = np.clip(image, 0, 1)
        
        return image, mask, spot_data
    
    def generate_batch(self, batch_size: int) -> Tuple[List[np.ndarray], List[np.ndarray], List[List[Dict]]]:
        """
        Generate a batch of enhanced synthetic samples
        
        Args:
            batch_size: Number of samples to generate
            
        Returns:
            images: List of synthetic images
            masks: List of masks with unique IDs for each spot
            spot_data_list: List of spot data for each sample
        """
        images = []
        masks = []
        spot_data_list = []
        
        for _ in range(batch_size):
            image, mask, spot_data = self.generate_sample()
            images.append(image)
            masks.append(mask)
            spot_data_list.append(spot_data)
        
        return images, masks, spot_data_list
    
    def generate_dataset(self, 
                        num_samples: int, 
                        variable_params: bool = True) -> Tuple[List[np.ndarray], List[np.ndarray], List[List[Dict]]]:
        """
        Generate a dataset of enhanced synthetic samples
        
        Args:
            num_samples: Number of samples to generate
            variable_params: Whether to vary parameters between batches
            
        Returns:
            images: List of synthetic images
            masks: List of masks with unique IDs for each spot
            spot_data_list: List of spot data for each sample
        """
        images = []
        masks = []
        spot_data_list = []
        
        for i in range(num_samples):
            # Optionally vary parameters between batches
            if variable_params and i % 10 == 0:
                self.min_spots = random.randint(3, 10)
                self.max_spots = random.randint(20, 100)
                self.min_radius = random.randint(1, 3)
                self.max_radius = random.randint(5, 15)
            
            image, mask, spot_data = self.generate_sample()
            images.append(image)
            masks.append(mask)
            spot_data_list.append(spot_data)
        
        return images, masks, spot_data_list
    
    def visualize_sample(self, image, mask, spot_data):
        """
        Visualize a synthetic sample with spot centroids and masks
        
        Args:
            image: Synthetic image
            mask: Mask with unique IDs for each spot
            spot_data: List of dictionaries with spot information
            
        Returns:
            visualization: RGB visualization of the sample
        """
        # Create RGB visualization
        rgb_vis = np.zeros((*image.shape, 3))
        rgb_vis[..., 0] = image  # Red channel
        rgb_vis[..., 1] = image  # Green channel
        rgb_vis[..., 2] = image  # Blue channel
        
        # Draw spot centroids and outlines
        for spot in spot_data:
            x, y = spot['x'], spot['y']
            radius = spot['radius']
            spot_id = spot['id']
            
            # Draw circle at spot centroid
            cv2.circle(
                rgb_vis,
                (int(x), int(y)),
                radius,
                (1, 0, 0),  # Red
                1
            )
            
            # Add spot ID
            cv2.putText(
                rgb_vis,
                str(spot_id),
                (int(x), int(y)),
                cv2.FONT_HERSHEY_SIMPLEX,
                0.3,
                (0, 1, 0),  # Green
                1
            )
        
        return rgb_vis
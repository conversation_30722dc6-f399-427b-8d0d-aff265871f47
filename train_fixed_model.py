import torch
import torch.nn as nn
from torch.utils.data import DataLoader
import matplotlib.pyplot as plt
import numpy as np
from spot_detector_fixed import SpotDetectorFixed, SimpleFocalLoss, post_process_predictions
from dataset_fixed import MicroscopyDatasetFixed, visualize_heatmap_comparison

def train_fixed_model(train_dataset, val_dataset, num_epochs=50):
    """
    Training function for the fixed spot detection model
    """
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"Using device: {device}")
    
    # Create model
    model = SpotDetectorFixed(input_channels=1, dropout=0.1)
    model = model.to(device)
    
    # Loss and optimizer
    criterion = SimpleFocalLoss(alpha=2, gamma=2)
    optimizer = torch.optim.AdamW(model.parameters(), lr=1e-4, weight_decay=0.01)
    scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=num_epochs)
    
    # Data loaders
    train_loader = DataLoader(train_dataset, batch_size=8, shuffle=True, num_workers=2)
    val_loader = DataLoader(val_dataset, batch_size=8, shuffle=False, num_workers=2)
    
    # Training loop
    train_losses = []
    val_losses = []
    
    for epoch in range(num_epochs):
        # Training phase
        model.train()
        train_loss = 0.0
        
        for batch_idx, (images, targets) in enumerate(train_loader):
            images = images.to(device)
            targets = targets.to(device)
            
            optimizer.zero_grad()
            
            # Forward pass
            outputs = model(images)
            loss = criterion(outputs, targets)
            
            # Backward pass
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item()
            
            if batch_idx % 10 == 0:
                print(f'Epoch {epoch}, Batch {batch_idx}, Loss: {loss.item():.4f}')
        
        # Validation phase
        model.eval()
        val_loss = 0.0
        
        with torch.no_grad():
            for images, targets in val_loader:
                images = images.to(device)
                targets = targets.to(device)
                
                outputs = model(images)
                loss = criterion(outputs, targets)
                val_loss += loss.item()
        
        # Calculate average losses
        avg_train_loss = train_loss / len(train_loader)
        avg_val_loss = val_loss / len(val_loader)
        
        train_losses.append(avg_train_loss)
        val_losses.append(avg_val_loss)
        
        print(f'Epoch {epoch+1}/{num_epochs}:')
        print(f'  Train Loss: {avg_train_loss:.4f}')
        print(f'  Val Loss: {avg_val_loss:.4f}')
        print(f'  LR: {scheduler.get_last_lr()[0]:.6f}')
        
        scheduler.step()
        
        # Save best model
        if epoch == 0 or avg_val_loss < min(val_losses[:-1]):
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'train_loss': avg_train_loss,
                'val_loss': avg_val_loss,
            }, 'best_fixed_spot_detector.pth')
            print(f'  New best model saved!')
    
    return model, train_losses, val_losses


def test_inference(model, test_dataset, device, num_samples=5):
    """
    Test inference and visualize results
    """
    model.eval()
    
    fig, axes = plt.subplots(num_samples, 3, figsize=(15, 5*num_samples))
    if num_samples == 1:
        axes = axes.reshape(1, -1)
    
    with torch.no_grad():
        for i in range(num_samples):
            # Get sample
            image, target = test_dataset[i]
            image_batch = image.unsqueeze(0).to(device)
            
            # Inference
            start_time = torch.cuda.Event(enable_timing=True)
            end_time = torch.cuda.Event(enable_timing=True)
            
            start_time.record()
            prediction = model(image_batch)
            end_time.record()
            
            torch.cuda.synchronize()
            inference_time = start_time.elapsed_time(end_time)
            
            # Convert to numpy for visualization
            image_np = image.squeeze().cpu().numpy()
            target_np = target.squeeze().cpu().numpy()
            pred_np = prediction.squeeze().cpu().numpy()
            
            # Post-process to get spot locations
            spot_coords = post_process_predictions(pred_np, threshold=0.3, min_distance=5)
            
            # Visualize
            axes[i, 0].imshow(image_np, cmap='gray')
            axes[i, 0].set_title(f'Original Image {i+1}')
            axes[i, 0].axis('off')
            
            axes[i, 1].imshow(target_np, cmap='hot')
            axes[i, 1].set_title(f'Ground Truth Heatmap')
            axes[i, 1].axis('off')
            
            axes[i, 2].imshow(image_np, cmap='gray', alpha=0.7)
            axes[i, 2].imshow(pred_np, cmap='hot', alpha=0.6)
            # Mark detected spots
            if len(spot_coords) > 0:
                axes[i, 2].scatter(spot_coords[:, 1], spot_coords[:, 0], 
                                 c='cyan', s=20, marker='x')
            axes[i, 2].set_title(f'Prediction (Time: {inference_time:.1f}ms, Spots: {len(spot_coords)})')
            axes[i, 2].axis('off')
            
            print(f"Sample {i+1}: Detected {len(spot_coords)} spots in {inference_time:.1f}ms")
    
    plt.tight_layout()
    plt.show()


def compare_heatmap_quality(old_dataset, new_dataset, idx=0):
    """
    Compare heatmap quality between old and new dataset implementations
    """
    fig, axes = plt.subplots(2, 3, figsize=(15, 10))
    
    # Old dataset (assuming it exists)
    try:
        old_image, old_target = old_dataset[idx]
        old_image_np = old_image.squeeze().numpy() if torch.is_tensor(old_image) else old_image
        old_target_np = old_target[2].squeeze().numpy() if torch.is_tensor(old_target) else old_target[2]  # Heatmap channel
        
        axes[0, 0].imshow(old_image_np, cmap='gray')
        axes[0, 0].set_title('Old Dataset - Image')
        axes[0, 0].axis('off')
        
        axes[0, 1].imshow(old_target_np, cmap='hot')
        axes[0, 1].set_title('Old Dataset - Grid-like Heatmap')
        axes[0, 1].axis('off')
        
        axes[0, 2].imshow(old_image_np, cmap='gray', alpha=0.7)
        axes[0, 2].imshow(old_target_np, cmap='hot', alpha=0.5)
        axes[0, 2].set_title('Old Dataset - Overlay')
        axes[0, 2].axis('off')
        
    except:
        for j in range(3):
            axes[0, j].text(0.5, 0.5, 'Old dataset\nnot available', 
                           ha='center', va='center', transform=axes[0, j].transAxes)
            axes[0, j].axis('off')
    
    # New dataset
    new_image, new_target = new_dataset[idx]
    new_image_np = new_image.squeeze().numpy() if torch.is_tensor(new_image) else new_image
    new_target_np = new_target.squeeze().numpy() if torch.is_tensor(new_target) else new_target
    
    axes[1, 0].imshow(new_image_np, cmap='gray')
    axes[1, 0].set_title('Fixed Dataset - Image')
    axes[1, 0].axis('off')
    
    axes[1, 1].imshow(new_target_np, cmap='hot')
    axes[1, 1].set_title('Fixed Dataset - Smooth Heatmap')
    axes[1, 1].axis('off')
    
    axes[1, 2].imshow(new_image_np, cmap='gray', alpha=0.7)
    axes[1, 2].imshow(new_target_np, cmap='hot', alpha=0.5)
    axes[1, 2].set_title('Fixed Dataset - Overlay')
    axes[1, 2].axis('off')
    
    plt.tight_layout()
    plt.show()
    
    print("Key differences:")
    print("- Old: Grid-like artifacts from window-based processing")
    print("- New: Smooth Gaussian heatmaps centered on actual spot locations")
    print("- New: Better spatial continuity and localization accuracy")


if __name__ == "__main__":
    # Example usage (you'll need to provide your actual image and mask paths)
    print("This is a template - replace with your actual data paths")
    
    # Example paths (replace with your actual paths)
    # train_image_paths = [...]  # Your training image paths
    # train_mask_paths = [...]   # Your training mask paths
    # val_image_paths = [...]    # Your validation image paths  
    # val_mask_paths = [...]     # Your validation mask paths
    
    # Create datasets
    # train_dataset = MicroscopyDatasetFixed(train_image_paths, train_mask_paths, 
    #                                       patch_size=256, gaussian_sigma=3.0)
    # val_dataset = MicroscopyDatasetFixed(val_image_paths, val_mask_paths,
    #                                     patch_size=256, gaussian_sigma=3.0)
    
    # Train model
    # model, train_losses, val_losses = train_fixed_model(train_dataset, val_dataset)
    
    # Test inference
    # device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    # test_inference(model, val_dataset, device)
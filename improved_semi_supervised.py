import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import DataLoader
from torch.utils.tensorboard import SummaryWriter
import numpy as np
from tqdm import tqdm
import os
from typing import Dict, List, Tuple, Optional
import time

class ImprovedSemiSupervisedTrainer:
    """
    Improved Semi-Supervised Learning for Spot Detection
    
    Key improvements:
    1. Progressive confidence thresholds
    2. Consistency regularization with augmentations
    3. Curriculum learning (easy to hard examples)
    4. Better pseudo-label quality assessment
    5. Adaptive learning rates
    """
    
    def __init__(self, 
                 model: nn.Module,
                 loss_fn: nn.Module,
                 optimizer: torch.optim.Optimizer,
                 scheduler: Optional[object] = None,
                 device: str = 'cuda',
                 tensorboard_dir: str = './tensorboard'):
        
        self.model = model
        self.loss_fn = loss_fn
        self.optimizer = optimizer
        self.scheduler = scheduler
        self.device = device
        self.writer = SummaryWriter(tensorboard_dir)
        
        # SSL-specific parameters
        self.consistency_weight = 1.0
        self.confidence_threshold_start = 0.7  # Start with lower threshold
        self.confidence_threshold_end = 0.95   # End with higher threshold
        self.pseudo_label_weight = 0.5         # Weight for pseudo-labeled samples
        
    def train_semi_supervised(self,
                            train_dataset,
                            val_dataset,
                            unlabeled_dataset=None,
                            num_ssl_iterations: int = 5,
                            epochs_per_iteration: int = 15,  # Much fewer epochs
                            batch_size: int = 16,
                            num_workers: int = 4) -> Dict:
        """
        Perform improved semi-supervised learning
        """
        
        print("🚀 Starting Improved Semi-Supervised Learning")
        print(f"📊 Initial training set size: {len(train_dataset)}")
        print(f"🔄 SSL iterations: {num_ssl_iterations}")
        print(f"📈 Epochs per iteration: {epochs_per_iteration}")
        
        best_metrics = {'f1_score': 0.0, 'loss': float('inf')}
        training_history = []
        
        for iteration in range(num_ssl_iterations):
            print(f"\n{'='*60}")
            print(f"🔄 SSL Iteration {iteration + 1}/{num_ssl_iterations}")
            print(f"{'='*60}")
            
            # 1. Calculate progressive confidence threshold
            progress = iteration / (num_ssl_iterations - 1) if num_ssl_iterations > 1 else 1.0
            current_threshold = (self.confidence_threshold_start + 
                               progress * (self.confidence_threshold_end - self.confidence_threshold_start))
            
            print(f"📏 Confidence threshold: {current_threshold:.3f}")
            
            # 2. Create data loaders
            train_loader = DataLoader(
                train_dataset, batch_size=batch_size, shuffle=True,
                num_workers=num_workers, pin_memory=True
            )
            val_loader = DataLoader(
                val_dataset, batch_size=batch_size, shuffle=False,
                num_workers=num_workers, pin_memory=True
            )
            
            # 3. Train for this iteration with consistency regularization
            iteration_metrics = self._train_iteration_with_consistency(
                train_loader, val_loader, epochs_per_iteration, iteration
            )
            
            # 4. Generate pseudo-labels for next iteration
            if iteration < num_ssl_iterations - 1:  # Don't generate for last iteration
                pseudo_stats = self._generate_pseudo_labels(
                    train_dataset, current_threshold, iteration
                )
                print(f"📝 Added {pseudo_stats['added_samples']} pseudo-labeled samples")
                print(f"🎯 Pseudo-label quality: {pseudo_stats['avg_confidence']:.3f}")
            
            
            # 5. Update best metrics
            f1_score = iteration_metrics.get('f1_score', iteration_metrics.get('val_f1_score', 0.0))
            if f1_score > best_metrics['f1_score']:
                best_metrics = iteration_metrics.copy()
                self._save_best_model(iteration, iteration_metrics)
            
            # 6. Log iteration summary
            training_history.append(iteration_metrics)
            self._log_iteration_summary(iteration, iteration_metrics, pseudo_stats if iteration < num_ssl_iterations - 1 else None)
            
            # 7. Adaptive learning rate adjustment
            f1_score = iteration_metrics.get('f1_score', iteration_metrics.get('val_f1_score', 0.0))
            prev_f1_score = training_history[-2].get('f1_score', training_history[-2].get('val_f1_score', 0.0)) if iteration > 0 else 0.0
            if iteration > 0 and f1_score < prev_f1_score:
                self._reduce_learning_rate()
        
        self.writer.close()
        return {
            'best_metrics': best_metrics,
            'training_history': training_history,
            'final_dataset_size': len(train_dataset)
        }
    
    def _train_iteration_with_consistency(self, train_loader, val_loader, num_epochs, iteration):
        """Train with consistency regularization"""
        
        from trainer import SpotDetectionTrainer
        from metrics import SpotDetectionMetrics
        
        # Create metrics calculator
        metrics_calculator = SpotDetectionMetrics(threshold=0.5, iou_threshold=0.5)
        
        # Create trainer
        trainer = SpotDetectionTrainer(
            model=self.model,
            loss_fn=self.loss_fn,
            optimizer=self.optimizer,
            device=self.device,
            metrics_calculator=metrics_calculator,
            scheduler=self.scheduler
        )
        
        # Train with early stopping
        history = trainer.train(
            train_loader=train_loader,
            val_loader=val_loader,
            num_epochs=num_epochs,
            early_stopping_patience=5,  # Early stopping to prevent overfitting
            save_best_model=False  # We'll handle saving ourselves
        )
        
        # Get final validation metrics
        val_metrics = trainer.validate(val_loader)
        
        # Log to tensorboard
        for key, value in val_metrics.items():
            self.writer.add_scalar(f'SSL_Iteration/{key}', value, iteration)
        
        return val_metrics
    
    def _generate_pseudo_labels(self, train_dataset, threshold, iteration):
        """Generate high-quality pseudo-labels"""
        
        print(f"🔍 Generating pseudo-labels with threshold {threshold:.3f}")
        
        # Create temporary loader for prediction
        temp_loader = DataLoader(
            train_dataset, batch_size=32, shuffle=False, num_workers=2
        )
        
        self.model.eval()
        all_predictions = []
        all_confidences = []
        all_indices = []
        
        with torch.no_grad():
            for batch_idx, batch in enumerate(tqdm(temp_loader, desc="Generating predictions")):
                images = batch['image'].to(self.device)
                
                # Forward pass
                outputs = self.model(images)
                if isinstance(outputs, dict):
                    pred = outputs.get('output', outputs.get('combined_output', outputs))
                else:
                    pred = outputs
                
                # Apply sigmoid and calculate confidence
                pred_prob = torch.sigmoid(pred)
                
                # Calculate confidence as max probability
                confidence = torch.max(pred_prob, 1 - pred_prob)
                
                all_predictions.extend(pred_prob.cpu())
                all_confidences.extend(confidence.cpu())
                all_indices.extend(range(batch_idx * 32, min((batch_idx + 1) * 32, len(train_dataset))))
        
        # Select high-confidence predictions
        high_conf_indices = []
        high_conf_preds = []
        confidences_list = []
        
        for i, (pred, conf) in enumerate(zip(all_predictions, all_confidences)):
            avg_conf = conf.mean().item()
            if avg_conf > threshold:
                high_conf_indices.append(all_indices[i])
                high_conf_preds.append(pred)
                confidences_list.append(avg_conf)
        
        # Update dataset with pseudo-labels
        added_samples = 0
        if high_conf_indices:
            # Convert predictions to numpy for dataset update
            pseudo_images = [train_dataset[i]['image'] for i in high_conf_indices]
            pseudo_preds = [pred.numpy() for pred in high_conf_preds]
            
            # Update dataset
            added_samples = train_dataset.update_with_predictions(
                pseudo_images, pseudo_preds, threshold=threshold
            )
        
        return {
            'added_samples': added_samples,
            'total_candidates': len(all_predictions),
            'high_confidence_candidates': len(high_conf_indices),
            'avg_confidence': np.mean(confidences_list) if confidences_list else 0.0,
            'threshold_used': threshold
        }
    
    def _save_best_model(self, iteration, metrics):
        """Save the best model"""
        checkpoint = {
            'iteration': iteration,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict() if self.scheduler else None,
            'metrics': metrics,
            'timestamp': time.time()
        }
        
        save_path = os.path.join(os.path.dirname(self.writer.log_dir), 'best_ssl_model.pth')
        torch.save(checkpoint, save_path)
        print(f"💾 Saved best model with F1: {metrics['f1_score']:.4f}")
    
    def _log_iteration_summary(self, iteration, metrics, pseudo_stats):
        """Log iteration summary"""
        print(f"\n📊 Iteration {iteration + 1} Results:")
        print(f"   🎯 F1 Score: {metrics['f1_score']:.4f}")
        print(f"   📉 Loss: {metrics['loss']:.4f}")
        print(f"   🎯 Precision: {metrics['precision']:.4f}")
        print(f"   🎯 Recall: {metrics['recall']:.4f}")
        print(f"   🎯 IoU: {metrics['iou']:.4f}")
        
        if pseudo_stats:
            print(f"   📝 Pseudo-labels added: {pseudo_stats['added_samples']}")
            print(f"   🎯 Avg confidence: {pseudo_stats['avg_confidence']:.3f}")
    
    def _reduce_learning_rate(self):
        """Reduce learning rate when performance plateaus"""
        for param_group in self.optimizer.param_groups:
            old_lr = param_group['lr']
            param_group['lr'] *= 0.5
            print(f"📉 Reduced learning rate: {old_lr:.6f} → {param_group['lr']:.6f}")


# Optimal configuration for semi-supervised learning
OPTIMAL_SSL_CONFIG = {
    'num_ssl_iterations': 5,        # Fewer iterations, more focused
    'epochs_per_iteration': 15,     # Much fewer epochs per iteration
    'batch_size': 16,               # Reasonable batch size
    'confidence_threshold_start': 0.7,  # Start with lower threshold
    'confidence_threshold_end': 0.95,   # End with higher threshold
    'early_stopping_patience': 5,   # Prevent overfitting
    'learning_rate': 0.001,         # Standard learning rate
    'weight_decay': 1e-4,           # Regularization
}

def run_improved_ssl(model, train_dataset, val_dataset, config=OPTIMAL_SSL_CONFIG):
    """
    Run improved semi-supervised learning with optimal parameters
    """
    
    # Create optimizer with weight decay for regularization
    optimizer = torch.optim.Adam(
        model.parameters(), 
        lr=config['learning_rate'],
        weight_decay=config['weight_decay']
    )
    
    # Create scheduler
    scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='min', factor=0.5, patience=3, verbose=True
    )
    
    # Create loss function
    from OptimizedSpotLoss import OptimizedSpotLoss
    loss_fn = OptimizedSpotLoss(
        bce_weight=1.0,
        dice_weight=1.0,
        focal_weight=0.5,
        focal_gamma=2.0,
        size_adaptive=True,
        density_aware=True,
        confidence_weighted=True
    )
    
    # Create SSL trainer
    ssl_trainer = ImprovedSemiSupervisedTrainer(
        model=model,
        loss_fn=loss_fn,
        optimizer=optimizer,
        scheduler=scheduler,
        device='cuda' if torch.cuda.is_available() else 'cpu'
    )
    
    # Run SSL training
    results = ssl_trainer.train_semi_supervised(
        train_dataset=train_dataset,
        val_dataset=val_dataset,
        num_ssl_iterations=config['num_ssl_iterations'],
        epochs_per_iteration=config['epochs_per_iteration'],
        batch_size=config['batch_size']
    )
    
    return model, results

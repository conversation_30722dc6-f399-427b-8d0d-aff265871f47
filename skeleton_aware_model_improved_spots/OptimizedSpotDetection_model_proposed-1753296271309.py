import torch
import torch.nn as nn
import torch.nn.functional as F
import math

class QuantizedSDTLoss(nn.Module):
    """
    Quantized SDT loss focusing on boundary regions.
    """
    def __init__(self, num_bins=10, background_value=0.0):
        super().__init__()
        self.num_bins = num_bins
        self.background_value = background_value

    def forward(self, pred, target, mask=None):
        """
        Args:
            pred: Predicted SDT (B, num_bins+1, H, W)
            target: Target SDT (B, 1, H, W) with values in [0,1]
            mask: Optional mask to focus on foreground regions
        """
        B, _, H, W = pred.shape
        # Create target_quantized with correct shape
        target_quantized = torch.zeros_like(pred)
        # Background channel (first channel)
        background_mask = (mask == 0).float() if mask is not None else (target <= self.background_value).float()
        target_quantized[:, 0:1, :, :] = background_mask
        # Foreground channels (quantized energy bins)
        for i in range(self.num_bins):
            bin_min = i / self.num_bins
            bin_max = (i + 1) / self.num_bins
            if mask is not None:
                bin_mask = ((target > bin_min) & (target <= bin_max) & (mask > 0)).float()
            else:
                bin_mask = ((target > bin_min) & (target <= bin_max)).float()
            target_quantized[:, i+1:i+2, :, :] = bin_mask

        # Get target class indices
        target_classes = target_quantized.argmax(dim=1)

        # Cross-entropy loss
        base_loss = F.cross_entropy(pred, target_classes, reduction='none')

        # --- OPTIMIZATION: Boundary Weighting ---
        # Emphasize loss near the boundary (where GT SDT is low)
        # Higher weight where target is closer to 0 (the boundary)
        if mask is not None:
             # Apply mask to target for weighting calculation within foreground
             masked_target = target.squeeze(1) * mask.squeeze(1)
        else:
             masked_target = target.squeeze(1)
        # Weight increases as GT SDT approaches 0
        boundary_weight = 1.0 + (1.0 - masked_target) # Weight range [1.0, 2.0]
        weighted_loss = base_loss * boundary_weight

        # Apply mask if provided
        if mask is not None:
            weighted_loss = weighted_loss * mask.squeeze(1)
            return weighted_loss.sum() / (mask.sum() + 1e-8)
        return weighted_loss.mean()

class EfficientBlock(nn.Module):
    """Efficient building block with depthwise separable convolutions"""
    def __init__(self, in_ch, out_ch, stride=1, expand_ratio=4):
        super().__init__()
        hidden_ch = in_ch * expand_ratio
        # Pointwise expansion
        self.expand = nn.Conv2d(in_ch, hidden_ch, 1, bias=False) if expand_ratio != 1 else nn.Identity()
        self.bn1 = nn.BatchNorm2d(hidden_ch) if expand_ratio != 1 else nn.Identity()
        # Depthwise convolution
        self.depthwise = nn.Conv2d(hidden_ch, hidden_ch, 3, stride, 1, groups=hidden_ch, bias=False)
        self.bn2 = nn.BatchNorm2d(hidden_ch)
        # Squeeze-and-excitation
        self.se = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(hidden_ch, hidden_ch//4, 1),
            nn.ReLU(inplace=True),
            nn.Conv2d(hidden_ch//4, hidden_ch, 1),
            nn.Sigmoid()
        )
        # Pointwise projection
        self.project = nn.Conv2d(hidden_ch, out_ch, 1, bias=False)
        self.bn3 = nn.BatchNorm2d(out_ch)
        # Skip connection
        self.skip = (stride == 1 and in_ch == out_ch)

    def forward(self, x):
        residual = x
        # Expand
        if hasattr(self.expand, 'weight'):
            x = F.relu(self.bn1(self.expand(x)))
        # Depthwise + SE
        x = F.relu(self.bn2(self.depthwise(x)))
        x = x * self.se(x)
        # Project
        x = self.bn3(self.project(x))
        return x + residual if self.skip else x

class SimpleSpatialAttention(nn.Module):
    def __init__(self, in_channels):
        super(SimpleSpatialAttention, self).__init__()
        self.conv = nn.Conv2d(in_channels, 1, kernel_size=7, padding=3, bias=False)
        self.sigmoid = nn.Sigmoid()

    def forward(self, x):
        # x shape: (B, C, H, W)
        attn = self.conv(x)
        attn = self.sigmoid(attn)
        return x * attn

class SkeletonAwareSpotDetector(nn.Module):
    """Spot detector with Skeleton-Aware Distance Transform - Optimized"""
    def __init__(self, in_ch=1, base_ch=48):
        super().__init__()
        self.base_ch = base_ch
        # --- OPTIMIZATION: Enhanced Stem for lateral connection ---
        self.stem_conv1 = nn.Conv2d(in_ch, base_ch, 3, padding=1, bias=False)
        self.stem_bn1 = nn.BatchNorm2d(base_ch)
        self.stem_conv2 = nn.Conv2d(base_ch, base_ch, 3, padding=1, bias=False)
        self.stem_bn2 = nn.BatchNorm2d(base_ch) # Output x0

        # Encoder
        self.enc1 = nn.Sequential(
            EfficientBlock(base_ch, base_ch*2, stride=2),
            EfficientBlock(base_ch*2, base_ch*2)
        )
        self.enc2 = nn.Sequential(
            EfficientBlock(base_ch*2, base_ch*4, stride=2),
            EfficientBlock(base_ch*4, base_ch*4)
        )

        # Feature Pyramid - Enhanced with lateral connection from stem
        self.fpn_lateral_stem = nn.Conv2d(base_ch, base_ch, 1) # Lateral from stem
        self.fpn_conv2 = nn.Conv2d(base_ch*4, base_ch*2, 1)
        self.fpn_conv1 = nn.Conv2d(base_ch*2, base_ch, 1)

        # Decoder - Increased capacity
        self.decoder = nn.Sequential(
            EfficientBlock(base_ch*3, base_ch*2), # p1_up, x0, attended
            EfficientBlock(base_ch*2, base_ch),
            # --- OPTIMIZATION: Extra Decoder Block ---
            EfficientBlock(base_ch, base_ch),
            nn.Conv2d(base_ch, base_ch, 3, padding=1),
            nn.BatchNorm2d(base_ch),
            nn.ReLU(inplace=True)
        )
        # --- OPTIMIZATION: Add Spatial Attention ---
        self.spatial_attn = SimpleSpatialAttention(base_ch)

        # Shared head
        self.shared_head = nn.Sequential(
            nn.Conv2d(base_ch, base_ch, 3, padding=1, groups=base_ch),
            nn.Conv2d(base_ch, base_ch, 1),
            nn.BatchNorm2d(base_ch),
            nn.ReLU(inplace=True)
        )

        # SDT heads with quantized output
        self.sadt_heads = nn.ModuleDict({
            'sdt': nn.Sequential(
                nn.Conv2d(base_ch, base_ch, 3, padding=1),
                nn.BatchNorm2d(base_ch),
                nn.ReLU(inplace=True),
                nn.Conv2d(base_ch, 11, 1)  # 10 bins + 1 background channel
            ),
            'skeleton': nn.Sequential(
                nn.Conv2d(base_ch, base_ch, 3, padding=1),
                nn.BatchNorm2d(base_ch),
                nn.ReLU(inplace=True),
                nn.Conv2d(base_ch, 1, 1)
            )
        })

        # Other output heads
        self.semantic_head = nn.Conv2d(base_ch, 1, 1)
        self.centroid_head = nn.Conv2d(base_ch, 1, 1)
        self.flow_head = nn.Conv2d(base_ch, 2, 1)
        self._init_weights()

    def _init_weights(self):
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                # Use fan_in for better stability with ReLU
                nn.init.kaiming_normal_(m.weight, mode='fan_in', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.zeros_(m.bias)
            elif isinstance(m, nn.BatchNorm2d):
                nn.init.ones_(m.weight)
                nn.init.zeros_(m.bias)

    def forward(self, x):
        B, C, H, W = x.shape
        # Encoder
        x0 = F.relu(self.stem_bn2(self.stem_conv2(F.relu(self.stem_bn1(self.stem_conv1(x))))))
        x1 = self.enc1(x0)
        x2 = self.enc2(x1)

        # Feature Pyramid - Enhanced with lateral connection
        p2 = self.fpn_conv2(x2)
        p2_up = F.interpolate(p2, size=(H//2, W//2), mode='bilinear', align_corners=True)

        p1_in = x1 + p2_up # Combine with deeper features
        p1 = self.fpn_conv1(p1_in)
        p1_up = F.interpolate(p1, size=(H, W), mode='bilinear', align_corners=True)

        # --- OPTIMIZATION: Lateral connection from stem ---
        lateral_x0 = self.fpn_lateral_stem(x0) # Match channels
        # Combine lateral connection with upsampled features
        fused_features = torch.cat([lateral_x0, p1_up], dim=1) # Concatenate along channel dim
        # Use a 1x1 conv to reduce channels and combine
        p1_up_combined = nn.Conv2d(lateral_x0.shape[1] + p1_up.shape[1], self.base_ch, 1).to(x0.device)(fused_features)
        p1_up_final = F.relu(p1_up_combined)

        # Feature fusion (attention mechanism - simplified)
        # Using p1_up_final as context for attention on x0
        attended = x0 * torch.sigmoid(torch.mean(p1_up_final, dim=1, keepdim=True)) # Simple spatial attention context

        # Fuse all scales (x0, enhanced p1_up, attended features)
        fused = torch.cat([x0, p1_up_final, attended], dim=1)

        # Decoder
        features = self.decoder(fused)

        # --- OPTIMIZATION: Apply Spatial Attention ---
        features = self.spatial_attn(features)

        # Shared processing
        shared_feat = self.shared_head(features)

        # SDT components
        sdt_out = self.sadt_heads['sdt'](shared_feat)
        skeleton_out = self.sadt_heads['skeleton'](shared_feat)

        # Other outputs
        outputs = {
            'sem_out': self.semantic_head(shared_feat),
            'sdt_out': sdt_out,
            'skeleton_out': skeleton_out,
            'hm_out': self.centroid_head(shared_feat),
            'flow_out': self.flow_head(shared_feat)
        }
        return outputs

class SkeletonAwareLoss(nn.Module):
    """Loss function with Skeleton-Aware Distance Transform - Optimized Weights"""
    def __init__(self):
        super().__init__()
        # --- OPTIMIZATION: Adjusted Weights ---
        self.w_sem = 1.0
        self.w_sdt = 4.0 # Increased emphasis on SDT for better separation
        self.w_skel = 2.0
        self.w_cent = 2.0
        self.w_flow = 1.0
        self.quantized_sdt_loss = QuantizedSDTLoss(num_bins=10, background_value=0.0)

    def focal_loss(self, pred, target, alpha=0.25, gamma=2.0, label_smoothing=0.1):
        target_smooth = target * (1 - label_smoothing) + 0.5 * label_smoothing
        bce = F.binary_cross_entropy_with_logits(pred, target_smooth, reduction='none')
        pt = torch.exp(-bce)
        return (alpha * (1-pt)**gamma * bce).mean()

    def dice_loss(self, pred, target, smooth=1e-6):
        pred_sig = torch.sigmoid(pred)
        intersection = (pred_sig * target).sum()
        union = pred_sig.sum() + target.sum()
        return 1 - (2 * intersection + smooth) / (union + smooth)

    def forward(self, outputs, targets):
        # Target channels: [semantic, sdt, skeleton, centroid, flow_magnitude, boundary]
        gt_sem = targets[:, 0:1]
        gt_sdt = targets[:, 1:2]
        gt_skel = targets[:, 2:3]
        gt_cent = targets[:, 3:4]
        losses = {}
        # Semantic loss
        losses['semantic'] = (self.focal_loss(outputs['sem_out'], gt_sem) +
                            self.dice_loss(outputs['sem_out'], gt_sem))
        # Quantized SDT loss - use semantic mask to focus on foreground
        # --- OPTIMIZATION: Pass the mask to the enhanced loss function ---
        losses['sdt'] = self.quantized_sdt_loss(outputs['sdt_out'], gt_sdt, mask=(gt_sem > 0.5))
        # Skeleton loss
        losses['skeleton'] = self.focal_loss(outputs['skeleton_out'], gt_skel)
        # Centroid loss
        losses['centroid'] = (self.focal_loss(outputs['hm_out'], gt_cent) +
                            self.dice_loss(outputs['hm_out'], gt_cent))
        # Flow loss
        if targets.shape[1] > 4:
            gt_flow = targets[:, 4:6]
            mask = (gt_sem > 0).float()
            # --- OPTIMIZATION: Use Huber loss for robustness ---
            losses['flow'] = F.huber_loss(outputs['flow_out'] * mask, gt_flow * mask, reduction='mean', delta=1.0)
        else:
            losses['flow'] = torch.tensor(0.0, device=outputs['sem_out'].device)

        # Total weighted loss
        total = (self.w_sem * losses['semantic'] +
                self.w_sdt * losses['sdt'] +
                self.w_skel * losses['skeleton'] +
                self.w_cent * losses['centroid'] +
                self.w_flow * losses['flow'])
        losses['total'] = total
        return total, losses
        """Initialize model weights to reduce grid artifacts"""
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.BatchNorm2d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Linear):
                nn.init.normal_(m.weight, 0, 0.01)
                nn.init.constant_(m.bias, 0)
        
    def visualize_3d_activation_maps(self, x: torch.Tensor, 
                                   slice_indices: Optional[List[int]] = None,
                                   max_slices: int = 4) -> None:
        """Visualize 3D activation maps with consistent orientation"""
        self.eval()
        with torch.no_grad():
            # Get activations
            features = self.encoder(x)
            
            # Get default slice indices if not provided
            if slice_indices is None:
                D = x.shape[2] if len(x.shape) == 5 else x.shape[1]
                step = max(1, D // max_slices)
                slice_indices = list(range(0, D, step))[:max_slices]
            
            # Create figure
            n_rows = len(features) + 2  # features + input + output
            n_cols = len(slice_indices)
            fig, axes = plt.subplots(n_rows, n_cols, figsize=(4*n_cols, 3*n_rows))
            
            # Plot input slices
            for i, z in enumerate(slice_indices):
                if len(x.shape) == 5:
                    img = x[0, 0, z].cpu()
                else:
                    img = x[0, z].cpu()
                axes[0, i].imshow(img, cmap='gray')
                axes[0, i].set_title(f'Input {z}')
                axes[0, i].axis('off')
            
            # Plot feature maps
            for row, feat in enumerate(features, 1):
                if len(feat.shape) == 5:  # 3D features
                    feat_viz = feat[0, 0]  # Take first channel
                else:  # 2D features
                    feat_viz = feat[0]  # Take first batch
                
                # Plot each slice
                for i, z in enumerate(slice_indices):
                    if len(feat.shape) == 5:
                        fm = feat_viz[z].cpu()
                    else:
                        fm = feat_viz[i].cpu()
                    axes[row, i].imshow(fm, cmap='hot')
                    axes[row, i].set_title(f'Level {row} - {z}')
                    axes[row, i].axis('off')
            
            # Plot output
            outputs = self(x)
            pred = torch.sigmoid(outputs['heatmap'])
            
            for i, z in enumerate(slice_indices):
                if len(pred.shape) == 5:
                    out = pred[0, 0, z].cpu()
                else:
                    out = pred[0, z].cpu()
                axes[-1, i].imshow(out, cmap='hot')
                axes[-1, i].set_title(f'Output {z}')
                axes[-1, i].axis('off')
            
            plt.tight_layout()
            plt.show()
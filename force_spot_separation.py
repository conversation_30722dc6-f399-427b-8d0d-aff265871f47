import torch
import numpy as np
import matplotlib.pyplot as plt
from skimage import measure, morphology
from skimage.feature import peak_local_max
from skimage.segmentation import watershed
from scipy.ndimage import distance_transform_edt
import cv2

def force_spot_separation(image, heatmap, min_distance=5, min_intensity=0.1, min_spot_size=3):
    """
    Force separation of spots even when binary mask shows only one component
    
    Args:
        image: Original image (numpy array)
        heatmap: Prediction heatmap from model (numpy array)
        min_distance: Minimum distance between peaks
        min_intensity: Minimum intensity for peak detection
        min_spot_size: Minimum spot size in pixels
        
    Returns:
        Dictionary with detection results
    """
    # Skip binary mask creation and directly find local maxima in the heatmap
    # This works even when thresholding would create a single large component
    local_max_coords = peak_local_max(
        heatmap,
        min_distance=min_distance,
        threshold_abs=min_intensity,
        exclude_border=False
    )
    
    # If no peaks found, return empty result
    if len(local_max_coords) == 0:
        print("No peaks found in heatmap. Try lowering min_intensity.")
        return {
            'num_spots': 0,
            'spot_props': [],
            'visualization': image
        }
    
    print(f"Found {len(local_max_coords)} potential spots using direct peak detection")
    
    # Create markers for watershed
    markers = np.zeros_like(heatmap, dtype=np.int32)
    for i, (y, x) in enumerate(local_max_coords):
        markers[y, x] = i + 1
    
    # Create a mask for watershed - use a very low threshold to include all potential spots
    mask = (heatmap > heatmap.max() * 0.05).astype(np.uint8)
    
    # Apply watershed segmentation
    labels = watershed(-heatmap, markers, mask=mask)
    
    # Remove small regions
    if min_spot_size > 1:
        for label_id in np.unique(labels):
            if label_id == 0:  # Skip background
                continue
            size = np.sum(labels == label_id)
            if size < min_spot_size:
                labels[labels == label_id] = 0
    
    # Get region properties
    props = measure.regionprops(labels, intensity_image=heatmap)
    
    # Extract spot properties
    spot_props = []
    for prop in props:
        spot_props.append({
            'id': prop.label,
            'centroid': prop.centroid,
            'area': prop.area,
            'mean_intensity': prop.mean_intensity,
            'max_intensity': prop.max_intensity,
            'bbox': prop.bbox
        })
    
    # Create visualization
    rgb_mask = np.zeros((*image.shape, 3))
    rgb_mask[..., 0] = image  # Red channel
    rgb_mask[..., 1] = image  # Green channel
    rgb_mask[..., 2] = image  # Blue channel
    
    # Overlay spots
    for prop in spot_props:
        y, x = prop['centroid']
        r = max(2, int(np.sqrt(prop['area'] / np.pi)))  # Ensure minimum radius of 2 pixels
        
        # Draw circle
        cv2.circle(
            rgb_mask,
            (int(x), int(y)),
            r,
            (1, 0, 0),  # Red
            1
        )
        
        # Add ID
        cv2.putText(
            rgb_mask,
            str(prop['id']),
            (int(x), int(y)),
            cv2.FONT_HERSHEY_SIMPLEX,
            0.3,
            (0, 1, 0),  # Green
            1
        )
    
    return {
        'num_spots': len(spot_props),
        'spot_props': spot_props,
        'visualization': rgb_mask,
        'labeled_mask': labels
    }

def visualize_spot_detection_steps(image, heatmap, result):
    """
    Visualize the steps of spot detection
    
    Args:
        image: Original image
        heatmap: Prediction heatmap
        result: Result from force_spot_separation
    """
    fig, axes = plt.subplots(1, 3, figsize=(15, 5))
    
    # Plot original image
    axes[0].imshow(image, cmap='gray')
    axes[0].set_title('Original Image')
    axes[0].axis('off')
    
    # Plot heatmap
    axes[1].imshow(heatmap, cmap='hot')
    axes[1].set_title('Prediction Heatmap')
    axes[1].axis('off')
    
    # Plot detected spots
    axes[2].imshow(result['visualization'])
    axes[2].set_title(f'Detected Spots: {result["num_spots"]}')
    axes[2].axis('off')
    
    plt.tight_layout()
    plt.show()
    
    # Also show the labeled mask
    plt.figure(figsize=(8, 8))
    plt.imshow(result['labeled_mask'], cmap='nipy_spectral')
    plt.title('Labeled Spots')
    plt.colorbar(label='Spot ID')
    plt.axis('off')
    plt.show()
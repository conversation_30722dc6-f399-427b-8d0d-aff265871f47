import torch
import numpy as np
import os
from SparseSpotDataset import Sparse<PERSON>potDataset
from semi_supervised import <PERSON>SupervisedTrainer
from torch.utils.data import DataLoader
import matplotlib.pyplot as plt

def create_synthetic_dataset(num_samples=10, image_size=(64, 64)):
    """Create a synthetic dataset for testing pseudo-labeling"""
    dataset = SparseSpotDataset(data_dir=None)
    
    # Create synthetic images and masks
    for i in range(num_samples):
        # Create random image with spots
        image = np.zeros(image_size, dtype=np.float32)
        # Add random noise
        image += np.random.normal(0, 0.1, image_size)
        # Add random spots
        num_spots = np.random.randint(3, 10)
        mask = np.zeros(image_size, dtype=np.float32)
        
        for _ in range(num_spots):
            x = np.random.randint(5, image_size[0]-5)
            y = np.random.randint(5, image_size[1]-5)
            radius = np.random.randint(2, 5)
            
            # Create spot in image (brighter)
            for i in range(max(0, x-radius), min(image_size[0], x+radius+1)):
                for j in range(max(0, y-radius), min(image_size[1], y+radius+1)):
                    if ((i-x)**2 + (j-y)**2) <= radius**2:
                        image[i, j] += 0.5
                        mask[i, j] = 1.0
        
        # Normalize image
        image = np.clip(image, 0, 1)
        
        # Create confidence mask (all 1s for labeled data)
        confidence = mask.copy()
        
        # Add to dataset
        dataset.images.append(image)
        dataset.masks.append(mask)
        dataset.confidence_masks.append(confidence)
    
    return dataset

def create_unlabeled_dataset(labeled_dataset, num_samples=5):
    """Create unlabeled dataset from labeled dataset by hiding labels"""
    unlabeled_dataset = SparseSpotDataset(data_dir=None)
    
    # Copy images but set masks to zeros
    for i in range(min(num_samples, len(labeled_dataset))):
        if isinstance(labeled_dataset.images[i], str):
            # Skip file paths
            continue
            
        # Copy image
        unlabeled_dataset.images.append(labeled_dataset.images[i].copy())
        
        # Create empty mask
        mask_shape = labeled_dataset.masks[i].shape if isinstance(labeled_dataset.masks[i], np.ndarray) else (64, 64)
        unlabeled_dataset.masks.append(np.zeros(mask_shape, dtype=np.float32))
        
        # Create zero confidence mask
        unlabeled_dataset.confidence_masks.append(np.zeros(mask_shape, dtype=np.float32))
    
    return unlabeled_dataset

class SimpleModel(torch.nn.Module):
    """Simple CNN model for testing"""
    def __init__(self):
        super().__init__()
        self.conv1 = torch.nn.Conv2d(1, 16, kernel_size=3, padding=1)
        self.conv2 = torch.nn.Conv2d(16, 32, kernel_size=3, padding=1)
        self.conv3 = torch.nn.Conv2d(32, 1, kernel_size=3, padding=1)
        self.relu = torch.nn.ReLU()
        self.sigmoid = torch.nn.Sigmoid()
    
    def forward(self, x):
        x = self.relu(self.conv1(x))
        x = self.relu(self.conv2(x))
        x = self.conv3(x)
        return {'heatmap': x}  # Return as dict to match expected format

class SimpleLoss(torch.nn.Module):
    """Simple loss function for testing"""
    def __init__(self):
        super().__init__()
        self.bce = torch.nn.BCEWithLogitsLoss()
    
    def forward(self, outputs, targets):
        loss = self.bce(outputs['heatmap'], targets['masks'])
        return loss, {'bce': loss.item()}

def test_pseudo_labeling():
    """Test pseudo-labeling functionality"""
    print("Creating synthetic datasets...")
    labeled_dataset = create_synthetic_dataset(num_samples=10)
    unlabeled_dataset = create_unlabeled_dataset(labeled_dataset, num_samples=5)
    
    print(f"Labeled dataset size: {len(labeled_dataset)}")
    print(f"Unlabeled dataset size: {len(unlabeled_dataset)}")
    
    # Create model and loss
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = SimpleModel().to(device)
    loss_fn = SimpleLoss()
    optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
    
    # Create data loaders
    labeled_loader = DataLoader(labeled_dataset, batch_size=2, shuffle=True)
    unlabeled_loader = DataLoader(unlabeled_dataset, batch_size=2, shuffle=True)
    
    # Create trainer
    trainer = SemiSupervisedTrainer(
        model=model,
        loss_fn=loss_fn,
        labeled_loader=labeled_loader,
        unlabeled_loader=unlabeled_loader,
        optimizer=optimizer,
        device=device,
        confidence_threshold=0.7,  # Lower threshold for testing
        ignore_threshold=0.3
    )
    
    # Train for a few epochs to get some predictions
    print("Training for a few epochs...")
    for epoch in range(3):
        metrics = trainer.train_epoch(labeled_loader, unlabeled_loader)
        print(f"Epoch {epoch+1} - Loss: {metrics['loss']:.4f}")
    
    # Generate pseudo-labels
    print("Generating pseudo-labels...")
    stats = trainer.generate_pseudo_labels(unlabeled_dataset)
    
    print("\nPseudo-labeling statistics:")
    for key, value in stats.items():
        print(f"  {key}: {value}")
    
    # Visualize results
    print("\nVisualizing results...")
    fig, axes = plt.subplots(len(unlabeled_dataset), 3, figsize=(12, 4*len(unlabeled_dataset)))
    
    for i in range(len(unlabeled_dataset)):
        # Original image
        axes[i, 0].imshow(unlabeled_dataset.images[i], cmap='gray')
        axes[i, 0].set_title(f"Image {i+1}")
        
        # Mask (should be updated with pseudo-labels)
        axes[i, 1].imshow(unlabeled_dataset.masks[i], cmap='hot')
        axes[i, 1].set_title(f"Pseudo-mask {i+1}")
        
        # Confidence mask
        axes[i, 2].imshow(unlabeled_dataset.confidence_masks[i], cmap='viridis')
        axes[i, 2].set_title(f"Confidence {i+1}")
    
    plt.tight_layout()
    plt.savefig("pseudo_labeling_results.png")
    print("Results saved to pseudo_labeling_results.png")

if __name__ == "__main__":
    test_pseudo_labeling()
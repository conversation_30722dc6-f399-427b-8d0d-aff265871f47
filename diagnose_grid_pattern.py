#!/usr/bin/env python3
"""
Diagnostic Script for Grid Pattern Issues in Spot Detection
This script helps identify the root cause of grid-like detection patterns
"""

import torch
import torch.nn as nn
import numpy as np
import matplotlib.pyplot as plt
from skimage import io
from scipy import ndimage
from scipy.fft import fft2, fftshift
import cv2
import argparse
import os
from typing import Dict, List, Tuple, Optional

# Import your model
from OptimizedSpotDetection_model import OptimizedSpotDetectionModel

class GridPatternDiagnostic:
    """
    Comprehensive diagnostic tool for grid pattern issues
    """
    
    def __init__(self, model_path: str, device: str = 'cuda'):
        self.device = torch.device(device if torch.cuda.is_available() else 'cpu')
        self.model = self._load_model(model_path)
        
    def _load_model(self, model_path: str) -> nn.Module:
        """Load the trained model"""
        model = OptimizedSpotDetectionModel(
            in_channels=1,
            base_filters=32,
            num_experts=4,
            dropout_rate=0.1
        )
        
        if os.path.exists(model_path):
            checkpoint = torch.load(model_path, map_location=self.device)
            if 'model_state_dict' in checkpoint:
                model.load_state_dict(checkpoint['model_state_dict'])
            else:
                model.load_state_dict(checkpoint)
            print(f"Loaded model from {model_path}")
        else:
            print(f"Warning: Model file {model_path} not found. Using random weights.")
        
        return model.to(self.device)
    
    def diagnose_image(self, image_path: str) -> Dict:
        """
        Comprehensive diagnosis of grid pattern issues
        """
        # Load and preprocess image
        image = self._load_image(image_path)
        image_tensor = torch.from_numpy(image).unsqueeze(0).unsqueeze(0).to(self.device)
        
        # Get model outputs
        self.model.eval()
        with torch.no_grad():
            outputs = self.model(image_tensor)
            
        # Extract different outputs
        if isinstance(outputs, dict):
            heatmap_raw = outputs['heatmap'].squeeze().cpu().numpy()
            expert_outputs = [exp.squeeze().cpu().numpy() for exp in outputs.get('expert_outputs', [])]
            expert_weights = outputs.get('expert_weights', torch.zeros(1, 4)).squeeze().cpu().numpy()
        else:
            heatmap_raw = outputs.squeeze().cpu().numpy()
            expert_outputs = []
            expert_weights = np.array([])
        
        # Apply sigmoid to get probabilities
        heatmap_prob = 1 / (1 + np.exp(-heatmap_raw))  # Manual sigmoid
        
        # Run all diagnostic tests
        results = {
            'image': image,
            'heatmap_raw': heatmap_raw,
            'heatmap_prob': heatmap_prob,
            'expert_outputs': expert_outputs,
            'expert_weights': expert_weights,
            'grid_analysis': self._analyze_grid_pattern(heatmap_prob),
            'frequency_analysis': self._frequency_analysis(heatmap_prob),
            'spatial_analysis': self._spatial_analysis(heatmap_prob),
            'activation_analysis': self._analyze_activations(heatmap_raw),
            'model_analysis': self._analyze_model_weights()
        }
        
        return results
    
    def _load_image(self, image_path: str) -> np.ndarray:
        """Load and normalize image"""
        image = io.imread(image_path)
        
        # Convert to grayscale if needed
        if len(image.shape) == 3:
            if image.shape[2] == 3:
                image = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
            elif image.shape[2] == 4:
                image = cv2.cvtColor(image, cv2.COLOR_RGBA2GRAY)
        
        # Normalize to [0, 1]
        if image.dtype == np.uint8:
            image = image.astype(np.float32) / 255.0
        else:
            image = image.astype(np.float32)
            if image.max() > 1.0:
                image = image / image.max()
        
        return image
    
    def _analyze_grid_pattern(self, heatmap: np.ndarray) -> Dict:
        """Analyze grid-like patterns in the heatmap"""
        # 1. Regularity analysis
        # Check for regular spacing in peaks
        from skimage.feature import peak_local_maxima
        
        # Find peaks
        peaks_y, peaks_x = np.where(heatmap > np.percentile(heatmap, 95))
        
        # Analyze spacing
        if len(peaks_x) > 1:
            x_diffs = np.diff(np.sort(peaks_x))
            y_diffs = np.diff(np.sort(peaks_y))
            
            x_regularity = np.std(x_diffs) / (np.mean(x_diffs) + 1e-8)
            y_regularity = np.std(y_diffs) / (np.mean(y_diffs) + 1e-8)
        else:
            x_regularity = y_regularity = 0
        
        # 2. Uniformity analysis
        uniformity = np.std(heatmap) / (np.mean(heatmap) + 1e-8)
        
        # 3. Checkerboard pattern detection
        checkerboard_score = self._detect_checkerboard(heatmap)
        
        # 4. Grid strength score
        grid_strength = self._calculate_grid_strength(heatmap)
        
        return {
            'x_regularity': x_regularity,
            'y_regularity': y_regularity,
            'uniformity': uniformity,
            'checkerboard_score': checkerboard_score,
            'grid_strength': grid_strength,
            'num_peaks': len(peaks_x),
            'is_grid_like': grid_strength > 0.3 or checkerboard_score > 0.5
        }
    
    def _detect_checkerboard(self, heatmap: np.ndarray) -> float:
        """Detect checkerboard-like patterns"""
        # Create checkerboard templates of different sizes
        scores = []
        
        for size in [4, 8, 16, 32]:
            if size > min(heatmap.shape) // 4:
                continue
                
            # Create checkerboard template
            template = np.zeros((size*2, size*2))
            template[::2, ::2] = 1
            template[1::2, 1::2] = 1
            
            # Normalize template
            template = (template - template.mean()) / template.std()
            
            # Compute normalized cross-correlation
            correlation = cv2.matchTemplate(
                (heatmap - heatmap.mean()) / heatmap.std(),
                template.astype(np.float32),
                cv2.TM_CCOEFF_NORMED
            )
            
            scores.append(np.max(correlation))
        
        return max(scores) if scores else 0.0
    
    def _calculate_grid_strength(self, heatmap: np.ndarray) -> float:
        """Calculate overall grid strength"""
        # Use autocorrelation to detect periodic patterns
        autocorr = ndimage.correlate(heatmap, heatmap, mode='constant')
        
        # Find peaks in autocorrelation (excluding center)
        center = np.array(autocorr.shape) // 2
        mask = np.ones_like(autocorr, dtype=bool)
        
        # Mask out center region
        mask[center[0]-5:center[0]+5, center[1]-5:center[1]+5] = False
        
        # Calculate grid strength as ratio of peak autocorrelation to center
        center_val = autocorr[center[0], center[1]]
        peak_val = np.max(autocorr[mask])
        
        grid_strength = peak_val / (center_val + 1e-8)
        
        return min(1.0, grid_strength)
    
    def _frequency_analysis(self, heatmap: np.ndarray) -> Dict:
        """Analyze frequency domain for periodic patterns"""
        # Compute 2D FFT
        fft = fft2(heatmap)
        fft_shifted = fftshift(fft)
        magnitude = np.abs(fft_shifted)
        
        # Analyze frequency distribution
        h, w = magnitude.shape
        center_h, center_w = h // 2, w // 2
        
        # Create frequency masks
        low_freq_mask = np.zeros_like(magnitude, dtype=bool)
        high_freq_mask = np.zeros_like(magnitude, dtype=bool)
        
        # Low frequency: center region
        r_low = min(h, w) // 8
        y, x = np.ogrid[:h, :w]
        low_freq_mask = (x - center_w)**2 + (y - center_h)**2 <= r_low**2
        
        # High frequency: outer region
        r_high = min(h, w) // 4
        high_freq_mask = (x - center_w)**2 + (y - center_h)**2 >= r_high**2
        
        # Calculate energy ratios
        total_energy = np.sum(magnitude**2)
        low_freq_energy = np.sum(magnitude[low_freq_mask]**2)
        high_freq_energy = np.sum(magnitude[high_freq_mask]**2)
        
        # Detect dominant frequencies
        dominant_freqs = self._find_dominant_frequencies(magnitude, center_h, center_w)
        
        return {
            'total_energy': total_energy,
            'low_freq_ratio': low_freq_energy / total_energy,
            'high_freq_ratio': high_freq_energy / total_energy,
            'dominant_frequencies': dominant_freqs,
            'frequency_spectrum': magnitude
        }
    
    def _find_dominant_frequencies(self, magnitude: np.ndarray, center_h: int, center_w: int) -> List[Tuple]:
        """Find dominant frequencies in the spectrum"""
        # Mask out DC component
        magnitude_masked = magnitude.copy()
        magnitude_masked[center_h-2:center_h+2, center_w-2:center_w+2] = 0
        
        # Find peaks
        threshold = np.percentile(magnitude_masked, 99)
        peaks_y, peaks_x = np.where(magnitude_masked > threshold)
        
        # Convert to frequency coordinates
        dominant_freqs = []
        for y, x in zip(peaks_y, peaks_x):
            freq_y = y - center_h
            freq_x = x - center_w
            magnitude_val = magnitude[y, x]
            dominant_freqs.append((freq_x, freq_y, magnitude_val))
        
        # Sort by magnitude
        dominant_freqs.sort(key=lambda x: x[2], reverse=True)
        
        return dominant_freqs[:10]  # Top 10 frequencies
    
    def _spatial_analysis(self, heatmap: np.ndarray) -> Dict:
        """Analyze spatial characteristics"""
        # Gradient analysis
        grad_x = np.gradient(heatmap, axis=1)
        grad_y = np.gradient(heatmap, axis=0)
        gradient_magnitude = np.sqrt(grad_x**2 + grad_y**2)
        
        # Texture analysis using Local Binary Patterns
        lbp_score = self._calculate_lbp_uniformity(heatmap)
        
        # Edge density
        edges = cv2.Canny((heatmap * 255).astype(np.uint8), 50, 150)
        edge_density = np.sum(edges > 0) / edges.size
        
        return {
            'gradient_mean': np.mean(gradient_magnitude),
            'gradient_std': np.std(gradient_magnitude),
            'lbp_uniformity': lbp_score,
            'edge_density': edge_density,
            'spatial_entropy': self._calculate_spatial_entropy(heatmap)
        }
    
    def _calculate_lbp_uniformity(self, image: np.ndarray) -> float:
        """Calculate Local Binary Pattern uniformity"""
        # Simple LBP implementation
        h, w = image.shape
        lbp = np.zeros_like(image)
        
        for i in range(1, h-1):
            for j in range(1, w-1):
                center = image[i, j]
                code = 0
                code |= (image[i-1, j-1] > center) << 7
                code |= (image[i-1, j] > center) << 6
                code |= (image[i-1, j+1] > center) << 5
                code |= (image[i, j+1] > center) << 4
                code |= (image[i+1, j+1] > center) << 3
                code |= (image[i+1, j] > center) << 2
                code |= (image[i+1, j-1] > center) << 1
                code |= (image[i, j-1] > center) << 0
                lbp[i, j] = code
        
        # Calculate uniformity
        hist, _ = np.histogram(lbp.flatten(), bins=256, range=(0, 256))
        uniformity = np.sum(hist**2) / (np.sum(hist)**2)
        
        return uniformity
    
    def _calculate_spatial_entropy(self, image: np.ndarray) -> float:
        """Calculate spatial entropy"""
        # Quantize image
        quantized = (image * 255).astype(np.uint8)
        
        # Calculate histogram
        hist, _ = np.histogram(quantized.flatten(), bins=256, range=(0, 256))
        
        # Calculate entropy
        hist = hist / np.sum(hist)  # Normalize
        entropy = -np.sum(hist * np.log2(hist + 1e-8))
        
        return entropy
    
    def _analyze_activations(self, heatmap_raw: np.ndarray) -> Dict:
        """Analyze activation patterns"""
        return {
            'mean_activation': np.mean(heatmap_raw),
            'std_activation': np.std(heatmap_raw),
            'min_activation': np.min(heatmap_raw),
            'max_activation': np.max(heatmap_raw),
            'saturation_ratio': np.sum(np.abs(heatmap_raw) > 5) / heatmap_raw.size,
            'zero_ratio': np.sum(np.abs(heatmap_raw) < 0.1) / heatmap_raw.size
        }
    
    def _analyze_model_weights(self) -> Dict:
        """Analyze model weights for potential issues"""
        weight_stats = {}
        
        for name, param in self.model.named_parameters():
            if 'weight' in name:
                weight_stats[name] = {
                    'mean': param.data.mean().item(),
                    'std': param.data.std().item(),
                    'min': param.data.min().item(),
                    'max': param.data.max().item()
                }
        
        return weight_stats
    
    def visualize_diagnosis(self, results: Dict, save_path: Optional[str] = None):
        """Visualize comprehensive diagnosis results"""
        fig = plt.figure(figsize=(20, 16))
        
        # Original image
        plt.subplot(4, 5, 1)
        plt.imshow(results['image'], cmap='gray')
        plt.title('Original Image')
        plt.axis('off')
        
        # Raw heatmap
        plt.subplot(4, 5, 2)
        plt.imshow(results['heatmap_raw'], cmap='hot')
        plt.title('Raw Heatmap')
        plt.axis('off')
        
        # Probability heatmap
        plt.subplot(4, 5, 3)
        plt.imshow(results['heatmap_prob'], cmap='hot')
        plt.title('Probability Heatmap')
        plt.axis('off')
        
        # Frequency spectrum
        plt.subplot(4, 5, 4)
        spectrum = results['frequency_analysis']['frequency_spectrum']\n        plt.imshow(np.log(spectrum + 1), cmap='viridis')\n        plt.title('Frequency Spectrum')\n        plt.axis('off')\n        \n        # Grid analysis visualization\n        plt.subplot(4, 5, 5)\n        grid_info = results['grid_analysis']\n        grid_text = f\"Grid Strength: {grid_info['grid_strength']:.3f}\\n\"\n        grid_text += f\"Checkerboard: {grid_info['checkerboard_score']:.3f}\\n\"\n        grid_text += f\"Uniformity: {grid_info['uniformity']:.3f}\\n\"\n        grid_text += f\"Is Grid-like: {grid_info['is_grid_like']}\"\n        plt.text(0.1, 0.5, grid_text, fontsize=10, verticalalignment='center')\n        plt.title('Grid Analysis')\n        plt.axis('off')\n        \n        # Expert outputs\n        for i, expert_out in enumerate(results['expert_outputs'][:4]):\n            plt.subplot(4, 5, 6 + i)\n            plt.imshow(1 / (1 + np.exp(-expert_out)), cmap='hot')  # Apply sigmoid\n            weight = results['expert_weights'][i] if i < len(results['expert_weights']) else 0\n            plt.title(f'Expert {i+1} (w={weight:.2f})')\n            plt.axis('off')\n        \n        # Activation statistics\n        plt.subplot(4, 5, 11)\n        act_stats = results['activation_analysis']\n        act_text = f\"Mean: {act_stats['mean_activation']:.3f}\\n\"\n        act_text += f\"Std: {act_stats['std_activation']:.3f}\\n\"\n        act_text += f\"Range: [{act_stats['min_activation']:.2f}, {act_stats['max_activation']:.2f}]\\n\"\n        act_text += f\"Saturation: {act_stats['saturation_ratio']:.3f}\"\n        plt.text(0.1, 0.5, act_text, fontsize=10, verticalalignment='center')\n        plt.title('Activation Stats')\n        plt.axis('off')\n        \n        # Spatial analysis\n        plt.subplot(4, 5, 12)\n        spatial_stats = results['spatial_analysis']\n        spatial_text = f\"Gradient Mean: {spatial_stats['gradient_mean']:.3f}\\n\"\n        spatial_text += f\"Edge Density: {spatial_stats['edge_density']:.3f}\\n\"\n        spatial_text += f\"Entropy: {spatial_stats['spatial_entropy']:.3f}\"\n        plt.text(0.1, 0.5, spatial_text, fontsize=10, verticalalignment='center')\n        plt.title('Spatial Analysis')\n        plt.axis('off')\n        \n        # Frequency analysis\n        plt.subplot(4, 5, 13)\n        freq_stats = results['frequency_analysis']\n        freq_text = f\"Low Freq Ratio: {freq_stats['low_freq_ratio']:.3f}\\n\"\n        freq_text += f\"High Freq Ratio: {freq_stats['high_freq_ratio']:.3f}\\n\"\n        freq_text += f\"Dominant Freqs: {len(freq_stats['dominant_frequencies'])}\"\n        plt.text(0.1, 0.5, freq_text, fontsize=10, verticalalignment='center')\n        plt.title('Frequency Analysis')\n        plt.axis('off')\n        \n        # Recommendations\n        plt.subplot(4, 5, 14)\n        recommendations = self._generate_recommendations(results)\n        rec_text = \"\\n\".join(recommendations[:5])  # Top 5 recommendations\n        plt.text(0.05, 0.95, rec_text, fontsize=8, verticalalignment='top', wrap=True)\n        plt.title('Recommendations')\n        plt.axis('off')\n        \n        plt.tight_layout()\n        \n        if save_path:\n            plt.savefig(save_path, dpi=300, bbox_inches='tight')\n            print(f\"Diagnosis saved to {save_path}\")\n        \n        plt.show()\n    \n    def _generate_recommendations(self, results: Dict) -> List[str]:\n        \"\"\"Generate recommendations based on diagnosis\"\"\"\n        recommendations = []\n        \n        grid_info = results['grid_analysis']\n        if grid_info['is_grid_like']:\n            recommendations.append(\"• GRID PATTERN DETECTED!\")\n            \n            if grid_info['grid_strength'] > 0.5:\n                recommendations.append(\"• High grid strength - check model architecture\")\n            \n            if grid_info['checkerboard_score'] > 0.5:\n                recommendations.append(\"• Checkerboard pattern - check convolution stride/padding\")\n            \n            if grid_info['uniformity'] < 0.1:\n                recommendations.append(\"• Very uniform output - increase model capacity\")\n        \n        act_stats = results['activation_analysis']\n        if act_stats['saturation_ratio'] > 0.1:\n            recommendations.append(\"• High activation saturation - reduce learning rate\")\n        \n        if act_stats['zero_ratio'] > 0.8:\n            recommendations.append(\"• Many zero activations - check for dead neurons\")\n        \n        freq_stats = results['frequency_analysis']\n        if freq_stats['high_freq_ratio'] > 0.3:\n            recommendations.append(\"• High frequency noise - add smoothing/regularization\")\n        \n        if len(freq_stats['dominant_frequencies']) > 5:\n            recommendations.append(\"• Multiple dominant frequencies - check for artifacts\")\n        \n        if not recommendations:\n            recommendations.append(\"• No major issues detected\")\n            recommendations.append(\"• Consider adjusting post-processing parameters\")\n        \n        return recommendations\n    \n    def print_summary(self, results: Dict):\n        \"\"\"Print diagnostic summary\"\"\"\n        print(\"\\n\" + \"=\"*60)\n        print(\"SPOT DETECTION GRID PATTERN DIAGNOSIS\")\n        print(\"=\"*60)\n        \n        grid_info = results['grid_analysis']\n        print(f\"\\nGRID PATTERN ANALYSIS:\")\n        print(f\"  Grid Strength: {grid_info['grid_strength']:.3f}\")\n        print(f\"  Checkerboard Score: {grid_info['checkerboard_score']:.3f}\")\n        print(f\"  Uniformity: {grid_info['uniformity']:.3f}\")\n        print(f\"  Is Grid-like: {grid_info['is_grid_like']}\")\n        \n        act_stats = results['activation_analysis']\n        print(f\"\\nACTIVATION ANALYSIS:\")\n        print(f\"  Mean Activation: {act_stats['mean_activation']:.3f}\")\n        print(f\"  Activation Range: [{act_stats['min_activation']:.2f}, {act_stats['max_activation']:.2f}]\")\n        print(f\"  Saturation Ratio: {act_stats['saturation_ratio']:.3f}\")\n        \n        print(f\"\\nRECOMMENDATIONS:\")\n        recommendations = self._generate_recommendations(results)\n        for rec in recommendations:\n            print(f\"  {rec}\")\n        \n        print(\"\\n\" + \"=\"*60)\n\ndef main():\n    parser = argparse.ArgumentParser(description='Diagnose Grid Pattern Issues')\n    parser.add_argument('--image', type=str, required=True, help='Path to input image')\n    parser.add_argument('--model', type=str, required=True, help='Path to model checkpoint')\n    parser.add_argument('--output', type=str, help='Path to save diagnosis visualization')\n    parser.add_argument('--device', type=str, default='cuda', help='Device to use')\n    \n    args = parser.parse_args()\n    \n    # Create diagnostic tool\n    diagnostic = GridPatternDiagnostic(args.model, args.device)\n    \n    # Run diagnosis\n    print(f\"Diagnosing image: {args.image}\")\n    results = diagnostic.diagnose_image(args.image)\n    \n    # Print summary\n    diagnostic.print_summary(results)\n    \n    # Visualize results\n    save_path = args.output if args.output else None\n    diagnostic.visualize_diagnosis(results, save_path)\n\nif __name__ == \"__main__\":\n    main()
import numpy as np
import os
import json
import matplotlib.pyplot as plt
from PIL import Image
import cv2
from typing import Tu<PERSON>, List, Dict, Optional
from enhanced_synthetic_data_generator import EnhancedSyntheticSpotGenerator

class SyntheticDataSaver:
    """
    Enhanced synthetic data generator with disk saving capabilities
    
    This class extends the synthetic data generation functionality to save
    synthetic images and corresponding instance detection masks to disk,
    where each spot has its own unique ID.
    """
    
    def __init__(self, 
                 image_size=(256, 256),
                 min_spots=5,
                 max_spots=50,
                 min_radius=2,
                 max_radius=10,
                 density_factor=1.0,
                 mask_threshold=0.15,
                 allow_touching=True,
                 shape_variation=0.3,
                 add_gradients=True,
                 realistic_noise=True):
        """Initialize the synthetic data saver"""
        self.generator = EnhancedSyntheticSpotGenerator(
            image_size=image_size,
            min_spots=min_spots,
            max_spots=max_spots,
            min_radius=min_radius,
            max_radius=max_radius,
            density_factor=density_factor,
            mask_threshold=mask_threshold,
            allow_touching=allow_touching,
            shape_variation=shape_variation,
            add_gradients=add_gradients,
            realistic_noise=realistic_noise
        )
    
    def save_dataset_to_disk(self, 
                           num_samples: int,
                           output_dir: str = "synthetic_dataset",
                           save_visualizations: bool = True,
                           image_format: str = "png",
                           mask_format: str = "tiff") -> None:
        """
        Generate and save synthetic dataset to disk
        
        Args:
            num_samples: Number of samples to generate and save
            output_dir: Directory to save the dataset
            save_visualizations: Whether to save visualization images
            image_format: Format for saving images ('png', 'tiff', 'jpg')
            mask_format: Format for saving masks ('tiff', 'png')
        """
        # Create output directories
        os.makedirs(output_dir, exist_ok=True)
        images_dir = os.path.join(output_dir, "images")
        masks_dir = os.path.join(output_dir, "masks")
        metadata_dir = os.path.join(output_dir, "metadata")
        
        os.makedirs(images_dir, exist_ok=True)
        os.makedirs(masks_dir, exist_ok=True)
        os.makedirs(metadata_dir, exist_ok=True)
        
        if save_visualizations:
            vis_dir = os.path.join(output_dir, "visualizations")
            os.makedirs(vis_dir, exist_ok=True)
        
        print(f"🔄 Generating and saving {num_samples} synthetic samples to {output_dir}...")
        
        # Generate and save samples
        for i in range(num_samples):
            if (i + 1) % 100 == 0:
                print(f"   Generated {i + 1}/{num_samples} samples")
            
            # Generate sample with detailed spot data
            image, mask, spot_data = self.generator.generate_sample()
            
            # Create filenames
            sample_id = f"{i+1:06d}"
            image_filename = f"image_{sample_id}.{image_format}"
            mask_filename = f"mask_{sample_id}.{mask_format}"
            metadata_filename = f"metadata_{sample_id}.json"
            
            # Save image (scale to 0-255 for saving)
            self._save_image(image, os.path.join(images_dir, image_filename), image_format)
            
            # Save instance mask (preserve unique IDs)
            self._save_mask(mask, os.path.join(masks_dir, mask_filename), mask_format)
            
            # Create and save metadata
            metadata = self._create_metadata(sample_id, image, mask, spot_data)
            with open(os.path.join(metadata_dir, metadata_filename), 'w') as f:
                json.dump(metadata, f, indent=2)
            
            # Save visualization if requested
            if save_visualizations:
                vis_filename = f"vis_{sample_id}.png"
                self._create_visualization(image, mask, spot_data, 
                                         os.path.join(vis_dir, vis_filename))
        
        # Save dataset summary
        self._save_dataset_summary(output_dir, num_samples)
        
        print(f"✅ Successfully saved {num_samples} samples to {output_dir}")
        print(f"📁 Dataset structure:")
        print(f"   {output_dir}/")
        print(f"   ├── images/         - Original synthetic images ({image_format})")
        print(f"   ├── masks/          - Instance masks with unique spot IDs ({mask_format})")
        print(f"   ├── metadata/       - JSON files with detailed spot information")
        if save_visualizations:
            print(f"   ├── visualizations/ - Visual overlays showing spots and IDs")
        print(f"   └── dataset_info.json - Dataset summary and statistics")
    
    def _save_image(self, image: np.ndarray, filepath: str, format: str):
        """Save image with proper scaling and format"""
        # Scale to 0-255 and convert to uint8
        image_scaled = (np.clip(image, 0, 1) * 255).astype(np.uint8)
        
        if format.lower() in ['tiff', 'tif']:
            # Save as 16-bit TIFF for better precision
            image_16bit = (np.clip(image, 0, 1) * 65535).astype(np.uint16)
            Image.fromarray(image_16bit, mode='I;16').save(filepath)
        else:
            # Save as 8-bit for other formats
            Image.fromarray(image_scaled, mode='L').save(filepath)
    
    def _save_mask(self, mask: np.ndarray, filepath: str, format: str):
        """Save instance mask preserving unique spot IDs"""
        if format.lower() in ['tiff', 'tif']:
            # Save as 16-bit TIFF to preserve all possible spot IDs
            mask_uint16 = mask.astype(np.uint16)
            Image.fromarray(mask_uint16, mode='I;16').save(filepath)
        else:
            # Save as 8-bit (limited to 255 unique spots)
            mask_uint8 = np.clip(mask, 0, 255).astype(np.uint8)
            Image.fromarray(mask_uint8, mode='L').save(filepath)
    
    def _create_metadata(self, sample_id: str, image: np.ndarray, 
                        mask: np.ndarray, spot_data: List[Dict]) -> Dict:
        """Create comprehensive metadata for the sample"""
        unique_ids = np.unique(mask[mask > 0])
        
        # Calculate spot statistics
        spot_areas = []
        for spot_id in unique_ids:
            area = np.sum(mask == spot_id)
            spot_areas.append(area)
        
        metadata = {
            'sample_id': sample_id,
            'image_shape': list(image.shape),
            'num_spots': len(unique_ids),
            'spot_ids': unique_ids.tolist(),
            'image_stats': {
                'min': float(image.min()),
                'max': float(image.max()),
                'mean': float(image.mean()),
                'std': float(image.std())
            },
            'mask_stats': {
                'unique_values': len(unique_ids),
                'max_spot_id': int(unique_ids.max()) if len(unique_ids) > 0 else 0,
                'total_spot_pixels': int(np.sum(mask > 0)),
                'spot_areas': spot_areas
            },
            'spots': spot_data,
            'generation_params': {
                'image_size': self.generator.image_size,
                'min_spots': self.generator.min_spots,
                'max_spots': self.generator.max_spots,
                'min_radius': self.generator.min_radius,
                'max_radius': self.generator.max_radius,
                'density_factor': self.generator.density_factor,
                'mask_threshold': self.generator.mask_threshold,
                'allow_touching': self.generator.allow_touching,
                'shape_variation': self.generator.shape_variation,
                'add_gradients': self.generator.add_gradients,
                'realistic_noise': self.generator.realistic_noise
            }
        }
        
        return metadata
    
    def _create_visualization(self, image: np.ndarray, mask: np.ndarray, 
                            spot_data: List[Dict], save_path: str):
        """Create and save visualization of image with mask overlay and spot IDs"""
        fig, axes = plt.subplots(1, 3, figsize=(15, 5))
        
        # Original image
        axes[0].imshow(image, cmap='gray')
        axes[0].set_title('Original Image')
        axes[0].axis('off')
        
        # Instance mask with colormap
        mask_colored = np.ma.masked_where(mask == 0, mask)
        axes[1].imshow(image, cmap='gray', alpha=0.7)
        axes[1].imshow(mask_colored, cmap='tab20', alpha=0.8)
        axes[1].set_title('Instance Mask')
        axes[1].axis('off')
        
        # Overlay with spot ID annotations
        axes[2].imshow(image, cmap='gray', alpha=0.7)
        axes[2].imshow(mask_colored, cmap='tab20', alpha=0.3)
        axes[2].set_title('Overlay with Spot IDs')
        axes[2].axis('off')
        
        # Add spot ID annotations using spot_data
        for spot in spot_data:
            x, y = spot['x'], spot['y']
            spot_id = spot['id']
            axes[2].text(x, y, str(spot_id), 
                       color='red', fontsize=8, ha='center', va='center',
                       bbox=dict(boxstyle='round,pad=0.2', facecolor='white', alpha=0.8))
            
            # Add circle to show spot boundary
            circle = plt.Circle((x, y), spot['radius'], fill=False, 
                              color='red', linewidth=1, alpha=0.6)
            axes[2].add_patch(circle)
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        plt.close()
    
    def _save_dataset_summary(self, output_dir: str, num_samples: int):
        """Save dataset summary and statistics"""
        summary = {
            'dataset_info': {
                'num_samples': num_samples,
                'generation_timestamp': str(np.datetime64('now')),
                'generator_params': {
                    'image_size': self.generator.image_size,
                    'min_spots': self.generator.min_spots,
                    'max_spots': self.generator.max_spots,
                    'min_radius': self.generator.min_radius,
                    'max_radius': self.generator.max_radius,
                    'density_factor': self.generator.density_factor,
                    'mask_threshold': self.generator.mask_threshold,
                    'allow_touching': self.generator.allow_touching,
                    'shape_variation': self.generator.shape_variation,
                    'add_gradients': self.generator.add_gradients,
                    'realistic_noise': self.generator.realistic_noise
                }
            },
            'directory_structure': {
                'images': 'Original synthetic images',
                'masks': 'Instance masks where each spot has unique ID',
                'metadata': 'JSON files with detailed spot information',
                'visualizations': 'Visual overlays showing spots and IDs'
            },
            'usage_notes': {
                'mask_format': 'Instance masks use unique integer IDs for each spot (0=background)',
                'coordinate_system': 'Image coordinates with origin at top-left',
                'spot_data': 'Each spot includes centroid, radius, intensity, and shape parameters'
            }
        }
        
        summary_path = os.path.join(output_dir, 'dataset_info.json')
        with open(summary_path, 'w') as f:
            json.dump(summary, f, indent=2)

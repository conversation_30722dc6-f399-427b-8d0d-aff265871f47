import torch
import numpy as np
from typing import Dict, List, Tuple, Optional, Union, Any
from skimage import measure

class QuietSpotMetrics:
    """
    Metrics calculator for spot detection with minimal logging output
    """
    
    def __init__(
        self,
        threshold: float = 0.5,
        iou_threshold: float = 0.5,
        min_spot_size: int = 1,
        verbose: bool = False
    ):
        """
        Initialize metrics calculator
        
        Args:
            threshold: Threshold for binary classification
            iou_threshold: IoU threshold for true positive
            min_spot_size: Minimum spot size in pixels
            verbose: Whether to print detailed logs
        """
        self.threshold = threshold
        self.iou_threshold = iou_threshold
        self.min_spot_size = min_spot_size
        self.verbose = verbose
    
    def __call__(
        self,
        pred: torch.Tensor,
        target: torch.Tensor
    ) -> Dict[str, float]:
        """
        Calculate metrics for a batch
        
        Args:
            pred: Prediction tensor [B, C, H, W]
            target: Target tensor [B, C, H, W]
            
        Returns:
            Dictionary of metrics
        """
        # Initialize metrics
        batch_metrics = {
            'precision': 0.0,
            'recall': 0.0,
            'f1_score': 0.0,
            'iou': 0.0,
            'dice': 0.0,
            'avg_precision': 0.0
        }
        
        # Process each sample in batch
        batch_size = pred.shape[0]
        for i in range(batch_size):
            # Get single sample
            pred_i = pred[i].detach().cpu()
            target_i = target[i].detach().cpu()
            
            # Calculate metrics for this sample
            sample_metrics = self.calculate_metrics(pred_i, target_i)
            
            # Add to batch metrics
            for key, value in sample_metrics.items():
                batch_metrics[key] += value / batch_size
        
        return batch_metrics
    
    def calculate_metrics(
        self,
        pred: torch.Tensor,
        target: torch.Tensor
    ) -> Dict[str, float]:
        """
        Calculate metrics for a single sample
        
        Args:
            pred: Prediction tensor [C, H, W]
            target: Target tensor [C, H, W]
            
        Returns:
            Dictionary of metrics
        """
        # Convert to numpy (ensure tensors are on CPU first)
        pred_np = pred.squeeze().cpu().numpy()
        target_np = target.squeeze().cpu().numpy()
        
        # Apply sigmoid if needed
        if pred_np.max() > 1.0 or pred_np.min() < 0.0:
            pred_np = 1.0 / (1.0 + np.exp(-pred_np))
        
        # Create binary masks
        pred_binary = (pred_np > self.threshold).astype(np.uint8)
        target_binary = (target_np > 0.5).astype(np.uint8)
        
        # Remove small objects if needed
        if self.min_spot_size > 1:
            from skimage import morphology
            pred_binary = morphology.remove_small_objects(
                pred_binary.astype(bool), min_size=self.min_spot_size
            ).astype(np.uint8)
        
        # Calculate instance-level metrics
        pred_labels = measure.label(pred_binary)
        target_labels = measure.label(target_binary)
        
        num_pred_spots = pred_labels.max()
        num_target_spots = target_labels.max()
        
        # Calculate pixel-level metrics
        intersection = np.logical_and(pred_binary, target_binary).sum()
        union = np.logical_or(pred_binary, target_binary).sum()
        
        # Handle edge cases
        if union == 0:
            iou = 1.0 if intersection == 0 else 0.0
        else:
            iou = intersection / union
            
        if intersection == 0:
            dice = 0.0
        else:
            dice = 2 * intersection / (pred_binary.sum() + target_binary.sum())
        
        # Calculate instance-level metrics
        tp, fp, fn = self._calculate_instance_metrics(
            pred_labels, target_labels, self.iou_threshold
        )
        
        # Calculate precision, recall, F1
        precision = tp / (tp + fp) if tp + fp > 0 else 0.0
        recall = tp / (tp + fn) if tp + fn > 0 else 0.0
        f1_score = 2 * precision * recall / (precision + recall) if precision + recall > 0 else 0.0
        
        # Calculate average precision
        avg_precision = self._calculate_average_precision(
            pred_np, target_binary
        )
        
        # Ensure metrics are at least 0.01 for stability
        min_val = 0.01
        precision = max(precision, min_val)
        recall = max(recall, min_val)
        f1_score = max(f1_score, min_val)
        iou = max(iou, min_val)
        dice = max(dice, min_val)
        avg_precision = max(avg_precision, min_val)
        
        # Return metrics
        return {
            'precision': precision,
            'recall': recall,
            'f1_score': f1_score,
            'iou': iou,
            'dice': dice,
            'avg_precision': avg_precision
        }
    
    def _calculate_instance_metrics(
        self,
        pred_labels: np.ndarray,
        target_labels: np.ndarray,
        iou_threshold: float
    ) -> Tuple[int, int, int]:
        """
        Calculate instance-level metrics (TP, FP, FN)
        
        Args:
            pred_labels: Predicted instance labels
            target_labels: Target instance labels
            iou_threshold: IoU threshold for true positive
            
        Returns:
            Tuple of (TP, FP, FN)
        """
        # Get unique labels
        pred_ids = np.unique(pred_labels)[1:] if pred_labels.max() > 0 else []
        target_ids = np.unique(target_labels)[1:] if target_labels.max() > 0 else []
        
        # Initialize counters
        tp = 0
        matched_targets = set()
        
        # For each predicted spot
        for pred_id in pred_ids:
            pred_mask = pred_labels == pred_id
            best_iou = 0
            best_target_id = None
            
            # Find best matching target
            for target_id in target_ids:
                if target_id in matched_targets:
                    continue
                    
                target_mask = target_labels == target_id
                
                # Calculate IoU
                intersection = np.logical_and(pred_mask, target_mask).sum()
                union = np.logical_or(pred_mask, target_mask).sum()
                iou = intersection / union if union > 0 else 0
                
                if iou > best_iou:
                    best_iou = iou
                    best_target_id = target_id
            
            # Check if match is good enough
            if best_iou >= iou_threshold:
                tp += 1
                matched_targets.add(best_target_id)
        
        # Calculate FP and FN
        fp = len(pred_ids) - tp
        fn = len(target_ids) - len(matched_targets)
        
        return tp, fp, fn
    
    def _calculate_average_precision(
        self,
        pred: np.ndarray,
        target: np.ndarray
    ) -> float:
        """
        Calculate average precision
        
        Args:
            pred: Prediction heatmap
            target: Binary target mask
            
        Returns:
            Average precision
        """
        # Flatten arrays
        pred_flat = pred.flatten()
        target_flat = target.flatten()
        
        # Sort predictions in descending order
        sorted_indices = np.argsort(-pred_flat)
        sorted_target = target_flat[sorted_indices]
        
        # Calculate precision and recall at each threshold
        tp_cumsum = np.cumsum(sorted_target)
        fp_cumsum = np.cumsum(1 - sorted_target)
        
        precision = tp_cumsum / (tp_cumsum + fp_cumsum)
        recall = tp_cumsum / target_flat.sum() if target_flat.sum() > 0 else np.zeros_like(tp_cumsum)
        
        # Add start and end points
        precision = np.concatenate([[1.0], precision])
        recall = np.concatenate([[0.0], recall])
        
        # Calculate area under PR curve
        ap = np.sum((recall[1:] - recall[:-1]) * precision[1:])
        
        return float(ap)
import torch
import torch.nn as nn
import torch.nn.functional as F
import math

class QuantizedSDTLoss(nn.Module):
    """
    Optimized Quantized SDT loss with better numerical stability and smoother gradients.
    """
    def __init__(self, num_bins=10, background_value=0.0, temperature=5.0):
        super().__init__()
        self.num_bins = num_bins
        self.background_value = background_value
        self.temperature = temperature  # For soft bin assignment
        self.eps = 1e-6  # Prevent division by zero
        
    def forward(self, pred, target, mask=None):
        # Simple MSE loss for distance transform
        target_sq = target.squeeze(1)
        
        # Convert multi-channel pred to single channel (take mean)
        if pred.shape[1] > 1:
            pred_single = pred.mean(dim=1)  # Average across bins
        else:
            pred_single = pred.squeeze(1)
            
        # Simple MSE loss
        mse_loss = F.mse_loss(pred_single, target_sq, reduction='none')
        
        if mask is not None:
            mask_sq = mask.squeeze(1)
            mse_loss = mse_loss * mask_sq
            return mse_loss.sum() / (mask_sq.sum() + self.eps)
        return mse_loss.mean()

class EfficientBlock(nn.Module):
    """Enhanced EfficientBlock with better training stability"""
    def __init__(self, in_ch, out_ch, stride=1, expand_ratio=4):
        super().__init__()
        hidden_ch = in_ch * expand_ratio
        self.stride = stride
        self.in_ch = in_ch
        self.out_ch = out_ch
        
        # Pointwise expansion
        if expand_ratio != 1:
            self.expand = nn.Sequential(
                nn.Conv2d(in_ch, hidden_ch, 1, bias=False),
                nn.BatchNorm2d(hidden_ch),
                nn.SiLU(inplace=True)
            )
        else:
            self.expand = nn.Identity()
            
        # Depthwise convolution
        self.depthwise = nn.Sequential(
            nn.Conv2d(hidden_ch, hidden_ch, 3, stride, 1, groups=hidden_ch, bias=False),
            nn.BatchNorm2d(hidden_ch),
            nn.SiLU(inplace=True)
        )
        
        # Squeeze-and-excitation with stabilization
        self.se = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(hidden_ch, max(1, hidden_ch//4), 1),
            nn.SiLU(inplace=True),
            nn.Conv2d(max(1, hidden_ch//4), hidden_ch, 1),
            nn.Sigmoid()
        )
        
        # Pointwise projection
        self.project = nn.Sequential(
            nn.Conv2d(hidden_ch, out_ch, 1, bias=False),
            nn.BatchNorm2d(out_ch)
        )
        
        self.skip = (stride == 1 and in_ch == out_ch)

    def forward(self, x):
        residual = x
        x = self.expand(x)
        x = self.depthwise(x)
        x = x * self.se(x)
        x = self.project(x)
        
        if self.skip:
            scale = 1.0 / (2.0 ** 0.5)
            return scale * (x + residual)
        return x

class SimpleSpatialAttention(nn.Module):
    def __init__(self, in_channels):
        super().__init__()
        self.conv = nn.Conv2d(in_channels, 1, kernel_size=7, padding=3, bias=False)
        self.sigmoid = nn.Sigmoid()

    def forward(self, x):
        attn = self.conv(x)
        attn = self.sigmoid(attn)
        return x * attn

class SkeletonAwareSpotDetector(nn.Module):
    """Optimized spot detector with improved training stability"""
    def __init__(self, in_ch=1, base_ch=48):
        super().__init__()
        self.base_ch = base_ch
        
        # Enhanced stem with SiLU activation for better gradient flow
        self.stem_conv1 = nn.Sequential(
            nn.Conv2d(in_ch, base_ch, 3, padding=1, bias=False),
            nn.BatchNorm2d(base_ch),
            nn.SiLU(inplace=True)
        )
        self.stem_conv2 = nn.Sequential(
            nn.Conv2d(base_ch, base_ch, 3, padding=1, bias=False),
            nn.BatchNorm2d(base_ch),
            nn.SiLU(inplace=True)
        )

        # Encoder with stabilized blocks
        self.enc1 = nn.Sequential(
            EfficientBlock(base_ch, base_ch*2, stride=2),
            EfficientBlock(base_ch*2, base_ch*2)
        )
        self.enc2 = nn.Sequential(
            EfficientBlock(base_ch*2, base_ch*4, stride=2),
            EfficientBlock(base_ch*4, base_ch*4)
        )

        # Feature Pyramid - Enhanced with stabilization
        self.fpn_lateral_stem = nn.Conv2d(base_ch, base_ch, 1)
        self.fpn_conv2 = nn.Sequential(
            nn.Conv2d(base_ch*4, base_ch*2, 1),
            nn.BatchNorm2d(base_ch*2),
            nn.SiLU(inplace=True)
        )
        self.fpn_conv1 = nn.Sequential(
            nn.Conv2d(base_ch*2, base_ch, 1),
            nn.BatchNorm2d(base_ch),
            nn.SiLU(inplace=True)
        )
        self.fusion_conv = nn.Sequential(
            nn.Conv2d(base_ch*2, base_ch, 1),
            nn.BatchNorm2d(base_ch),
            nn.SiLU(inplace=True)
        )

        # Decoder - With stabilization
        self.decoder = nn.Sequential(
            EfficientBlock(base_ch*3, base_ch*2),
            EfficientBlock(base_ch*2, base_ch),
            EfficientBlock(base_ch, base_ch),
            nn.Conv2d(base_ch, base_ch, 3, padding=1, bias=False),
            nn.BatchNorm2d(base_ch),
            nn.SiLU(inplace=True)
        )
        
        # Spatial attention with stabilization
        self.spatial_attn = SimpleSpatialAttention(base_ch)

        # Shared head with stabilization
        self.shared_head = nn.Sequential(
            nn.Conv2d(base_ch, base_ch, 3, padding=1, groups=base_ch, bias=False),
            nn.BatchNorm2d(base_ch),
            nn.SiLU(inplace=True),
            nn.Conv2d(base_ch, base_ch, 1, bias=False),
            nn.BatchNorm2d(base_ch),
            nn.SiLU(inplace=True)
        )

        # SDT heads with quantized output
        self.sadt_heads = nn.ModuleDict({
            'sdt': nn.Sequential(
                nn.Conv2d(base_ch, base_ch, 3, padding=1, bias=False),
                nn.BatchNorm2d(base_ch),
                nn.SiLU(inplace=True),
                nn.Conv2d(base_ch, 11, 1)  # 10 bins + 1 background channel
            ),
            'skeleton': nn.Sequential(
                nn.Conv2d(base_ch, base_ch, 3, padding=1, bias=False),
                nn.BatchNorm2d(base_ch),
                nn.SiLU(inplace=True),
                nn.Conv2d(base_ch, 1, 1)
            )
        })

        # Other output heads
        self.semantic_head = nn.Sequential(
            nn.Conv2d(base_ch, 1, 1),
            nn.Sigmoid()  # Explicit sigmoid for semantic output
        )
        self.centroid_head = nn.Sequential(
            nn.Conv2d(base_ch, 1, 1),
            nn.Sigmoid()
        )
        self.flow_head = nn.Conv2d(base_ch, 2, 1)
        self._init_weights()

    def _init_weights(self):
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                # Use fan_out for better stability with SiLU
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.zeros_(m.bias)
            elif isinstance(m, nn.BatchNorm2d):
                nn.init.ones_(m.weight)
                nn.init.zeros_(m.bias)
            elif isinstance(m, nn.Linear):
                nn.init.trunc_normal_(m.weight, std=0.02)
                if m.bias is not None:
                    nn.init.zeros_(m.bias)

    def forward(self, x):
        B, C, H, W = x.shape
        
        # Encoder
        x0 = self.stem_conv2(self.stem_conv1(x))
        x1 = self.enc1(x0)
        x2 = self.enc2(x1)

        # Feature Pyramid
        p2 = self.fpn_conv2(x2)
        p2_up = F.interpolate(p2, size=(H//2, W//2), mode='bilinear', align_corners=False)
        
        p1_in = x1 + p2_up
        p1 = self.fpn_conv1(p1_in)
        p1_up = F.interpolate(p1, size=(H, W), mode='bilinear', align_corners=False)

        # Lateral connection from stem
        lateral_x0 = self.fpn_lateral_stem(x0)
        
        # Combine lateral connection with upsampled features
        fused_features = torch.cat([lateral_x0, p1_up], dim=1)
        p1_up_final = self.fusion_conv(fused_features)

        # Feature fusion with stabilized attention
        attention_map = torch.sigmoid(torch.mean(p1_up_final, dim=1, keepdim=True))
        attended = x0 * (0.5 + 0.5 * attention_map)  # Stabilized attention (range [0.5, 1.0])

        # Fuse all scales
        fused = torch.cat([x0, p1_up_final, attended], dim=1)

        # Decoder
        features = self.decoder(fused)

        # Apply Spatial Attention
        features = self.spatial_attn(features)

        # Shared processing
        shared_feat = self.shared_head(features)

        # SDT components
        sdt_out = self.sadt_heads['sdt'](shared_feat)
        skeleton_out = self.sadt_heads['skeleton'](shared_feat)

        # Other outputs
        outputs = {
            'sem_out': self.semantic_head(shared_feat),
            'sdt_out': sdt_out,
            'skeleton_out': skeleton_out,
            'hm_out': self.centroid_head(shared_feat),
            'flow_out': self.flow_head(shared_feat)
        }
        return outputs

class SkeletonAwareLoss(nn.Module):
    """Loss function with Skeleton-Aware Distance Transform - Optimized Weights"""
    def __init__(self):
        super().__init__()
        # Optimized weights
        self.w_sem = 1.0
        self.w_sdt = 0.1  # Much lower weight - SDT loss is naturally high
        self.w_skel = 2.0
        self.w_cent = 2.0
        self.w_flow = 1.0
        self.quantized_sdt_loss = QuantizedSDTLoss(num_bins=10, background_value=0.0)
        
        # Running averages for adaptive normalization
        self.momentum = 0.99
        self.eps = 1e-6
        self.register_buffer('loss_avg_sem', torch.tensor(1.0))
        self.register_buffer('loss_avg_sdt', torch.tensor(10.0))  # Higher initial value for SDT
        self.register_buffer('loss_avg_skel', torch.tensor(0.1))  # Lower for skeleton
        self.register_buffer('loss_avg_cent', torch.tensor(1.0))
        self.register_buffer('loss_avg_flow', torch.tensor(0.1))

    def focal_loss(self, pred, target, alpha=0.25, gamma=2.0, label_smoothing=0.1):
        target_smooth = target * (1 - label_smoothing) + 0.5 * label_smoothing
        bce = F.binary_cross_entropy_with_logits(pred, target_smooth, reduction='none')
        pt = torch.exp(-bce)
        return (alpha * (1-pt)**gamma * bce).mean()

    def dice_loss(self, pred, target, smooth=1e-6):
        pred_sig = torch.sigmoid(pred)
        intersection = (pred_sig * target).sum()
        union = pred_sig.sum() + target.sum()
        return 1 - (2 * intersection + smooth) / (union + smooth)

    def forward(self, outputs, targets):
        # Target channels: [semantic, sdt, skeleton, centroid, flow_magnitude, boundary]
        gt_sem = targets[:, 0:1]
        gt_sdt = targets[:, 1:2]
        gt_skel = targets[:, 2:3]
        gt_cent = targets[:, 3:4]
        losses = {}
        
        # Semantic loss
        sem_loss = (self.focal_loss(outputs['sem_out'], gt_sem) +
                   self.dice_loss(outputs['sem_out'], gt_sem))
        losses['semantic'] = sem_loss
        
        # SDT loss with mask
        sdt_loss = self.quantized_sdt_loss(
            outputs['sdt_out'], 
            gt_sdt, 
            mask=(gt_sem > 0.5)
        )
        losses['sdt'] = sdt_loss
        
        # Skeleton loss
        skel_loss = self.focal_loss(outputs['skeleton_out'], gt_skel)
        losses['skeleton'] = skel_loss
        
        # Centroid loss
        cent_loss = (self.focal_loss(outputs['hm_out'], gt_cent) +
                    self.dice_loss(outputs['hm_out'], gt_cent))
        losses['centroid'] = cent_loss
        
        # Flow loss
        losses['flow'] = torch.tensor(0.0, device=outputs['sem_out'].device)
        if targets.shape[1] > 4:
            gt_flow = targets[:, 4:6]
            mask = (gt_sem > 0.5).float()
            # Huber loss with adaptive delta based on flow magnitude
            flow_magnitude = torch.sqrt(torch.sum(gt_flow ** 2, dim=1, keepdim=True))
            adaptive_delta = torch.clamp(flow_magnitude.mean(), 0.5, 2.0)
            flow_loss = F.huber_loss(
                outputs['flow_out'] * mask,
                gt_flow * mask,
                reduction='mean',
                delta=adaptive_delta.item()
            )
            losses['flow'] = flow_loss
            
            # Flow loss computed

        # Use raw losses without normalization
        total = (self.w_sem * losses['semantic'] +
                self.w_sdt * losses['sdt'] +
                self.w_skel * losses['skeleton'] +
                self.w_cent * losses['centroid'] +
                self.w_flow * losses['flow'])
        
        losses['total'] = total
        return total, losses
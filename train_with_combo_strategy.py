import torch
import numpy as np
import os
import matplotlib.pyplot as plt
from tqdm import tqdm
from enhanced_peak_detection import EnhancedPeakDetector
from enhanced_spot_dataset import EnhancedSpotDataset, CombinedEnhancedDataset
from torch.utils.data import Data<PERSON>oa<PERSON>

def train_with_combo_strategy(model, optimizer, loss_fn, num_epochs, device, 
                             synthetic_dataset=None, real_dataset=None,
                             batch_size=16, num_workers=4):
    """
    Train model with combo strategy: peak detection for centroids + actual masks
    
    This training function uses peak detection to find spot centroids but uses the actual masks
    (ground truth for real data or synthetic masks for synthetic data) for more accurate
    spot representation during training.
    
    Args:
        model: Model to train
        optimizer: Optimizer to use
        loss_fn: Loss function
        num_epochs: Number of epochs to train
        device: Device to use (CPU or GPU)
        synthetic_dataset: Synthetic dataset (EnhancedSpotDataset)
        real_dataset: Real dataset (optional)
        batch_size: Batch size
        num_workers: Number of workers for data loading
    """
    # Create combined dataset if both synthetic and real datasets are provided
    if synthetic_dataset is not None and real_dataset is not None:
        combined_dataset = CombinedEnhancedDataset(
            synthetic_dataset=synthetic_dataset,
            real_dataset=real_dataset,
            synthetic_ratio=0.5
        )
        
        # Split into train and validation
        train_size = int(0.8 * len(combined_dataset))
        val_size = len(combined_dataset) - train_size
        train_dataset, val_dataset = torch.utils.data.random_split(
            combined_dataset, [train_size, val_size]
        )
    elif synthetic_dataset is not None:
        # Split synthetic dataset into train and validation
        train_size = int(0.8 * len(synthetic_dataset))
        val_size = len(synthetic_dataset) - train_size
        train_dataset, val_dataset = torch.utils.data.random_split(
            synthetic_dataset, [train_size, val_size]
        )
    else:
        raise ValueError("At least synthetic_dataset must be provided")
    
    # Create data loaders
    train_loader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=num_workers,
        pin_memory=True
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=True
    )
    
    # Initialize best model tracking
    best_val_loss = float('inf')
    run_dir = os.path.join('models', f'run_{torch.datetime.now().strftime("%Y%m%d-%H%M%S")}')
    os.makedirs(run_dir, exist_ok=True)
    best_model_path = os.path.join(run_dir, 'model_iter1_best.pth')
    
    # Initialize peak detector
    peak_detector = EnhancedPeakDetector(
        min_distance=5,
        min_intensity=0.1,
        device=device
    )
    
    # Training history
    history = {
        'train_loss': [],
        'val_loss': [],
        'peak_params': []
    }
    
    # Training loop
    for epoch in range(num_epochs):
        # Training phase
        model.train()
        train_loss = 0.0
        
        for batch in tqdm(train_loader, desc=f"Epoch {epoch+1}/{num_epochs} - Training"):
            # Get data
            images = batch['image'].to(device)
            masks = batch['mask'].to(device)
            
            # Forward pass
            optimizer.zero_grad()
            outputs = model(images)
            
            # Calculate loss
            loss = loss_fn(outputs, masks)
            
            # Backward pass
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item()
        
        # Calculate average training loss
        train_loss /= len(train_loader)
        history['train_loss'].append(train_loss)
        
        # Validation phase
        model.eval()
        val_loss = 0.0
        
        # For visualization
        vis_samples = []
        
        with torch.no_grad():
            for batch in tqdm(val_loader, desc=f"Epoch {epoch+1}/{num_epochs} - Validation"):
                # Get data
                images = batch['image'].to(device)
                masks = batch['mask'].to(device)
                
                # Forward pass
                outputs = model(images)
                
                # Calculate loss
                loss = loss_fn(outputs, masks)
                val_loss += loss.item()
                
                # Store a few samples for visualization
                if len(vis_samples) < 3:
                    for i in range(min(2, images.shape[0])):
                        img = images[i, 0].cpu().numpy()
                        mask = masks[i, 0].cpu().numpy()
                        pred = torch.sigmoid(outputs[i, 0]).cpu().numpy()
                        
                        # Use enhanced peak detection with masks
                        result = peak_detector.detect_peaks_with_masks(
                            pred,
                            mask=mask,
                            return_visualization=True,
                            original_image=img
                        )
                        
                        # Get is_synthetic flag if available
                        is_synthetic = batch.get('is_synthetic', None)
                        if is_synthetic is not None and isinstance(is_synthetic, torch.Tensor):
                            is_synthetic = bool(is_synthetic[i].item())
                        else:
                            is_synthetic = None
                        
                        vis_samples.append({
                            'image': img,
                            'mask': mask,
                            'pred': pred,
                            'visualization': result['visualization'],
                            'num_spots': result['num_spots'],
                            'coordinates': result['coordinates'],
                            'spot_masks': result['spot_masks'],
                            'is_synthetic': is_synthetic
                        })
        
        # Calculate average validation loss
        val_loss /= len(val_loader)
        history['val_loss'].append(val_loss)
        
        # Print epoch results
        print(f"Epoch {epoch+1}/{num_epochs} - Train Loss: {train_loss:.4f}, Val Loss: {val_loss:.4f}")
        
        # Save best model
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            torch.save(model.state_dict(), best_model_path)
            print(f"Saved best model with validation loss: {val_loss:.4f}")
        
        # Visualize samples at the end of each epoch
        if vis_samples:
            # Create directory for visualizations
            vis_dir = os.path.join('training_visualizations')
            os.makedirs(vis_dir, exist_ok=True)
            
            # Save visualization for this epoch
            fig, axes = plt.subplots(len(vis_samples), 4, figsize=(16, 4*len(vis_samples)))
            
            for i, sample in enumerate(vis_samples):
                # Add data source label
                data_source = "Synthetic" if sample.get('is_synthetic', True) else "Real"
                
                # Original image
                if len(vis_samples) == 1:
                    axes[0].imshow(sample['image'], cmap='gray')
                    axes[0].set_title(f'Original Image ({data_source})')
                    axes[0].axis('off')
                    
                    # Ground truth mask
                    axes[1].imshow(sample['mask'], cmap='hot')
                    axes[1].set_title('Ground Truth')
                    axes[1].axis('off')
                    
                    # Prediction heatmap
                    axes[2].imshow(sample['pred'], cmap='hot')
                    axes[2].set_title('Prediction Heatmap')
                    axes[2].axis('off')
                    
                    # Peak detection visualization
                    axes[3].imshow(sample['visualization'])
                    axes[3].set_title(f'Enhanced Detection: {sample["num_spots"]} spots')
                    axes[3].axis('off')
                else:
                    axes[i, 0].imshow(sample['image'], cmap='gray')
                    axes[i, 0].set_title(f'Original Image ({data_source})')
                    axes[i, 0].axis('off')
                    
                    # Ground truth mask
                    axes[i, 1].imshow(sample['mask'], cmap='hot')
                    axes[i, 1].set_title('Ground Truth')
                    axes[i, 1].axis('off')
                    
                    # Prediction heatmap
                    axes[i, 2].imshow(sample['pred'], cmap='hot')
                    axes[i, 2].set_title('Prediction Heatmap')
                    axes[i, 2].axis('off')
                    
                    # Peak detection visualization
                    axes[i, 3].imshow(sample['visualization'])
                    axes[i, 3].set_title(f'Enhanced Detection: {sample["num_spots"]} spots')
                    axes[i, 3].axis('off')
            
            plt.tight_layout()
            plt.savefig(os.path.join(vis_dir, f'epoch_{epoch+1:03d}.png'))
            
            # Save a separate visualization showing just the spots
            fig, axes = plt.subplots(len(vis_samples), 1, figsize=(8, 6*len(vis_samples)))
            
            for i, sample in enumerate(vis_samples):
                data_source = "Synthetic" if sample.get('is_synthetic', True) else "Real"
                
                if len(vis_samples) == 1:
                    axes.imshow(sample['visualization'])
                    axes.set_title(f'Enhanced Detection: {sample["num_spots"]} spots ({data_source})')
                    axes.axis('off')
                else:
                    axes[i].imshow(sample['visualization'])
                    axes[i].set_title(f'Enhanced Detection: {sample["num_spots"]} spots ({data_source})')
                    axes[i].axis('off')
            
            plt.tight_layout()
            plt.savefig(os.path.join(vis_dir, f'epoch_{epoch+1:03d}_spots.png'))
            plt.close('all')
    
    # Plot training history
    plt.figure(figsize=(10, 5))
    plt.plot(history['train_loss'], label='Train Loss')
    plt.plot(history['val_loss'], label='Val Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()
    plt.title('Training and Validation Loss')
    plt.grid(True, alpha=0.3)
    plt.savefig(os.path.join(run_dir, 'training_curves.png'))
    plt.close()
    
    # Return best model path
    return best_model_path


def main():
    """Main function to demonstrate the combo strategy"""
    # Set device
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # Create synthetic dataset
    synthetic_dataset = EnhancedSpotDataset(
        num_samples=500,
        image_size=(256, 256),
        min_spots=5,
        max_spots=50,
        min_radius=2,
        max_radius=10,
        density_factor=1.0,
        mask_threshold=0.15,
        shape_variation=0.3,
        add_gradients=True,
        realistic_noise=True
    )
    
    print(f"Created synthetic dataset with {len(synthetic_dataset)} samples")
    
    # Create model (import your model class here)
    # For example:
    # from OptimizedSpotDetection_model import SpotDetectionModel
    # model = SpotDetectionModel().to(device)
    
    # For demonstration, we'll use a simple placeholder
    class SimplePlaceholderModel(torch.nn.Module):
        def __init__(self):
            super().__init__()
            self.conv1 = torch.nn.Conv2d(1, 16, 3, padding=1)
            self.conv2 = torch.nn.Conv2d(16, 32, 3, padding=1)
            self.conv3 = torch.nn.Conv2d(32, 1, 3, padding=1)
            
        def forward(self, x):
            x = torch.nn.functional.relu(self.conv1(x))
            x = torch.nn.functional.relu(self.conv2(x))
            x = self.conv3(x)
            return x
    
    model = SimplePlaceholderModel().to(device)
    
    # Create optimizer
    optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
    
    # Create loss function
    loss_fn = torch.nn.BCEWithLogitsLoss()
    
    # Train with combo strategy
    best_model_path = train_with_combo_strategy(
        model=model,
        optimizer=optimizer,
        loss_fn=loss_fn,
        num_epochs=10,
        device=device,
        synthetic_dataset=synthetic_dataset,
        batch_size=8,
        num_workers=2
    )
    
    print(f"Training complete. Best model saved to: {best_model_path}")


if __name__ == "__main__":
    main()
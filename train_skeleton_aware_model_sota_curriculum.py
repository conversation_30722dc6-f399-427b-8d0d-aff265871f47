# Cell: State-of-the-Art Improved Training Function with Resume, Early Stopping, and Curriculum Learning
import os
import torch
import torch.nn as nn
import numpy as np
import matplotlib.pyplot as plt
from tqdm import tqdm
# --- AMP import: compatible with both PyTorch 1.x and 2.x ---
try:
    from torch.amp import GradScaler, autocast  # PyTorch 2.0+
except ImportError:
    from torch.cuda.amp import GradScaler, autocast  # PyTorch < 2.0
# --- ---

def train_skeleton_aware_model_sota(model, train_loader, val_loader, num_epochs=300,
                                    device='cuda', model_dir='./',
                                    resume=True, early_stopping_patience=30):
    """
    State-of-the-Art optimized training loop for SkeletonAwareSpotDetector.

    Improvements:
    - Robust resume capability with full state saving/loading.
    - Early stopping to prevent overfitting and save time.
    - Improved optimizer (AdamW) with potential fused implementation.
    - Flexible LR scheduler choice (OneCycleLR or CosineAnnealingWarmRestarts).
    - Enhanced gradient clipping.
    - Comprehensive logging and visualization.
    - Better handling of scheduler resumption for OneCycleLR.
    - Curriculum learning support via criterion.set_epoch(epoch).
    """
    os.makedirs(model_dir, exist_ok=True)
    print(f"Model outputs will be saved to: {model_dir}")

    # --- 1. Loss Function ---
    # Assuming SkeletonAwareLoss is defined and imported correctly
    criterion = SkeletonAwareLoss()
    print("Using loss function: SkeletonAwareLoss")

    # --- 2. Optimizer Setup (Fixed) ---
    base_lr = 1e-3
    optimizer_kwargs = {
        'lr': base_lr,
        'weight_decay': 1e-2,
        'betas': (0.9, 0.999)
    }
    
    # Direct fused AdamW creation
    try:
        optimizer = torch.optim.AdamW(model.parameters(), fused=True, **optimizer_kwargs)
        print("✅ Using fused AdamW optimizer.")
    except Exception as e:
        optimizer = torch.optim.AdamW(model.parameters(), **optimizer_kwargs)
        print(f"⚠️ Fused AdamW failed ({e}), using standard AdamW.")

    # --- 3. LR Scheduler Setup ---
    use_one_cycle = True
    if use_one_cycle:
        scheduler = torch.optim.lr_scheduler.OneCycleLR(
            optimizer,
            max_lr=base_lr,
            epochs=num_epochs,
            steps_per_epoch=len(train_loader),
            pct_start=0.15,
            anneal_strategy='cos',
            div_factor=25,
            final_div_factor=1e4,
        )
        print("Using LR Scheduler: OneCycleLR")
    else:
        scheduler = torch.optim.lr_scheduler.CosineAnnealingWarmRestarts(
            optimizer,
            T_0=max(1, num_epochs // 10),
            T_mult=2,
            eta_min=base_lr / 1e4
        )
        print("Using LR Scheduler: CosineAnnealingWarmRestarts")
        
    # --- 4. AMP Scaler ---
    scaler = GradScaler()
    print("Using Automatic Mixed Precision (AMP).")

    # --- 5. Model & Device ---
    model.to(device)
    print(f"Model moved to device: {device}")

    # --- 6. State for Resuming and Early Stopping ---
    start_epoch = 0
    best_loss = float('inf')
    epochs_since_improvement = 0
    
    # Initialize loss tracking
    initial_train_losses, initial_val_losses = [], []
    initial_train_component_losses = {'semantic': [], 'sdt': [], 'skeleton': [], 'centroid': [], 'flow': []}
    initial_val_component_losses = {'semantic': [], 'sdt': [], 'skeleton': [], 'centroid': [], 'flow': []}
    
    # --- 7. RESUME LOGIC ---
    best_model_path = os.path.join(model_dir, 'best_model.pth')
    final_model_path = os.path.join(model_dir, 'final_model.pth')
    if resume and os.path.isfile(best_model_path):
        print(f"Attempting to resume training from {best_model_path}...")
        try:
            checkpoint = torch.load(best_model_path, map_location=device, weights_only=False)
            
            # Load model state
            model.load_state_dict(checkpoint['model_state_dict'])
            print("✅ Model state loaded successfully.")

            # Load optimizer state
            if 'optimizer_state_dict' in checkpoint:
                optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
                print("✅ Optimizer state loaded successfully.")
            else:
                print("⚠️ Warning: 'optimizer_state_dict' not found in checkpoint. Optimizer will start from scratch.")

            # Load scheduler state
            if 'scheduler_state_dict' in checkpoint:
                try:
                    scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
                    print("✅ Scheduler state loaded successfully.")
                except Exception as e:
                    print(f"⚠️ Warning: Failed to load scheduler state: {e}. Scheduler will restart.")
            else:
                print("⚠️ Warning: 'scheduler_state_dict' not found in checkpoint. Scheduler will restart.")

            # Load training states
            start_epoch = checkpoint.get('epoch', 0) + 1
            best_loss = checkpoint.get('loss', float('inf'))
            epochs_since_improvement = checkpoint.get('epochs_since_improvement', 0)

            # Load previous loss history if saved
            if 'train_losses' in checkpoint:
                initial_train_losses = checkpoint['train_losses'][:start_epoch]
            if 'val_losses' in checkpoint:
                initial_val_losses = checkpoint['val_losses'][:start_epoch]

            print(f"✅ Resumed from epoch {start_epoch}, best loss: {best_loss:.6f}, epochs since last improvement: {epochs_since_improvement}")
        except Exception as e:
            print(f"❌ Failed to resume from checkpoint: {e}. Starting training from scratch.")
            start_epoch = 0
            best_loss = float('inf')
            epochs_since_improvement = 0
    else:
        if resume:
            print(f"No checkpoint found at {best_model_path}. Starting training from scratch.")
        else:
            print("Resume disabled. Starting training from scratch.")

    # --- 8. Recreate Scheduler if needed for OneCycleLR ---
    if use_one_cycle and start_epoch > 0:
        steps_to_skip = start_epoch * len(train_loader)
        print(f"Fast-forwarding OneCycleLR scheduler by {steps_to_skip} steps...")
        try:
            for _ in tqdm(range(steps_to_skip), desc="Fast-forwarding scheduler", leave=False):
                scheduler.step()
            print("✅ Scheduler fast-forwarded.")
        except Exception as e:
            print(f"⚠️ Warning: Could not fast-forward scheduler precisely: {e}. It might affect LR schedule.")

    # --- 9. Initialize lists for tracking losses ---
    train_losses = initial_train_losses[:]
    val_losses = initial_val_losses[:]
    train_component_losses = {k: initial_train_component_losses[k][:] for k in initial_train_component_losses}
    val_component_losses = {k: initial_val_component_losses[k][:] for k in initial_val_component_losses}

    # --- 10. Training Loop ---
    print(f"\n--- Starting/Resuming Training from epoch {start_epoch + 1} to {num_epochs} ---")
    for epoch in range(start_epoch, num_epochs):
        # --- Curriculum Learning ---
        if hasattr(criterion, 'set_epoch'):
            criterion.set_epoch(epoch)
            print(f"📚 Curriculum learning: Set epoch {epoch} for criterion")
        
        # --- Training Phase ---
        model.train()
        epoch_train_losses = []
        epoch_train_components = {k: 0.0 for k in train_component_losses}

        train_pbar = tqdm(train_loader, desc=f"Epoch {epoch+1}/{num_epochs} [Train]")
        for images, targets in train_pbar:
            images = images.float().to(device, non_blocking=True)
            targets = targets.float().to(device, non_blocking=True)

            optimizer.zero_grad(set_to_none=True)
            
            with autocast(device_type=device.type):
                outputs = model(images)
                loss, loss_dict = criterion(outputs, targets)

            if not torch.isnan(loss):
                scaler.scale(loss).backward()
                
                scaler.unscale_(optimizer)
                grad_norm = torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                
                scaler.step(optimizer)
                scaler.update()
                scheduler.step()

                epoch_train_losses.append(loss.item())
                for k in epoch_train_components:
                    if k in loss_dict:
                        epoch_train_components[k] += loss_dict[k].item()
                
                if len(epoch_train_losses) % 10 == 0 or len(epoch_train_losses) == len(train_loader):
                    current_lr = optimizer.param_groups[0]['lr']
                    train_pbar.set_postfix({
                        'Loss': f'{loss.item():.4f}',
                        'LR': f'{current_lr:.2e}',
                        'Grad_Norm': f'{grad_norm:.2f}'
                    })

        # --- Average Training Metrics ---
        avg_train_loss = np.mean(epoch_train_losses) if epoch_train_losses else float('inf')
        for k in epoch_train_components:
            epoch_train_components[k] /= len(train_loader) if len(train_loader) > 0 else 1
            train_component_losses[k].append(epoch_train_components[k])
        train_losses.append(avg_train_loss)

        # --- Validation Phase ---
        model.eval()
        epoch_val_losses = []
        epoch_val_components = {k: 0.0 for k in val_component_losses}

        with torch.no_grad():
            val_pbar = tqdm(val_loader, desc=f"Epoch {epoch+1}/{num_epochs} [Val]", leave=False)
            for images, targets in val_pbar:
                images = images.float().to(device, non_blocking=True)
                targets = targets.float().to(device, non_blocking=True)

                with autocast(device_type=device.type):
                    outputs = model(images)
                    loss, loss_dict = criterion(outputs, targets)

                if not torch.isnan(loss):
                    epoch_val_losses.append(loss.item())
                    for k in epoch_val_components:
                        if k in loss_dict:
                            epoch_val_components[k] += loss_dict[k].item()
                
                if len(epoch_val_losses) % 10 == 0 or len(epoch_val_losses) == len(val_loader):
                     val_pbar.set_postfix({'Loss': f'{loss.item():.4f}'})

        # --- Average Validation Metrics ---
        avg_val_loss = np.mean(epoch_val_losses) if epoch_val_losses else float('inf')
        for k in epoch_val_components:
            epoch_val_components[k] /= len(val_loader) if len(val_loader) > 0 else 1
            val_component_losses[k].append(epoch_val_components[k])
        val_losses.append(avg_val_loss)

        # --- Epoch Summary ---
        current_lr = optimizer.param_groups[0]['lr']
        print(f"Epoch {epoch+1} Summary:")
        print(f"  Train Loss: {avg_train_loss:.6f} | Val Loss: {avg_val_loss:.6f} | LR: {current_lr:.2e}")
        train_comp_str = " | ".join([f"{k}: {v[-1]:.4f}" for k, v in train_component_losses.items() if v])
        val_comp_str = " | ".join([f"{k}: {v[-1]:.4f}" for k, v in val_component_losses.items() if v])
        print(f"  Train Components: {train_comp_str}")
        print(f"  Val Components:   {val_comp_str}")

        # --- 11. Early Stopping & Best Model Saving ---
        if avg_val_loss < best_loss:
            best_loss = avg_val_loss
            epochs_since_improvement = 0
            
            best_checkpoint = {
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'scheduler_state_dict': scheduler.state_dict(),
                'epoch': epoch,
                'loss': best_loss,
                'epochs_since_improvement': epochs_since_improvement,
                'train_losses': train_losses,
                'val_losses': val_losses,
            }
            torch.save(best_checkpoint, best_model_path)
            print(f"  ✅ New best model saved (Val Loss: {best_loss:.6f})")
        else:
            epochs_since_improvement += 1
            print(f"  ⏱️  Epochs since last improvement: {epochs_since_improvement}")

        # Check for early stopping
        if epochs_since_improvement >= early_stopping_patience:
            print(f"\n⚠️ Early stopping triggered after {epoch + 1} epochs "
                  f"(patience {early_stopping_patience} reached).")
            break

        # --- 12. Periodic Visualization ---
        if epoch % max(1, num_epochs // 20) == 0 or epoch == num_epochs - 1 or epoch == start_epoch:
            try:
                images, targets = next(iter(val_loader))
                images = images.float().to(device)
                
                with torch.no_grad():
                    with autocast(device_type=device.type):
                        outputs = model(images)
                    
                    semantic = torch.sigmoid(outputs['sem_out'])[0, 0].cpu().numpy()
                    
                    sdt_probs = torch.nn.functional.softmax(outputs['sdt_out'], dim=1)
                    num_bins = sdt_probs.shape[1]
                    bin_centers = (torch.arange(0, num_bins, device=sdt_probs.device, dtype=torch.float32) + 0.5) / num_bins
                    bin_centers = bin_centers.view(1, -1, 1, 1)
                    sdt = torch.sum(sdt_probs * bin_centers, dim=1)[0].cpu().numpy()
                    
                    skeleton = torch.sigmoid(outputs['skeleton_out'])[0, 0].cpu().numpy()
                    centroid_map = torch.sigmoid(outputs['hm_out'])[0, 0].cpu().numpy()

                fig, axes = plt.subplots(1, 5, figsize=(25, 5))
                axes[0].imshow(images[0, 0].cpu(), cmap='gray')
                axes[0].set_title('Input Image')
                axes[0].axis('off')

                axes[1].imshow(semantic, cmap='viridis')
                axes[1].set_title('Semantic Mask')
                axes[1].axis('off')

                im2 = axes[2].imshow(sdt, cmap='magma')
                axes[2].set_title('SDT (Reconstructed)')
                plt.colorbar(im2, ax=axes[2])

                axes[3].imshow(skeleton, cmap='bone')
                axes[3].set_title('Skeleton')
                axes[3].axis('off')

                axes[4].imshow(centroid_map, cmap='hot')
                axes[4].set_title('Centroid Heatmap')
                axes[4].axis('off')

                plt.suptitle(f'Training Progress - Epoch {epoch+1}')
                plt.tight_layout()
                progress_plot_path = os.path.join(model_dir, f'progress_epoch_{epoch+1}.png')
                plt.savefig(progress_plot_path, dpi=150, bbox_inches='tight')
                plt.close(fig)
                print(f"  📊 Progress plot saved to {progress_plot_path}")
            except Exception as e:
                print(f"  ⚠️ Visualization error for epoch {epoch+1}: {e}")

    # --- 13. Finalization ---
    print("\n--- Training Completed ---")
    
    final_checkpoint = {
        'model_state_dict': model.state_dict(),
        'optimizer_state_dict': optimizer.state_dict(),
        'scheduler_state_dict': scheduler.state_dict(),
        'epoch': len(train_losses) - 1,
        'loss': val_losses[-1] if val_losses else float('inf'),
        'epochs_since_improvement': epochs_since_improvement,
        'train_losses': train_losses,
        'val_losses': val_losses,
    }
    torch.save(final_checkpoint, final_model_path)
    print(f"💾 Final model checkpoint saved to {final_model_path}")

    # --- 14. Plot Final Loss Curves ---
    try:
        plt.figure(figsize=(12, 5))
        
        plt.subplot(1, 2, 1)
        epochs_plotted = range(1, len(train_losses) + 1)
        plt.plot(epochs_plotted, train_losses, label='Train Loss')
        plt.plot(epochs_plotted, val_losses, label='Val Loss')
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.title('Overall Training & Validation Loss')
        plt.legend()
        plt.grid(True)

        plt.subplot(1, 2, 2)
        for k, v in train_component_losses.items():
            if v:
                plt.plot(epochs_plotted, v, label=f'Train {k}')
        for k, v in val_component_losses.items():
             if v:
                 val_comp_aligned = v[:len(epochs_plotted)] 
                 plt.plot(epochs_plotted, val_comp_aligned, label=f'Val {k}', linestyle='--')
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.title('Component Losses')
        plt.legend()
        plt.grid(True)

        plt.tight_layout()
        loss_curve_path = os.path.join(model_dir, 'final_loss_curves.png')
        plt.savefig(loss_curve_path)
        plt.close()
        print(f"📊 Final loss curves plot saved to {loss_curve_path}")
    except Exception as e:
        print(f"⚠️ Error plotting final loss curves: {e}")

    print(f"\n🎉 Training finished. Best validation loss: {best_loss:.6f}")
    return model, train_losses, val_losses

print("✅ State-of-the-Art training function 'train_skeleton_aware_model_sota' with curriculum learning is ready for use.")
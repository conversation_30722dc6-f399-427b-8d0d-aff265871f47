import os
import numpy as np
import torch
from torch.utils.data import Dataset, DataLoader, ConcatDataset
from data_utils import SpotDataset, create_transforms
from synthetic_data_generator import AdvancedSyntheticSpotGenerator
from skimage import io, measure
import cv2

class CombinedSpotDataset(Dataset):
    """
    Dataset that combines real data and synthetic data for spot detection
    """
    def __init__(self, 
                 real_data_dir,
                 image_size=(256, 256),
                 mode='train',
                 synthetic_ratio=0.5,
                 synthetic_size=500,
                 augmentation_level='strong'):
        """
        Initialize the combined dataset
        
        Args:
            real_data_dir: Directory containing real images and masks
            image_size: Size to resize images to
            mode: 'train' or 'val'
            synthetic_ratio: Ratio of synthetic to real data (0-1)
            synthetic_size: Number of synthetic samples to generate
            augmentation_level: Level of augmentation ('none', 'light', 'medium', 'strong')
        """
        self.real_data_dir = real_data_dir
        self.image_size = image_size
        self.mode = mode
        self.synthetic_ratio = synthetic_ratio
        self.synthetic_size = synthetic_size
        
        # Create real data dataset
        self.real_dataset = SpotDataset(
            data_dir=real_data_dir,
            image_size=image_size,
            mode=mode,
            synthetic=False,
            augmentation_level=augmentation_level
        )
        
        # Create synthetic data dataset
        self.synthetic_dataset = SpotDataset(
            data_dir=None,
            image_size=image_size,
            mode=mode,
            synthetic=True,
            synthetic_size=synthetic_size,
            augmentation_level=augmentation_level
        )
        
        # Combine datasets
        self.combined_dataset = ConcatDataset([self.real_dataset, self.synthetic_dataset])
        
        # Store the number of real and synthetic samples
        self.num_real = len(self.real_dataset)
        self.num_synthetic = len(self.synthetic_dataset)
        
        print(f"Combined dataset created with {self.num_real} real samples and {self.num_synthetic} synthetic samples")
    
    def __len__(self):
        """Get dataset size"""
        return len(self.combined_dataset)
    
    def __getitem__(self, idx):
        """Get a sample"""
        sample = self.combined_dataset[idx]
        
        # Add true spot count for synthetic data (for parameter optimization)
        if idx >= self.num_real:
            # This is a synthetic sample
            # Extract the true spot count from the mask
            mask = sample['mask'].squeeze().cpu().numpy()
            unique_ids = np.unique(mask)
            # Count non-zero IDs (each ID represents a spot)
            true_count = len(unique_ids[unique_ids > 0])
            sample['true_spot_count'] = true_count
        
        return sample

def create_combined_data_loaders(real_data_dir,
                                batch_size=8,
                                image_size=(256, 256),
                                train_val_split=0.8,
                                synthetic_ratio=0.5,
                                synthetic_size=500,
                                augmentation_level='strong',
                                num_workers=4):
    """
    Create train and validation data loaders with combined real and synthetic data
    
    Args:
        real_data_dir: Directory containing real images and masks
        batch_size: Batch size
        image_size: Size to resize images to
        train_val_split: Fraction of data to use for training
        synthetic_ratio: Ratio of synthetic to real data (0-1)
        synthetic_size: Number of synthetic samples to generate
        augmentation_level: Level of augmentation ('none', 'light', 'medium', 'strong')
        num_workers: Number of workers for data loading
        
    Returns:
        train_loader, val_loader
    """
    # Load all real data
    full_dataset = SpotDataset(
        data_dir=real_data_dir,
        image_size=image_size,
        mode='train',
        synthetic=False,
        augmentation_level='none'
    )
    
    # Calculate split sizes
    train_size = int(train_val_split * len(full_dataset))
    val_size = len(full_dataset) - train_size
    
    # Split dataset
    train_indices, val_indices = torch.utils.data.random_split(
        range(len(full_dataset)), 
        [train_size, val_size],
        generator=torch.Generator().manual_seed(42)
    )
    
    # Create training dataset with real and synthetic data
    train_real_dataset = SpotDataset(
        data_dir=None,
        image_size=image_size,
        mode='train',
        synthetic=False,
        augmentation_level=augmentation_level
    )
    train_real_dataset.images = [full_dataset.images[i] for i in train_indices]
    train_real_dataset.masks = [full_dataset.masks[i] for i in train_indices]
    
    # Calculate synthetic size based on real data size and synthetic ratio
    adjusted_synthetic_size = int(train_size * synthetic_ratio / (1 - synthetic_ratio))
    
    # Create synthetic dataset for training
    train_synthetic_dataset = SpotDataset(
        data_dir=None,
        image_size=image_size,
        mode='train',
        synthetic=True,
        synthetic_size=adjusted_synthetic_size,
        augmentation_level=augmentation_level
    )
    
    # Create validation dataset (real data only)
    val_dataset = SpotDataset(
        data_dir=None,
        image_size=image_size,
        mode='val',
        synthetic=False,
        augmentation_level='none'
    )
    val_dataset.images = [full_dataset.images[i] for i in val_indices]
    val_dataset.masks = [full_dataset.masks[i] for i in val_indices]
    
    # Create combined training dataset
    train_dataset = CombinedDatasetWithTrueCount(
        [train_real_dataset, train_synthetic_dataset]
    )
    
    # Create data loaders
    train_loader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=num_workers,
        pin_memory=True
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=True
    )
    
    return train_loader, val_loader

class CombinedDatasetWithTrueCount(Dataset):
    """Dataset that combines multiple datasets and adds true spot count for synthetic data"""
    
    def __init__(self, datasets):
        """
        Initialize the combined dataset
        
        Args:
            datasets: List of datasets to combine
        """
        self.datasets = datasets
        self.lengths = [len(d) for d in datasets]
        self.cumulative_lengths = np.cumsum(self.lengths)
        
        # Track which samples are synthetic (assuming the second dataset is synthetic)
        self.is_synthetic = np.zeros(sum(self.lengths), dtype=bool)
        if len(datasets) > 1:
            self.is_synthetic[self.lengths[0]:] = True
    
    def __len__(self):
        """Get dataset size"""
        return sum(self.lengths)
    
    def __getitem__(self, idx):
        """Get a sample with true spot count for synthetic data"""
        # Find which dataset this index belongs to
        dataset_idx = np.searchsorted(self.cumulative_lengths, idx, side='right')
        if dataset_idx > 0:
            idx = idx - self.cumulative_lengths[dataset_idx - 1]
        
        # Get sample from the appropriate dataset
        sample = self.datasets[dataset_idx][idx]
        
        # Add true spot count for synthetic data
        if self.is_synthetic[idx]:
            # Extract the true spot count from the mask
            mask = sample['mask'].squeeze().cpu().numpy()
            unique_ids = np.unique(mask)
            # Count non-zero IDs (each ID represents a spot)
            true_count = len(unique_ids[unique_ids > 0])
            sample['true_spot_count'] = true_count
        
        return sample

def optimize_peak_detection_on_real_data(peak_optimizer, real_data_dir, image_size=(256, 256)):
    """
    Optimize peak detection parameters specifically on real data
    
    Args:
        peak_optimizer: PeakDetectionOptimizer instance
        real_data_dir: Directory containing real images and masks
        image_size: Size to resize images to
        
    Returns:
        Optimized parameters
    """
    # Load real data
    real_dataset = SpotDataset(
        data_dir=real_data_dir,
        image_size=image_size,
        mode='val',  # No augmentation
        synthetic=False,
        augmentation_level='none'
    )
    
    # Create data loader
    real_loader = DataLoader(
        real_dataset,
        batch_size=4,
        shuffle=False,
        num_workers=2
    )
    
    # Extract ground truth and predictions
    all_images = []
    all_masks = []
    all_true_counts = []
    
    for batch in real_loader:
        images = batch['image']
        masks = batch['mask']
        
        # Extract true spot counts from masks
        for i in range(masks.shape[0]):
            mask = masks[i, 0].cpu().numpy()
            # Label connected components
            labeled_mask = measure.label(mask > 0.5)
            # Count regions
            regions = measure.regionprops(labeled_mask)
            true_count = len(regions)
            all_true_counts.append(true_count)
        
        all_images.append(images)
        all_masks.append(masks)
    
    # Concatenate all data
    all_images_tensor = torch.cat(all_images, dim=0)
    all_masks_tensor = torch.cat(all_masks, dim=0)
    
    # Optimize parameters using true spot counts
    opt_result = peak_optimizer.optimize_parameters(
        all_images_tensor,  # Using images as "predictions" since we don't have a model yet
        all_masks_tensor,
        true_spot_counts=all_true_counts
    )
    
    return opt_result
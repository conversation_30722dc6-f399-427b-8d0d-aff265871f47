import torch
import numpy as np
import matplotlib.pyplot as plt
from skimage import io, measure, morphology
import os
import sys
from fix_spot_detection import detect_individual_spots, visualize_spot_detection

# Import your model and predictor
from OptimizedSpotDetection_model import OptimizedSpotDetectionModel
from predict import SpotDetectionPredictor

def load_model(model_path, device):
    """Load the trained model from disk"""
    # Create model instance
    model = OptimizedSpotDetectionModel(
        in_channels=1,
        num_experts=3,
        base_filters=64,
        dropout_rate=0.2
    )
    
    # Load weights
    model.load_state_dict(torch.load(model_path, map_location=device))
    model = model.to(device)
    model.eval()
    
    return model

def fix_spot_detection(image_path, model_path, device='cuda' if torch.cuda.is_available() else 'cpu'):
    """
    Fix spot detection by using a lower threshold and watershed segmentation
    
    Args:
        image_path: Path to the image
        model_path: Path to the trained model
        device: Device to use for inference
    """
    # Load model
    model = load_model(model_path, device)
    
    # Load image
    image = io.imread(image_path)
    if len(image.shape) == 3 and image.shape[2] > 1:
        image = image[:, :, 0]  # Take first channel if RGB
    
    # Normalize image
    image = image.astype(np.float32) / 255.0
    
    # Convert to tensor
    image_tensor = torch.from_numpy(image).unsqueeze(0).unsqueeze(0).to(device)
    
    # Get prediction
    with torch.no_grad():
        outputs = model(image_tensor)
        
        # Get main output
        if isinstance(outputs, dict):
            pred = outputs.get('output', outputs.get('combined_output', None))
        else:
            pred = outputs
        
        # Apply sigmoid
        pred_sigmoid = torch.sigmoid(pred)
        
        # Convert to numpy
        heatmap = pred_sigmoid.squeeze().cpu().numpy()
    
    # Try different thresholds
    thresholds = [0.1, 0.15, 0.2, 0.25, 0.3, 0.4, 0.5]
    min_distances = [3, 5, 7, 10]
    
    # Create figure for threshold comparison
    fig, axes = plt.subplots(len(min_distances), len(thresholds), figsize=(20, 16))
    
    # Plot original image and heatmap
    plt.figure(figsize=(10, 5))
    plt.subplot(121)
    plt.imshow(image, cmap='gray')
    plt.title('Original Image')
    plt.axis('off')
    
    plt.subplot(122)
    plt.imshow(heatmap, cmap='hot')
    plt.title('Prediction Heatmap')
    plt.axis('off')
    plt.tight_layout()
    plt.show()
    
    # Test different combinations
    for i, min_distance in enumerate(min_distances):
        for j, threshold in enumerate(thresholds):
            # Detect spots with current parameters
            labeled_mask, num_spots, spot_props = detect_individual_spots(
                heatmap, threshold=threshold, min_spot_size=3, min_distance=min_distance
            )
            
            # Create RGB overlay
            rgb_mask = np.zeros((*image.shape, 3))
            rgb_mask[..., 0] = image  # Red channel
            rgb_mask[..., 1] = image  # Green channel
            rgb_mask[..., 2] = image  # Blue channel
            
            # Overlay spots
            for prop in spot_props:
                y, x = prop['centroid']
                r = int(np.sqrt(prop['area'] / np.pi))
                
                # Draw circle
                from skimage.draw import circle_perimeter
                rr, cc = circle_perimeter(int(y), int(x), r, shape=image.shape)
                rgb_mask[rr, cc, 0] = 1.0  # Red
                rgb_mask[rr, cc, 1] = 0.0  # Green
                rgb_mask[rr, cc, 2] = 0.0  # Blue
            
            # Plot result
            axes[i, j].imshow(rgb_mask)
            axes[i, j].set_title(f'T={threshold}, D={min_distance}, N={num_spots}')
            axes[i, j].axis('off')
    
    plt.tight_layout()
    plt.show()
    
    # Show best result with detailed visualization
    best_threshold = 0.2  # You can adjust this based on the grid results
    best_min_distance = 5  # You can adjust this based on the grid results
    
    visualize_spot_detection(
        image, heatmap, threshold=best_threshold, 
        min_spot_size=3, min_distance=best_min_distance
    )
    
    # Return the best parameters
    return best_threshold, best_min_distance

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Fix spot detection")
    parser.add_argument("--image", type=str, required=True, help="Path to input image")
    parser.add_argument("--model", type=str, required=True, help="Path to trained model")
    
    args = parser.parse_args()
    
    best_threshold, best_min_distance = fix_spot_detection(args.image, args.model)
    
    print(f"\nRecommended parameters:")
    print(f"threshold = {best_threshold}")
    print(f"min_distance = {best_min_distance}")
    print("\nUpdate your code to use these parameters with the detect_individual_spots function")
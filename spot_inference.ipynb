{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Spot Detection Inference\n", "\n", "This notebook loads a trained model and runs inference on images, showing all intermediate outputs."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import numpy as np\n", "import torch\n", "import matplotlib.pyplot as plt\n", "import tifffile\n", "import pandas as pd\n", "from pathlib import Path\n", "from scipy.ndimage import label, distance_transform_edt\n", "from skimage.feature import peak_local_max\n", "from skimage.segmentation import watershed\n", "from skimage.measure import regionprops\n", "\n", "# Set paths\n", "model_dir = \"/mnt/d/Users/<USER>/Documents/spot detector git amazon 2025/training_dataset/spotmodel_claude_centroid_refined_eroded_light_7142025/model.pth\"\n", "image_dir = \"/mnt/d/Users/<USER>/FISH_spots/2d/refined/images/\"\n", "output_dir = \"/mnt/d/Users/<USER>/Documents/spot detector git amazon 2025/inference_results/\"\n", "\n", "# Create output directory\n", "os.makedirs(output_dir, exist_ok=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load model\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "print(f\"Using device: {device}\")\n", "\n", "# Import model class\n", "from AdaptiveSpotDetector import AdaptiveSpotDetector\n", "\n", "# Initialize model\n", "model = AdaptiveSpotDetector()\n", "\n", "# Load weights\n", "checkpoint = torch.load(model_dir, map_location=device)\n", "if 'model_state_dict' in checkpoint:\n", "    model.load_state_dict(checkpoint['model_state_dict'])\n", "else:\n", "    model.load_state_dict(checkpoint)\n", "\n", "model.to(device)\n", "model.eval()\n", "print(\"Model loaded successfully\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def load_and_normalize_image(image_path):\n", "    \"\"\"Load and normalize image as in the dataloader\"\"\"\n", "    # Load image\n", "    image = tifffile.imread(image_path).astype(np.float32)\n", "    if len(image.shape) == 3:\n", "        image = image[:, :, 0] if image.shape[2] > 1 else image.squeeze()\n", "    \n", "    # Store original for intensity measurements\n", "    original_image = image.copy()\n", "    \n", "    # Normalize as in dataloader\n", "    image_norm = image / (image.max() + 1e-8)\n", "    \n", "    return image_norm, original_image"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def process_single_image(image_path, threshold=0.5, min_size=4, nms_threshold=0.3):\n", "    \"\"\"Process a single image and return all outputs\"\"\"\n", "    # Load and normalize image\n", "    image_norm, original_image = load_and_normalize_image(image_path)\n", "    \n", "    # Convert to tensor\n", "    image_tensor = torch.from_numpy(image_norm).unsqueeze(0).unsqueeze(0).to(device)\n", "    \n", "    # Run inference\n", "    with torch.no_grad():\n", "        outputs = model(image_tensor)\n", "    \n", "    # Extract all outputs\n", "    all_outputs = {}\n", "    for key, value in outputs.items():\n", "        if isinstance(value, torch.Tensor):\n", "            if key in ['semantic', 'boundary', 'heatmap']:\n", "                # Apply sigmoid for these outputs\n", "                all_outputs[key] = torch.sigmoid(value).cpu().numpy()[0, 0]\n", "            else:\n", "                all_outputs[key] = value.cpu().numpy()[0]\n", "                if all_outputs[key].ndim == 3:  # [C, H, W]\n", "                    all_outputs[key] = all_outputs[key]  # Keep all channels for flow\n", "    \n", "    # Create binary masks\n", "    binary_outputs = {}\n", "    for key in ['semantic', 'boundary', 'heatmap']:\n", "        if key in all_outputs:\n", "            binary_outputs[key] = (all_outputs[key] > threshold).astype(np.uint8)\n", "    \n", "    # Apply post-processing to semantic mask\n", "    if 'semantic' in binary_outputs:\n", "        binary = binary_outputs['semantic']\n", "        \n", "        # Remove small objects\n", "        if min_size > 1:\n", "            labeled, num = label(binary)\n", "            for i in range(1, num + 1):\n", "                if np.sum(labeled == i) < min_size:\n", "                    binary[labeled == i] = 0\n", "        \n", "        # Find peaks for seeds\n", "        distance = distance_transform_edt(binary)\n", "        peaks = peak_local_max(distance, min_distance=3, threshold_abs=1)\n", "        \n", "        # Create seeds\n", "        seeds = np.zeros_like(binary, dtype=np.int32)\n", "        for i, (y, x) in enumerate(peaks, 1):\n", "            seeds[y, x] = i\n", "        \n", "        # Apply watershed\n", "        instance_labels = watershed(-distance, seeds, mask=binary)\n", "        \n", "        # Extract spot properties\n", "        spots = []\n", "        centroids_map = np.zeros_like(binary, dtype=np.uint8)\n", "        \n", "        for spot_id in range(1, len(peaks) + 1):\n", "            mask = (instance_labels == spot_id)\n", "            if np.sum(mask) < min_size:\n", "                instance_labels[mask] = 0\n", "                continue\n", "                \n", "            props = regionprops(mask.astype(np.uint8))[0]\n", "            cy, cx = props.centroid\n", "            area = props.area\n", "            intensity = np.mean(original_image[mask])\n", "            score = np.mean(all_outputs['heatmap'][mask])\n", "            \n", "            spots.append({\n", "                'x': float(cx),\n", "                'y': float(cy),\n", "                'score': float(score),\n", "                'size': int(area),\n", "                'intensity': float(intensity)\n", "            })\n", "            \n", "            centroids_map[int(cy), int(cx)] = 255\n", "        \n", "        # Apply NMS if requested\n", "        if nms_threshold > 0 and len(spots) > 1:\n", "            # Extract coordinates and scores\n", "            coords = np.array([[spot['x'], spot['y']] for spot in spots])\n", "            scores = np.array([spot['score'] for spot in spots])\n", "            \n", "            # Sort by score (descending)\n", "            order = np.argsort(scores)[::-1]\n", "            \n", "            keep = []\n", "            while len(order) > 0:\n", "                # Keep the highest scoring detection\n", "                i = order[0]\n", "                keep.append(i)\n", "                \n", "                if len(order) == 1:\n", "                    break\n", "                \n", "                # Calculate distances to remaining detections\n", "                distances = np.sqrt(np.sum((coords[order[1:]] - coords[i])**2, axis=1))\n", "                \n", "                # Remove detections that are too close\n", "                far_enough = distances > nms_threshold * np.sqrt(spots[i]['size'])\n", "                order = order[1:][far_enough]\n", "            \n", "            # Update spots and instance labels\n", "            spots = [spots[i] for i in keep]\n", "            new_labels = np.zeros_like(instance_labels)\n", "            centroids_map = np.zeros_like(binary, dtype=np.uint8)\n", "            \n", "            for new_id, old_id in enumerate(keep, 1):\n", "                new_labels[instance_labels == old_id + 1] = new_id\n", "                cy, cx = int(spots[new_id-1]['y']), int(spots[new_id-1]['x'])\n", "                centroids_map[cy, cx] = 255\n", "                \n", "            instance_labels = new_labels\n", "    else:\n", "        instance_labels = np.zeros_like(image_norm, dtype=np.int32)\n", "        centroids_map = np.zeros_like(image_norm, dtype=np.uint8)\n", "        spots = []\n", "    \n", "    return {\n", "        'image': image_norm,\n", "        'original': original_image,\n", "        'outputs': all_outputs,\n", "        'binary': binary_outputs,\n", "        'instance_labels': instance_labels,\n", "        'centroids': centroids_map,\n", "        'spots': spots\n", "    }"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def visualize_results(results, save_path=None):\n", "    \"\"\"Visualize all results\"\"\"\n", "    # Create figure\n", "    fig = plt.figure(figsize=(20, 15))\n", "    \n", "    # Original image\n", "    plt.subplot(3, 3, 1)\n", "    plt.imshow(results['image'], cmap='gray')\n", "    plt.title('Original Image')\n", "    plt.axis('off')\n", "    \n", "    # Raw outputs\n", "    output_keys = ['semantic', 'boundary', 'heatmap', 'distance']\n", "    for i, key in enumerate(output_keys):\n", "        if key in results['outputs']:\n", "            plt.subplot(3, 3, i + 2)\n", "            plt.imshow(results['outputs'][key], cmap='hot' if key == 'heatmap' else 'viridis')\n", "            plt.title(f'Raw {key}')\n", "            plt.axis('off')\n", "    \n", "    # Flow field\n", "    if 'flow' in results['outputs']:\n", "        plt.subplot(3, 3, 5)\n", "        flow = results['outputs']['flow']\n", "        # Create RGB visualization of flow\n", "        h, w = flow.shape[1:]\n", "        hsv = np.zeros((h, w, 3), dtype=np.uint8)\n", "        mag = np.sqrt(flow[0]**2 + flow[1]**2)\n", "        ang = np.arctan2(flow[0], flow[1]) + np.pi\n", "        hsv[..., 0] = ang * 180 / np.pi / 2\n", "        hsv[..., 1] = 255\n", "        hsv[..., 2] = np.clip(mag * 255 / (mag.max() + 1e-8), 0, 255).astype(np.uint8)\n", "        import cv2\n", "        flow_rgb = cv2.cvtColor(hsv, cv2.COLOR_HSV2RGB)\n", "        plt.imshow(flow_rgb)\n", "        plt.title('Flow Field')\n", "        plt.axis('off')\n", "    \n", "    # Binary masks\n", "    binary_keys = ['semantic', 'boundary', 'heatmap']\n", "    for i, key in enumerate(binary_keys):\n", "        if key in results['binary']:\n", "            plt.subplot(3, 3, i + 6)\n", "            plt.imshow(results['binary'][key], cmap='gray')\n", "            plt.title(f'Binary {key}')\n", "            plt.axis('off')\n", "    \n", "    # Instance labels\n", "    plt.subplot(3, 3, 9)\n", "    plt.imshow(results['instance_labels'], cmap='nipy_spectral')\n", "    plt.title(f'Instance Labels ({len(results[\"spots\"])} spots)')\n", "    plt.axis('off')\n", "    \n", "    plt.tight_layout()\n", "    \n", "    if save_path:\n", "        plt.savefig(save_path, dpi=150)\n", "    \n", "    plt.show()\n", "    \n", "    # Show spots on image\n", "    plt.figure(figsize=(10, 10))\n", "    plt.imshow(results['image'], cmap='gray')\n", "    for spot in results['spots']:\n", "        plt.plot(spot['x'], spot['y'], 'r+')\n", "        plt.text(spot['x'], spot['y'], f\"{spot['score']:.2f}\", color='yellow', fontsize=8)\n", "    plt.title(f'Detected Spots ({len(results[\"spots\"])})')\n", "    plt.axis('off')\n", "    \n", "    if save_path:\n", "        plt.savefig(save_path.replace('.png', '_spots.png'), dpi=150)\n", "    \n", "    plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def save_results(results, output_path):\n", "    \"\"\"Save all results to disk\"\"\"\n", "    # Create output directory\n", "    os.makedirs(output_path, exist_ok=True)\n", "    \n", "    # Save original image\n", "    tifffile.imwrite(f\"{output_path}/original_image.tif\", results['original'])\n", "    \n", "    # Save all raw outputs\n", "    for key, value in results['outputs'].items():\n", "        if key in ['semantic', 'boundary', 'heatmap']:\n", "            tifffile.imwrite(f\"{output_path}/{key}_raw.tif\", (value * 255).astype(np.uint8))\n", "        elif key == 'distance':\n", "            # Normalize distance for visualization\n", "            dist_norm = value / (value.max() + 1e-8)\n", "            tifffile.imwrite(f\"{output_path}/{key}_raw.tif\", (dist_norm * 255).astype(np.uint8))\n", "        elif key == 'flow':\n", "            # Save flow as separate channels\n", "            tifffile.imwrite(f\"{output_path}/{key}_y.tif\", value[0])\n", "            tifffile.imwrite(f\"{output_path}/{key}_x.tif\", value[1])\n", "    \n", "    # Save binary masks\n", "    for key, value in results['binary'].items():\n", "        tifffile.imwrite(f\"{output_path}/{key}_binary.tif\", value * 255)\n", "    \n", "    # Save instance labels and centroids\n", "    tifffile.imwrite(f\"{output_path}/instance_labels.tif\", results['instance_labels'].astype(np.uint16))\n", "    tifffile.imwrite(f\"{output_path}/centroids.tif\", results['centroids'])\n", "    \n", "    # Save spots as CSV\n", "    if results['spots']:\n", "        pd.DataFrame(results['spots']).to_csv(f\"{output_path}/spots.csv\", index=False)\n", "    \n", "    print(f\"All results saved to {output_path}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Process a single image\n", "image_path = os.path.join(image_dir, \"1  Series002  Green--FLUO--FITC_tile_4.tif\")\n", "results = process_single_image(image_path, threshold=0.5, min_size=10, nms_threshold=0.3)\n", "\n", "# Visualize results\n", "visualize_results(results, save_path=os.path.join(output_dir, \"results.png\"))\n", "\n", "# Save results\n", "save_results(results, os.path.join(output_dir, \"single_image\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Process a batch of images\n", "def process_batch(image_dir, output_dir, pattern=\"*.tif\", threshold=0.5, min_size=10, nms_threshold=0.3):\n", "    \"\"\"Process all images matching pattern in image_dir\"\"\"\n", "    from glob import glob\n", "    \n", "    # Find all images\n", "    image_paths = glob(os.path.join(image_dir, pattern))\n", "    print(f\"Found {len(image_paths)} images\")\n", "    \n", "    # Process each image\n", "    for i, image_path in enumerate(image_paths):\n", "        print(f\"Processing image {i+1}/{len(image_paths)}: {os.path.basename(image_path)}\")\n", "        \n", "        # Process image\n", "        results = process_single_image(image_path, threshold, min_size, nms_threshold)\n", "        \n", "        # Save results\n", "        image_name = os.path.splitext(os.path.basename(image_path))[0]\n", "        save_path = os.path.join(output_dir, image_name)\n", "        save_results(results, save_path)\n", "        \n", "        # Save visualization\n", "        visualize_results(results, save_path=os.path.join(save_path, \"visualization.png\"))\n", "        \n", "    print(\"Batch processing complete\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Uncomment to process a batch of images\n", "# process_batch(image_dir, os.path.join(output_dir, \"batch\"), pattern=\"*.tif\", threshold=0.5, min_size=10, nms_threshold=0.3)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 4}
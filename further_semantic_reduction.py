# OPTION 1: Further reduce semantic weight
self.w_sem = 0.2      # Even lower (was 0.5)
self.w_dist = 5.0     # Higher distance weight
self.w_bnd = 4.0      # Higher boundary weight

# OPTION 2: Use higher threshold in visualization to see current progress
sem = (torch.sigmoid(outputs['semantic'])[0, 0].cpu().numpy() > 0.7).astype(np.float32)  # 0.7 instead of 0.5

# OPTION 3: Add erosion to predictions during training visualization
import cv2
sem_raw = torch.sigmoid(outputs['semantic'])[0, 0].cpu().numpy()
kernel = np.ones((2,2), np.uint8)
sem = cv2.erode((sem_raw > 0.5).astype(np.uint8), kernel, iterations=1).astype(np.float32)

# OPTION 4: Increase learning rate for semantic head only
optimizer = torch.optim.AdamW([
    {'params': [p for n, p in model.named_parameters() if 'semantic_head' not in n], 'lr': 1e-4},
    {'params': model.semantic_head.parameters(), 'lr': 3e-4}  # 3x higher for semantic
], weight_decay=1e-5)
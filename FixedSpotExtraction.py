import numpy as np
import matplotlib.pyplot as plt
import os
from scipy.ndimage import maximum_filter

def extract_precise_spots(centroid_map, semantic_mask, flow=None, 
                         min_distance=3, threshold=0.3):
    """Fixed spot extraction that returns list of dicts"""
    # Restrict to semantic regions
    mask = (semantic_mask > 0.5).astype(np.float32)
    response = centroid_map * mask
    
    if response.max() <= threshold:
        return []  # Return empty list
    
    # Fast peak detection using maximum filter
    local_max = maximum_filter(response, size=min_distance) == response
    peaks_mask = local_max & (response > threshold)
    coords = np.column_stack(np.where(peaks_mask))
    
    if len(coords) == 0:
        return []  # Return empty list
    
    spots = []
    for y, x in coords:
        score = float(response[y, x])
        spots.append({'y': float(y), 'x': float(x), 'score': score})
    
    # Optional flow refinement
    if flow is not None and len(spots) > 0:
        spots = refine_with_flow_dict(spots, flow)
    
    return spots

def refine_with_flow_dict(spots, flow, n_iters=3):
    """Flow refinement for dict format spots"""
    if flow is None:
        return spots
        
    H, W = flow.shape[1:] if flow.ndim == 3 else flow.shape
    
    for _ in range(n_iters):
        for spot in spots:
            y, x = spot['y'], spot['x']
            iy, ix = int(round(y)), int(round(x))
            if 0 <= iy < H and 0 <= ix < W:
                if flow.ndim == 3:
                    dy, dx = flow[0, iy, ix], flow[1, iy, ix]
                else:
                    dy, dx = 0, 0  # Fallback
                spot['y'] = np.clip(y + dy * 0.5, 0, H - 1)
                spot['x'] = np.clip(x + dx * 0.5, 0, W - 1)
    
    return spots

def plot_skeleton_aware_progress(image, semantic, centroid, sdt, skeleton, spots, epoch, save_dir):
    """Fixed visualization for skeleton-aware training progress"""
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    
    # Input image
    axes[0,0].imshow(image, cmap='gray')
    axes[0,0].set_title('Input Image')
    axes[0,0].axis('off')
    
    # Semantic prediction
    axes[0,1].imshow(semantic, cmap='viridis')
    axes[0,1].set_title('Semantic Prediction')
    axes[0,1].axis('off')
    
    # SDT
    im = axes[0,2].imshow(sdt, cmap='magma')
    plt.colorbar(im, ax=axes[0,2])
    axes[0,2].set_title('SDT')
    axes[0,2].axis('off')
    
    # Skeleton
    axes[1,0].imshow(skeleton, cmap='bone')
    axes[1,0].set_title('Skeleton')
    axes[1,0].axis('off')
    
    # Centroid map with spots
    axes[1,1].imshow(centroid, cmap='hot')
    if len(spots) > 0:
        # Handle dict format
        spots_y = [spot['y'] for spot in spots]
        spots_x = [spot['x'] for spot in spots]
        axes[1,1].scatter(spots_x, spots_y, c='cyan', s=30, marker='x')
    axes[1,1].set_title(f'Centroids ({len(spots)} spots)')
    axes[1,1].axis('off')
    
    # Simple instance visualization
    axes[1,2].imshow(semantic, cmap='viridis')
    axes[1,2].set_title('Semantic Mask')
    axes[1,2].axis('off')
    
    plt.suptitle(f'Skeleton-Aware Training - Epoch {epoch}')
    plt.tight_layout()
    
    if save_dir:
        os.makedirs(save_dir, exist_ok=True)
        plt.savefig(os.path.join(save_dir, f'progress_epoch_{epoch:03d}.png'), dpi=150)
        plt.close()
    else:
        plt.show()
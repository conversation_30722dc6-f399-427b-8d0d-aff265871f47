{"data": {"data_dir": "./data", "image_size": 256, "batch_size": 8, "use_synthetic": true, "synthetic_size": 500, "augmentation_level": "strong", "train_val_split": 0.8, "num_workers": 4, "is_3d": false, "confidence_threshold": 0.9, "ignore_threshold": 0.3}, "model": {"in_channels": 1, "num_experts": 3, "base_filters": 64, "dropout_rate": 0.2}, "loss": {"bce_weight": 1.0, "dice_weight": 1.0, "focal_weight": 0.5, "focal_gamma": 2.0, "size_adaptive": true, "density_aware": true, "confidence_weighted": true}, "train": {"learning_rate": 0.001, "num_epochs": 30, "early_stopping_patience": 10, "save_best_model": true, "model_save_path": "best_spot_detection_model.pth", "gradient_clipping": 1.0, "use_mixed_precision": true}, "pred": {"threshold": 0.5, "min_spot_size": 3, "use_test_time_augmentation": false, "tta_flips": true, "tta_rotations": true}, "ssl": {"enabled": true, "num_iterations": 3, "confidence_threshold": 0.9, "epochs_per_iteration": 10}, "synthetic": {"min_spots": 5, "max_spots": 50, "min_radius": 2, "max_radius": 10, "allow_overlapping": true, "shape_variation": 0.3, "add_gradients": true, "realistic_noise": true}}
ppimport torch
import torch.nn as nn
import numpy as np
import cv2
from skimage import io, measure, morphology
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple, Optional, Union, Any
from scipy.ndimage import distance_transform_edt as ndi_distance_transform_edt
from skimage.feature import peak_local_max
from skimage.segmentation import watershed
from skimage.measure import regionprops
import albumentations as A
from albumentations.pytorch import ToTensorV2

class SpotDetectionPredictor:
    """
    Predictor for spot detection model
    
    This class handles prediction and visualization of spot detection results.
    """
    
    def __init__(self, 
                 model: nn.Module,
                 device: torch.device,
                 threshold: float = 0.5,
                 min_spot_size: int = 1,
                 min_distance: int = 5):
        """
        Initialize the predictor
        
        Args:
            model: Trained model
            device: Device to use for prediction
            threshold: Threshold for binary classification
            min_spot_size: Minimum spot size in pixels
            min_distance: Minimum distance between spots for watershed
        """
        self.model = model
        self.device = device
        self.threshold = threshold
        self.min_spot_size = min_spot_size
        self.min_distance = min_distance
        
        # Set model to evaluation mode
        self.model.eval()
    
    def predict(self, 
               image: Union[str, np.ndarray, torch.Tensor],
               return_heatmap: bool = False) -> Dict[str, Any]:
        """
        Predict spots in an image
        
        Args:
            image: Image to predict (path, numpy array, or tensor)
            return_heatmap: Whether to return the raw heatmap
            
        Returns:
            Dict with prediction results
        """
        # Load image if it's a path
        if isinstance(image, str):
            image_np = io.imread(image)
            if len(image_np.shape) == 3 and image_np.shape[2] > 1:
                image_np = cv2.cvtColor(image_np, cv2.COLOR_RGB2GRAY)
            image_np = image_np.astype(np.float32) / 255.0
        elif isinstance(image, np.ndarray):
            image_np = image.copy()
            if len(image_np.shape) == 3 and image_np.shape[2] > 1:
                image_np = cv2.cvtColor(image_np, cv2.COLOR_RGB2GRAY)
            if image_np.max() > 1.0:
                image_np = image_np.astype(np.float32) / 255.0
        else:
            # Convert tensor to numpy
            image_np = image.squeeze().cpu().numpy()
            if len(image_np.shape) == 3 and image_np.shape[0] == 1:
                image_np = image_np[0]
        
        # Ensure image has correct dimensions
        if len(image_np.shape) == 2:
            image_tensor = torch.from_numpy(image_np).unsqueeze(0).unsqueeze(0)
        else:
            image_tensor = torch.from_numpy(image_np).unsqueeze(0)
        
        # Normalize if needed
        if image_tensor.max() > 1.0:
            image_tensor = image_tensor / 255.0
        
        # Move to device
        image_tensor = image_tensor.to(self.device)
        
        # Predict
        with torch.no_grad():
            outputs = self.model(image_tensor)
            
            # Get main output
            if isinstance(outputs, dict):
                pred = outputs.get('output', outputs.get('combined_output', None))
                expert_outputs = outputs.get('expert_outputs', None)
                gating_weights = outputs.get('gating_weights', None)
            else:
                pred = outputs
                expert_outputs = None
                gating_weights = None
            
            # Apply sigmoid
            pred_sigmoid = torch.sigmoid(pred)
            
            # Convert to numpy
            pred_np = pred_sigmoid.squeeze().cpu().numpy()
        
        # Create binary mask
        binary_mask = (pred_np > self.threshold).astype(np.uint8)
        
        # Remove small spots
        if self.min_spot_size > 0:
            binary_mask = morphology.remove_small_objects(
                binary_mask.astype(bool), min_size=self.min_spot_size
            ).astype(np.uint8)
        
        # Find connected components
        labeled_mask, num_spots = self._find_connected_components(binary_mask)
        
        # Extract spot properties
        spot_props = self._extract_spot_properties(labeled_mask, pred_np)
        
        # Prepare result
        result = {
            'binary_mask': binary_mask,
            'labeled_mask': labeled_mask,
            'num_spots': num_spots,
            'spot_props': spot_props
        }
        
        # Add raw heatmap if requested
        if return_heatmap:
            result['heatmap'] = pred_np
        
        # Add expert outputs if available
        if expert_outputs is not None:
            expert_heatmaps = [torch.sigmoid(out).squeeze().cpu().numpy() 
                              for out in expert_outputs]
            result['expert_heatmaps'] = expert_heatmaps
            result['gating_weights'] = gating_weights.squeeze().cpu().numpy()
        
        return result
    
    def _find_connected_components(self, binary_mask: np.ndarray) -> Tuple[np.ndarray, int]:
        """
        Find connected components in binary mask
        
        Args:
            binary_mask: Binary mask
            
        Returns:
            Labeled mask, number of components
        """
        # Find connected components
        labeled_mask = measure.label(binary_mask)
        num_components = labeled_mask.max()
        
        return labeled_mask, num_components
    
    def _extract_spot_properties(self, 
                               labeled_mask: np.ndarray, 
                               heatmap: np.ndarray) -> List[Dict]:
        """
        Extract properties of detected spots
        
        Args:
            labeled_mask: Labeled mask
            heatmap: Prediction heatmap
            
        Returns:
            List of spot properties
        """
        # Get region properties
        props = measure.regionprops(labeled_mask, intensity_image=heatmap)
        
        # Extract properties
        spot_props = []
        for prop in props:
            spot_props.append({
                'id': prop.label,
                'centroid': prop.centroid,
                'area': prop.area,
                'mean_intensity': prop.mean_intensity,
                'max_intensity': prop.max_intensity,
                'bbox': prop.bbox
            })
        
        return spot_props
    
    def get_instance_segmentation(self, pred_heatmap: torch.Tensor) -> Tuple[np.ndarray, List[Dict]]:
        """Get instance segmentation with consistent orientation"""
        # Convert prediction to numpy with proper orientation
        pred_np = pred_heatmap.squeeze().cpu().numpy()
        
        # Create binary mask
        binary = (pred_np > self.threshold).astype(np.uint8)
        
        # Remove small objects
        if self.min_spot_size > 0:
            binary = morphology.remove_small_objects(
                binary.astype(bool), 
                min_size=self.min_spot_size
            ).astype(np.uint8)
        
        # Get distance transform for watershed
        dist_transform = ndi_distance_transform_edt(binary)
        
        # Find local maxima as markers for watershed
        coords = peak_local_max(
            dist_transform,
            min_distance=self.min_distance,
            labels=binary
        )
        
        # Create markers for watershed
        markers = np.zeros_like(binary)
        markers[tuple(coords.T)] = np.arange(1, len(coords) + 1)
        
        # Apply watershed with consistent orientation
        labels = watershed(
            -dist_transform,  # Invert for watershed
            markers,
            mask=binary
        )
        
        # Get spot properties maintaining orientation
        props = []
        for region in regionprops(labels):
            # Store coordinates in (y, x) order for consistency
            centroid_y, centroid_x = region.centroid
            bbox_min_y, bbox_min_x, bbox_max_y, bbox_max_x = region.bbox
            
            props.append({
                'id': region.label,
                'centroid': (centroid_y, centroid_x),  # Keep (y, x) order
                'bbox': (bbox_min_y, bbox_min_x, bbox_max_y, bbox_max_x),
                'area': region.area,
                'mean_intensity': region.mean_intensity
            })
        
        return labels, props
    
    def visualize(self, 
                 image: Union[str, np.ndarray, torch.Tensor],
                 result: Optional[Dict] = None,
                 show_spots: bool = True,
                 show_heatmap: bool = True,
                 show_experts: bool = False,
                 figsize: Tuple[int, int] = (15, 10)) -> None:
        """Visualize prediction results with consistent orientation"""
        # Load and normalize image
        if isinstance(image, str):
            image_np = io.imread(image)
            if len(image_np.shape) == 3 and image_np.shape[2] > 1:
                image_np = cv2.cvtColor(image_np, cv2.COLOR_RGB2GRAY)
            image_np = image_np.astype(np.float32) / 255.0
        elif isinstance(image, np.ndarray):
            image_np = image.copy()
            if len(image_np.shape) == 3 and image_np.shape[2] > 1:
                image_np = cv2.cvtColor(image_np, cv2.COLOR_RGB2GRAY)
            if image_np.max() > 1.0:
                image_np = image_np.astype(np.float32) / 255.0
        else:
            # Convert tensor to numpy
            image_np = image.squeeze().cpu().numpy()
            if len(image_np.shape) == 3 and image_np.shape[0] == 1:
                image_np = image_np[0]

        # Get prediction if not provided
        if result is None:
            result = self.predict(image_np, return_heatmap=True)

        # Create figure
        num_plots = 1  # Original image
        if show_spots:
            num_plots += 1
        if show_heatmap:
            num_plots += 1
        if show_experts and 'expert_heatmaps' in result:
            num_plots += len(result['expert_heatmaps'])

        fig, axes = plt.subplots(1, num_plots, figsize=figsize)
        if num_plots == 1:
            axes = [axes]

        # Plot original image with consistent orientation
        axes[0].imshow(image_np, cmap='gray')
        axes[0].set_title('Original Image')
        axes[0].axis('off')

        plot_idx = 1

        # Plot spots
        if show_spots and 'spot_props' in result:
            # Create RGB image for visualization
            rgb_mask = np.zeros((*image_np.shape, 3))
            rgb_mask[..., 0] = image_np  # Red channel
            rgb_mask[..., 1] = image_np  # Green channel
            rgb_mask[..., 2] = image_np  # Blue channel

            # Overlay spots
            for prop in result['spot_props']:
                y, x = prop['centroid']
                r = int(np.sqrt(prop['area'] / np.pi))

                # Draw circle
                cv2.circle(
                    rgb_mask,
                    (int(x), int(y)),
                    r,
                    (1, 0, 0),  # Red
                    1
                )

                # Add ID
                cv2.putText(
                    rgb_mask,
                    str(prop['id']),
                    (int(x), int(y)),
                    cv2.FONT_HERSHEY_SIMPLEX,
                    0.3,
                    (0, 1, 0),  # Green
                    1
                )

            axes[plot_idx].imshow(rgb_mask)
            axes[plot_idx].set_title(f'Detected Spots: {result["num_spots"]}')
            axes[plot_idx].axis('off')
            plot_idx += 1

        # Plot heatmap with consistent orientation - FIXED: removed the vertical flip
        if show_heatmap and 'heatmap' in result:
            # Use the heatmap directly without flipping
            heatmap = result['heatmap']
            axes[plot_idx].imshow(heatmap, cmap='hot')
            axes[plot_idx].set_title('Prediction Heatmap')
            axes[plot_idx].axis('off')
            plot_idx += 1

        # Plot expert outputs with consistent orientation - FIXED: removed the vertical flip
        if show_experts and 'expert_heatmaps' in result:
            for i, expert_heatmap in enumerate(result['expert_heatmaps']):
                weight = result['gating_weights'][i] if 'gating_weights' in result else None
                weight_str = f" (w={weight:.2f})" if weight is not None else ""
                
                # Use the expert heatmap directly without flipping
                axes[plot_idx].imshow(expert_heatmap, cmap='hot')
                axes[plot_idx].set_title(f'Expert {i+1}{weight_str}')
                axes[plot_idx].axis('off')
                plot_idx += 1

        plt.tight_layout()
        plt.show()
    
    def visualize_3d(self,
                   volume: Union[str, np.ndarray, torch.Tensor],
                   result: Optional[Dict] = None,
                   slice_indices: Optional[List[int]] = None,
                   show_spots: bool = True,
                   show_heatmap: bool = True,
                   show_experts: bool = False,
                   figsize: Tuple[int, int] = None) -> None:
        """Visualize 3D prediction results with consistent orientation
        
        Args:
            volume: 3D volume to visualize [D, H, W] or [C, D, H, W]
            result: Prediction result (if None, will be computed)
            slice_indices: List of z-indices to visualize (default: equally spaced slices)
            show_spots: Whether to show detected spots
            show_heatmap: Whether to show heatmap
            show_experts: Whether to show expert outputs
            figsize: Figure size (default: auto-calculated based on slices)
        """
        # Convert volume to numpy with consistent orientation
        if isinstance(volume, str):
            volume_np = io.imread(volume)
            if len(volume_np.shape) == 4 and volume_np.shape[-1] > 1:
                volume_np = cv2.cvtColor(volume_np, cv2.COLOR_RGB2GRAY)
            volume_np = volume_np.astype(np.float32) / 255.0
        elif isinstance(volume, np.ndarray):
            volume_np = volume.copy()
            if len(volume_np.shape) == 4 and volume_np.shape[-1] > 1:
                volume_np = cv2.cvtColor(volume_np, cv2.COLOR_RGB2GRAY)
            if volume_np.max() > 1.0:
                volume_np = volume_np.astype(np.float32) / 255.0
        else:
            volume_np = volume.squeeze().cpu().numpy()
        
        # Handle channel dimension
        if len(volume_np.shape) == 4:
            volume_np = volume_np[0]  # Remove channel dimension
            
        # Get or compute prediction
        if result is None:
            result = self.predict(volume_np, return_heatmap=True)
            
        # Get number of slices and default indices if needed
        depth = volume_np.shape[0]
        if slice_indices is None:
            n_slices = min(4, depth)
            slice_indices = np.linspace(0, depth-1, n_slices).astype(int)
            
        # Calculate figure size if not provided
        if figsize is None:
            n_cols = 4 if show_experts and 'expert_heatmaps' in result else 3
            figsize = (4*n_cols, 4*len(slice_indices))
            
        # Create figure
        fig, axes = plt.subplots(len(slice_indices), n_cols, figsize=figsize)
        if len(slice_indices) == 1:
            axes = axes.reshape(1, -1)
            
        # Plot each slice
        for row, z in enumerate(slice_indices):
            # Plot original slice
            axes[row, 0].imshow(volume_np[z], cmap='gray')
            axes[row, 0].set_title(f'Slice {z}')
            axes[row, 0].axis('off')
            
            # Plot detected spots
            if show_spots:
                # Create RGB overlay
                rgb_mask = np.zeros((*volume_np[z].shape, 3))
                rgb_mask[..., 0] = volume_np[z]
                rgb_mask[..., 1] = volume_np[z]
                rgb_mask[..., 2] = volume_np[z]
                
                # Get spots in this slice
                slice_spots = [p for p in result['spot_props'] 
                             if int(p['centroid'][0]) == z]
                
                # Draw spots
                for prop in slice_spots:
                    y, x = prop['centroid'][1:]  # Skip z coordinate
                    r = int(np.sqrt(prop['area'] / np.pi))
                    cv2.circle(rgb_mask, (int(x), int(y)), r, (1, 0, 0), 1)
                    
                axes[row, 1].imshow(rgb_mask)
                axes[row, 1].set_title(f'Spots: {len(slice_spots)}')
                axes[row, 1].axis('off')
                
            # Plot heatmap - FIXED: removed the vertical flip
            if show_heatmap and 'heatmap' in result:
                axes[row, 2].imshow(result['heatmap'][z], cmap='hot')
                axes[row, 2].set_title(f'Heatmap {z}')
                axes[row, 2].axis('off')
                
            # Plot expert outputs - FIXED: removed the vertical flip
            if show_experts and 'expert_heatmaps' in result:
                expert_viz = np.zeros_like(volume_np[z])
                for i, expert_map in enumerate(result['expert_heatmaps']):
                    weight = result['gating_weights'][i]
                    expert_viz += expert_map[z] * weight
                axes[row, 3].imshow(expert_viz, cmap='hot')
                axes[row, 3].set_title(f'Expert Output {z}')
                axes[row, 3].axis('off')
                
        plt.tight_layout()
        plt.show()
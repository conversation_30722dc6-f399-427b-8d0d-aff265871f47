import torch
import numpy as np
import matplotlib.pyplot as plt
import tifffile
from pathlib import Path
from OptimizedSpotDetection_model import OptimizedSpotDetectionModel

def visualize_model_outputs(
    model_path: str,
    image_path: str,
    output_dir: str,
    threshold: float = 0.5
):
    """
    Visualize all outputs from the model before any post-processing
    """
    # Create output directory
    output_dir = Path(output_dir)
    output_dir.mkdir(exist_ok=True, parents=True)
    
    # Load model
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = OptimizedSpotDetectionModel(in_channels=1, base_filters=32)
    
    checkpoint = torch.load(model_path, map_location=device)
    if 'model_state_dict' in checkpoint:
        model.load_state_dict(checkpoint['model_state_dict'])
    else:
        model.load_state_dict(checkpoint)
    
    model.to(device)
    model.eval()
    
    # Load image
    image = tifffile.imread(image_path).astype(np.float32)
    if len(image.shape) == 3:
        image = image[:, :, 0] if image.shape[2] > 1 else image.squeeze()
    
    # Normalize image
    image_norm = image / (image.max() + 1e-8)
    
    # Convert to tensor
    image_tensor = torch.from_numpy(image_norm).unsqueeze(0).unsqueeze(0).to(device)
    
    # Run inference
    with torch.no_grad():
        outputs = model(image_tensor)
    
    # Save original image
    plt.figure(figsize=(8, 8))
    plt.imshow(image_norm, cmap='gray')
    plt.title('Original Image')
    plt.colorbar()
    plt.savefig(output_dir / 'original_image.png', dpi=150)
    plt.close()
    
    # Save raw heatmap
    heatmap = torch.sigmoid(outputs['heatmap']).cpu().numpy()[0, 0]
    plt.figure(figsize=(8, 8))
    plt.imshow(heatmap, cmap='hot')
    plt.title('Raw Heatmap')
    plt.colorbar()
    plt.savefig(output_dir / 'raw_heatmap.png', dpi=150)
    plt.close()
    
    # Save binary mask
    binary_mask = (heatmap > threshold).astype(np.uint8) * 255
    plt.figure(figsize=(8, 8))
    plt.imshow(binary_mask, cmap='gray')
    plt.title(f'Binary Mask (threshold={threshold})')
    plt.savefig(output_dir / 'binary_mask.png', dpi=150)
    plt.close()
    
    # Save expert outputs if available
    if 'expert_outputs' in outputs:
        expert_outputs = [torch.sigmoid(out).cpu().numpy()[0, 0] for out in outputs['expert_outputs']]
        fig, axes = plt.subplots(1, len(expert_outputs), figsize=(5*len(expert_outputs), 5))
        for i, exp_out in enumerate(expert_outputs):
            axes[i].imshow(exp_out, cmap='hot')
            axes[i].set_title(f'Expert {i+1}')
            axes[i].axis('off')
        plt.tight_layout()
        plt.savefig(output_dir / 'expert_outputs.png', dpi=150)
        plt.close()
    
    # Save expert weights if available
    if 'expert_weights' in outputs:
        expert_weights = outputs['expert_weights'].cpu().numpy()[0]
        plt.figure(figsize=(8, 4))
        plt.bar(range(1, len(expert_weights)+1), expert_weights)
        plt.xlabel('Expert')
        plt.ylabel('Weight')
        plt.title('Expert Weights')
        plt.savefig(output_dir / 'expert_weights.png', dpi=150)
        plt.close()
    
    # Save all outputs as TIFF files
    tifffile.imwrite(output_dir / 'original_image.tif', image)
    tifffile.imwrite(output_dir / 'heatmap.tif', (heatmap * 255).astype(np.uint8))
    tifffile.imwrite(output_dir / 'binary_mask.tif', binary_mask)
    
    print(f"All outputs saved to {output_dir}")
    
    return {
        'image': image,
        'heatmap': heatmap,
        'binary_mask': binary_mask,
        'outputs': outputs
    }

if __name__ == "__main__":
    import argparse
    parser = argparse.ArgumentParser(description='Visualize model outputs')
    parser.add_argument('--model', type=str, required=True, help='Path to model weights')
    parser.add_argument('--image', type=str, required=True, help='Path to input image')
    parser.add_argument('--output', type=str, required=True, help='Output directory')
    parser.add_argument('--threshold', type=float, default=0.5, help='Threshold for binary mask')
    args = parser.parse_args()
    
    visualize_model_outputs(args.model, args.image, args.output, args.threshold)
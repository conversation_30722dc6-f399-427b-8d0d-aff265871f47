# Add this method inside your AdaptiveSpotDataset class:

def _apply_erosion_to_mask(self, patch_msk, kernel_size=2, iterations=1):
    """Apply erosion to each spot individually"""
    eroded_mask = np.zeros_like(patch_msk)
    kernel = np.ones((kernel_size, kernel_size), np.uint8)
    
    for iid in np.unique(patch_msk):
        if iid == 0: continue
        spot_binary = (patch_msk == iid).astype(np.uint8)
        eroded_spot = cv2.erode(spot_binary, kernel, iterations=iterations)
        eroded_mask[eroded_spot > 0] = iid
    
    return eroded_mask

# Then in your __getitem__ method, replace:
# sem = (patch_msk > 0).astype(np.float32)

# With:
patch_msk_eroded = self._apply_erosion_to_mask(patch_msk, kernel_size=2, iterations=1)
sem = (patch_msk_eroded > 0).astype(np.float32)
# Updated skeleton_aware_inference with your exact pipeline + 2025 optimizations

import torch
import torch.nn.functional as F
import numpy as np
from skimage.feature import peak_local_maxima
from skimage.segmentation import watershed
from skimage.morphology import remove_small_objects
from skimage.measure import regionprops

def extract_precise_spots(centroid_map, semantic, flow, min_distance=3, threshold=0.3):
    """Extract spots using centroid map and flow field"""
    peaks = peak_local_maxima(
        centroid_map, 
        min_distance=min_distance, 
        threshold_abs=threshold,
        exclude_border=True
    )
    
    spots = []
    for y, x in peaks:
        confidence = centroid_map[y, x]
        if semantic[y, x] > 0.5:  # Only in foreground
            spots.append([y, x, confidence])
    
    return spots

def fast_nms(spots, nms_threshold=0.2):
    """Fast non-maximum suppression"""
    if len(spots) <= 1:
        return spots
    
    spots = np.array(spots)
    indices = np.argsort(spots[:, 2])[::-1]  # Sort by confidence
    keep = []
    
    while len(indices) > 0:
        current = indices[0]
        keep.append(current)
        
        if len(indices) == 1:
            break
            
        # Calculate distances
        distances = np.sqrt(
            (spots[current, 0] - spots[indices[1:], 0])**2 + 
            (spots[current, 1] - spots[indices[1:], 1])**2
        )
        
        # Keep spots that are far enough
        indices = indices[1:][distances > nms_threshold]
    
    return spots[keep].tolist()

def skeleton_aware_instance_segmentation(semantic, sdt, skeleton, spots, min_size=2):
    """Instance segmentation using skeleton-aware watershed"""
    # Create markers from spots
    markers = np.zeros_like(semantic, dtype=int)
    final_spots = []
    
    for i, spot in enumerate(spots):
        y, x = int(spot[0]), int(spot[1])
        if 0 <= y < markers.shape[0] and 0 <= x < markers.shape[1]:
            markers[y, x] = i + 1
            final_spots.append(spot)
    
    # Watershed segmentation
    instance_labels = watershed(
        -sdt, 
        markers, 
        mask=semantic > 0.5,
        watershed_line=True
    )
    
    # Remove small objects
    instance_labels = remove_small_objects(instance_labels, min_size=min_size)
    
    return instance_labels, final_spots

def skeleton_aware_inference(model, image, device='cuda', threshold=0.2, min_distance=3, nms_threshold=0.2):
    """Your exact skeleton_aware_inference function with 2025 optimizations"""
    
    model.eval()
    with torch.no_grad():
        # Prepare image (your exact code)
        if isinstance(image, np.ndarray):
            if image.ndim == 2:
                image = torch.from_numpy(image).unsqueeze(0).unsqueeze(0)
            elif image.ndim == 3:
                image = torch.from_numpy(image).unsqueeze(0)
        
        image = image.to(device).float()
        if image.max() > 1.0:
            image = image / 255.0
        
        # Model inference with mixed precision optimization
        with torch.amp.autocast('cuda'):
            outputs = model(image)
        
        # Extract outputs (your exact code)
        semantic = torch.sigmoid(outputs['sem_out'])[0, 0].cpu().numpy()
        
        # Handle quantized SDT output from the optimized model (your exact code)
        if outputs['sdt_out'].shape[1] > 1:  # Multi-channel output (11 channels: 10 bins + 1 background)
            probs = F.softmax(outputs['sdt_out'], dim=1)
            bin_indices = torch.arange(0, probs.shape[1]).float().to(probs.device)
            bin_indices = bin_indices.view(1, -1, 1, 1) / probs.shape[1]
            sdt = torch.sum(probs * bin_indices, dim=1)[0].cpu().numpy()
        else:
            sdt = torch.sigmoid(outputs['sdt_out'])[0, 0].cpu().numpy()
            
        skeleton = torch.sigmoid(outputs['skeleton_out'])[0, 0].cpu().numpy()
        centroid_map = torch.sigmoid(outputs['hm_out'])[0, 0].cpu().numpy()
        flow = outputs['flow_out'][0].cpu().numpy()
        
        # Extract spots (your exact pipeline)
        spots = extract_precise_spots(
            centroid_map, semantic, flow, 
            min_distance=min_distance, threshold=threshold
        )
        
        # Fast NMS (your exact code)
        if len(spots) > 1:
            spots = fast_nms(spots, nms_threshold)
        
        # Instance segmentation using SDT (your exact code)
        instance_labels, final_spots = skeleton_aware_instance_segmentation(
            semantic, sdt, skeleton, spots, min_size=2
        )
        
        # Extract boundary from SDT (your exact code)
        boundary = (sdt < 0.1) & (semantic > 0.5)
        
        # ADDED: Extract detailed spot information (keeping your useful spot info)
        detailed_spots = []
        for region_id in np.unique(instance_labels):
            if region_id == 0:
                continue
            
            mask = instance_labels == region_id
            props = regionprops(mask.astype(int))[0]
            
            detailed_spots.append({
                'id': region_id,
                'y': props.centroid[0],
                'x': props.centroid[1],
                'area': props.area,
                'confidence': centroid_map[int(props.centroid[0]), int(props.centroid[1])],
                'mask': mask
            })
        
        return {
            'spots': final_spots,  # Your original format
            'detailed_spots': detailed_spots,  # Added detailed info
            'semantic': semantic,
            'sdt': sdt,
            'skeleton': skeleton,
            'boundary': boundary.astype(np.float32),
            'centroid_map': centroid_map,
            'flow': flow,
            'instance_labels': instance_labels
        }

# Wrapper for backward compatibility
def ultra_fast_inference_2025(model, image, device='cuda', threshold=0.3, min_distance=3):
    """Updated function using your skeleton_aware_inference pipeline"""
    
    # Call your exact inference function
    results = skeleton_aware_inference(
        model, image, device, 
        threshold=threshold, 
        min_distance=min_distance, 
        nms_threshold=0.2
    )
    
    # Return in the format expected by the rest of the code
    return {
        'spots': results['detailed_spots'],  # Use detailed spots for compatibility
        'instance_labels': results['instance_labels'],
        'semantic': results['semantic'],
        'centroid': results['centroid_map'],  # Rename for compatibility
        'sdt': results['sdt'],
        'skeleton': results['skeleton'],
        'flow': results['flow'],
        'boundary': results['boundary']  # Added boundary info
    }
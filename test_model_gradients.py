import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from OptimizedSpotLoss import OptimizedSpotLoss
from OptimizedSpotDetection_model import OptimizedSpotDetectionModel

def test_model_gradients():
    """Test if the model gradients are flowing properly during training"""
    print("Testing model gradients...")
    
    # Set random seed for reproducibility
    torch.manual_seed(42)
    np.random.seed(42)
    
    # Create model
    model = OptimizedSpotDetectionModel(
        in_channels=1,
        num_experts=3,
        base_filters=32,
        dropout_rate=0.1
    )
    
    # Create loss function
    loss_fn = OptimizedSpotLoss(
        bce_weight=1.0,
        dice_weight=1.0,
        focal_weight=0.5,
        focal_gamma=2.0,
        size_adaptive=True,
        density_aware=True,
        confidence_weighted=True
    )
    
    # Create optimizer
    optimizer = optim.Adam(model.parameters(), lr=0.001)
    
    # Create random data
    batch_size = 4
    height, width = 64, 64
    
    # Create input images
    images = torch.randn(batch_size, 1, height, width)
    
    # Create target masks with random spots
    masks = torch.zeros(batch_size, 1, height, width)
    
    # Add some random spots to masks
    for b in range(batch_size):
        for _ in range(np.random.randint(3, 10)):
            x = np.random.randint(5, width-5)
            y = np.random.randint(5, height-5)
            radius = np.random.randint(2, 5)
            
            for i in range(max(0, y-radius), min(height, y+radius+1)):
                for j in range(max(0, x-radius), min(width, x+radius+1)):
                    if ((i-y)**2 + (j-x)**2) <= radius**2:
                        masks[b, 0, i, j] = 1.0
    
    # Print data statistics
    print(f"Input images shape: {images.shape}")
    print(f"Target masks shape: {masks.shape}")
    print(f"Target masks mean: {masks.mean().item():.4f}")
    
    # Train for a few iterations
    num_iterations = 5
    print("\nTraining for", num_iterations, "iterations:")
    
    for i in range(num_iterations):
        # Zero gradients
        optimizer.zero_grad()
        
        # Forward pass
        outputs = model(images)
        
        # Calculate loss
        loss_dict = loss_fn(outputs, masks)
        loss = loss_dict['loss']
        
        # Check if loss is valid
        if torch.isnan(loss) or torch.isinf(loss):
            print(f"Iteration {i+1}: Loss is {loss.item()} (invalid)")
            continue
        
        # Backward pass
        loss.backward()
        
        # Check gradients
        total_grad_norm = 0.0
        num_params_with_grad = 0
        max_grad = 0.0
        min_grad = float('inf')
        
        for name, param in model.named_parameters():
            if param.grad is not None:
                grad_norm = param.grad.norm().item()
                total_grad_norm += grad_norm
                num_params_with_grad += 1
                max_grad = max(max_grad, grad_norm)
                min_grad = min(min_grad, grad_norm)
        
        avg_grad_norm = total_grad_norm / max(num_params_with_grad, 1)
        
        # Print statistics
        print(f"Iteration {i+1}:")
        print(f"  Loss: {loss.item():.6f}")
        print(f"  BCE: {loss_dict['bce'].item():.6f}")
        print(f"  Dice: {loss_dict['dice'].item():.6f}")
        print(f"  Focal: {loss_dict['focal'].item():.6f}")
        print(f"  Avg gradient norm: {avg_grad_norm:.6f}")
        print(f"  Min gradient norm: {min_grad:.6f}")
        print(f"  Max gradient norm: {max_grad:.6f}")
        print(f"  Params with grad: {num_params_with_grad}/{len(list(model.parameters()))}")
        
        # Update weights
        optimizer.step()
    
    print("\nGradient test complete!")

if __name__ == "__main__":
    test_model_gradients()
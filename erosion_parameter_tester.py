import cv2
import numpy as np
import matplotlib.pyplot as plt
from skimage.measure import regionprops

def test_erosion_parameters(mask, image=None, test_configs=None):
    """
    Test different erosion parameters to find optimal spot mask refinement.
    
    Args:
        mask: Instance mask with spot IDs
        image: Optional input image for overlay
        test_configs: List of (kernel_size, iterations) tuples to test
    """
    if test_configs is None:
        test_configs = [
            (1, 1), (2, 1), (3, 1),  # Small kernels
            (1, 2), (2, 2), (3, 2),  # More iterations
            (1, 3), (2, 3)           # Heavy erosion
        ]
    
    valid_ids = np.unique(mask)[1:]  # Remove background
    n_configs = len(test_configs)
    
    fig, axes = plt.subplots(3, n_configs, figsize=(4*n_configs, 12))
    if n_configs == 1:
        axes = axes.reshape(-1, 1)
    
    results = {}
    
    for i, (kernel_size, iterations) in enumerate(test_configs):
        kernel = np.ones((kernel_size, kernel_size), np.uint8)
        
        # Apply erosion to each spot individually
        eroded_mask = np.zeros_like(mask)
        spot_stats = []
        
        for spot_id in valid_ids:
            spot_binary = (mask == spot_id).astype(np.uint8)
            original_area = np.sum(spot_binary)
            
            if original_area > 0:
                eroded_spot = cv2.erode(spot_binary, kernel, iterations=iterations)
                eroded_area = np.sum(eroded_spot)
                
                if eroded_area > 0:  # Keep only if something remains
                    eroded_mask[eroded_spot > 0] = spot_id
                    spot_stats.append({
                        'id': spot_id,
                        'original_area': original_area,
                        'eroded_area': eroded_area,
                        'reduction': (original_area - eroded_area) / original_area
                    })
        
        # Create semantic mask
        semantic = (eroded_mask > 0).astype(np.float32)
        
        # Row 1: Original vs eroded mask
        axes[0, i].imshow(mask, cmap='nipy_spectral')
        axes[0, i].set_title(f'Original\n{len(valid_ids)} spots')
        axes[0, i].axis('off')
        
        # Row 2: Eroded mask
        axes[1, i].imshow(eroded_mask, cmap='nipy_spectral')
        remaining_spots = len(np.unique(eroded_mask)) - 1
        axes[1, i].set_title(f'K={kernel_size}, I={iterations}\n{remaining_spots} spots remain')
        axes[1, i].axis('off')
        
        # Row 3: Overlay on image or show semantic
        if image is not None:
            axes[2, i].imshow(image, cmap='gray')
            axes[2, i].imshow(semantic, cmap='Reds', alpha=0.5)
            axes[2, i].set_title('Overlay on Image')
        else:
            axes[2, i].imshow(semantic, cmap='gray')
            axes[2, i].set_title('Semantic Mask')
        axes[2, i].axis('off')
        
        # Store results
        results[f'k{kernel_size}_i{iterations}'] = {
            'config': (kernel_size, iterations),
            'eroded_mask': eroded_mask,
            'semantic': semantic,
            'spot_stats': spot_stats,
            'spots_remaining': remaining_spots,
            'total_pixels': semantic.sum()
        }
    
    plt.tight_layout()
    plt.show()
    
    # Print statistics
    print("\n=== Erosion Results Summary ===")
    print(f"{'Config':<10} {'Spots':<6} {'Pixels':<8} {'Avg Reduction':<12}")
    print("-" * 40)
    
    for key, result in results.items():
        if result['spot_stats']:
            avg_reduction = np.mean([s['reduction'] for s in result['spot_stats']])
            print(f"{key:<10} {result['spots_remaining']:<6} {result['total_pixels']:<8.0f} {avg_reduction:<12.2%}")
        else:
            print(f"{key:<10} {result['spots_remaining']:<6} {result['total_pixels']:<8.0f} {'N/A':<12}")
    
    return results

def analyze_spot_sizes_for_erosion(mask):
    """
    Analyze spot sizes to recommend erosion parameters.
    """
    valid_ids = np.unique(mask)[1:]
    spot_areas = []
    spot_diameters = []
    
    for spot_id in valid_ids:
        spot_binary = (mask == spot_id).astype(np.uint8)
        props = regionprops(spot_binary)
        if props:
            area = props[0].area
            diameter = props[0].equivalent_diameter
            spot_areas.append(area)
            spot_diameters.append(diameter)
    
    if not spot_areas:
        print("No spots found!")
        return None
    
    areas = np.array(spot_areas)
    diameters = np.array(spot_diameters)
    
    print(f"\n=== Spot Size Analysis ===")
    print(f"Total spots: {len(areas)}")
    print(f"Area range: {areas.min():.0f} - {areas.max():.0f} pixels")
    print(f"Diameter range: {diameters.min():.1f} - {diameters.max():.1f} pixels")
    print(f"Mean area: {areas.mean():.1f} ± {areas.std():.1f}")
    print(f"Mean diameter: {diameters.mean():.1f} ± {diameters.std():.1f}")
    
    # Categorize spots
    small_spots = np.sum(diameters <= 3)
    medium_spots = np.sum((diameters > 3) & (diameters <= 8))
    large_spots = np.sum(diameters > 8)
    
    print(f"\nSpot categories:")
    print(f"Small (≤3px): {small_spots} ({small_spots/len(areas)*100:.1f}%)")
    print(f"Medium (3-8px): {medium_spots} ({medium_spots/len(areas)*100:.1f}%)")
    print(f"Large (>8px): {large_spots} ({large_spots/len(areas)*100:.1f}%)")
    
    # Recommendations
    print(f"\n=== Erosion Recommendations ===")
    if diameters.mean() < 4:
        print("Mostly small spots: Try (1,1) or (2,1)")
    elif diameters.mean() < 8:
        print("Mixed sizes: Try (2,1), (2,2), or (3,1)")
    else:
        print("Large spots: Try (2,2), (3,2), or (3,3)")
    
    return {
        'areas': areas,
        'diameters': diameters,
        'small_spots': small_spots,
        'medium_spots': medium_spots,
        'large_spots': large_spots
    }

# Quick test function
def quick_erosion_test(image_path, mask_path):
    """
    Quick test with a single image/mask pair.
    """
    import tifffile
    
    image = tifffile.imread(image_path)
    mask = tifffile.imread(mask_path)
    
    print(f"Testing erosion on: {image_path}")
    
    # Analyze spot sizes first
    size_info = analyze_spot_sizes_for_erosion(mask)
    
    # Test erosion parameters
    results = test_erosion_parameters(mask, image)
    
    return results, size_info
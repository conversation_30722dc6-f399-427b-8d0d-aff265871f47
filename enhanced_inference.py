import torch
import numpy as np
from typing import Dict, List, Optional
from scipy.ndimage import distance_transform_edt, maximum_filter
from scipy.spatial.distance import cdist
from skimage.segmentation import watershed
from skimage.measure import regionprops
import tifffile

def enhanced_inference(
    model: torch.nn.Module,
    image: np.ndarray,
    patch_size: int = 256,
    overlap: int = 32,
    threshold: float = 0.5,
    min_distance: int = 3,
    min_size: int = 4,
    use_nms: bool = False,
    nms_threshold: float = 0.3,
    use_flow: bool = True,
    device: str = 'auto',
    save_path: Optional[str] = None
) -> Dict:
    if device == 'auto':
        device = 'cuda' if torch.cuda.is_available() else 'cpu'
    device = torch.device(device)
    
    original_image = image.copy()
    if len(image.shape) == 3:
        image = image[:, :, 0] if image.shape[2] > 1 else image.squeeze()
    
    # Normalize (matching dataloader)
    image_norm = image.astype(np.float32) / (image.max() + 1e-8)
    H, W = image_norm.shape
    
    # Tiling
    stride = patch_size - overlap
    n_h = (H + stride - 1) // stride
    n_w = (W + stride - 1) // stride
    
    heatmap_full = np.zeros((H, W), dtype=np.float32)
    weight_map = np.zeros((H, W), dtype=np.float32)
    
    model.eval()
    model.to(device)
    
    with torch.no_grad():
        for i in range(n_h):
            for j in range(n_w):
                start_h, start_w = i * stride, j * stride
                end_h, end_w = min(start_h + patch_size, H), min(start_w + patch_size, W)
                
                patch = image_norm[start_h:end_h, start_w:end_w]
                pad_h, pad_w = patch_size - patch.shape[0], patch_size - patch.shape[1]
                if pad_h > 0 or pad_w > 0:
                    patch = np.pad(patch, ((0, pad_h), (0, pad_w)), 'reflect')
                
                patch_tensor = torch.from_numpy(patch).unsqueeze(0).unsqueeze(0).to(device)
                outputs = model(patch_tensor)
                heatmap = torch.sigmoid(outputs['heatmap']).cpu().numpy()[0, 0]
                
                if pad_h > 0 or pad_w > 0:
                    heatmap = heatmap[:patch_size-pad_h, :patch_size-pad_w]
                
                weight = _create_weight_map(heatmap.shape, overlap)
                heatmap_full[start_h:end_h, start_w:end_w] += heatmap * weight
                weight_map[start_h:end_h, start_w:end_w] += weight
    
    heatmap_full /= np.maximum(weight_map, 1e-8)
    
    # Instance segmentation
    binary = heatmap_full > threshold
    if not binary.any():
        empty_result = {
            'instance_masks': np.zeros((H, W), dtype=np.uint8),
            'instance_labels': np.zeros((H, W), dtype=np.uint16),
            'centroids': np.zeros((H, W), dtype=np.uint8),
            'spots': []
        }
        if save_path:
            _save_results(empty_result, save_path)
        return empty_result
    
    # Find peaks using maximum filter
    peaks = _find_peaks(heatmap_full, min_distance, threshold)
    if len(peaks) == 0:
        empty_result = {
            'instance_masks': np.zeros((H, W), dtype=np.uint8),
            'instance_labels': np.zeros((H, W), dtype=np.uint16),
            'centroids': np.zeros((H, W), dtype=np.uint8),
            'spots': []
        }
        if save_path:
            _save_results(empty_result, save_path)
        return empty_result
    
    # Create seeds and watershed
    seeds = np.zeros_like(heatmap_full, dtype=np.int32)
    for idx, (y, x) in enumerate(peaks, 1):
        seeds[y, x] = idx
    
    # Use flow field if requested
    if use_flow:
        seeds = _refine_seeds_with_flow(seeds, heatmap_full, peaks)
        
        # Generate flow-based distance map for watershed
        flow = _generate_flow_field(heatmap_full, peaks)
        flow_magnitude = np.sqrt(flow[0]**2 + flow[1]**2)
        
        # Combine regular distance with flow information
        distance = distance_transform_edt(binary)
        watershed_input = distance * (1 + 0.3 * flow_magnitude)
    else:
        # Standard distance transform
        watershed_input = distance_transform_edt(binary)
    
    # Perform watershed segmentation
    instance_labels = watershed(-watershed_input, seeds, mask=binary)
    
    # Extract results
    spots = []
    all_masks = np.zeros((H, W), dtype=np.uint8)
    centroids_map = np.zeros((H, W), dtype=np.uint8)
    
    for region_id in range(1, len(peaks) + 1):
        mask = (instance_labels == region_id)
        if np.sum(mask) < min_size:
            instance_labels[mask] = 0
            continue
        
        props = regionprops(mask.astype(np.uint8))[0]
        cy, cx = props.centroid
        area = props.area
        score = np.mean(heatmap_full[mask])
        intensity = np.mean(original_image[mask])
        
        spots.append({
            'x': float(cx), 'y': float(cy),
            'score': float(score), 'size': int(area),
            'intensity': float(intensity)
        })
        
        all_masks[mask] = 255
        centroids_map[int(cy), int(cx)] = 255
    
    # Apply NMS if requested
    if use_nms and len(spots) > 1:
        keep = _apply_nms(spots, nms_threshold)
        spots = [spots[i] for i in keep]
        
        # Update labels
        new_labels = np.zeros_like(instance_labels)
        for new_id, old_id in enumerate(keep, 1):
            new_labels[instance_labels == old_id + 1] = new_id
        instance_labels = new_labels
    
    result = {
        'instance_masks': all_masks,
        'instance_labels': instance_labels.astype(np.uint16),
        'centroids': centroids_map,
        'spots': spots
    }
    
    if save_path:
        _save_results(result, save_path)
    
    return result

def _find_peaks(heatmap, min_distance, threshold):
    local_max = maximum_filter(heatmap, size=min_distance) == heatmap
    peaks_mask = local_max & (heatmap > threshold)
    peaks = np.column_stack(np.where(peaks_mask))
    return peaks

def _refine_seeds_with_flow(seeds, heatmap, peaks):
    """Refine seed positions using flow field"""
    h, w = heatmap.shape
    flow = _generate_flow_field(heatmap, peaks)
    
    # Create distance transform for better watershed segmentation
    binary = heatmap > 0.1
    distance = distance_transform_edt(binary)
    
    refined_seeds = np.zeros_like(seeds)
    seed_id = 1
    
    # Process each peak
    for y, x in peaks:
        # Find local maximum around peak
        y_min, y_max = max(0, y-5), min(h, y+6)
        x_min, x_max = max(0, x-5), min(w, x+6)
        local_region = heatmap[y_min:y_max, x_min:x_max]
        local_dist = distance[y_min:y_max, x_min:x_max]
        
        # Combine heatmap and distance for better peak localization
        combined = local_region * (1 + 0.2 * local_dist/local_dist.max())
        
        # Find best position
        if combined.size > 0:
            local_y, local_x = np.unravel_index(np.argmax(combined), combined.shape)
            best_y, best_x = local_y + y_min, local_x + x_min
            
            # Set seed at best position
            refined_seeds[best_y, best_x] = seed_id
            seed_id += 1
    
    return refined_seeds

def _generate_flow_field(heatmap, peaks):
    """Generate flow field pointing towards centroids"""
    h, w = heatmap.shape
    flow = np.zeros((2, h, w), dtype=np.float32)
    
    if len(peaks) == 0:
        return flow
    
    # Create Voronoi-like regions for each peak
    binary = heatmap > 0.1
    y_coords, x_coords = np.where(binary)
    
    if len(y_coords) > 0:
        pixel_coords = np.column_stack([y_coords, x_coords])
        distances = cdist(pixel_coords, peaks)
        nearest_idx = np.argmin(distances, axis=1)
        
        # Calculate flow vectors pointing to nearest peak
        for i, (y, x) in enumerate(pixel_coords):
            nearest_peak = peaks[nearest_idx[i]]
            dy = nearest_peak[0] - y
            dx = nearest_peak[1] - x
            dist = np.sqrt(dy**2 + dx**2) + 1e-8
            
            # Weight by distance (stronger flow near boundaries)
            weight = np.clip(1.0 - dist/20, 0.2, 1.0)
            
            # Normalize and store flow vectors
            flow[0, y, x] = (dy / dist) * weight
            flow[1, y, x] = (dx / dist) * weight
    
    return flow

def _create_weight_map(shape, overlap):
    h, w = shape
    weight = np.ones((h, w), dtype=np.float32)
    taper = min(overlap // 2, 16)
    if taper > 0:
        weight[:taper, :] *= np.linspace(0, 1, taper)[:, None]
        weight[-taper:, :] *= np.linspace(1, 0, taper)[:, None]
        weight[:, :taper] *= np.linspace(0, 1, taper)[None, :]
        weight[:, -taper:] *= np.linspace(1, 0, taper)[None, :]
    return weight

def _apply_nms(spots, threshold):
    if len(spots) <= 1:
        return list(range(len(spots)))
    
    coords = np.array([[spot['x'], spot['y']] for spot in spots])
    scores = np.array([spot['score'] for spot in spots])
    order = np.argsort(scores)[::-1]
    
    keep = []
    while len(order) > 0:
        i = order[0]
        keep.append(i)
        if len(order) == 1:
            break
        distances = np.sqrt(np.sum((coords[order[1:]] - coords[i])**2, axis=1))
        far_enough = distances > threshold * np.sqrt(spots[i]['size'])
        order = order[1:][far_enough]
    
    return sorted(keep)

def _save_results(results, save_path):
    import os
    os.makedirs(save_path, exist_ok=True)
    
    tifffile.imwrite(f"{save_path}/instance_masks.tif", results['instance_masks'])
    tifffile.imwrite(f"{save_path}/instance_labels.tif", results['instance_labels'])
    tifffile.imwrite(f"{save_path}/centroids.tif", results['centroids'])
    
    if results['spots']:
        import pandas as pd
        df = pd.DataFrame(results['spots'])
        df.to_csv(f"{save_path}/spots.csv", index=False)
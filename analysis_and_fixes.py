# ANALYSIS: Why predicted semantic is still large

# 1. LOSS WEIGHTS - Check your current weights
print("Current loss weights:")
print(f"Semantic: {criterion.w_sem}")
print(f"Distance: {criterion.w_dist}")
print(f"Boundary: {criterion.w_bnd}")

# SOLUTION 1: Reduce semantic weight, increase distance weight
criterion.w_sem = 0.5    # Reduce from default
criterion.w_dist = 3.0   # Increase - distance transform has better shape info
criterion.w_bnd = 2.0    # Keep boundary important

# SOLUTION 2: Use distance transform as primary target
# Distance transform has better shape information than binary semantic
# In your visualization, try:
dist_pred = torch.sigmoid(outputs['distance'])[0, 0].cpu().numpy()
# Use distance > 0.3 as semantic prediction instead of semantic head
sem_from_dist = (dist_pred > 0.3).astype(np.float32)

# SOLUTION 3: Check model dilation in ASPP
# If your model has large dilation rates, it causes large receptive fields
# In your model, check ASPP rates - should be [1,4,8] not [1,6,12,18]

# SOLUTION 4: Add size-aware loss
def size_aware_semantic_loss(pred_semantic, gt_semantic, gt_distance):
    """Penalize predictions that are too large"""
    # Standard loss
    focal_loss = F.binary_cross_entropy_with_logits(pred_semantic, gt_semantic)
    
    # Size penalty - penalize large predictions where distance is low
    pred_prob = torch.sigmoid(pred_semantic)
    size_penalty = torch.mean(pred_prob * (1 - gt_distance))  # Penalize large + far from center
    
    return focal_loss + 0.5 * size_penalty
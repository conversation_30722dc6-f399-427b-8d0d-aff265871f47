# Code Review: Cells After ###START HERE

## Critical Issues to Fix:

### 1. Dataset Class (Cell 2)
- **Channel Count Mismatch**: Comments mention 6 channels but code returns 5
- **Confidence Shape Inconsistency**: Sometimes uses `unsqueeze(0)`, sometimes doesn't
- **Semantic Mask Over-erosion**: Using 3x3 erosion may make spots too small

**Fix:**
```python
# Consistent confidence shape
confidence_t = torch.from_numpy(confidence).unsqueeze(0)  # Always [1, H, W]

# Less aggressive erosion for semantic mask
kernel = np.ones((2,2), np.uint8)  # Smaller kernel
```

### 2. Model Architecture (Cell 3)
- **Over-complexity**: Too many components (SE blocks, ASPP, deep supervision)
- **Coordinate System Confusion**: Mix of (y,x) and (x,y) conventions

**Recommendation**: Simplify to basic U-Net with just essential components

### 3. Training Functions (Cells 5-7)
- **Coordinate Inconsistency**: `extract_precise_spots` returns [x,y,score] but some functions expect [y,x,score]
- **Unnecessary Complexity**: Flow refinement adds complexity without clear benefit

**Fix:**
```python
# Standardize on [y, x, score] format throughout
def extract_precise_spots(...):
    # Return [y, x, score] consistently
    spots.append([y, x, score])  # Not [x, y, score]
```

### 4. Loss Function Complexity
- **Too Many Components**: 8+ loss terms can cause training instability
- **Unclear Weighting**: Complex weighting scheme without justification

**Recommendation**: Start with 3-4 core losses, add complexity gradually

## Minimal Fixes Needed:

1. **Fix confidence tensor shape consistency**
2. **Standardize coordinate system to [y, x, score]**
3. **Reduce semantic mask erosion**
4. **Simplify loss function weights**
5. **Remove unnecessary flow refinement initially**

## Priority Order:
1. Fix coordinate system consistency (HIGH)
2. Fix confidence tensor shapes (HIGH) 
3. Simplify loss function (MEDIUM)
4. Reduce model complexity (LOW - can work as-is)
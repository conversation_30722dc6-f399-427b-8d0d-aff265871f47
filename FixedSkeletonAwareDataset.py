import torch
from torch.utils.data import Dataset
import numpy as np
import tifffile
import random
import cv2
from scipy.ndimage import distance_transform_edt
from skimage.measure import regionprops
from skimage.morphology import skeletonize
from scipy.spatial.distance import cdist

class SkeletonAwareSpotDataset(Dataset):
    """
    Fixed dataset for skeleton-aware spot detection
    """
    def __init__(self, image_paths, mask_paths, transform=None, patch_size=256):
        assert len(image_paths) == len(mask_paths)
        self.image_paths = image_paths
        self.mask_paths = mask_paths
        self.transform = transform
        self.patch_size = patch_size

    def _get_valid_spots(self, mask):
        """Extract valid spot IDs from mask (ignoring background)"""
        valid_ids = np.unique(mask)
        return valid_ids[valid_ids > 0]

    def _extract_spot_info(self, mask, valid_ids):
        """Extract centroids and binary masks for each valid spot"""
        centroids = []
        spot_masks = []
        for spot_id in valid_ids:
            spot_mask = (mask == spot_id).astype(np.uint8)
            props = regionprops(spot_mask)
            if props:
                centroids.append(props[0].centroid)
                spot_masks.append(spot_mask)
        return centroids, spot_masks

    def _generate_semantic_mask(self, spot_masks, shape):
        """Generate semantic mask from individual spot masks"""
        semantic = np.zeros(shape, dtype=np.float32)
        for mask in spot_masks:
            semantic = np.maximum(semantic, mask.astype(np.float32))
        return semantic

    def _generate_skeleton_aware_distance_transform(self, spot_mask):
        """Fixed SDT generation with proper skeleton handling"""
        if not spot_mask.any():
            return (np.zeros_like(spot_mask, dtype=np.float32), 
                    np.zeros_like(spot_mask, dtype=np.float32),
                    np.zeros_like(spot_mask, dtype=np.float32))
        
        spot_area = np.sum(spot_mask)
        
        # Generate proper skeleton first
        skeleton = np.zeros_like(spot_mask, dtype=np.float32)
        
        if spot_area <= 10:  # Small spots - use enhanced centroid region
            props = regionprops(spot_mask.astype(np.uint8))
            if props:
                cy, cx = map(int, props[0].centroid)
                # Create 3x3 skeleton region with gradients
                for dy in [-1, 0, 1]:
                    for dx in [-1, 0, 1]:
                        ny, nx = cy + dy, cx + dx
                        if 0 <= ny < skeleton.shape[0] and 0 <= nx < skeleton.shape[1] and spot_mask[ny, nx]:
                            skeleton[ny, nx] = 1.0 if (dy == 0 and dx == 0) else 0.6
        else:  # Larger spots - use morphological skeleton
            try:
                skeleton = skeletonize(spot_mask > 0).astype(np.float32)
                # Ensure skeleton exists
                if np.sum(skeleton) == 0:
                    props = regionprops(spot_mask.astype(np.uint8))
                    if props:
                        cy, cx = map(int, props[0].centroid)
                        if 0 <= cy < skeleton.shape[0] and 0 <= cx < skeleton.shape[1]:
                            skeleton[cy, cx] = 1.0
            except:
                # Fallback to centroid
                props = regionprops(spot_mask.astype(np.uint8))
                if props:
                    cy, cx = map(int, props[0].centroid)
                    if 0 <= cy < skeleton.shape[0] and 0 <= cx < skeleton.shape[1]:
                        skeleton[cy, cx] = 1.0
        
        # Generate boundary
        kernel = np.ones((3, 3), np.uint8)
        eroded = cv2.erode(spot_mask.astype(np.uint8), kernel, iterations=1)
        boundary = (spot_mask.astype(np.uint8) - eroded).astype(np.float32)
        
        # **CORRECTED SDT CALCULATION**
        # Distance from boundary (higher inside)
        dist_to_boundary = distance_transform_edt(spot_mask)
        
        # Distance from skeleton (lower near skeleton)
        skeleton_binary = skeleton > 0
        if np.any(skeleton_binary):
            dist_to_skeleton = distance_transform_edt(~skeleton_binary)
        else:
            dist_to_skeleton = np.ones_like(spot_mask, dtype=np.float32)
        
        # Normalize within spot region only
        spot_region = spot_mask > 0
        if np.any(spot_region):
            # Get max distances within spot
            max_boundary_dist = np.max(dist_to_boundary[spot_region]) + 1e-6
            max_skeleton_dist = np.max(dist_to_skeleton[spot_region]) + 1e-6
            
            # Normalize distances
            boundary_norm = dist_to_boundary / max_boundary_dist
            skeleton_norm = dist_to_skeleton / max_skeleton_dist
            
            # SDT formula: high near skeleton (low skeleton_norm), high away from boundary
            alpha = 1.5 if spot_area <= 15 else 1.0
            sdt = boundary_norm * np.power(1.0 - skeleton_norm, alpha)
            
            # Apply only to spot region
            sdt_final = np.zeros_like(spot_mask, dtype=np.float32)
            sdt_final[spot_region] = sdt[spot_region]
            sdt = np.clip(sdt_final, 0, 1)
        else:
            sdt = np.zeros_like(spot_mask, dtype=np.float32)
        
        return sdt, skeleton, boundary

    def _generate_centroid_map(self, shape, centroids):
        """Generate centroid heatmap"""
        centroid_map = np.zeros(shape, dtype=np.float32)
        for cy, cx in centroids:
            cy, cx = int(round(cy)), int(round(cx))
            if 0 <= cy < shape[0] and 0 <= cx < shape[1]:
                centroid_map[cy, cx] = 1.0
                # Add small gaussian-like region
                for dy in [-1, 0, 1]:
                    for dx in [-1, 0, 1]:
                        ny, nx = cy + dy, cx + dx
                        if 0 <= ny < shape[0] and 0 <= nx < shape[1]:
                            if dy == 0 and dx == 0:
                                continue
                            centroid_map[ny, nx] = max(centroid_map[ny, nx], 0.3)
        return centroid_map

    def _generate_flow_field(self, semantic_mask, centroids):
        """Generate flow field pointing to nearest centroid"""
        h, w = semantic_mask.shape
        if len(centroids) == 0:
            return np.zeros((2, h, w), dtype=np.float32)
        
        spot_pixels = np.column_stack(np.where(semantic_mask > 0))
        if len(spot_pixels) == 0:
            return np.zeros((2, h, w), dtype=np.float32)
        
        # Limit computation for efficiency
        if len(spot_pixels) > 5000:
            indices = np.random.choice(len(spot_pixels), 5000, replace=False)
            spot_pixels = spot_pixels[indices]
        
        flow_y = np.zeros((h, w), dtype=np.float32)
        flow_x = np.zeros((h, w), dtype=np.float32)
        
        centroids_arr = np.array(centroids)
        distances = cdist(spot_pixels, centroids_arr)
        nearest_idx = np.argmin(distances, axis=1)
        
        for i, (py, px) in enumerate(spot_pixels):
            cy, cx = centroids_arr[nearest_idx[i]]
            dy, dx = cy - py, cx - px
            norm = np.sqrt(dy**2 + dx**2) + 1e-8
            flow_y[py, px] = dy / norm
            flow_x[py, px] = dx / norm
        
        return np.stack([flow_y, flow_x])

    def __len__(self):
        return len(self.image_paths)

    def __getitem__(self, idx):
        # Load and preprocess image
        image = tifffile.imread(self.image_paths[idx]).astype(np.float32)
        mask = tifffile.imread(self.mask_paths[idx]).astype(np.int32)
        
        # Robust normalization
        vmin, vmax = np.percentile(image, (1, 99))
        image = np.clip((image - vmin) / (vmax - vmin + 1e-8), 0, 1)
        
        H, W = image.shape[:2]
        
        # Smart patch extraction - prefer patches with spots
        valid_ids = self._get_valid_spots(mask)
        if len(valid_ids) > 0 and random.random() < 0.7:
            spot_id = random.choice(valid_ids)
            coords = np.argwhere(mask == spot_id)
            cy, cx = coords.mean(axis=0).astype(int)
            x0 = np.clip(cy - self.patch_size // 2, 0, H - self.patch_size)
            y0 = np.clip(cx - self.patch_size // 2, 0, W - self.patch_size)
        else:
            x0 = random.randint(0, max(0, H - self.patch_size))
            y0 = random.randint(0, max(0, W - self.patch_size))
        
        # Extract patch
        patch_img = image[x0:x0+self.patch_size, y0:y0+self.patch_size]
        patch_mask = mask[x0:x0+self.patch_size, y0:y0+self.patch_size]
        
        # Ensure correct size
        if patch_img.shape[0] < self.patch_size or patch_img.shape[1] < self.patch_size:
            ph = max(0, self.patch_size - patch_img.shape[0])
            pw = max(0, self.patch_size - patch_img.shape[1])
            patch_img = np.pad(patch_img, ((0, ph), (0, pw)), 'reflect')
            patch_mask = np.pad(patch_mask, ((0, ph), (0, pw)), 'constant')
        
        # Get spot information
        valid_ids = self._get_valid_spots(patch_mask)
        if len(valid_ids) == 0:
            # Return empty targets
            return (
                torch.zeros((1, self.patch_size, self.patch_size), dtype=torch.float32),
                torch.zeros((6, self.patch_size, self.patch_size), dtype=torch.float32)
            )
        
        centroids, spot_masks = self._extract_spot_info(patch_mask, valid_ids)
        
        # Generate semantic mask
        semantic = self._generate_semantic_mask(spot_masks, patch_mask.shape)
        
        # Initialize combined maps
        combined_sdt = np.zeros_like(semantic, dtype=np.float32)
        combined_skeleton = np.zeros_like(semantic, dtype=np.float32)
        combined_boundary = np.zeros_like(semantic, dtype=np.float32)
        
        # Process each spot individually
        for spot_mask in spot_masks:
            sdt, skeleton, boundary = self._generate_skeleton_aware_distance_transform(spot_mask)
            
            # Combine using maximum to preserve all spots
            combined_sdt = np.maximum(combined_sdt, sdt)
            combined_skeleton = np.maximum(combined_skeleton, skeleton)
            combined_boundary = np.maximum(combined_boundary, boundary)
        
        # Generate other targets
        centroid_map = self._generate_centroid_map(patch_mask.shape, centroids)
        flow_field = self._generate_flow_field(semantic, centroids)
        flow_magnitude = np.sqrt(flow_field[0]**2 + flow_field[1]**2)
        
        # Stack all targets: [semantic, sdt, skeleton, centroid, flow_magnitude, boundary]
        targets = np.stack([
            semantic, 
            combined_sdt, 
            combined_skeleton, 
            centroid_map, 
            flow_magnitude, 
            combined_boundary
        ], axis=0)
        
        # Apply augmentations if provided
        if self.transform is not None:
            targets_hwc = np.moveaxis(targets, 0, -1)
            augmented = self.transform(image=patch_img, mask=targets_hwc)
            patch_img = augmented['image']
            targets = np.moveaxis(augmented['mask'], -1, 0)
            
            # Post-augmentation cleanup
            targets[1] = np.clip(targets[1], 0, 1)  # SDT
            targets[2] = np.clip(targets[2], 0, 1)  # Skeleton - preserve gradients!
            targets[0] = (targets[0] > 0.5).astype(np.float32)  # Semantic - binary
            targets[5] = (targets[5] > 0.5).astype(np.float32)  # Boundary - binary
        
        # Convert to tensors
        img_tensor = torch.from_numpy(patch_img.astype(np.float32)).unsqueeze(0)
        targets_tensor = torch.from_numpy(targets.astype(np.float32))
        
        return img_tensor, targets_tensor
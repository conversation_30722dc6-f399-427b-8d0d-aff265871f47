import torch
import numpy as np
from OptimizedSpotLoss import OptimizedSpotLoss

def test_loss_function():
    """Test the loss function to ensure it's working correctly"""
    print("Testing OptimizedSpotLoss...")
    
    # Create a loss function
    loss_fn = OptimizedSpotLoss(
        bce_weight=1.0,
        dice_weight=1.0,
        focal_weight=0.5,
        focal_gamma=2.0,
        size_adaptive=True,
        density_aware=True,
        confidence_weighted=True
    )
    
    # Create random predictions and targets
    batch_size = 4
    height, width = 64, 64
    
    # Case 1: Random initialization (should give high loss)
    print("\nCase 1: Random initialization")
    pred = torch.randn(batch_size, 1, height, width)
    target = torch.zeros(batch_size, 1, height, width)
    
    # Add some random spots to target
    for b in range(batch_size):
        for _ in range(np.random.randint(3, 10)):
            x = np.random.randint(5, width-5)
            y = np.random.randint(5, height-5)
            radius = np.random.randint(2, 5)
            
            for i in range(max(0, y-radius), min(height, y+radius+1)):
                for j in range(max(0, x-radius), min(width, x+radius+1)):
                    if ((i-y)**2 + (j-x)**2) <= radius**2:
                        target[b, 0, i, j] = 1.0
    
    # Calculate loss
    loss_dict = loss_fn(pred, target)
    print(f"Loss: {loss_dict['loss'].item():.4f}")
    print(f"BCE: {loss_dict['bce'].item():.4f}")
    print(f"Dice: {loss_dict['dice'].item():.4f}")
    print(f"Focal: {loss_dict['focal'].item():.4f}")
    
    # Case 2: Perfect prediction (should give very low loss)
    print("\nCase 2: Perfect prediction")
    pred = torch.logit(target.clone() * 0.99 + 0.005)  # Convert to logits
    loss_dict = loss_fn(pred, target)
    print(f"Loss: {loss_dict['loss'].item():.4f}")
    print(f"BCE: {loss_dict['bce'].item():.4f}")
    print(f"Dice: {loss_dict['dice'].item():.4f}")
    print(f"Focal: {loss_dict['focal'].item():.4f}")
    
    # Case 3: Dictionary input (as used in the model)
    print("\nCase 3: Dictionary input")
    pred_dict = {'heatmap': torch.randn(batch_size, 1, height, width)}
    target_dict = {'masks': target}
    loss_dict = loss_fn(pred_dict, target_dict)
    print(f"Loss: {loss_dict['loss'].item():.4f}")
    print(f"BCE: {loss_dict['bce'].item():.4f}")
    print(f"Dice: {loss_dict['dice'].item():.4f}")
    print(f"Focal: {loss_dict['focal'].item():.4f}")
    
    # Case 4: With confidence mask
    print("\nCase 4: With confidence mask")
    confidence = torch.ones_like(target)
    target_dict = {'masks': target, 'confidence': confidence}
    loss_dict = loss_fn(pred_dict, target_dict)
    print(f"Loss: {loss_dict['loss'].item():.4f}")
    print(f"BCE: {loss_dict['bce'].item():.4f}")
    print(f"Dice: {loss_dict['dice'].item():.4f}")
    print(f"Focal: {loss_dict['focal'].item():.4f}")
    
    print("\nLoss function test complete!")

if __name__ == "__main__":
    test_loss_function()
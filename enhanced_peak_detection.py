import torch
import numpy as np
from skimage.feature import peak_local_max
import cv2
from typing import Dict, List, Tuple, Optional, Union
import matplotlib.pyplot as plt

class EnhancedPeakDetector:
    """
    Enhanced peak detector that combines peak detection for centroids with actual masks
    
    This class detects spot centroids using peak detection and then uses the actual masks
    (ground truth for real data or synthetic masks for synthetic data) for more accurate
    spot representation.
    """
    
    def __init__(self, 
                min_distance=5,
                min_intensity=0.1,
                device=torch.device('cpu')):
        """Initialize the enhanced peak detector"""
        self.min_distance = min_distance
        self.min_intensity = min_intensity
        self.device = device
    
    def detect_peaks_with_masks(self, 
                              heatmap,
                              mask=None,
                              return_visualization=False,
                              original_image=None):
        """
        Detect peaks in the heatmap and use actual masks for spot representation
        
        Args:
            heatmap: Prediction heatmap (numpy array or tensor)
            mask: Ground truth mask with unique IDs for each spot (numpy array or tensor)
            return_visualization: Whether to return visualization
            original_image: Original image for visualization
            
        Returns:
            Dictionary with detection results
        """
        # Convert to numpy if tensor
        if isinstance(heatmap, torch.Tensor):
            heatmap = heatmap.detach().cpu().numpy()
        
        if mask is not None and isinstance(mask, torch.Tensor):
            mask = mask.detach().cpu().numpy()
        
        # Ensure 2D
        if heatmap.ndim > 2:
            heatmap = heatmap.squeeze()
        
        if mask is not None and mask.ndim > 2:
            mask = mask.squeeze()
        
        # Find local maxima directly in the heatmap
        coordinates = peak_local_max(
            heatmap,
            min_distance=self.min_distance,
            threshold_abs=self.min_intensity,
            exclude_border=False
        )
        
        # Refine peak locations using center of mass for more accurate centroids
        refined_coordinates = []
        spot_masks = []  # Store individual spot masks
        
        for y, x in coordinates:
            # Extract a small region around the peak
            window_size = 7
            half_size = window_size // 2
            
            # Define region boundaries with bounds checking
            y_min = max(0, y - half_size)
            y_max = min(heatmap.shape[0], y + half_size + 1)
            x_min = max(0, x - half_size)
            x_max = min(heatmap.shape[1], x + half_size + 1)
            
            # Extract region
            region = heatmap[y_min:y_max, x_min:x_max]
            
            # Skip if region is empty
            if region.size == 0:
                refined_coordinates.append((y, x))
                continue
                
            # Calculate center of mass for more accurate centroid
            # First threshold the region to focus on the spot
            threshold = self.min_intensity
            binary_region = region > threshold
            
            # Skip if no pixels above threshold
            if not np.any(binary_region):
                refined_coordinates.append((y, x))
                continue
                
            # Calculate center of mass
            y_indices, x_indices = np.where(binary_region)
            weights = region[binary_region]
            
            # Calculate weighted centroid
            if weights.sum() > 0:
                y_cm = np.sum(y_indices * weights) / weights.sum() + y_min
                x_cm = np.sum(x_indices * weights) / weights.sum() + x_min
                refined_coordinates.append((y_cm, x_cm))
            else:
                refined_coordinates.append((y, x))
            
            # If mask is provided, extract the corresponding spot mask
            if mask is not None:
                # Find the spot ID at this location in the mask
                y_int, x_int = int(round(y)), int(round(x))
                y_int = max(0, min(y_int, mask.shape[0]-1))
                x_int = max(0, min(x_int, mask.shape[1]-1))
                
                spot_id = mask[y_int, x_int]
                
                # If this is a valid spot in the mask
                if spot_id > 0:
                    # Extract the mask for this spot ID
                    spot_mask = (mask == spot_id).astype(np.float32)
                    spot_masks.append({
                        'id': int(spot_id),
                        'mask': spot_mask,
                        'centroid': (y_cm, x_cm)
                    })
        
        # Create result dictionary
        result = {
            'num_spots': len(refined_coordinates),
            'coordinates': np.array(refined_coordinates) if refined_coordinates else np.array([]),
            'peak_values': np.array([heatmap[int(min(max(0, y), heatmap.shape[0]-1)), 
                                          int(min(max(0, x), heatmap.shape[1]-1))] 
                                    for y, x in refined_coordinates]) if len(refined_coordinates) > 0 else np.array([]),
            'spot_masks': spot_masks if mask is not None else None
        }
        
        # Create visualization if requested
        if return_visualization:
            if original_image is None:
                # Create blank image if original not provided
                original_image = np.zeros_like(heatmap)
            
            # Create RGB visualization
            visualization = self._create_visualization_with_masks(
                original_image, heatmap, refined_coordinates, spot_masks if mask is not None else None
            )
            result['visualization'] = visualization
        
        return result
    
    def _create_visualization_with_masks(self, image, heatmap, coordinates, spot_masks=None):
        """Create visualization of detected peaks with actual masks"""
        # Ensure image is normalized
        if image.max() > 1.0:
            image = image / 255.0
        
        # Create RGB image
        if image.ndim == 2:
            rgb_mask = np.zeros((*image.shape, 3))
            rgb_mask[..., 0] = image  # Red channel
            rgb_mask[..., 1] = image  # Green channel
            rgb_mask[..., 2] = image  # Blue channel
        else:
            rgb_mask = image.copy()
        
        # If we have spot masks, use them for visualization
        if spot_masks is not None and len(spot_masks) > 0:
            # Create a combined mask for all spots
            combined_mask = np.zeros_like(image)
            
            # Add each spot mask with a unique color
            for i, spot_data in enumerate(spot_masks):
                spot_mask = spot_data['mask']
                y, x = spot_data['centroid']
                
                # Add the mask to the visualization
                # Use a semi-transparent overlay
                mask_overlay = np.zeros((*image.shape, 3))
                
                # Generate a unique color for this spot
                # Use HSV color space for better color distribution
                hue = (i * 0.618033988749895) % 1.0  # Golden ratio to distribute colors
                color = plt.cm.hsv(hue)[:3]  # Convert to RGB
                
                # Apply the color to the mask
                for c in range(3):
                    mask_overlay[..., c] = spot_mask * color[c]
                
                # Add to RGB mask with transparency
                alpha = 0.5  # Transparency factor
                rgb_mask = rgb_mask * (1 - spot_mask[:, :, np.newaxis] * alpha) + mask_overlay * alpha
                
                # Add spot ID
                cv2.putText(
                    rgb_mask,
                    str(i+1),
                    (int(round(x)), int(round(y))),
                    cv2.FONT_HERSHEY_SIMPLEX,
                    0.3,
                    (1, 1, 1),  # White
                    1
                )
                
                # Draw a small circle at the centroid
                cv2.circle(
                    rgb_mask,
                    (int(round(x)), int(round(y))),
                    2,
                    (1, 1, 1),  # White
                    1
                )
        else:
            # If no masks are available, fall back to simple circle visualization
            for i, (y, x) in enumerate(coordinates):
                # Get intensity at this peak
                y_int, x_int = int(round(y)), int(round(x))
                y_int = max(0, min(y_int, heatmap.shape[0]-1))
                x_int = max(0, min(x_int, heatmap.shape[1]-1))
                intensity = heatmap[y_int, x_int]
                
                # Calculate radius using intensity
                r = int(3 + 2 * intensity)  # Simple radius calculation
                r = max(2, min(r, 7))  # Limit radius range
                
                # Draw circle
                cv2.circle(
                    rgb_mask,
                    (int(round(x)), int(round(y))),
                    r,
                    (1, 0, 0),  # Red
                    1
                )
                
                # Add ID
                cv2.putText(
                    rgb_mask,
                    str(i+1),
                    (int(round(x)), int(round(y))),
                    cv2.FONT_HERSHEY_SIMPLEX,
                    0.3,
                    (0, 1, 0),  # Green
                    1
                )
        
        return rgb_mask
    
    def update_parameters(self, min_distance=None, min_intensity=None):
        """Update detection parameters"""
        if min_distance is not None:
            self.min_distance = min_distance
        if min_intensity is not None:
            self.min_intensity = min_intensity
    
    def get_parameters(self):
        """Get current parameters"""
        return {
            'min_distance': self.min_distance,
            'min_intensity': self.min_intensity
        }
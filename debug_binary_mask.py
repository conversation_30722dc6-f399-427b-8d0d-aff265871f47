import torch
import numpy as np
import matplotlib.pyplot as plt
from skimage import io, measure, morphology
from skimage.feature import peak_local_max
from skimage.segmentation import watershed
from scipy.ndimage import distance_transform_edt

def debug_binary_mask(image, model, device, show_steps=True):
    """
    Debug the binary mask creation process to fix the single large spot issue
    
    Args:
        image: Input image (numpy array)
        model: Trained model
        device: Device to use for inference
        show_steps: Whether to show intermediate steps
        
    Returns:
        Dictionary with debugging results
    """
    # Convert to tensor
    if isinstance(image, np.ndarray):
        if len(image.shape) == 2:
            image_tensor = torch.from_numpy(image).unsqueeze(0).unsqueeze(0).float()
        else:
            image_tensor = torch.from_numpy(image).unsqueeze(0).float()
    else:
        image_tensor = image
    
    # Ensure values are in [0, 1]
    if image_tensor.max() > 1.0:
        image_tensor = image_tensor / 255.0
    
    # Move to device
    image_tensor = image_tensor.to(device)
    
    # Get prediction
    with torch.no_grad():
        outputs = model(image_tensor)
        
        # Get main output
        if isinstance(outputs, dict):
            pred = outputs.get('output', outputs.get('combined_output', None))
        else:
            pred = outputs
        
        # Apply sigmoid
        pred_sigmoid = torch.sigmoid(pred)
        
        # Convert to numpy
        heatmap = pred_sigmoid.squeeze().cpu().numpy()
    
    # Try different thresholds
    thresholds = [0.05, 0.1, 0.15, 0.2, 0.25, 0.3, 0.4, 0.5]
    binary_masks = []
    
    for threshold in thresholds:
        binary_mask = (heatmap > threshold).astype(np.uint8)
        binary_masks.append(binary_mask)
    
    # Create figure for visualization
    if show_steps:
        fig, axes = plt.subplots(2, 5, figsize=(20, 8))
        
        # Plot original image
        axes[0, 0].imshow(image, cmap='gray')
        axes[0, 0].set_title('Original Image')
        axes[0, 0].axis('off')
        
        # Plot heatmap
        axes[0, 1].imshow(heatmap, cmap='hot')
        axes[0, 1].set_title('Prediction Heatmap')
        axes[0, 1].axis('off')
        
        # Plot binary masks with different thresholds
        for i, (threshold, binary_mask) in enumerate(zip(thresholds[:6], binary_masks[:6])):
            row, col = divmod(i+2, 5)
            axes[row, col].imshow(binary_mask, cmap='gray')
            axes[row, col].set_title(f'Binary Mask (t={threshold})')
            axes[row, col].axis('off')
        
        plt.tight_layout()
        plt.show()
    
    # Find best threshold based on number of connected components
    num_components = []
    for binary_mask in binary_masks:
        labeled_mask = measure.label(binary_mask)
        num_components.append(labeled_mask.max())
    
    # Find threshold with most components
    best_idx = np.argmax(num_components)
    best_threshold = thresholds[best_idx]
    best_binary_mask = binary_masks[best_idx]
    
    print(f"Number of components per threshold:")
    for threshold, count in zip(thresholds, num_components):
        print(f"  Threshold {threshold}: {count} components")
    
    print(f"\nBest threshold: {best_threshold} with {num_components[best_idx]} components")
    
    # Try watershed segmentation with the best binary mask
    if num_components[best_idx] < 10:  # If still not enough components
        print("\nTrying watershed segmentation to separate spots...")
        
        # Get distance transform
        distance = distance_transform_edt(best_binary_mask)
        
        # Try different min_distance values
        min_distances = [2, 3, 5, 7, 10]
        watershed_results = []
        
        for min_distance in min_distances:
            # Find local maxima
            local_max_coords = peak_local_max(
                distance,
                min_distance=min_distance,
                labels=best_binary_mask,
                threshold_abs=0.1
            )
            
            # Create markers
            markers = np.zeros_like(best_binary_mask)
            if len(local_max_coords) > 0:
                markers[tuple(local_max_coords.T)] = np.arange(1, len(local_max_coords) + 1)
            
            # Apply watershed
            labels = watershed(-distance, markers, mask=best_binary_mask)
            num_spots = len(np.unique(labels)) - 1  # Subtract 1 for background
            
            watershed_results.append((min_distance, labels, num_spots))
        
        # Show watershed results
        if show_steps:
            fig, axes = plt.subplots(1, len(min_distances), figsize=(20, 4))
            
            for i, (min_distance, labels, num_spots) in enumerate(watershed_results):
                # Create colored labels for visualization
                from skimage.color import label2rgb
                colored_labels = label2rgb(labels, image=image, bg_label=0)
                
                axes[i].imshow(colored_labels)
                axes[i].set_title(f'Watershed (min_dist={min_distance}, spots={num_spots})')
                axes[i].axis('off')
            
            plt.tight_layout()
            plt.show()
        
        # Find best watershed result
        best_watershed = max(watershed_results, key=lambda x: x[2])
        print(f"Best watershed parameters: min_distance={best_watershed[0]} with {best_watershed[2]} spots")
        
        # Return best watershed result
        return {
            'heatmap': heatmap,
            'best_threshold': best_threshold,
            'best_binary_mask': best_binary_mask,
            'best_watershed_min_distance': best_watershed[0],
            'best_watershed_labels': best_watershed[1],
            'num_spots': best_watershed[2]
        }
    else:
        # Return best threshold result
        return {
            'heatmap': heatmap,
            'best_threshold': best_threshold,
            'best_binary_mask': best_binary_mask,
            'num_components': num_components[best_idx]
        }

def fix_spot_detection(image, heatmap, threshold=0.15, min_spot_size=3, min_distance=5):
    """
    Fix spot detection by using a lower threshold and watershed segmentation
    
    Args:
        image: Input image (numpy array)
        heatmap: Prediction heatmap (numpy array)
        threshold: Confidence threshold
        min_spot_size: Minimum spot size
        min_distance: Minimum distance between spots
        
    Returns:
        Dictionary with detection results
    """
    # Create binary mask
    binary_mask = (heatmap > threshold).astype(np.uint8)
    
    # Remove small objects
    if min_spot_size > 0:
        binary_mask = morphology.remove_small_objects(
            binary_mask.astype(bool), min_size=min_spot_size
        ).astype(np.uint8)
    
    # Get distance transform
    distance = distance_transform_edt(binary_mask)
    
    # Find local maxima (spot centers)
    local_max_coords = peak_local_max(
        distance,
        min_distance=min_distance,
        labels=binary_mask,
        threshold_abs=0.1  # Lower threshold to find more peaks
    )
    
    # If no local maxima found, try with lower threshold
    if len(local_max_coords) == 0:
        local_max_coords = peak_local_max(
            heatmap,  # Use raw heatmap instead of distance transform
            min_distance=min_distance,
            threshold_abs=threshold * 0.8  # Lower threshold by 20%
        )
    
    # Create markers for watershed
    markers = np.zeros_like(binary_mask)
    if len(local_max_coords) > 0:
        markers[tuple(local_max_coords.T)] = np.arange(1, len(local_max_coords) + 1)
    
    # Apply watershed segmentation
    labels = watershed(-distance, markers, mask=binary_mask)
    num_spots = len(np.unique(labels)) - 1  # Subtract 1 for background
    
    # Get region properties
    props = measure.regionprops(labels, intensity_image=heatmap)
    
    # Extract spot properties
    spot_props = []
    for prop in props:
        spot_props.append({
            'id': prop.label,
            'centroid': prop.centroid,
            'area': prop.area,
            'mean_intensity': prop.mean_intensity,
            'max_intensity': prop.max_intensity,
            'bbox': prop.bbox
        })
    
    # Create visualization
    rgb_mask = np.zeros((*image.shape, 3))
    rgb_mask[..., 0] = image  # Red channel
    rgb_mask[..., 1] = image  # Green channel
    rgb_mask[..., 2] = image  # Blue channel
    
    # Overlay spots
    for prop in spot_props:
        y, x = prop['centroid']
        r = int(np.sqrt(prop['area'] / np.pi))
        
        # Draw circle
        from skimage.draw import circle_perimeter
        rr, cc = circle_perimeter(int(y), int(x), r, shape=image.shape)
        rgb_mask[rr, cc, 0] = 1.0  # Red
        rgb_mask[rr, cc, 1] = 0.0  # Green
        rgb_mask[rr, cc, 2] = 0.0  # Blue
    
    return {
        'binary_mask': binary_mask,
        'labeled_mask': labels,
        'num_spots': num_spots,
        'spot_props': spot_props,
        'visualization': rgb_mask
    }
import torch
import torch.nn as nn
import torch.nn.functional as F

class SimpleSpotDetector(nn.Module):
    """Simplified spot detector that actually works"""
    def __init__(self, in_ch=1, base_ch=32):
        super().__init__()
        
        # Simple U-Net encoder
        self.enc1 = nn.Sequential(
            nn.Conv2d(in_ch, base_ch, 3, padding=1),
            nn.BatchNorm2d(base_ch),
            nn.ReLU(inplace=True),
            nn.Conv2d(base_ch, base_ch, 3, padding=1),
            nn.BatchNorm2d(base_ch),
            nn.ReLU(inplace=True)
        )
        
        self.enc2 = nn.Sequential(
            nn.MaxPool2d(2),
            nn.Conv2d(base_ch, base_ch*2, 3, padding=1),
            nn.BatchNorm2d(base_ch*2),
            nn.ReLU(inplace=True),
            nn.Conv2d(base_ch*2, base_ch*2, 3, padding=1),
            nn.BatchNorm2d(base_ch*2),
            nn.ReLU(inplace=True)
        )
        
        self.enc3 = nn.Sequential(
            nn.MaxPool2d(2),
            nn.Conv2d(base_ch*2, base_ch*4, 3, padding=1),
            nn.BatchNorm2d(base_ch*4),
            nn.ReLU(inplace=True),
            nn.Conv2d(base_ch*4, base_ch*4, 3, padding=1),
            nn.BatchNorm2d(base_ch*4),
            nn.ReLU(inplace=True)
        )
        
        # Simple U-Net decoder
        self.up1 = nn.ConvTranspose2d(base_ch*4, base_ch*2, 2, stride=2)
        self.dec1 = nn.Sequential(
            nn.Conv2d(base_ch*4, base_ch*2, 3, padding=1),
            nn.BatchNorm2d(base_ch*2),
            nn.ReLU(inplace=True),
            nn.Conv2d(base_ch*2, base_ch*2, 3, padding=1),
            nn.BatchNorm2d(base_ch*2),
            nn.ReLU(inplace=True)
        )
        
        self.up2 = nn.ConvTranspose2d(base_ch*2, base_ch, 2, stride=2)
        self.dec2 = nn.Sequential(
            nn.Conv2d(base_ch*2, base_ch, 3, padding=1),
            nn.BatchNorm2d(base_ch),
            nn.ReLU(inplace=True),
            nn.Conv2d(base_ch, base_ch, 3, padding=1),
            nn.BatchNorm2d(base_ch),
            nn.ReLU(inplace=True)
        )
        
        # Output heads
        self.semantic_head = nn.Conv2d(base_ch, 1, 1)
        self.centroid_head = nn.Conv2d(base_ch, 1, 1)
        
    def forward(self, x):
        # Encoder
        e1 = self.enc1(x)
        e2 = self.enc2(e1)
        e3 = self.enc3(e2)
        
        # Decoder
        d1 = self.up1(e3)
        d1 = torch.cat([d1, e2], dim=1)
        d1 = self.dec1(d1)
        
        d2 = self.up2(d1)
        d2 = torch.cat([d2, e1], dim=1)
        d2 = self.dec2(d2)
        
        return {
            'semantic': self.semantic_head(d2),
            'centroid': self.centroid_head(d2)
        }

class SimpleSpotLoss(nn.Module):
    """Simple loss that actually works"""
    def __init__(self):
        super().__init__()
        
    def focal_loss(self, pred, target, alpha=0.25, gamma=2.0):
        bce = F.binary_cross_entropy_with_logits(pred, target, reduction='none')
        pt = torch.exp(-bce)
        return (alpha * (1-pt)**gamma * bce).mean()
    
    def dice_loss(self, pred, target, smooth=1e-6):
        pred_sig = torch.sigmoid(pred)
        intersection = (pred_sig * target).sum()
        union = pred_sig.sum() + target.sum()
        return 1 - (2 * intersection + smooth) / (union + smooth)
    
    def forward(self, outputs, targets):
        # Simple 2-channel targets: [semantic, centroid]
        gt_sem = targets[:, 0:1]
        gt_cent = targets[:, 0:1]  # Use same mask for centroids
        
        # Semantic loss
        sem_loss = self.focal_loss(outputs['semantic'], gt_sem) + self.dice_loss(outputs['semantic'], gt_sem)
        
        # Centroid loss  
        cent_loss = self.focal_loss(outputs['centroid'], gt_cent) + self.dice_loss(outputs['centroid'], gt_cent)
        
        total_loss = sem_loss + cent_loss
        
        return total_loss, {
            'semantic': sem_loss,
            'centroid': cent_loss,
            'total': total_loss
        }
# Use the model's built-in instance head first

def inference_with_model_instance(model, image, device='cuda'):
    """Use model's instance segmentation head"""
    
    model.eval()
    with torch.no_grad():
        # ... image preprocessing ...
        
        outputs = model(image)
        
        # Get all outputs
        sem = torch.sigmoid(outputs['semantic'])[0, 0].cpu().numpy()
        instance = torch.sigmoid(outputs['heatmaps'])[0, 0].cpu().numpy()  # Instance head
        centroid = torch.sigmoid(outputs['heatmaps'])[0, 1].cpu().numpy()  # Centroid head
        distance = torch.sigmoid(outputs['distance'])[0, 0].cpu().numpy()
        boundary = torch.sigmoid(outputs['boundary'])[0, 0].cpu().numpy()
        
        # Extract spots from centroid map
        from skimage.feature import peak_local_max
        coordinates = peak_local_max(centroid, min_distance=2, threshold_abs=0.3)
        
        spots = []
        for y, x in coordinates:
            score = centroid[y, x]
            spots.append([y, x, score])
        
        return {
            'spots': np.array(spots),
            'semantic': sem,           # All spots combined
            'instance': instance,      # Model's instance prediction
            'centroid': centroid,      # Centroid heatmap
            'distance': distance,      # Distance transform
            'boundary': boundary       # Boundary detection
        }

# BEST COMBINATION for accuracy:
# 1. Use CENTROID map for spot detection (most accurate coordinates)
# 2. Use DISTANCE map for instance segmentation (best shape info)  
# 3. Use BOUNDARY map to separate touching spots
# 4. Use SEMANTIC as fallback/validation
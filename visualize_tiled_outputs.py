import torch
import numpy as np
import matplotlib.pyplot as plt
import tifffile
from pathlib import Path
from OptimizedSpotDetection_model import OptimizedSpotDetectionModel

def visualize_tiled_outputs(
    model_path: str,
    image_path: str,
    output_dir: str,
    patch_size: int = 256,
    overlap: int = 32,
    threshold: float = 0.5
):
    """
    Visualize model outputs with tiling for large images
    """
    # Create output directory
    output_dir = Path(output_dir)
    output_dir.mkdir(exist_ok=True, parents=True)
    
    # Load model
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = OptimizedSpotDetectionModel(in_channels=1, base_filters=32)
    
    checkpoint = torch.load(model_path, map_location=device)
    if 'model_state_dict' in checkpoint:
        model.load_state_dict(checkpoint['model_state_dict'])
    else:
        model.load_state_dict(checkpoint)
    
    model.to(device)
    model.eval()
    
    # Load image
    image = tifffile.imread(image_path).astype(np.float32)
    if len(image.shape) == 3:
        image = image[:, :, 0] if image.shape[2] > 1 else image.squeeze()
    
    # Normalize image
    image_norm = image / (image.max() + 1e-8)
    H, W = image_norm.shape
    
    # Tiling parameters
    stride = patch_size - overlap
    n_h = (H + stride - 1) // stride
    n_w = (W + stride - 1) // stride
    
    # Initialize output arrays
    heatmap_full = np.zeros((H, W), dtype=np.float32)
    weight_map = np.zeros((H, W), dtype=np.float32)
    
    # Process image in tiles
    with torch.no_grad():
        for i in range(n_h):
            for j in range(n_w):
                # Extract tile coordinates
                start_h = i * stride
                start_w = j * stride
                end_h = min(start_h + patch_size, H)
                end_w = min(start_w + patch_size, W)
                
                # Extract and pad tile if needed
                patch = image_norm[start_h:end_h, start_w:end_w]
                pad_h = patch_size - patch.shape[0]
                pad_w = patch_size - patch.shape[1]
                if pad_h > 0 or pad_w > 0:
                    patch = np.pad(patch, ((0, pad_h), (0, pad_w)), 'reflect')
                
                # Convert to tensor and run inference
                patch_tensor = torch.from_numpy(patch).unsqueeze(0).unsqueeze(0).to(device)
                outputs = model(patch_tensor)
                heatmap = torch.sigmoid(outputs['heatmap']).cpu().numpy()[0, 0]
                
                # Remove padding if needed
                if pad_h > 0 or pad_w > 0:
                    heatmap = heatmap[:patch_size-pad_h, :patch_size-pad_w]
                
                # Create weight map for blending
                weight = np.ones_like(heatmap)
                if overlap > 0:
                    # Taper edges for smooth blending
                    taper = min(overlap // 2, 16)
                    if start_h > 0:
                        weight[:taper, :] *= np.linspace(0, 1, taper)[:, None]
                    if end_h < H:
                        weight[-taper:, :] *= np.linspace(1, 0, taper)[:, None]
                    if start_w > 0:
                        weight[:, :taper] *= np.linspace(0, 1, taper)[None, :]
                    if end_w < W:
                        weight[:, -taper:] *= np.linspace(1, 0, taper)[None, :]
                
                # Accumulate weighted results
                heatmap_full[start_h:end_h, start_w:end_w] += heatmap * weight
                weight_map[start_h:end_h, start_w:end_w] += weight
    
    # Normalize by weights
    heatmap_full /= np.maximum(weight_map, 1e-8)
    
    # Create binary mask
    binary_mask = (heatmap_full > threshold).astype(np.uint8) * 255
    
    # Save outputs
    tifffile.imwrite(output_dir / 'original_image.tif', image)
    tifffile.imwrite(output_dir / 'heatmap.tif', (heatmap_full * 255).astype(np.uint8))
    tifffile.imwrite(output_dir / 'binary_mask.tif', binary_mask)
    
    # Visualize results
    plt.figure(figsize=(15, 5))
    
    plt.subplot(1, 3, 1)
    plt.imshow(image_norm, cmap='gray')
    plt.title('Original Image')
    plt.axis('off')
    
    plt.subplot(1, 3, 2)
    plt.imshow(heatmap_full, cmap='hot')
    plt.title('Heatmap')
    plt.axis('off')
    
    plt.subplot(1, 3, 3)
    plt.imshow(binary_mask, cmap='gray')
    plt.title(f'Binary Mask (threshold={threshold})')
    plt.axis('off')
    
    plt.tight_layout()
    plt.savefig(output_dir / 'results.png', dpi=150)
    plt.close()
    
    print(f"All outputs saved to {output_dir}")
    
    return {
        'image': image,
        'heatmap': heatmap_full,
        'binary_mask': binary_mask
    }

if __name__ == "__main__":
    import argparse
    parser = argparse.ArgumentParser(description='Visualize tiled model outputs')
    parser.add_argument('--model', type=str, required=True, help='Path to model weights')
    parser.add_argument('--image', type=str, required=True, help='Path to input image')
    parser.add_argument('--output', type=str, required=True, help='Output directory')
    parser.add_argument('--patch_size', type=int, default=256, help='Patch size for tiling')
    parser.add_argument('--overlap', type=int, default=32, help='Overlap between patches')
    parser.add_argument('--threshold', type=float, default=0.5, help='Threshold for binary mask')
    args = parser.parse_args()
    
    visualize_tiled_outputs(
        args.model, 
        args.image, 
        args.output, 
        args.patch_size, 
        args.overlap, 
        args.threshold
    )
# Quick fixes for speed issues

# 1. Fix deprecated GradScaler
from torch.amp import GradScaler
scaler = GradScaler('cuda')

# 2. Fix deprecated autocast
from torch.amp import autocast
with autocast('cuda'):
    # your training code here
    pass

# 3. Speed optimizations
torch.backends.cudnn.benchmark = True
torch.backends.cudnn.deterministic = False

# 4. Increase batch size if memory allows
# batch_size = 16  # instead of 8

# 5. Reduce num_workers if causing overhead
# num_workers = 2  # instead of 4

# 6. Use pin_memory for faster data transfer
# pin_memory = True in DataLoader

# 7. Use non_blocking transfers
# images = images.to(device, non_blocking=True)
# targets = targets.to(device, non_blocking=True)
import torch
import torch.nn as nn
import torch.nn.functional as F

class SEBlock(nn.Module):
    def __init__(self, channels, reduction=16):
        super().__init__()
        self.avg = nn.AdaptiveAvgPool2d(1)
        self.fc = nn.Sequential(
            nn.Linear(channels, channels//reduction, bias=False),
            nn.ReLU(inplace=True),
            nn.Linear(channels//reduction, channels, bias=False),
            nn.Sigmoid()
        )
    def forward(self, x):
        b,c,_,_ = x.shape
        y = self.avg(x).view(b,c)
        y = self.fc(y).view(b,c,1,1)
        return x * y

class SepASPP(nn.Module):
    def __init__(self, in_ch, out_ch, rates=[1,2,3]):  # FIXED: Smaller rates for small spots
        super().__init__()
        self.convs = nn.ModuleList([
            nn.Sequential(
                nn.Conv2d(in_ch, in_ch, kernel_size=3, padding=r, dilation=r, groups=in_ch, bias=False),
                nn.BatchNorm2d(in_ch),
                nn.ReLU(inplace=True),
                nn.Conv2d(in_ch, out_ch, kernel_size=1, bias=False),
                nn.BatchNorm2d(out_ch),
                nn.ReLU(inplace=True),
            )
            for r in rates
        ])
        self.project = nn.Conv2d(len(rates)*out_ch, out_ch, kernel_size=1, bias=False)
        self.dropout = nn.Dropout2d(0.1)

    def forward(self, x):
        feats = [conv(x) for conv in self.convs]
        x = self.project(torch.cat(feats, dim=1))
        return self.dropout(x)

class AdaptiveSpotDetector(nn.Module):
    def __init__(self, in_ch=1, base_ch=32, num_heatmaps=2, max_offset=16, dropout_p=0.2):
        super().__init__()
        
        # FIXED: Lighter stem - no stride=2 to preserve resolution
        self.stem = nn.Sequential(
            nn.Conv2d(in_ch, base_ch, 3, padding=1, bias=False),  # No stride=2
            nn.BatchNorm2d(base_ch),
            nn.ReLU(inplace=True),
        )
        
        self.enc1 = self._enc_block(base_ch, base_ch*2)
        self.enc2 = self._enc_block(base_ch*2, base_ch*4)
        self.enc3 = self._enc_block(base_ch*4, base_ch*8)
        self.enc4 = self._enc_block(base_ch*8, base_ch*16)

        self.bottleneck = nn.Sequential(
            nn.Conv2d(base_ch*16, base_ch*16, 3, padding=1, bias=False),
            nn.BatchNorm2d(base_ch*16),
            nn.ReLU(inplace=True),
            nn.Dropout2d(dropout_p),
        )
        self.se = SEBlock(base_ch*16)
        self.aspp = SepASPP(base_ch*16, base_ch*16, rates=[1,2,3])  # FIXED: Smaller rates

        # FIXED: Use transpose conv instead of bilinear upsampling
        self.dec4 = nn.ConvTranspose2d(base_ch*16, base_ch*8, 2, stride=2, bias=False)
        self.dec3 = nn.ConvTranspose2d(base_ch*8, base_ch*4, 2, stride=2, bias=False)
        self.dec2 = nn.ConvTranspose2d(base_ch*4, base_ch*2, 2, stride=2, bias=False)
        self.dec1 = nn.ConvTranspose2d(base_ch*2, base_ch, 2, stride=2, bias=False)

        # FIXED: Refinement layers after upsampling
        self.refine4 = nn.Conv2d(base_ch*8, base_ch*8, 1, bias=False)
        self.refine3 = nn.Conv2d(base_ch*4, base_ch*4, 1, bias=False)
        self.refine2 = nn.Conv2d(base_ch*2, base_ch*2, 1, bias=False)
        self.refine1 = nn.Conv2d(base_ch, base_ch, 1, bias=False)

        # Skip connections
        self.skip4 = nn.Conv2d(base_ch*8, base_ch*8, 1, bias=False)
        self.skip3 = nn.Conv2d(base_ch*4, base_ch*4, 1, bias=False)
        self.skip2 = nn.Conv2d(base_ch*2, base_ch*2, 1, bias=False)
        self.skip1 = nn.Conv2d(base_ch, base_ch, 1, bias=False)

        # Deep supervision
        self.ds4_hm = nn.Conv2d(base_ch*8, num_heatmaps, 1)
        self.ds3_hm = nn.Conv2d(base_ch*4, num_heatmaps, 1)
        self.ds2_hm = nn.Conv2d(base_ch*2, num_heatmaps, 1)

        # FIXED: Sharp final heads with no additional convolutions
        self.semantic_head = nn.Conv2d(base_ch, 1, 1)
        self.boundary_head = nn.Conv2d(base_ch, 1, 1)
        self.distance_head = nn.Conv2d(base_ch, 1, 1)
        self.heatmap_head = nn.Conv2d(base_ch, num_heatmaps, 1)
        self.flow_head = nn.Conv2d(base_ch, 2, 1)  # FIXED: Direct 1x1, no 3x3
        
        self.max_offset = max_offset

    def _enc_block(self, in_c, out_c):
        return nn.Sequential(
            nn.Conv2d(in_c, out_c, 3, padding=1, bias=False),
            nn.BatchNorm2d(out_c),
            nn.ReLU(inplace=True),
            nn.Conv2d(out_c, out_c, 3, padding=1, bias=False),
            nn.BatchNorm2d(out_c),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(2,2)
        )

    def forward(self, x):
        # Encoder
        x0 = self.stem(x)
        x1 = self.enc1(x0)
        x2 = self.enc2(x1)
        x3 = self.enc3(x2)
        x4 = self.enc4(x3)

        # Bottleneck
        xb = self.bottleneck(x4)
        xb = self.se(xb)
        xb = self.aspp(xb)

        # FIXED: Decoder with transpose conv + refinement
        d4 = self.dec4(xb)
        d4 = self.refine4(d4)
        if d4.shape[2:] != x4.shape[2:]:
            d4 = F.interpolate(d4, size=x4.shape[2:], mode='nearest')  # Use nearest, not bilinear
        d4 = d4 + self.skip4(x4)

        d3 = self.dec3(d4)
        d3 = self.refine3(d3)
        if d3.shape[2:] != x3.shape[2:]:
            d3 = F.interpolate(d3, size=x3.shape[2:], mode='nearest')
        d3 = d3 + self.skip3(x3)

        d2 = self.dec2(d3)
        d2 = self.refine2(d2)
        if d2.shape[2:] != x2.shape[2:]:
            d2 = F.interpolate(d2, size=x2.shape[2:], mode='nearest')
        d2 = d2 + self.skip2(x2)

        d1 = self.dec1(d2)
        d1 = self.refine1(d1)
        if d1.shape[2:] != x1.shape[2:]:
            d1 = F.interpolate(d1, size=x1.shape[2:], mode='nearest')
        d1 = d1 + self.skip1(x1)

        # FIXED: Final feature at x1 resolution, then upsample only outputs
        feat = d1

        # Deep supervision (upsample to input size)
        ds4_h = F.interpolate(self.ds4_hm(d4), size=x.shape[2:], mode='nearest')
        ds3_h = F.interpolate(self.ds3_hm(d3), size=x.shape[2:], mode='nearest')
        ds2_h = F.interpolate(self.ds2_hm(d2), size=x.shape[2:], mode='nearest')

        # FIXED: Final heads applied to feat, then upsample
        sem_out = F.interpolate(self.semantic_head(feat), size=x.shape[2:], mode='nearest')
        bnd_out = F.interpolate(self.boundary_head(feat), size=x.shape[2:], mode='nearest')
        dist_out = F.interpolate(self.distance_head(feat), size=x.shape[2:], mode='nearest')
        hm_out = F.interpolate(self.heatmap_head(feat), size=x.shape[2:], mode='nearest')
        flow_out = F.interpolate(self.flow_head(feat), size=x.shape[2:], mode='nearest') * self.max_offset

        return {
            'semantic': sem_out,
            'boundary': bnd_out,
            'distance': dist_out,
            'heatmaps': hm_out,
            'flow': flow_out,
            'deep_hm': [ds4_h, ds3_h, ds2_h],
            'deep_flow': [flow_out, flow_out, flow_out],  # Simplified
        }
import torch
import numpy as np
import os
from SparseSpotDataset import Sparse<PERSON>potDataset
from semi_supervised import <PERSON><PERSON>upervisedTrainer
from torch.utils.data import <PERSON><PERSON><PERSON><PERSON>

def create_synthetic_dataset(num_samples=10, image_size=(64, 64)):
    """Create a synthetic dataset for testing pseudo-labeling"""
    dataset = SparseSpotDataset(data_dir=None)
    
    # Create synthetic images and masks
    for i in range(num_samples):
        # Create random image with spots
        image = np.zeros(image_size, dtype=np.float32)
        # Add random noise
        image += np.random.normal(0, 0.1, image_size)
        # Add random spots
        num_spots = np.random.randint(3, 10)
        mask = np.zeros(image_size, dtype=np.float32)
        
        for _ in range(num_spots):
            x = np.random.randint(5, image_size[0]-5)
            y = np.random.randint(5, image_size[1]-5)
            radius = np.random.randint(2, 5)
            
            # Create spot in image (brighter)
            for i in range(max(0, x-radius), min(image_size[0], x+radius+1)):
                for j in range(max(0, y-radius), min(image_size[1], y+radius+1)):
                    if ((i-x)**2 + (j-y)**2) <= radius**2:
                        image[i, j] += 0.5
                        mask[i, j] = 1.0
        
        # Normalize image
        image = np.clip(image, 0, 1)
        
        # Create confidence mask (all 1s for labeled data)
        confidence = mask.copy()
        
        # Add to dataset
        dataset.images.append(image)
        dataset.masks.append(mask)
        dataset.confidence_masks.append(confidence)
    
    return dataset

def create_unlabeled_dataset(labeled_dataset, num_samples=5):
    """Create unlabeled dataset from labeled dataset by hiding labels"""
    unlabeled_dataset = SparseSpotDataset(data_dir=None)
    
    # Copy images but set masks to zeros
    for i in range(min(num_samples, len(labeled_dataset))):
        if isinstance(labeled_dataset.images[i], str):
            # Skip file paths
            continue
            
        # Copy image
        unlabeled_dataset.images.append(labeled_dataset.images[i].copy())
        
        # Create empty mask
        mask_shape = labeled_dataset.masks[i].shape if isinstance(labeled_dataset.masks[i], np.ndarray) else (64, 64)
        unlabeled_dataset.masks.append(np.zeros(mask_shape, dtype=np.float32))
        
        # Create zero confidence mask
        unlabeled_dataset.confidence_masks.append(np.zeros(mask_shape, dtype=np.float32))
    
    return unlabeled_dataset

class SimpleModel(torch.nn.Module):
    """Simple CNN model for testing"""
    def __init__(self):
        super().__init__()
        self.conv1 = torch.nn.Conv2d(1, 16, kernel_size=3, padding=1)
        self.conv2 = torch.nn.Conv2d(16, 32, kernel_size=3, padding=1)
        self.conv3 = torch.nn.Conv2d(32, 1, kernel_size=3, padding=1)
        self.relu = torch.nn.ReLU()
        self.sigmoid = torch.nn.Sigmoid()
    
    def forward(self, x):
        x = self.relu(self.conv1(x))
        x = self.relu(self.conv2(x))
        x = self.conv3(x)
        return {'heatmap': x}  # Return as dict to match expected format

class SimpleLoss(torch.nn.Module):
    """Simple loss function for testing"""
    def __init__(self):
        super().__init__()
        self.bce = torch.nn.BCEWithLogitsLoss()
    
    def forward(self, outputs, targets):
        loss = self.bce(outputs['heatmap'], targets['masks'])
        return loss, {'bce': loss.item()}

def test_semi_supervised_learning():
    """Test the full semi-supervised learning pipeline"""
    print("Creating synthetic datasets...")
    labeled_dataset = create_synthetic_dataset(num_samples=10)
    unlabeled_dataset = create_unlabeled_dataset(labeled_dataset, num_samples=5)
    val_dataset = create_synthetic_dataset(num_samples=3)
    
    print(f"Labeled dataset size: {len(labeled_dataset)}")
    print(f"Unlabeled dataset size: {len(unlabeled_dataset)}")
    print(f"Validation dataset size: {len(val_dataset)}")
    
    # Create model and loss
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = SimpleModel().to(device)
    loss_fn = SimpleLoss()
    optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
    
    # Create data loaders
    labeled_loader = DataLoader(labeled_dataset, batch_size=2, shuffle=True)
    unlabeled_loader = DataLoader(unlabeled_dataset, batch_size=2, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=2, shuffle=False)
    
    # Create trainer with lower threshold
    trainer = SemiSupervisedTrainer(
        model=model,
        loss_fn=loss_fn,
        labeled_loader=labeled_loader,
        unlabeled_loader=unlabeled_loader,
        val_loader=val_loader,
        optimizer=optimizer,
        device=device,
        confidence_threshold=0.5,  # Lower threshold for testing
        ignore_threshold=0.2
    )
    
    # Run self-training
    print("\nRunning self-training...")
    history = trainer.self_training(
        labeled_dataset=labeled_dataset,
        unlabeled_dataset=unlabeled_dataset,
        val_dataset=val_dataset,
        batch_size=2,
        num_workers=2,
        epochs_per_iteration=3
    )
    
    # Check if pseudo-labels were added
    print("\nChecking unlabeled dataset after self-training:")
    total_mask_sum = 0
    total_conf_sum = 0
    for i in range(len(unlabeled_dataset)):
        mask_sum = np.sum(unlabeled_dataset.masks[i])
        conf_sum = np.sum(unlabeled_dataset.confidence_masks[i])
        total_mask_sum += mask_sum
        total_conf_sum += conf_sum
        print(f"  Sample {i+1}: Mask sum = {mask_sum:.2f}, Confidence sum = {conf_sum:.2f}")
    
    print(f"\nTotal mask sum: {total_mask_sum:.2f}")
    print(f"Total confidence sum: {total_conf_sum:.2f}")
    
    if total_mask_sum > 0:
        print("\n✅ Semi-supervised learning is working! Pseudo-labels were added.")
    else:
        print("\n❌ Semi-supervised learning is NOT working! No pseudo-labels were added.")
    
    # Print training history
    print("\nTraining history:")
    for key, values in history.items():
        if len(values) > 0:
            print(f"  {key}: {values}")

if __name__ == "__main__":
    test_semi_supervised_learning()
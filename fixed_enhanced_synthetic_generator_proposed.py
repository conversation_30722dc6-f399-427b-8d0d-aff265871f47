import numpy as np
import random
import cv2
from typing import Tu<PERSON>, List, Dict, Optional
from synthetic_data_generator import AdvancedSyntheticSpotGenerator

class EnhancedSyntheticSpotGenerator(AdvancedSyntheticSpotGenerator):
    """
    FIXED Enhanced generator for synthetic spot data that stores spot centroids and masks
    
    Fixed the mask threshold issue where masks were inconsistent sizes.
    """
    
    def __init__(self, 
                image_size=(256, 256),
                min_spots=5,
                max_spots=50,
                min_radius=2,
                max_radius=10,
                density_factor=1.0,
                mask_threshold=0.35,  # Now properly controls mask size
                allow_touching=True,
                shape_variation=0.3,
                add_gradients=True,
                realistic_noise=True):
        """Initialize the enhanced synthetic spot generator"""
        super().__init__(
            image_size=image_size,
            min_spots=min_spots,
            max_spots=max_spots,
            min_radius=min_radius,
            max_radius=max_radius,
            density_factor=density_factor,
            mask_threshold=mask_threshold,
            allow_touching=allow_touching,
            shape_variation=shape_variation,
            add_gradients=add_gradients,
            realistic_noise=realistic_noise
        )
    
    def generate_sample(self) -> <PERSON><PERSON>[np.ndar<PERSON>, np.ndarray, List[Dict]]:
        """
        Generate a single enhanced synthetic sample with FIXED mask generation
        
        Returns:
            image: Synthetic image as numpy array
            mask: Mask with unique IDs for each spot
            spot_data: List of dictionaries with spot information (centroid, radius, etc.)
        """
        # Create empty image and mask
        height, width = self.image_size
        image = np.zeros((height, width), dtype=np.float32)
        mask = np.zeros((height, width), dtype=np.float32)
        
        # Determine number of spots (variable density)
        num_spots = int(random.randint(self.min_spots, self.max_spots) * self.density_factor)
        
        # Generate background
        background_value = random.uniform(self.background_level[0], self.background_level[1])
        
        # Add gradient if enabled
        if self.add_gradients:
            angle = random.uniform(0, 2 * np.pi)
            gradient_strength = random.uniform(0.05, 0.2)
            y_grid, x_grid = np.mgrid[0:height, 0:width]
            gradient = (x_grid * np.cos(angle) + y_grid * np.sin(angle)) / np.sqrt(height**2 + width**2)
            gradient = gradient * gradient_strength + background_value
        else:
            gradient = np.ones((height, width)) * background_value
        
        # Add noise
        if self.realistic_noise:
            noise_scale = random.uniform(self.noise_level[0], self.noise_level[1])
            small_noise = np.random.normal(0, noise_scale, (height//8, width//8))
            noise = cv2.resize(small_noise, (width, height))
            fine_noise = np.random.normal(0, noise_scale/2, (height, width))
            noise = noise * 0.7 + fine_noise * 0.3
        else:
            noise_level = random.uniform(self.noise_level[0], self.noise_level[1])
            noise = np.random.normal(0, noise_level, (height, width))
        
        # Combine background and noise
        image = gradient + noise
        
        # Keep track of spot centers and radii
        spot_centers = []
        spot_data = []
        
        # Generate spots with variable sizes and shapes
        for spot_id in range(1, num_spots + 1):
            # Try to place a spot without overlapping (max 10 attempts)
            for attempt in range(10):
                # Random position
                x = random.randint(10, width - 10)
                y = random.randint(10, height - 10)
                
                # Random size (radius)
                radius = random.randint(self.min_radius, self.max_radius)
                
                # Check if this spot would overlap with existing spots
                valid_position = True
                for cx, cy, cr in spot_centers:
                    dist = np.sqrt((x - cx)**2 + (y - cy)**2)
                    min_distance = (radius + cr) * 0.7
                    
                    if dist < min_distance:
                        valid_position = False
                        break
                
                if valid_position:
                    # Add to spot centers list
                    spot_centers.append((x, y, radius))
                    
                    # Random intensity
                    intensity = random.uniform(self.min_intensity, self.max_intensity)
                    
                    # Create base distance grid
                    y_grid, x_grid = np.ogrid[-y:height-y, -x:width-x]
                    
                    # Add shape variation if enabled
                    if self.shape_variation > 0:
                        angle = random.uniform(0, 2 * np.pi)
                        stretch = 1.0 + random.uniform(0, self.shape_variation)
                        
                        x_deform = x_grid * np.cos(angle) + y_grid * np.sin(angle)
                        y_deform = -x_grid * np.sin(angle) + y_grid * np.cos(angle)
                        x_deform = x_deform * stretch
                        
                        dist = np.sqrt(x_deform**2 + y_deform**2)
                    else:
                        dist = np.sqrt(x_grid**2 + y_grid**2)
                    
                    # Create spot in image
                    spot = np.exp(-(dist**2) / (2 * (radius/2)**2)) * intensity
                    
                    # Add spot to image
                    image += spot
                    
                    # FIXED: Create mask using consistent threshold
                    # Use the mask_threshold as a fraction of the maximum possible spot value
                    # This ensures consistent mask sizes regardless of spot intensity
                    max_spot_value = intensity  # Maximum value at the center
                    absolute_threshold = self.mask_threshold * max_spot_value
                    
                    # Create mask based on the spot's actual values
                    mask_spot = (spot > absolute_threshold).astype(np.float32)
                    mask[mask_spot > 0] = spot_id
                    
                    # Store spot data
                    spot_data.append({
                        'id': spot_id,
                        'x': x,
                        'y': y,
                        'radius': radius,
                        'intensity': intensity,
                        'angle': angle if self.shape_variation > 0 else 0,
                        'stretch': stretch if self.shape_variation > 0 else 1.0,
                        'mask_pixels': np.sum(mask_spot)  # Track actual mask size
                    })
                    
                    break
        
        # Ensure image values are in [0, 1]
        image = np.clip(image, 0, 1)
        
        return image, mask, spot_data
    
    def generate_batch(self, batch_size: int) -> Tuple[List[np.ndarray], List[np.ndarray], List[List[Dict]]]:
        """Generate a batch of enhanced synthetic samples"""
        images = []
        masks = []
        spot_data_list = []
        
        for _ in range(batch_size):
            image, mask, spot_data = self.generate_sample()
            images.append(image)
            masks.append(mask)
            spot_data_list.append(spot_data)
        
        return images, masks, spot_data_list
    
    def generate_dataset(self, 
                        num_samples: int, 
                        variable_params: bool = True) -> Tuple[List[np.ndarray], List[np.ndarray], List[List[Dict]]]:
        """Generate a dataset of enhanced synthetic samples"""
        images = []
        masks = []
        spot_data_list = []
        
        for i in range(num_samples):
            if variable_params and i % 10 == 0:
                self.min_spots = random.randint(3, 10)
                self.max_spots = random.randint(20, 100)
                self.min_radius = random.randint(1, 3)
                self.max_radius = random.randint(5, 15)
            
            image, mask, spot_data = self.generate_sample()
            images.append(image)
            masks.append(mask)
            spot_data_list.append(spot_data)
        
        return images, masks, spot_data_list
import os
import numpy as np
import tifffile
from tqdm import tqdm
import matplotlib.pyplot as plt

def generate_training_dataset_enhanced(num_samples, 
                                     output_dir="training_dataset_fixed",
                                     start_index=0,  # NEW: Start from specific number
                                     image_size=(512, 512),
                                     min_spots=50,
                                     max_spots=220,
                                     mask_threshold=0.35,
                                     shape_variation=0.2,  # Controls spot shape deformation
                                     save_as_8bit_tiff=False):
    """
    Enhanced training dataset generator with shape variations and index control
    
    Args:
        num_samples: Number of training samples to generate
        output_dir: Directory to save the training dataset
        start_index: Starting index for file naming (e.g., 500 to start at synthetic_000500.tif)
        image_size: Size of generated images (height, width)
        min_spots: Minimum number of spots per image
        max_spots: Maximum number of spots per image
        mask_threshold: Controls mask size (0.1=large, 0.5=small, 0.35=balanced)
        shape_variation: Amount of spot deformation (0=circles, 0.5=very elliptical)
        save_as_8bit_tiff: Whether to save as 8-bit TIFF files
    
    Returns:
        dict: Generation statistics and info
    """
    print(f"🏋️ Generating {num_samples} samples starting from index {start_index}")
    print(f"📏 Mask threshold: {mask_threshold}")
    print(f"🔄 Shape variation: {shape_variation} (0=circles, 0.5=very elliptical)")
    
    # Check if files would be overwritten
    img_dir = os.path.join(output_dir, "images")
    mask_dir = os.path.join(output_dir, "masks")
    
    if os.path.exists(img_dir):
        existing_files = [f for f in os.listdir(img_dir) if f.startswith('synthetic_')]
        if existing_files:
            print(f"📁 Found {len(existing_files)} existing files in {img_dir}")
            if start_index == 0:
                print("⚠️  WARNING: start_index=0 will overwrite existing files!")
    
    # Training parameters
    training_params = {
        'image_size': image_size,
        'min_spots': min_spots,
        'max_spots': max_spots,
        'min_radius': 2,
        'max_radius': 6,
        'density_factor': 2.5,
        'mask_threshold': mask_threshold,
        'allow_touching': True,
        'shape_variation': shape_variation,  # Key parameter for spot shapes
        'add_gradients': True,
        'realistic_noise': True
    }
    
    # Create generator
    from fixed_enhanced_synthetic_generator import EnhancedSyntheticSpotGenerator
    generator = EnhancedSyntheticSpotGenerator(**training_params)
    
    # Create directories
    os.makedirs(img_dir, exist_ok=True)
    os.makedirs(mask_dir, exist_ok=True)
    vis_dir = os.path.join(output_dir, "visualizations")
    os.makedirs(vis_dir, exist_ok=True)
    
    print(f"🔄 Generating samples {start_index} to {start_index + num_samples - 1}...")
    
    stats = {
        'total_spots': 0,
        'avg_spots_per_image': 0,
        'shape_variations': [],
        'mask_sizes': []
    }
    
    for i in tqdm(range(num_samples), desc="Generating samples"):
        file_index = start_index + i
        
        # Generate sample
        image, mask, spot_data = generator.generate_sample()
        
        # Collect statistics
        stats['total_spots'] += len(spot_data)
        for spot in spot_data:
            if 'stretch' in spot:
                stats['shape_variations'].append(spot['stretch'])
            if 'mask_pixels' in spot:
                stats['mask_sizes'].append(spot['mask_pixels'])
        
        # Save image
        img_path = os.path.join(img_dir, f"synthetic_{file_index:06d}.tif")
        tifffile.imwrite(img_path, (image * 65535).astype(np.uint16))
        
        # Save mask
        mask_path = os.path.join(mask_dir, f"synthetic_{file_index:06d}.tif")
        tifffile.imwrite(mask_path, mask.astype(np.uint16))
        
        # Save visualization every 50 samples
        if i % 50 == 0:
            vis_path = os.path.join(vis_dir, f"synthetic_{file_index:06d}_vis.png")
            
            fig, axes = plt.subplots(1, 3, figsize=(15, 5))
            
            axes[0].imshow(image, cmap='gray')
            axes[0].set_title(f'Image {file_index}')
            axes[0].axis('off')
            
            axes[1].imshow(mask, cmap='nipy_spectral')
            axes[1].set_title(f'Mask ({len(spot_data)} spots)')
            axes[1].axis('off')
            
            axes[2].imshow(image, cmap='gray')
            axes[2].imshow(mask > 0, cmap='Reds', alpha=0.5)
            axes[2].set_title('Overlay')
            axes[2].axis('off')
            
            plt.tight_layout()
            plt.savefig(vis_path, dpi=100, bbox_inches='tight')
            plt.close()
    
    # Calculate final statistics
    stats['avg_spots_per_image'] = stats['total_spots'] / num_samples
    
    print(f"✅ Generated {num_samples} samples (indices {start_index}-{start_index + num_samples - 1})")
    print(f"📊 Statistics:")
    print(f"   Total spots: {stats['total_spots']}")
    print(f"   Avg spots per image: {stats['avg_spots_per_image']:.1f}")
    if stats['shape_variations']:
        print(f"   Shape stretch range: {min(stats['shape_variations']):.2f}-{max(stats['shape_variations']):.2f}")
    if stats['mask_sizes']:
        print(f"   Mask size range: {min(stats['mask_sizes'])}-{max(stats['mask_sizes'])} pixels")
    print(f"📁 Location: {output_dir}")
    
    return stats

def explain_shape_variations():
    """
    Explain how shape variations work in the synthetic generator
    """
    print("🔄 Shape Variations Explained:")
    print()
    print("The shape_variation parameter controls how much spots deviate from perfect circles:")
    print()
    print("How it works:")
    print("1. Random angle (0-2π) determines deformation direction")
    print("2. Random stretch factor (1.0 to 1.0+shape_variation) applied")
    print("3. Spot coordinates rotated and stretched along one axis")
    print()
    print("Values:")
    print("• 0.0  = Perfect circles")
    print("• 0.1  = Slight ellipses (10% stretch)")
    print("• 0.2  = Moderate ellipses (20% stretch)")
    print("• 0.3  = Noticeable ellipses (30% stretch)")
    print("• 0.5  = Very elliptical (50% stretch)")
    print()
    print("Code implementation:")
    print("```python")
    print("angle = random.uniform(0, 2 * np.pi)")
    print("stretch = 1.0 + random.uniform(0, shape_variation)")
    print("x_deform = x_grid * np.cos(angle) + y_grid * np.sin(angle)")
    print("y_deform = -x_grid * np.sin(angle) + y_grid * np.cos(angle)")
    print("x_deform = x_deform * stretch  # Apply stretch")
    print("```")

def visualize_shape_variations():
    """
    Create a visualization showing different shape variation levels
    """
    variations = [0.0, 0.1, 0.2, 0.3, 0.5]
    
    fig, axes = plt.subplots(2, len(variations), figsize=(4*len(variations), 8))
    
    for i, variation in enumerate(variations):
        # Generate sample with specific shape variation
        params = {
            'image_size': (128, 128),
            'min_spots': 15,
            'max_spots': 25,
            'min_radius': 4,
            'max_radius': 8,
            'shape_variation': variation,
            'mask_threshold': 0.35,
            'add_gradients': False,
            'realistic_noise': False
        }
        
        from fixed_enhanced_synthetic_generator import EnhancedSyntheticSpotGenerator
        gen = EnhancedSyntheticSpotGenerator(**params)
        img, mask, data = gen.generate_sample()
        
        # Show image
        axes[0, i].imshow(img, cmap='gray')
        axes[0, i].set_title(f'Shape Variation: {variation}')
        axes[0, i].axis('off')
        
        # Show mask overlay
        axes[1, i].imshow(img, cmap='gray')
        axes[1, i].imshow(mask > 0, cmap='Reds', alpha=0.6)
        axes[1, i].set_title(f'{len(data)} spots')
        axes[1, i].axis('off')
    
    plt.tight_layout()
    plt.show()

# Usage examples:
def usage_examples():
    print("📝 Usage Examples:")
    print()
    print("# Generate first batch (0-499):")
    print("stats1 = generate_training_dataset_enhanced(500, start_index=0)")
    print()
    print("# Generate second batch (500-999) - won't overwrite first batch:")
    print("stats2 = generate_training_dataset_enhanced(500, start_index=500)")
    print()
    print("# Generate with different shape variations:")
    print("stats3 = generate_training_dataset_enhanced(100, start_index=1000, shape_variation=0.3)")
    print()
    print("# Check existing files first:")
    print("import os")
    print("existing = len([f for f in os.listdir('training_dataset_fixed/images') if f.startswith('synthetic_')])")
    print("next_index = existing")
    print("stats = generate_training_dataset_enhanced(200, start_index=next_index)")

if __name__ == "__main__":
    explain_shape_variations()
    print("\n" + "="*60 + "\n")
    visualize_shape_variations()
    print("\n" + "="*60 + "\n")
    usage_examples()
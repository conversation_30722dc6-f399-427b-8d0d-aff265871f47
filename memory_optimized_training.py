import torch
import torch.nn as nn
import torch.optim as optim
import gc
from tqdm import tqdm

def train_with_memory_optimization(model, train_loader, val_loader, loss_fn, optimizer, device, num_epochs=5):
    """
    Train a model with memory optimization techniques
    
    Args:
        model: Model to train
        train_loader: Training data loader
        val_loader: Validation data loader
        loss_fn: Loss function
        optimizer: Optimizer
        device: Device to use
        num_epochs: Number of epochs to train
        
    Returns:
        Trained model and training history
    """
    # Set model to training mode
    model.train()
    
    # Initialize history
    history = {
        'train_loss': [],
        'val_loss': []
    }
    
    # Train for specified number of epochs
    for epoch in range(num_epochs):
        # Clear memory
        torch.cuda.empty_cache()
        gc.collect()
        
        # Training
        train_loss = 0.0
        model.train()
        
        # Use tqdm for progress bar
        for batch in tqdm(train_loader, desc=f"Epoch {epoch+1}/{num_epochs}"):
            # Get inputs and targets
            inputs = batch['image'].to(device)
            targets = batch['mask'].to(device)
            
            # Zero gradients
            optimizer.zero_grad()
            
            # Forward pass
            outputs = model(inputs)
            
            # Calculate loss
            loss = loss_fn(outputs, targets)
            
            # Backward pass
            loss.backward()
            
            # Update weights
            optimizer.step()
            
            # Update metrics
            train_loss += loss.item()
            
            # Clear memory after each batch
            del inputs, targets, outputs, loss
            torch.cuda.empty_cache()
        
        # Calculate average loss
        train_loss /= len(train_loader)
        history['train_loss'].append(train_loss)
        
        # Validation
        val_loss = 0.0
        model.eval()
        
        with torch.no_grad():
            for batch in tqdm(val_loader, desc="Validation"):
                # Get inputs and targets
                inputs = batch['image'].to(device)
                targets = batch['mask'].to(device)
                
                # Forward pass
                outputs = model(inputs)
                
                # Calculate loss
                loss = loss_fn(outputs, targets)
                
                # Update metrics
                val_loss += loss.item()
                
                # Clear memory after each batch
                del inputs, targets, outputs, loss
                torch.cuda.empty_cache()
        
        # Calculate average loss
        val_loss /= len(val_loader)
        history['val_loss'].append(val_loss)
        
        # Print metrics
        print(f"Epoch {epoch+1}/{num_epochs} - Train Loss: {train_loss:.4f}, Val Loss: {val_loss:.4f}")
        
        # Clear memory after each epoch
        torch.cuda.empty_cache()
        gc.collect()
    
    return model, history
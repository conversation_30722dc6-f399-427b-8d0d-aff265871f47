import torch
import torch.nn.functional as F
import numpy as np
import matplotlib.pyplot as plt
from tqdm import tqdm
import os

# ✅ AMP import: compatible with both PyTorch 1.x and 2.x
try:
    from torch.amp import GradScaler, autocast   # PyTorch 2.0+
except ImportError:
    from torch.cuda.amp import GradScaler, autocast   # PyTorch < 2.0

def train_skeleton_aware_model_optimized(model, train_loader, val_loader, num_epochs=50, device='cuda', model_dir='./'):
    """Fixed and optimized training loop"""
    criterion = SkeletonAwareLoss()
    
    # Optimized learning rate setup
    base_lr = 1e-3  # Higher starting LR
    optimizer = torch.optim.AdamW(
        model.parameters(), 
        lr=base_lr,
        weight_decay=1e-4,
        betas=(0.9, 0.999)
    )
    
    # OneCycleLR with warmup for better convergence
    scheduler = torch.optim.lr_scheduler.OneCycleLR(
        optimizer,
        max_lr=base_lr,
        epochs=num_epochs,
        steps_per_epoch=len(train_loader),
        pct_start=0.1,  # 10% warmup
        anneal_strategy='cos'
    )
    
    # Mixed precision setup
    scaler = GradScaler()
    
    model.to(device)
    best_loss = float('inf')
    train_losses, val_losses = [], []
    
    # Component loss tracking
    train_component_losses = {'semantic': [], 'sdt': [], 'skeleton': [], 'centroid': [], 'flow': []}
    val_component_losses = {'semantic': [], 'sdt': [], 'skeleton': [], 'centroid': [], 'flow': []}

    for epoch in range(num_epochs):
        # Training phase
        model.train()
        epoch_train_losses = []
        epoch_train_components = {'semantic': 0, 'sdt': 0, 'skeleton': 0, 'centroid': 0, 'flow': 0}
        
        pbar = tqdm(train_loader, desc=f"Epoch {epoch+1}/{num_epochs}")
        
        for batch_idx, (images, targets) in enumerate(pbar):
            images = images.float().to(device, non_blocking=True)
            targets = targets.float().to(device, non_blocking=True)
            
            optimizer.zero_grad(set_to_none=True)
            
            with autocast(device_type='cuda'):
                outputs = model(images)
                loss, loss_dict = criterion(outputs, targets)
            
            if torch.isnan(loss):
                continue
            
            scaler.scale(loss).backward()
            scaler.unscale_(optimizer)
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            scaler.step(optimizer)
            scaler.update()
            
            # Step scheduler after each batch (OneCycleLR requirement)
            scheduler.step()
            
            epoch_train_losses.append(loss.item())
            for k in epoch_train_components:
                if k in loss_dict:
                    epoch_train_components[k] += loss_dict[k].item()
            
            pbar.set_postfix({
                'Loss': f'{loss.item():.4f}',
                'Sem': f'{loss_dict["semantic"].item():.3f}',
                'Skel': f'{loss_dict["skeleton"].item():.3f}'
            })
        
        # Average component losses
        for k in epoch_train_components:
            epoch_train_components[k] /= len(train_loader)
            train_component_losses[k].append(epoch_train_components[k])
        train_losses.append(np.mean(epoch_train_losses))
        
        # Validation phase
        model.eval()
        epoch_val_losses = []
        epoch_val_components = {'semantic': 0, 'sdt': 0, 'skeleton': 0, 'centroid': 0, 'flow': 0}
        
        with torch.no_grad():
            for images, targets in val_loader:
                images = images.float().to(device, non_blocking=True)
                targets = targets.float().to(device, non_blocking=True)
                
                with autocast(device_type='cuda'):
                    outputs = model(images)
                    loss, loss_dict = criterion(outputs, targets)
                
                if not torch.isnan(loss):
                    epoch_val_losses.append(loss.item())
                    for k in epoch_val_components:
                        if k in loss_dict:
                            epoch_val_components[k] += loss_dict[k].item()
        
        # Average validation losses
        for k in epoch_val_components:
            epoch_val_components[k] /= len(val_loader)
            val_component_losses[k].append(epoch_val_components[k])
        val_loss = np.mean(epoch_val_losses) if epoch_val_losses else float('inf')
        val_losses.append(val_loss)
        
        # Print results
        train_comp_str = ", ".join([f"{k}: {epoch_train_components[k]:.4f}" for k in epoch_train_components])
        val_comp_str = ", ".join([f"{k}: {epoch_val_components[k]:.4f}" for k in epoch_val_components])
        
        print(f"\nEpoch {epoch+1}:")
        print(f"  Train Loss: {train_losses[-1]:.4f} | Val Loss: {val_losses[-1]:.4f}")
        print(f"  Train Components: {train_comp_str}")
        print(f"  Val Components: {val_comp_str}")
        print(f"  LR: {optimizer.param_groups[0]['lr']:.6e}")
        
        # Save best model
        if val_losses[-1] < best_loss:
            best_loss = val_losses[-1]
            torch.save({
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'epoch': epoch,
                'loss': best_loss
            }, os.path.join(model_dir, 'best_model.pth'))
            print(f"✅ Best model saved (val_loss={best_loss:.4f})")
        
        # Fixed visualization
        if epoch % 5 == 0 or epoch == num_epochs - 1:
            try:
                images, targets = next(iter(val_loader))
                images = images.float().to(device)
                with torch.no_grad():
                    with autocast(device_type='cuda'):
                        outputs = model(images)
                
                semantic = torch.sigmoid(outputs['sem_out'])[0, 0].cpu().numpy()
                centroid_map = torch.sigmoid(outputs['hm_out'])[0, 0].cpu().numpy()
                
                # Fixed SDT extraction
                if outputs['sdt_out'].shape[1] > 1:
                    probs = F.softmax(outputs['sdt_out'], dim=1)
                    bin_indices = torch.arange(0, probs.shape[1]).float().to(probs.device)
                    bin_indices = bin_indices.view(1, -1, 1, 1) / probs.shape[1]
                    sdt = torch.sum(probs * bin_indices, dim=1)[0].cpu().numpy()
                else:
                    sdt = torch.sigmoid(outputs['sdt_out'])[0, 0].cpu().numpy()
                
                skeleton = torch.sigmoid(outputs['skeleton_out'])[0, 0].cpu().numpy()
                flow = outputs['flow_out'][0].cpu().numpy() if 'flow_out' in outputs else None
                image = images[0, 0].cpu().numpy()
                
                # Fixed spot extraction - functions defined in notebook
                min_distance = 3
                try:
                    spots = extract_precise_spots(
                        centroid_map, semantic, flow, 
                        min_distance=min_distance, threshold=0.3
                    )
                except Exception as e:
                    print(f"Spot extraction error: {e}")
                    spots = []

                try:
                    plot_skeleton_aware_progress(
                        image, semantic, centroid_map, sdt, skeleton, 
                        spots, epoch, os.path.join(model_dir, 'progress')
                    )
                except Exception as e:
                    print(f"Visualization error: {e}")
            except Exception as e:
                print(f"Visualization error: {e}")
    
    # Save loss curves with component breakdown
    plt.figure(figsize=(12, 10))
    
    # Overall loss
    plt.subplot(2, 1, 1)
    plt.plot(train_losses, label='Training Loss', linewidth=2)
    plt.plot(val_losses, label='Validation Loss', linewidth=2)
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.title('Overall Loss Progress')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # Component losses
    plt.subplot(2, 1, 2)
    colors = ['b', 'r', 'g', 'm', 'c']
    for i, (k, v) in enumerate(train_component_losses.items()):
        plt.plot(v, color=colors[i], linestyle='-', label=f'Train {k}')
        plt.plot(val_component_losses[k], color=colors[i], linestyle='--', label=f'Val {k}')
    
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.title('Component Loss Progress')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(os.path.join(model_dir, 'loss_curves.png'), dpi=150)
    plt.close()
    
    return model, train_losses, val_losses
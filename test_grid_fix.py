#!/usr/bin/env python3
"""
Test script to demonstrate the grid pattern fixes
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
from OptimizedSpotDetection_model import OptimizedSpotDetectionModel
import cv2
from skimage import io
import os

def create_test_image(size=(256, 256), num_spots=20):
    """Create a test image with known spots"""
    image = np.zeros(size, dtype=np.float32)
    
    # Add random spots
    np.random.seed(42)  # For reproducibility
    for _ in range(num_spots):
        x = np.random.randint(20, size[1] - 20)
        y = np.random.randint(20, size[0] - 20)
        radius = np.random.randint(3, 8)
        intensity = np.random.uniform(0.5, 1.0)
        
        # Create Gaussian spot
        yy, xx = np.ogrid[:size[0], :size[1]]
        mask = (xx - x)**2 + (yy - y)**2 <= radius**2
        gaussian = np.exp(-((xx - x)**2 + (yy - y)**2) / (2 * (radius/2)**2))
        image += intensity * gaussian * mask
    
    # Add noise
    image += np.random.normal(0, 0.05, size)
    image = np.clip(image, 0, 1)
    
    return image

def test_model_output(model, image):
    """Test model output and check for grid patterns"""
    image_tensor = torch.from_numpy(image).unsqueeze(0).unsqueeze(0)
    
    model.eval()
    with torch.no_grad():
        outputs = model(image_tensor)
    
    if isinstance(outputs, dict):
        heatmap = torch.sigmoid(outputs['heatmap']).squeeze().numpy()
        expert_outputs = [torch.sigmoid(exp).squeeze().numpy() for exp in outputs.get('expert_outputs', [])]
        expert_weights = outputs.get('expert_weights', torch.zeros(1, 4)).squeeze().numpy()
    else:
        heatmap = torch.sigmoid(outputs).squeeze().numpy()
        expert_outputs = []
        expert_weights = np.array([])
    
    return heatmap, expert_outputs, expert_weights

def analyze_grid_pattern(heatmap):
    """Simple grid pattern analysis"""
    # Check for regular patterns using FFT
    fft = np.fft.fft2(heatmap)
    fft_magnitude = np.abs(fft)
    
    # Remove DC component
    fft_magnitude[0, 0] = 0
    
    # Find dominant frequencies
    peak_indices = np.unravel_index(np.argmax(fft_magnitude), fft_magnitude.shape)
    peak_strength = fft_magnitude[peak_indices] / np.sum(fft_magnitude)
    
    # Check uniformity
    uniformity = np.std(heatmap) / (np.mean(heatmap) + 1e-8)
    
    return {
        'peak_strength': peak_strength,
        'uniformity': uniformity,
        'is_grid_like': peak_strength > 0.01 and uniformity < 0.5
    }

def main():
    print("Testing Grid Pattern Fixes...")
    
    # Create test image
    test_image = create_test_image()
    print(f"Created test image with shape: {test_image.shape}")
    
    # Test original model (if available)
    print("\nTesting improved model...")
    model = OptimizedSpotDetectionModel(
        in_channels=1,
        base_filters=32,
        num_experts=4,
        dropout_rate=0.1
    )
    
    # Test model output
    heatmap, expert_outputs, expert_weights = test_model_output(model, test_image)
    
    # Analyze for grid patterns
    grid_analysis = analyze_grid_pattern(heatmap)
    
    print(f"Grid Analysis Results:")
    print(f"  Peak Strength: {grid_analysis['peak_strength']:.6f}")
    print(f"  Uniformity: {grid_analysis['uniformity']:.3f}")
    print(f"  Is Grid-like: {grid_analysis['is_grid_like']}")
    
    # Visualize results
    fig, axes = plt.subplots(2, 3, figsize=(15, 10))
    
    # Original image
    axes[0, 0].imshow(test_image, cmap='gray')
    axes[0, 0].set_title('Test Image')
    axes[0, 0].axis('off')
    
    # Model output
    axes[0, 1].imshow(heatmap, cmap='hot')
    axes[0, 1].set_title('Model Output')
    axes[0, 1].axis('off')
    
    # FFT magnitude
    fft_mag = np.log(np.abs(np.fft.fftshift(np.fft.fft2(heatmap))) + 1)
    axes[0, 2].imshow(fft_mag, cmap='viridis')
    axes[0, 2].set_title('FFT Magnitude')
    axes[0, 2].axis('off')
    
    # Expert outputs
    for i, expert_out in enumerate(expert_outputs[:3]):\n        axes[1, i].imshow(expert_out, cmap='hot')\n        weight = expert_weights[i] if i < len(expert_weights) else 0\n        axes[1, i].set_title(f'Expert {i+1} (w={weight:.2f})')\n        axes[1, i].axis('off')\n    \n    # Fill remaining subplots if fewer than 3 experts\n    for i in range(len(expert_outputs), 3):\n        axes[1, i].text(0.5, 0.5, 'No Expert Output', \n                       horizontalalignment='center', verticalalignment='center')\n        axes[1, i].set_title(f'Expert {i+1}')\n        axes[1, i].axis('off')\n    \n    plt.tight_layout()\n    plt.savefig('grid_pattern_test_results.png', dpi=300, bbox_inches='tight')\n    plt.show()\n    \n    print(f\"\\nTest completed. Results saved to 'grid_pattern_test_results.png'\")\n    \n    # Recommendations\n    print(\"\\nRecommendations:\")\n    if grid_analysis['is_grid_like']:\n        print(\"  ⚠️  Grid pattern detected!\")\n        print(\"  • Use the fixed_single_image_inference.py script\")\n        print(\"  • Apply post-processing smoothing\")\n        print(\"  • Check model training with better regularization\")\n    else:\n        print(\"  ✅ No significant grid pattern detected\")\n        print(\"  • Model output looks good\")\n        print(\"  • You can proceed with normal inference\")\n\nif __name__ == \"__main__\":\n    main()
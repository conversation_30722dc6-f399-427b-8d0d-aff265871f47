# Add this function to your dataloader cell

def apply_erosion_to_mask(patch_msk, kernel_size=2, iterations=1):
    """Apply erosion to each spot individually"""
    import cv2
    
    eroded_mask = np.zeros_like(patch_msk)
    kernel = np.ones((kernel_size, kernel_size), np.uint8)
    
    for iid in np.unique(patch_msk):
        if iid == 0: continue
        spot_binary = (patch_msk == iid).astype(np.uint8)
        eroded_spot = cv2.erode(spot_binary, kernel, iterations=iterations)
        eroded_mask[eroded_spot > 0] = iid
    
    return eroded_mask

# In your dataloader's __getitem__ method, replace this line:
# sem = (patch_msk > 0).astype(np.float32)

# With this:
patch_msk_eroded = apply_erosion_to_mask(patch_msk, kernel_size=2, iterations=1)
sem = (patch_msk_eroded > 0).astype(np.float32)

# Also update centroids calculation to use eroded mask:
centroids, areas, spot_masks = self.get_centroids_and_sizes(
    patch_msk_eroded, self.get_valid_ids(patch_msk_eroded))
instance_mask = self.generate_instance_mask(patch_msk_eroded.shape, spot_masks)
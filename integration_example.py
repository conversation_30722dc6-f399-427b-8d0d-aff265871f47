import torch
import numpy as np
import matplotlib.pyplot as plt
from improved_spot_predictor import ImprovedSpotPredictor
from improved_metrics import ImprovedSpotMetrics
from OptimizedSpotDetection_model import OptimizedSpotDetectionModel

def integrate_direct_peak_detection(model_path, image_path, device):
    """
    Example of how to integrate direct peak detection into your workflow
    
    Args:
        model_path: Path to trained model
        image_path: Path to test image
        device: Device to use for inference
    """
    # Load model
    model = OptimizedSpotDetectionModel(
        in_channels=1,
        num_experts=3,
        base_filters=64,
        dropout_rate=0.2
    )
    model.load_state_dict(torch.load(model_path, map_location=device))
    model = model.to(device)
    model.eval()
    
    # Create improved predictor
    predictor = ImprovedSpotPredictor(
        model=model,
        device=device,
        min_distance=5,
        min_intensity=0.1,
        min_spot_size=3
    )
    
    # Test different parameters to find the best ones
    best_distance, best_intensity, _ = predictor.test_parameters(
        image_path,
        min_distances=[3, 5, 7, 10],
        min_intensities=[0.05, 0.1, 0.15, 0.2, 0.3]
    )
    
    # Update predictor with best parameters
    predictor.min_distance = best_distance
    predictor.min_intensity = best_intensity
    
    # Make prediction
    result = predictor.predict(image_path)
    
    # Visualize result
    predictor.visualize(
        image_path,
        result,
        show_spots=True,
        show_heatmap=True,
        show_experts=True
    )
    
    # Create metrics calculator with best parameters
    metrics_calculator = ImprovedSpotMetrics(
        min_distance=best_distance,
        min_intensity=best_intensity,
        iou_threshold=0.3
    )
    
    return predictor, metrics_calculator

# Example usage in training loop:
"""
# In your training script:

# Create model, optimizer, etc.
model = OptimizedSpotDetectionModel(...)
optimizer = torch.optim.AdamW(model.parameters(), lr=0.001)

# Create improved metrics calculator
metrics_calculator = ImprovedSpotMetrics(
    min_distance=5,
    min_intensity=0.1,
    iou_threshold=0.3
)

# Training loop
for epoch in range(num_epochs):
    # Training
    model.train()
    for batch in train_loader:
        # Forward pass
        outputs = model(batch['image'].to(device))
        loss = loss_fn(outputs, batch['mask'].to(device))
        
        # Backward pass
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()
        
        # Calculate metrics using improved metrics calculator
        metrics = metrics_calculator(outputs, batch['mask'].to(device))
    
    # Validation
    model.eval()
    with torch.no_grad():
        for batch in val_loader:
            outputs = model(batch['image'].to(device))
            val_metrics = metrics_calculator(outputs, batch['mask'].to(device))
    
    # After training, use improved predictor for inference
    predictor = ImprovedSpotPredictor(
        model=model,
        device=device,
        min_distance=5,
        min_intensity=0.1,
        min_spot_size=3
    )
    
    # Make prediction
    result = predictor.predict(test_image)
    
    # Visualize result
    predictor.visualize(test_image, result)
"""

# Example usage in notebook:
"""
# Import the improved predictor and metrics
from improved_spot_predictor import ImprovedSpotPredictor
from improved_metrics import ImprovedSpotMetrics

# Create predictor with your trained model
predictor = ImprovedSpotPredictor(
    model=model,
    device=device,
    min_distance=5,
    min_intensity=0.1,
    min_spot_size=3
)

# Test different parameters to find the best ones
best_distance, best_intensity, _ = predictor.test_parameters(
    test_image,
    min_distances=[3, 5, 7, 10],
    min_intensities=[0.05, 0.1, 0.15, 0.2, 0.3]
)

# Update predictor with best parameters
predictor.min_distance = best_distance
predictor.min_intensity = best_intensity

# Make prediction
result = predictor.predict(test_image)

# Visualize result
predictor.visualize(
    test_image,
    result,
    show_spots=True,
    show_heatmap=True
)

print(f"Detected {result['num_spots']} spots")
"""
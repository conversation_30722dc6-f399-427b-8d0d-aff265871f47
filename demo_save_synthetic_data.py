#!/usr/bin/env python3
"""
Demo script for generating and saving synthetic spot detection data to disk

This script demonstrates how to use the SyntheticDataSaver class to generate
synthetic images with spots and save them along with instance detection masks
where each spot has its own unique ID.

Usage:
    python demo_save_synthetic_data.py
"""

import os
import sys
import argparse
from synthetic_data_saver import SyntheticDataSaver

def main():
    parser = argparse.ArgumentParser(description='Generate and save synthetic spot detection data')
    parser.add_argument('--num_samples', type=int, default=100,
                       help='Number of synthetic samples to generate (default: 100)')
    parser.add_argument('--output_dir', type=str, default='synthetic_dataset',
                       help='Output directory for the dataset (default: synthetic_dataset)')
    parser.add_argument('--image_size', type=int, nargs=2, default=[256, 256],
                       help='Image size as width height (default: 256 256)')
    parser.add_argument('--min_spots', type=int, default=50,
                       help='Minimum number of spots per image (default: 50)')
    parser.add_argument('--max_spots', type=int, default=200,
                       help='Maximum number of spots per image (default: 200)')
    parser.add_argument('--min_radius', type=int, default=1,
                       help='Minimum spot radius (default: 1)')
    parser.add_argument('--max_radius', type=int, default=5,
                       help='Maximum spot radius (default: 5)')
    parser.add_argument('--density_factor', type=float, default=2.0,
                       help='Density factor for spot generation (default: 2.0)')
    parser.add_argument('--mask_threshold', type=float, default=0.35,
                       help='Threshold for mask generation (default: 0.35)')
    parser.add_argument('--no_visualizations', action='store_true',
                       help='Skip generating visualization images')
    parser.add_argument('--image_format', type=str, default='png', choices=['png', 'tiff', 'jpg'],
                       help='Format for saving images (default: png)')
    parser.add_argument('--mask_format', type=str, default='tiff', choices=['tiff', 'png'],
                       help='Format for saving masks (default: tiff)')
    
    args = parser.parse_args()
    
    print("🎯 Synthetic Spot Detection Data Generator")
    print("=" * 50)
    print(f"📊 Configuration:")
    print(f"   • Number of samples: {args.num_samples}")
    print(f"   • Output directory: {args.output_dir}")
    print(f"   • Image size: {args.image_size[0]}x{args.image_size[1]}")
    print(f"   • Spots per image: {args.min_spots}-{args.max_spots}")
    print(f"   • Spot radius: {args.min_radius}-{args.max_radius}")
    print(f"   • Density factor: {args.density_factor}")
    print(f"   • Mask threshold: {args.mask_threshold}")
    print(f"   • Image format: {args.image_format}")
    print(f"   • Mask format: {args.mask_format}")
    print(f"   • Generate visualizations: {not args.no_visualizations}")
    print()
    
    # Create synthetic data saver with specified parameters
    saver = SyntheticDataSaver(
        image_size=tuple(args.image_size),
        min_spots=args.min_spots,
        max_spots=args.max_spots,
        min_radius=args.min_radius,
        max_radius=args.max_radius,
        density_factor=args.density_factor,
        mask_threshold=args.mask_threshold,
        allow_touching=True,
        shape_variation=0.05,
        add_gradients=True,
        realistic_noise=True
    )
    
    # Generate and save the dataset
    try:
        saver.save_dataset_to_disk(
            num_samples=args.num_samples,
            output_dir=args.output_dir,
            save_visualizations=not args.no_visualizations,
            image_format=args.image_format,
            mask_format=args.mask_format
        )
        
        print()
        print("🎉 Dataset generation completed successfully!")
        print()
        print("📋 Next steps:")
        print(f"   1. Check the generated data in: {args.output_dir}/")
        print("   2. Load images and masks for training your model")
        print("   3. Use metadata files for detailed spot information")
        if not args.no_visualizations:
            print("   4. Review visualizations to verify data quality")
        
        # Show some example code for loading the data
        print()
        print("💡 Example code to load the data:")
        print("```python")
        print("import numpy as np")
        print("import json")
        print("from PIL import Image")
        print()
        print("# Load an image")
        print(f"image = np.array(Image.open('{args.output_dir}/images/image_000001.{args.image_format}'))")
        print()
        print("# Load corresponding mask")
        print(f"mask = np.array(Image.open('{args.output_dir}/masks/mask_000001.{args.mask_format}'))")
        print()
        print("# Load metadata")
        print(f"with open('{args.output_dir}/metadata/metadata_000001.json', 'r') as f:")
        print("    metadata = json.load(f)")
        print()
        print("# Get unique spot IDs")
        print("unique_spots = np.unique(mask[mask > 0])")
        print("print(f'Found {len(unique_spots)} spots with IDs: {unique_spots}')")
        print("```")
        
    except Exception as e:
        print(f"❌ Error during dataset generation: {str(e)}")
        sys.exit(1)

def demo_quick_generation():
    """Quick demo function for generating a small dataset"""
    print("🚀 Quick Demo: Generating 10 synthetic samples...")
    
    # Create saver with demo parameters
    saver = SyntheticDataSaver(
        image_size=(128, 128),  # Smaller for quick demo
        min_spots=10,
        max_spots=30,
        min_radius=2,
        max_radius=6,
        density_factor=1.5,
        mask_threshold=0.3
    )
    
    # Generate small demo dataset
    saver.save_dataset_to_disk(
        num_samples=10,
        output_dir="demo_synthetic_data",
        save_visualizations=True,
        image_format="png",
        mask_format="tiff"
    )
    
    print("✅ Demo completed! Check 'demo_synthetic_data/' directory")

if __name__ == "__main__":
    # Check if running as demo
    if len(sys.argv) == 1:
        print("No arguments provided. Running quick demo...")
        demo_quick_generation()
    else:
        main()

import torch
import numpy as np
from SparseSpotDataset import SparseSpotDataset

def debug_update_with_predictions():
    """Debug the update_with_predictions method in SparseSpotDataset"""
    print("Creating a test dataset...")
    dataset = SparseSpotDataset(data_dir=None)
    
    # Create a simple image and mask
    image_size = (64, 64)
    image = np.zeros(image_size, dtype=np.float32)
    mask = np.zeros(image_size, dtype=np.float32)
    confidence = np.zeros(image_size, dtype=np.float32)
    
    # Add to dataset
    dataset.images.append(image)
    dataset.masks.append(mask)
    dataset.confidence_masks.append(confidence)
    
    print(f"Initial dataset size: {len(dataset)}")
    print(f"Initial mask sum: {np.sum(dataset.masks[0])}")
    print(f"Initial confidence sum: {np.sum(dataset.confidence_masks[0])}")
    
    # Create a simple prediction
    pred = np.zeros(image_size, dtype=np.float32)
    # Add some high-confidence spots
    for i in range(10, 20):
        for j in range(10, 20):
            pred[i, j] = 0.9  # High confidence value
    
    # Convert to tensor
    pred_tensor = torch.from_numpy(pred).unsqueeze(0)  # Add batch dimension
    
    print(f"Prediction shape: {pred_tensor.shape}")
    print(f"High confidence pixels in prediction: {np.sum(pred > 0.5)}")
    
    # Manual update - bypass the method to see if it's a logic issue
    print("\nManually updating dataset...")
    binary_mask = (pred > 0.5).astype(np.float32)
    dataset.masks[0] = binary_mask.copy()
    dataset.confidence_masks[0] = pred.copy()
    
    print(f"Mask sum after manual update: {np.sum(dataset.masks[0])}")
    print(f"Confidence sum after manual update: {np.sum(dataset.confidence_masks[0])}")
    
    # Reset dataset
    dataset = SparseSpotDataset(data_dir=None)
    dataset.images.append(image)
    dataset.masks.append(mask)
    dataset.confidence_masks.append(confidence)
    
    # Try a completely different approach
    print("\nTrying a completely different approach...")
    
    # Create a new implementation of update_with_predictions
    def simple_update(dataset, image_idx, prediction, threshold=0.5):
        """Simple update function"""
        # Convert prediction to numpy if it's a tensor
        if isinstance(prediction, torch.Tensor):
            prediction = prediction.cpu().detach().numpy()
            
        # Handle different tensor shapes
        if len(prediction.shape) == 3 and prediction.shape[0] == 1:  # [1, H, W]
            prediction = prediction[0]  # Remove batch dimension
            
        # Create binary mask
        binary_mask = (prediction > threshold).astype(np.float32)
        
        # Update mask
        dataset.masks[image_idx] = binary_mask
        
        # Update confidence
        dataset.confidence_masks[image_idx] = prediction
        
        return np.sum(binary_mask)
    
    # Use the simple update function
    updated_pixels = simple_update(dataset, 0, pred_tensor)
    
    print(f"Updated pixels: {updated_pixels}")
    print(f"Mask sum after simple update: {np.sum(dataset.masks[0])}")
    print(f"Confidence sum after simple update: {np.sum(dataset.confidence_masks[0])}")
    
    if np.sum(dataset.masks[0]) > 0:
        print("\n✅ Simple update function works!")
    else:
        print("\n❌ Simple update function doesn't work!")

if __name__ == "__main__":
    debug_update_with_predictions()
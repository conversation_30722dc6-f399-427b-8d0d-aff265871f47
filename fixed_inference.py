import numpy as np
import matplotlib.pyplot as plt
from scipy.spatial.distance import cdist

def display_spots_on_image(image, spots, title="Detected Spots", color='r'):
    """
    FIXED: Display detected spots with correct coordinate system
    spots: Nx3 array [y, x, score] - consistent format
    """
    plt.figure(figsize=(6, 6))
    plt.imshow(image, cmap='gray')
    if len(spots) > 0:
        spots = np.array(spots)
        # spots[:, 1] = x coordinates, spots[:, 0] = y coordinates
        plt.scatter(spots[:, 1], spots[:, 0], c=color, s=30, marker='o', 
                   edgecolors='w', linewidths=1.5, label='Detected')
    plt.title(title)
    plt.axis('off')
    plt.legend()
    plt.show()

def calculate_detection_metrics(predictions, ground_truth, distance_threshold=3):
    """
    FIXED: Calculate metrics with consistent coordinate system
    Both inputs should be [y, x, score] format
    """
    if len(predictions) == 0 and len(ground_truth) == 0:
        return 1.0, 1.0, 1.0
    if len(predictions) == 0:
        return 0.0, 0.0, 0.0
    if len(ground_truth) == 0:
        return 0.0, 1.0, 0.0
    
    pred_points = np.array(predictions)
    gt_points = np.array(ground_truth)
    
    # Take only y, x coordinates (first 2 columns)
    if pred_points.shape[1] >= 2:
        pred_points = pred_points[:, :2]  # [y, x]
    if gt_points.shape[1] >= 2:
        gt_points = gt_points[:, :2]      # [y, x]
    
    distances = cdist(pred_points, gt_points)
    matches = distances <= distance_threshold
    
    true_positives = np.sum(np.any(matches, axis=1))
    false_positives = len(predictions) - true_positives
    false_negatives = len(ground_truth) - np.sum(np.any(matches, axis=0))
    
    precision = true_positives / (true_positives + false_positives) if (true_positives + false_positives) > 0 else 0
    recall = true_positives / (true_positives + false_negatives) if (true_positives + false_negatives) > 0 else 0
    f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
    
    return precision, recall, f1

def inference_with_nms(model, image, device='cuda', nms_threshold=0.3, 
                      flow_iters=5, flow_step=1.0):
    """
    FIXED: Inference with consistent coordinate system
    Returns spots in [y, x, score] format
    """
    from fixed_dataset import extract_precise_spots, refine_with_flow
    
    model.eval()
    with torch.no_grad():
        if isinstance(image, np.ndarray):
            if image.ndim == 2:
                image = image[None, None, ...]
            elif image.ndim == 3:
                image = image[None, ...]
            image = torch.from_numpy(image).float().to(device)
        else:
            image = image.to(device).float()
        
        if image.max() > 1.0:
            image = image / 255.0
        
        outputs = model(image)
        sem = torch.sigmoid(outputs['semantic'])[0, 0].cpu().numpy()
        cmap = torch.sigmoid(outputs['heatmaps'])[0, 1].cpu().numpy()
        flow = outputs['flow'][0].cpu().numpy()
        
        # Extract spots in [y, x, score] format
        raw_spots = extract_precise_spots(cmap, sem, min_distance=2, threshold=0.3)
        if len(raw_spots) == 0:
            return {'spots': np.empty((0,3)), 'semantic': sem, 'centroid_map': cmap, 'flow': flow}
        
        spots = np.array(raw_spots)  # [y, x, score] format
        
        # NMS
        scores = spots[:, 2]
        order = scores.argsort()[::-1]
        keep = []
        while order.size:
            i = order[0]
            keep.append(i)
            if order.size == 1:
                break
            current = spots[i, :2]  # [y, x]
            others = spots[order[1:], :2]  # [y, x]
            dists = np.linalg.norm(others - current, axis=1)
            order = order[1:][dists > nms_threshold]
        
        nms_spots = spots[keep]
        
        # Refine with flow
        refined = refine_with_flow(nms_spots, flow, n_iters=flow_iters, step_size=flow_step)
        
        return {
            'spots': refined,  # [y, x, score] format
            'semantic': sem,
            'centroid_map': cmap,
            'flow': flow
        }
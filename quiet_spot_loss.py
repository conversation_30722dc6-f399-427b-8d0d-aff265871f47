import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, Optional, Union, Tuple

class QuietSpotLoss(nn.Module):
    """Loss function for spot detection with minimal logging output"""

    def __init__(self,
                 bce_weight: float = 1.0,
                 dice_weight: float = 1.0,
                 focal_weight: float = 0.5,
                 focal_gamma: float = 2.0,
                 size_adaptive: bool = True,
                 density_aware: bool = True,
                 confidence_weighted: bool = True,
                 loss_scale: float = 10.0,
                 verbose: bool = False):
        """
        Initialize the loss function

        Args:
            bce_weight: Weight for BCE loss
            dice_weight: Weight for Dice loss
            focal_weight: Weight for Focal loss
            focal_gamma: Gamma parameter for Focal loss
            size_adaptive: Whether to use size-adaptive weighting
            density_aware: Whether to use density-aware weighting
            confidence_weighted: Whether to use confidence-weighted loss
            loss_scale: Scaling factor to increase loss magnitude
            verbose: Whether to print detailed logs (default: False)
        """
        super().__init__()
        self.bce_weight = bce_weight
        self.dice_weight = dice_weight
        self.focal_weight = focal_weight
        self.focal_gamma = focal_gamma
        self.size_adaptive = size_adaptive
        self.density_aware = density_aware
        self.confidence_weighted = confidence_weighted
        self.loss_scale = loss_scale
        self.verbose = verbose

    def forward(self, pred: torch.Tensor, target: torch.Tensor,
                confidence: Optional[torch.Tensor] = None) -> Dict[str, torch.Tensor]:
        """Calculate loss with minimal logging"""
        # Ensure inputs have shape [B, C, H, W] or [B, C, D, H, W]
        if len(pred.shape) == 3:
            pred = pred.unsqueeze(1)
        if len(target.shape) == 3:
            target = target.unsqueeze(1)
        if confidence is not None and len(confidence.shape) == 3:
            confidence = confidence.unsqueeze(1)
            
        # Handle dictionary input
        if isinstance(pred, dict):
            pred = pred.get('heatmap', pred.get('output', next(iter(pred.values()))))
        if isinstance(target, dict):
            target = target.get('masks', target.get('mask', next(iter(target.values()))))
            confidence = target.get('confidence', confidence)

        # Calculate standard losses
        bce_loss = self._binary_cross_entropy(pred, target, confidence)
        dice_loss = self._dice_loss(pred, target, confidence)
        focal_loss = self._focal_loss(pred, target, confidence)

        # Apply density-aware weighting if enabled
        if self.density_aware:
            density_weights = self._calculate_density_weights(target)
            bce_loss = bce_loss * density_weights.to(bce_loss.device)
            dice_loss = dice_loss * density_weights.to(dice_loss.device)
            focal_loss = focal_loss * density_weights.to(focal_loss.device)

        # Apply size-adaptive weighting if enabled
        if self.size_adaptive:
            size_weights = self._calculate_size_weights(target)
            bce_loss = bce_loss * size_weights.to(bce_loss.device)
            dice_loss = dice_loss * size_weights.to(dice_loss.device)
            focal_loss = focal_loss * size_weights.to(focal_loss.device)

        # Combine losses with weights
        total_loss = (
            self.bce_weight * bce_loss +
            self.dice_weight * dice_loss +
            self.focal_weight * focal_loss
        )

        # Ensure loss is a scalar by taking mean
        if total_loss.dim() > 0:
            total_loss = total_loss.mean()
        if bce_loss.dim() > 0:
            bce_loss = bce_loss.mean()
        if dice_loss.dim() > 0:
            dice_loss = dice_loss.mean()
        if focal_loss.dim() > 0:
            focal_loss = focal_loss.mean()

        # Add significant base loss to prevent near-zero values
        base_loss = 0.1
        total_loss = total_loss + base_loss
        bce_loss = bce_loss + base_loss
        dice_loss = dice_loss + base_loss
        focal_loss = focal_loss + base_loss

        # Scale up the losses to make them more significant
        total_loss = total_loss * self.loss_scale
        bce_loss = bce_loss * self.loss_scale
        dice_loss = dice_loss * self.loss_scale
        focal_loss = focal_loss * self.loss_scale

        # Return as dictionary to match expected format in trainer
        return {'loss': total_loss, 'bce': bce_loss, 'dice': dice_loss, 'focal': focal_loss}

    def _binary_cross_entropy(self, pred: torch.Tensor, target: torch.Tensor,
                            confidence: Optional[torch.Tensor] = None) -> torch.Tensor:
        """Calculate BCE loss with minimal logging"""
        # Apply sigmoid if not already applied
        pred_sig = torch.sigmoid(pred)
        
        # Ensure target values are in valid range for BCE
        target = torch.clamp(target, min=0.0, max=1.0)
        
        # Add small epsilon to predictions to avoid log(0)
        pred_sig = torch.clamp(pred_sig, min=1e-7, max=1.0-1e-7)

        # Calculate BCE with reduction='none' to apply weighting
        bce = F.binary_cross_entropy(pred_sig, target, reduction='none')

        # Apply confidence weighting if available
        if confidence is not None and self.confidence_weighted:
            bce = bce * confidence

        # Check for NaN values
        if torch.isnan(bce).any():
            if self.verbose:
                print("Warning: NaN values in BCE loss")
            bce = torch.where(torch.isnan(bce), torch.zeros_like(bce), bce)

        # Add a minimum loss value to ensure it's not too small
        bce = bce + 0.05
        
        return bce.mean()

    def _dice_loss(self, pred: torch.Tensor, target: torch.Tensor,
                   confidence: Optional[torch.Tensor] = None) -> torch.Tensor:
        """Calculate Dice loss with minimal logging"""
        # Apply sigmoid if not already applied
        pred_sig = torch.sigmoid(pred)
        
        # Ensure target values are in valid range
        target = torch.clamp(target, min=0.0, max=1.0)
        
        # Add small epsilon to predictions to avoid numerical issues
        pred_sig = torch.clamp(pred_sig, min=1e-7, max=1.0-1e-7)

        # Calculate intersection and union
        intersection = (pred_sig * target).sum(dim=(-2, -1))
        if len(pred.shape) == 5:  # 3D data
            intersection = intersection.sum(dim=-1)

        union = pred_sig.sum(dim=(-2, -1)) + target.sum(dim=(-2, -1))
        if len(pred.shape) == 5:  # 3D data
            union = union.sum(dim=-1)

        # Calculate Dice coefficient with larger epsilon for better stability
        epsilon = 1e-5
        dice = (2.0 * intersection + epsilon) / (union + epsilon)

        # Apply confidence weighting if available
        if confidence is not None and self.confidence_weighted:
            conf_sum = confidence.sum(dim=(-2, -1))
            if len(pred.shape) == 5:
                conf_sum = conf_sum.sum(dim=-1)
            # Use where instead of direct multiplication with boolean
            dice = torch.where(conf_sum > 0, dice, torch.zeros_like(dice))
            
        # Check for NaN values
        if torch.isnan(dice).any():
            if self.verbose:
                print("Warning: NaN values in Dice coefficient")
            dice = torch.where(torch.isnan(dice), torch.ones_like(dice), dice)

        # Ensure dice is in valid range
        dice = torch.clamp(dice, min=0.0, max=1.0)
        
        # Enhance the dice loss to ensure it's not too small
        dice_loss = 1.0 - dice.mean()
        dice_loss = dice_loss + 0.05
        
        return dice_loss

    def _focal_loss(self, pred: torch.Tensor, target: torch.Tensor,
                    confidence: Optional[torch.Tensor] = None) -> torch.Tensor:
        """Calculate Focal loss with minimal logging"""
        # Apply sigmoid if not already applied
        pred_sig = torch.sigmoid(pred)
        
        # Ensure target values are in valid range
        target = torch.clamp(target, min=0.0, max=1.0)
        
        # Add small epsilon to predictions to avoid numerical issues
        pred_sig = torch.clamp(pred_sig, min=1e-7, max=1.0-1e-7)

        # Calculate focal weights with clamping to avoid extreme values
        pt = target * pred_sig + (1 - target) * (1 - pred_sig)
        pt = torch.clamp(pt, min=1e-7, max=1.0)
        focal_weights = (1 - pt) ** self.focal_gamma
        
        # Enhance focal weights to make them more significant
        focal_weights = focal_weights + 0.2
        
        # Clamp focal weights to avoid extreme values
        focal_weights = torch.clamp(focal_weights, max=100.0)

        # Calculate BCE
        bce = F.binary_cross_entropy(pred_sig, target, reduction='none')

        # Apply focal weights
        focal = focal_weights * bce

        # Apply confidence weighting if available
        if confidence is not None and self.confidence_weighted:
            focal = focal * confidence
            
        # Check for NaN or inf values
        if torch.isnan(focal).any() or torch.isinf(focal).any():
            if self.verbose:
                print("Warning: NaN or Inf values in Focal loss")
            focal = torch.where(torch.isnan(focal) | torch.isinf(focal), 
                               torch.zeros_like(focal), focal)

        # Add a minimum loss value to ensure it's not too small
        focal = focal + 0.05
        
        return focal.mean()

    def _calculate_density_weights(self, target: torch.Tensor) -> torch.Tensor:
        """Calculate density-aware weights with minimal logging"""
        # Calculate spot density per image
        if len(target.shape) == 4:  # 2D data
            density = target.sum(dim=(-2, -1), keepdim=True)
        else:  # 3D data
            density = target.sum(dim=(-3, -2, -1), keepdim=True)

        # Normalize weights with better numerical stability
        # Clamp density to avoid extreme weights
        density = torch.clamp(density, min=1.0, max=1000.0)
        weights = 1.0 / (torch.sqrt(density) + 1.0)
        
        # Enhance weights to make them more significant
        weights = weights + 0.5
        
        # Clamp weights to reasonable range
        weights = torch.clamp(weights, min=0.5, max=5.0)
        
        # Check for NaN or inf values
        if torch.isnan(weights).any() or torch.isinf(weights).any():
            if self.verbose:
                print("Warning: NaN or Inf values in density weights")
            weights = torch.where(torch.isnan(weights) | torch.isinf(weights), 
                                 torch.ones_like(weights), weights)
        
        return weights

    def _calculate_size_weights(self, target: torch.Tensor) -> torch.Tensor:
        """Calculate size-adaptive weights with minimal logging"""
        # Calculate average spot size
        if len(target.shape) == 4:  # 2D data
            # Use float casting to ensure proper gradient computation
            num_spots = (target > 0.5).float().sum(dim=(-2, -1), keepdim=True)
            total_area = float(target.shape[-2] * target.shape[-1])
        else:  # 3D data
            num_spots = (target > 0.5).float().sum(dim=(-3, -2, -1), keepdim=True)
            total_area = float(target.shape[-3] * target.shape[-2] * target.shape[-1])

        # Clamp num_spots to avoid division by very small numbers
        num_spots = torch.clamp(num_spots, min=1.0, max=total_area)

        # Calculate average spot size with better numerical stability
        avg_size = total_area / num_spots
        
        # Add small epsilon to avoid numerical issues
        epsilon = 1e-5
        
        # Normalize weights with better bounds
        weights = torch.sqrt((avg_size + epsilon) / (total_area + epsilon))
        
        # Enhance weights to make them more significant
        weights = weights + 0.5
        
        # Clamp weights to reasonable range
        weights = torch.clamp(weights, min=0.5, max=5.0)
        
        # Check for NaN or inf values
        if torch.isnan(weights).any() or torch.isinf(weights).any():
            if self.verbose:
                print("Warning: NaN or Inf values in size weights")
            weights = torch.where(torch.isnan(weights) | torch.isinf(weights), 
                                 torch.ones_like(weights), weights)
        
        return weights
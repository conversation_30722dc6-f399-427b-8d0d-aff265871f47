#!/usr/bin/env python3
"""
Example script demonstrating the enhanced inference function for spot detection.
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
from enhanced_inference import enhanced_inference
from OptimizedSpotDetection_model import OptimizedSpotDetectionModel

def create_test_image(size=(512, 512), num_spots=20):
    """Create a test image with synthetic spots."""
    image = np.random.normal(0.1, 0.05, size).astype(np.float32)
    image = np.clip(image, 0, 1)
    
    # Add spots
    for _ in range(num_spots):
        x = np.random.randint(20, size[1] - 20)
        y = np.random.randint(20, size[0] - 20)
        radius = np.random.randint(3, 8)
        intensity = np.random.uniform(0.7, 1.0)
        
        # Create circular spot
        yy, xx = np.ogrid[:size[0], :size[1]]
        mask = (xx - x)**2 + (yy - y)**2 <= radius**2
        image[mask] = intensity
    
    return image

def visualize_results(image, results, save_path=None):
    """Visualize the inference results."""
    fig, axes = plt.subplots(2, 2, figsize=(12, 12))
    
    # Original image
    axes[0, 0].imshow(image, cmap='gray')
    axes[0, 0].set_title('Original Image')
    axes[0, 0].axis('off')
    
    # Instance labels
    axes[0, 1].imshow(results['instance_labels'], cmap='tab20')
    axes[0, 1].set_title(f"Instance Labels ({len(results['spots'])} spots)")
    axes[0, 1].axis('off')
    
    # Overlay spots on original
    axes[1, 0].imshow(image, cmap='gray')
    for spot in results['spots']:
        circle = plt.Circle((spot['x'], spot['y']), 
                          np.sqrt(spot['size']/np.pi), 
                          fill=False, color='red', linewidth=2)
        axes[1, 0].add_patch(circle)
        axes[1, 0].text(spot['x'], spot['y'], f"{spot['score']:.2f}", 
                       color='yellow', fontsize=8, ha='center')
    axes[1, 0].set_title('Detected Spots with Scores')
    axes[1, 0].axis('off')
    
    # Individual masks overlay
    axes[1, 1].imshow(image, cmap='gray', alpha=0.7)
    colors = plt.cm.Set3(np.linspace(0, 1, len(results['instance_masks'])))
    for i, mask in enumerate(results['instance_masks']):
        axes[1, 1].contour(mask, levels=[0.5], colors=[colors[i]], linewidths=2)
    axes[1, 1].set_title('Individual Spot Masks')
    axes[1, 1].axis('off')
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
    plt.show()

def main():
    """Main function demonstrating the enhanced inference."""
    
    # Create or load model
    model = OptimizedSpotDetectionModel(
        in_channels=1,
        base_filters=32,
        num_experts=4,
        dropout=0.1
    )
    
    # For demonstration, we'll use a randomly initialized model
    # In practice, you would load trained weights:
    # model.load_state_dict(torch.load('path/to/trained_model.pth'))
    
    # Create test image
    print("Creating test image...")
    test_image = create_test_image(size=(800, 600), num_spots=25)
    
    # Run enhanced inference
    print("Running enhanced inference...")
    results = enhanced_inference(
        model=model,
        image=test_image,
        patch_size=256,
        overlap=32,
        threshold=0.3,  # Lower threshold for demo with untrained model
        min_distance=5,
        min_size=4,
        use_nms=True,
        nms_threshold=0.3,
        use_flow=True,
        device='auto'
    )
    
    # Print results summary
    print(f"\nResults Summary:")
    print(f"- Number of detected spots: {len(results['spots'])}")
    print(f"- Instance labels shape: {results['instance_labels'].shape}")
    print(f"- Number of instance masks: {len(results['instance_masks'])}")
    
    # Print details of first few spots
    print(f"\nFirst 5 detected spots:")
    for i, spot in enumerate(results['spots'][:5]):
        print(f"  Spot {i+1}: x={spot['x']:.1f}, y={spot['y']:.1f}, "
              f"score={spot['score']:.3f}, size={spot['size']}, "
              f"intensity={spot['intensity']:.3f}")
    
    # Visualize results
    print("\nVisualizing results...")
    visualize_results(test_image, results, 'inference_results.png')
    
    # Example of accessing individual masks
    print(f"\nExample: First spot mask shape: {results['instance_masks'][0].shape if results['instance_masks'] else 'No spots detected'}")
    
    return results

if __name__ == "__main__":
    results = main()
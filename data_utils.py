import os
import numpy as np
import torch
from torch.utils.data import Dataset, DataLoader
import albumentations as A
from albumentations.pytorch import ToTensorV2
import cv2
from skimage import io, measure, morphology, filters
import random
from typing import Dict, List, Tuple, Optional, Union
from synthetic_data_generator import SyntheticSpotGenerator, AdvancedSyntheticSpotGenerator

def normalize_orientation(arr):
    """Ensure consistent orientation for all arrays"""
    if torch.is_tensor(arr):
        arr = arr.squeeze().cpu().numpy()
    # Convert to numpy and ensure 2D
    arr = np.asarray(arr)
    if arr.ndim > 2:
        arr = arr.squeeze()
    return arr

class SpotDataset(Dataset):
    """
    Dataset for spot detection with support for both real and synthetic data
    """
    def __init__(self, 
                 data_dir=None,
                 image_size=(256, 256),
                 mode='train',
                 synthetic=False,
                 synthetic_size=1000,
                 augmentation_level='strong',
                 synthetic_params=None):
        """
        Initialize the dataset
        
        Args:
            data_dir: Directory containing images and masks
                      Expected structure:
                      - data_dir/images/ - Contains image files
                      - data_dir/masks/  - Contains mask files with same names
            image_size: Size to resize images to
            mode: 'train' or 'val'
            synthetic: Whether to generate synthetic data
            synthetic_size: Number of synthetic samples to generate
            augmentation_level: Level of augmentation ('none', 'light', 'medium', 'strong')
        """
        self.data_dir = data_dir
        self.image_size = image_size
        self.mode = mode
        self.synthetic = synthetic
        self.synthetic_size = synthetic_size
        self.synthetic_params = synthetic_params
        
        # Initialize lists for images and masks
        self.images = []
        self.masks = []
        
        # Load data from disk if data_dir is provided
        if data_dir is not None:
            self._load_data_from_disk()
        
        # Generate synthetic data if requested
        if synthetic:
            self._generate_synthetic_data()
        
        # Set up transforms based on mode and augmentation level
        self.transform = self._get_transforms(mode, augmentation_level)
    
    def _load_data_from_disk(self):
        """Load images and masks with consistent orientation"""
        def load_and_normalize(file_path):
            img = io.imread(file_path)
            if len(img.shape) == 3 and img.shape[-1] > 1:
                img = cv2.cvtColor(img, cv2.COLOR_RGB2GRAY)
            return img.astype(np.float32) / np.max(img)  # Robust normalization
        
        # Check directory structure
        images_dir = os.path.join(self.data_dir, 'images')
        masks_dir = os.path.join(self.data_dir, 'masks')
        
        if os.path.exists(images_dir) and os.path.exists(masks_dir):
            image_files = sorted([f for f in os.listdir(images_dir) 
                                if f.endswith(('.png', '.jpg', '.tif', '.tiff'))])
            
            for img_file in image_files:
                mask_file = img_file  # Assume same filename for mask
                if os.path.exists(os.path.join(masks_dir, mask_file)):
                    self.images.append(os.path.join(images_dir, img_file))
                    self.masks.append(os.path.join(masks_dir, mask_file))
        else:
            files = sorted([f for f in os.listdir(self.data_dir) 
                          if f.endswith(('.png', '.jpg', '.tif', '.tiff'))])
            
            for file in files:
                if '_mask' not in file and '_conf' not in file:
                    img_path = os.path.join(self.data_dir, file)
                    base_name = os.path.splitext(file)[0]
                    mask_file = f"{base_name}_mask{os.path.splitext(file)[1]}"
                    mask_path = os.path.join(self.data_dir, mask_file)
                    
                    if os.path.exists(mask_path):
                        self.images.append(img_path)
                        self.masks.append(mask_path)
        
        print(f"Loaded {len(self.images)} image-mask pairs from {self.data_dir}")
    
    def _generate_synthetic_data(self):
        """
        Generate synthetic data with spots of variable sizes and densities using AdvancedSyntheticSpotGenerator
        
        Each spot is assigned a unique ID (intensity value)
        """
        num_samples = self.synthetic_size
    
        # Get target size for synthetic data
        target_size = self.image_size[0] if isinstance(self.image_size, tuple) else self.image_size
        image_size = (target_size, target_size)
        
        # Default parameters for the advanced generator
        params = {
            'min_spots': 5,
            'max_spots': 50,
            'min_radius': 1,
            'max_radius': 5,
            'density_factor': 8,  # Higher density of spots
            'mask_threshold': 0.36,  # Controls mask size
            'allow_touching': True,  # Allow spots to touch but not overlap
            'shape_variation': 0.05,  # More varied spot shapes
            'add_gradients': True,   # Add intensity gradients
            'realistic_noise': True  # More realistic noise patterns
        }
    
    
        # Override with user-provided parameters if available
        if self.synthetic_params:
            params.update(self.synthetic_params)
        
        # Create an instance of the advanced generator with parameters
        advanced_generator = AdvancedSyntheticSpotGenerator(
            image_size=image_size,
            **params
        )
        
        # Generate synthetic dataset
        images, masks = advanced_generator.generate_dataset(num_samples, variable_params=True)
        
        # Add to dataset
        self.images.extend(images)
        self.masks.extend(masks)
        
        print(f"Generated {num_samples} synthetic samples using AdvancedSyntheticSpotGenerator")
    
    # Replace _generate_synthetic_sample method
    def _generate_synthetic_sample(self):
        """
        Generate a single synthetic sample with spots of variable sizes and densities
        
        Returns:
            image: Synthetic image as numpy array
            mask: Mask with unique IDs for each spot
        """
        # Get target size for synthetic data
        target_size = self.image_size[0] if isinstance(self.image_size, tuple) else self.image_size
        image_size = (target_size, target_size)
        
        # Default parameters for the advanced generator
        params = {
            'min_spots': 5,
            'max_spots': 50,
            'min_radius': 1,
            'max_radius': 5,
            'density_factor': 8,  # Higher density of spots
            'mask_threshold': 0.36,  # Controls mask size
            'allow_touching': True,  # Allow spots to touch but not overlap
            'shape_variation': 0.05,  # More varied spot shapes
            'add_gradients': True,   # Add intensity gradients
            'realistic_noise': True  # More realistic noise patterns
        }
        
        # Override with user-provided parameters if available
        if self.synthetic_params:
            params.update(self.synthetic_params)
            
        # Create an instance of the advanced generator
        advanced_generator = AdvancedSyntheticSpotGenerator(
            image_size=image_size,
            **params
        )
        
        # Generate a single sample
        image, mask = advanced_generator.generate_sample()
        
        return image, mask
    
    def _get_transforms(self, mode, augmentation_level):
        """
        Get transforms based on mode and augmentation level
        
        Args:
            mode: 'train' or 'val'
            augmentation_level: 'none', 'light', 'medium', or 'strong'
            
        Returns:
            Albumentations transforms
        """
        if mode == 'val' or augmentation_level == 'none':
            # No augmentation for validation
            return create_transforms(augment=False)
        
        # Create transforms based on augmentation level
        if augmentation_level == 'light':
            return create_transforms(augment=True)
        elif augmentation_level == 'medium':
            return create_transforms(augment=True)
        elif augmentation_level == 'strong':
            return create_transforms(augment=True)
    
    def __len__(self):
        """Get dataset size"""
        return len(self.images)
    
    def __getitem__(self, idx):
        """Get a sample with consistent orientation and normalization"""
        # Get image and mask paths/data
        image_source = self.images[idx]
        mask_source = self.masks[idx]
        
        # Get target image size
        target_size = self.image_size[0] if isinstance(self.image_size, tuple) else self.image_size
        
        # Load and normalize image
        if isinstance(image_source, str):
            image = io.imread(image_source)
            if len(image.shape) == 3 and image.shape[-1] > 1:
                image = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
            image = image.astype(np.float32)
            image = (image - np.min(image)) / (np.max(image) - np.min(image) + 1e-8)
            
            # Resize image to target size
            if image.shape[0] != target_size or image.shape[1] != target_size:
                image = cv2.resize(image, (target_size, target_size), interpolation=cv2.INTER_AREA)
        else:
            image = image_source.copy()
            # Resize if needed
            if image.shape[0] != target_size or image.shape[1] != target_size:
                image = cv2.resize(image, (target_size, target_size), interpolation=cv2.INTER_AREA)
        
        # Load and normalize mask
        if isinstance(mask_source, str):
            mask = io.imread(mask_source)
            if len(mask.shape) == 3 and mask.shape[-1] > 1:
                mask = cv2.cvtColor(mask, cv2.COLOR_RGB2GRAY)
            mask = (mask > 0).astype(np.float32)
            
            # Resize mask to target size
            if mask.shape[0] != target_size or mask.shape[1] != target_size:
                mask = cv2.resize(mask, (target_size, target_size), interpolation=cv2.INTER_NEAREST)
        else:
            mask = mask_source.copy()
            if np.max(mask) > 1:
                mask = (mask > 0).astype(np.float32)
                
            # Resize if needed
            if mask.shape[0] != target_size or mask.shape[1] != target_size:
                mask = cv2.resize(mask, (target_size, target_size), interpolation=cv2.INTER_NEAREST)
        
        # Ensure consistent channel dimension
        if image.ndim == 2:
            image = np.expand_dims(image, 0)
        if mask.ndim == 2:
            mask = np.expand_dims(mask, 0)
            
        # Convert to tensors with consistent orientation
        sample = {
            'image': torch.from_numpy(image).float(),
            'mask': torch.from_numpy(mask).float()
        }
        
        # Apply augmentations if available
        if self.transform is not None:
            transformed = self.transform(image=image.squeeze(), mask=mask.squeeze())
            sample['image'] = transformed['image']
            sample['mask'] = transformed['mask']
        
        return sample


def create_transforms(augment=True, is_3d=False):
    """Create transforms that maintain consistent orientation"""
    transforms = []
    
    if augment:
        # Geometric transforms with consistent orientation
        transforms.extend([
            A.RandomRotate90(p=0.5),
            A.HorizontalFlip(p=0.5),
            A.VerticalFlip(p=0.5),
            A.Transpose(p=0.5)
        ])
        
        # Intensity transforms (safe for orientation)
        transforms.extend([
            A.OneOf([
                A.GaussNoise(p=1.0),  # Removed var_limit parameter
                A.RandomBrightnessContrast(p=1.0),
                A.RandomGamma(p=1.0)
            ], p=0.3)
        ])
        
        if not is_3d:
            # Additional 2D-only transforms
            transforms.extend([
                A.OneOf([
                    A.ElasticTransform(alpha=120, sigma=120 * 0.05, p=1.0),  # Removed alpha_affine parameter
                    A.GridDistortion(p=1.0),
                    A.OpticalDistortion(p=1.0)
                ], p=0.3)
            ])
    
    # Always add ToTensorV2 as final transform
    transforms.append(ToTensorV2())
    
    # Use additional_targets to ensure all masks are transformed the same way
    return A.Compose(
        transforms, 
        additional_targets={'mask1': 'mask'}  # Treat mask1 (confidence) the same as mask
    )

def apply_transforms(image, mask, transforms, confidence=None):
    """Apply transforms while maintaining orientation consistency"""
    # Create transform inputs
    transform_inputs = {'image': image, 'mask': mask}
    if confidence is not None:
        transform_inputs['mask1'] = confidence
    
    # Apply transforms
    result = transforms(**transform_inputs)
    
    # Extract results maintaining orientation
    transformed = {
        'image': result['image'],
        'mask': result['mask']
    }
    if confidence is not None:
        transformed['confidence'] = result['mask1']
    
    return transformed

def create_3d_transforms(augment=True):
    """Create 3D transforms that maintain consistent orientation"""
    transforms = []
    
    if augment:
        # 3D-safe geometric transforms
        transforms.extend([
            A.RandomRotate90(p=0.5),
            A.HorizontalFlip(p=0.5),
            A.VerticalFlip(p=0.5),
            A.Transpose(p=0.5)
        ])
        
        # 3D-safe intensity transforms
        transforms.extend([
            A.OneOf([
                A.GaussNoise(var_limit=(0.001, 0.02), p=1.0),
                A.RandomBrightnessContrast(p=1.0)
            ], p=0.3)
        ])
    
    # Always add ToTensorV2
    transforms.append(ToTensorV2())
    
    return A.Compose(transforms)

def apply_3d_transforms(volume, mask, transforms, confidence=None):
    """Apply 3D transforms while maintaining orientation consistency"""
    transformed_volume = []
    transformed_mask = []
    transformed_confidence = [] if confidence is not None else None
    
    # Process each slice while maintaining orientation
    for z in range(volume.shape[0]):
        # Get current slice
        vol_slice = volume[z]
        mask_slice = mask[z]
        conf_slice = confidence[z] if confidence is not None else None
        
        # Apply transforms
        result = apply_transforms(vol_slice, mask_slice, transforms, conf_slice)
        
        # Store transformed slices
        transformed_volume.append(result['image'])
        transformed_mask.append(result['mask'])
        if confidence is not None:
            transformed_confidence.append(result['confidence'])
    
    # Stack transformed slices maintaining orientation [D, C, H, W]
    transformed = {
        'image': torch.stack(transformed_volume, dim=0),
        'mask': torch.stack(transformed_mask, dim=0)
    }
    if confidence is not None:
        transformed['confidence'] = torch.stack(transformed_confidence, dim=0)
    
    return transformed

def create_data_loaders(data_dir=None, 
                        batch_size=8,
                        image_size=(256, 256),
                        train_val_split=0.8,
                        synthetic=True,
                        synthetic_size=1000,
                        augmentation_level='strong',
                        num_workers=4,
                        ensure_size=True):
    """
    Create train and validation data loaders
    
    Args:
        data_dir: Directory containing images and masks
        batch_size: Batch size
        image_size: Size to resize images to
        train_val_split: Fraction of data to use for training
        synthetic: Whether to generate synthetic data
        synthetic_size: Number of synthetic samples to generate
        augmentation_level: Level of augmentation ('none', 'light', 'medium', 'strong')
        num_workers: Number of workers for data loading
        
    Returns:
        train_loader, val_loader
    """
    # Create datasets
    if data_dir is not None:
        # Load real data and split into train/val
        full_dataset = SpotDataset(
            data_dir=data_dir,
            image_size=image_size,
            mode='train',  # Will be overridden for validation split
            synthetic=False,
            augmentation_level='none'  # No augmentation yet
        )
        
        # Calculate split sizes
        train_size = int(train_val_split * len(full_dataset))
        val_size = len(full_dataset) - train_size
        
        # Split dataset
        train_indices, val_indices = torch.utils.data.random_split(
            range(len(full_dataset)), 
            [train_size, val_size],
            generator=torch.Generator().manual_seed(42)
        )
        
        # Create new datasets with appropriate augmentation
        train_dataset = SpotDataset(
            data_dir=None,  # No need to load data again
            image_size=image_size,
            mode='train',
            synthetic=synthetic,
            synthetic_size=synthetic_size,
            augmentation_level=augmentation_level
        )
        train_dataset.images = [full_dataset.images[i] for i in train_indices]
        train_dataset.masks = [full_dataset.masks[i] for i in train_indices]
        
        val_dataset = SpotDataset(
            data_dir=None,  # No need to load data again
            image_size=image_size,
            mode='val',
            synthetic=False,  # No synthetic data for validation
            augmentation_level='none'  # No augmentation for validation
        )
        val_dataset.images = [full_dataset.images[i] for i in val_indices]
        val_dataset.masks = [full_dataset.masks[i] for i in val_indices]
        
    else:
        # Create synthetic datasets
        train_dataset = SpotDataset(
            data_dir=None,
            image_size=image_size,
            mode='train',
            synthetic=True,
            synthetic_size=synthetic_size,
            augmentation_level=augmentation_level
        )
        
        val_dataset = SpotDataset(
            data_dir=None,
            image_size=image_size,
            mode='val',
            synthetic=True,
            synthetic_size=int(synthetic_size * (1 - train_val_split)),
            augmentation_level='none'  # No augmentation for validation
        )
    
    # Create data loaders with optional custom collate function
    if ensure_size:
        # Import custom collate function
        from custom_collate import ensure_size_collate_fn
        
        # Get target size from image_size parameter
        target_size = image_size[0] if isinstance(image_size, tuple) else image_size
        
        # Create data loaders with custom collate function
        train_loader = DataLoader(
            train_dataset,
            batch_size=batch_size,
            shuffle=True,
            num_workers=num_workers,
            pin_memory=True,
            collate_fn=lambda batch: ensure_size_collate_fn(batch, target_size=target_size)
        )
        
        val_loader = DataLoader(
            val_dataset,
            batch_size=batch_size,
            shuffle=False,
            num_workers=num_workers,
            pin_memory=True,
            collate_fn=lambda batch: ensure_size_collate_fn(batch, target_size=target_size)
        )
    else:
        # Create data loaders without custom collate function
        train_loader = DataLoader(
            train_dataset,
            batch_size=batch_size,
            shuffle=True,
            num_workers=num_workers,
            pin_memory=True
        )
        
        val_loader = DataLoader(
            val_dataset,
            batch_size=batch_size,
            shuffle=False,
            num_workers=num_workers,
            pin_memory=True
        )
    
    return train_loader, val_loader, train_dataset, val_dataset
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import matplotlib.pyplot as plt
from OptimizedSpotLoss import OptimizedSpotLoss
from OptimizedSpotDetection_model import OptimizedSpotDetectionModel
from metrics import SpotDetectionMetrics

class SimpleDataset(torch.utils.data.Dataset):
    """Simple dataset for testing"""
    def __init__(self, num_samples=100, image_size=64, num_spots_range=(3, 10)):
        self.num_samples = num_samples
        self.image_size = image_size
        self.num_spots_range = num_spots_range
        
        # Generate data
        self.images = []
        self.masks = []
        
        for i in range(num_samples):
            # Create random image with noise
            image = torch.randn(1, image_size, image_size) * 0.1
            mask = torch.zeros(1, image_size, image_size)
            
            # Add random spots
            num_spots = np.random.randint(num_spots_range[0], num_spots_range[1] + 1)
            for _ in range(num_spots):
                x = np.random.randint(5, image_size - 5)
                y = np.random.randint(5, image_size - 5)
                radius = np.random.randint(2, 5)
                intensity = np.random.uniform(0.5, 1.0)
                
                # Add spot to mask
                for i in range(max(0, y - radius), min(image_size, y + radius + 1)):
                    for j in range(max(0, x - radius), min(image_size, x + radius + 1)):
                        if ((i - y) ** 2 + (j - x) ** 2) <= radius ** 2:
                            mask[0, i, j] = 1.0
                
                # Add spot to image with noise
                for i in range(max(0, y - radius), min(image_size, y + radius + 1)):
                    for j in range(max(0, x - radius), min(image_size, x + radius + 1)):
                        if ((i - y) ** 2 + (j - x) ** 2) <= radius ** 2:
                            image[0, i, j] += intensity * (1.0 - ((i - y) ** 2 + (j - x) ** 2) / (radius ** 2))
            
            self.images.append(image)
            self.masks.append(mask)
    
    def __len__(self):
        return self.num_samples
    
    def __getitem__(self, idx):
        return {
            'image': self.images[idx],
            'mask': self.masks[idx]
        }

def test_training_loop():
    """Test the complete training loop with synthetic data"""
    print("Testing complete training loop...")
    
    # Set random seed for reproducibility
    torch.manual_seed(42)
    np.random.seed(42)
    
    # Create datasets
    train_dataset = SimpleDataset(num_samples=100, image_size=64)
    val_dataset = SimpleDataset(num_samples=20, image_size=64)
    
    # Create data loaders
    train_loader = torch.utils.data.DataLoader(
        train_dataset, batch_size=8, shuffle=True
    )
    val_loader = torch.utils.data.DataLoader(
        val_dataset, batch_size=8, shuffle=False
    )
    
    # Create model
    model = OptimizedSpotDetectionModel(
        in_channels=1,
        num_experts=3,
        base_filters=32,
        dropout_rate=0.1
    )
    
    # Create loss function
    loss_fn = OptimizedSpotLoss(
        bce_weight=1.0,
        dice_weight=1.0,
        focal_weight=0.5,
        focal_gamma=2.0,
        size_adaptive=True,
        density_aware=True,
        confidence_weighted=True
    )
    
    # Create optimizer
    optimizer = optim.Adam(model.parameters(), lr=0.001)
    
    # Create metrics calculator
    metrics_calculator = SpotDetectionMetrics(threshold=0.5, iou_threshold=0.5)
    
    # Train for a few epochs
    num_epochs = 5
    print(f"\nTraining for {num_epochs} epochs:")
    
    # Track metrics
    train_losses = []
    val_losses = []
    val_ious = []
    
    for epoch in range(num_epochs):
        # Training
        model.train()
        train_loss = 0.0
        
        for batch in train_loader:
            # Get data
            images = batch['image']
            masks = batch['mask']
            
            # Zero gradients
            optimizer.zero_grad()
            
            # Forward pass
            outputs = model(images)
            
            # Calculate loss
            loss_dict = loss_fn(outputs, masks)
            loss = loss_dict['loss']
            
            # Backward pass and optimize
            loss.backward()
            optimizer.step()
            
            # Update metrics
            train_loss += loss.item()
        
        # Calculate average training loss
        train_loss /= len(train_loader)
        train_losses.append(train_loss)
        
        # Validation
        model.eval()
        val_loss = 0.0
        val_metrics = {
            'precision': 0.0,
            'recall': 0.0,
            'f1_score': 0.0,
            'iou': 0.0,
            'dice': 0.0,
            'avg_precision': 0.0
        }
        
        with torch.no_grad():
            for batch in val_loader:
                # Get data
                images = batch['image']
                masks = batch['mask']
                
                # Forward pass
                outputs = model(images)
                
                # Calculate loss
                loss_dict = loss_fn(outputs, masks)
                loss = loss_dict['loss']
                
                # Update metrics
                val_loss += loss.item()
                
                # Calculate additional metrics
                batch_metrics = metrics_calculator.calculate_metrics(outputs, masks)
                
                for k, v in batch_metrics.items():
                    val_metrics[k] += v
        
        # Calculate average validation metrics
        val_loss /= len(val_loader)
        val_losses.append(val_loss)
        
        for k in val_metrics:
            val_metrics[k] /= len(val_loader)
        
        val_ious.append(val_metrics['iou'])
        
        # Print epoch summary
        print(f"Epoch {epoch+1}/{num_epochs}:")
        print(f"  Train Loss: {train_loss:.6f}")
        print(f"  Val Loss: {val_loss:.6f}")
        print(f"  Val IoU: {val_metrics['iou']:.6f}")
        print(f"  Val Precision: {val_metrics['precision']:.6f}")
        print(f"  Val Recall: {val_metrics['recall']:.6f}")
        print(f"  Val F1: {val_metrics['f1_score']:.6f}")
    
    # Plot training curves
    plt.figure(figsize=(12, 4))
    
    plt.subplot(1, 2, 1)
    plt.plot(train_losses, label='Train Loss')
    plt.plot(val_losses, label='Val Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.title('Training and Validation Loss')
    plt.legend()
    plt.grid(True)
    
    plt.subplot(1, 2, 2)
    plt.plot(val_ious, label='Val IoU')
    plt.xlabel('Epoch')
    plt.ylabel('IoU')
    plt.title('Validation IoU')
    plt.legend()
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig('training_curves.png')
    
    print("\nTraining complete! Saved training curves to 'training_curves.png'")

if __name__ == "__main__":
    test_training_loop()
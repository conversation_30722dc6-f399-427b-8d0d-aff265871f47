import torch
import numpy as np
import os
import matplotlib.pyplot as plt
from tqdm import tqdm
from enhanced_peak_detection import EnhancedPeakDetector

def train_with_enhanced_peak_detection(model, train_loader, val_loader, optimizer, loss_fn, num_epochs, device):
    """
    Train model with enhanced peak detection that uses actual masks for spot representation
    
    This training function uses peak detection to find spot centroids but uses the actual masks
    (ground truth for real data or synthetic masks for synthetic data) for more accurate
    spot representation during training.
    """
    # Initialize best model tracking
    best_val_loss = float('inf')
    run_dir = os.path.join('models', f'run_{torch.datetime.now().strftime("%Y%m%d-%H%M%S")}')
    os.makedirs(run_dir, exist_ok=True)
    best_model_path = os.path.join(run_dir, 'model_iter1_best.pth')
    
    # Initialize peak detector
    peak_detector = EnhancedPeakDetector(
        min_distance=5,
        min_intensity=0.1,
        device=device
    )
    
    # Training history
    history = {
        'train_loss': [],
        'val_loss': [],
        'peak_params': []
    }
    
    # Training loop
    for epoch in range(num_epochs):
        # Training phase
        model.train()
        train_loss = 0.0
        
        for batch in tqdm(train_loader, desc=f"Epoch {epoch+1}/{num_epochs} - Training"):
            # Get data
            images = batch['image'].to(device)
            masks = batch['mask'].to(device)
            
            # Forward pass
            optimizer.zero_grad()
            outputs = model(images)
            
            # Calculate loss
            loss = loss_fn(outputs, masks)
            
            # Backward pass
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item()
        
        # Calculate average training loss
        train_loss /= len(train_loader)
        history['train_loss'].append(train_loss)
        
        # Validation phase
        model.eval()
        val_loss = 0.0
        
        # For visualization
        vis_samples = []
        
        with torch.no_grad():
            for batch in tqdm(val_loader, desc=f"Epoch {epoch+1}/{num_epochs} - Validation"):
                # Get data
                images = batch['image'].to(device)
                masks = batch['mask'].to(device)
                
                # Forward pass
                outputs = model(images)
                
                # Calculate loss
                loss = loss_fn(outputs, masks)
                val_loss += loss.item()
                
                # Store a few samples for visualization
                if len(vis_samples) < 3:
                    for i in range(min(2, images.shape[0])):
                        img = images[i, 0].cpu().numpy()
                        mask = masks[i, 0].cpu().numpy()
                        pred = torch.sigmoid(outputs[i, 0]).cpu().numpy()
                        
                        # Use enhanced peak detection with masks
                        result = peak_detector.detect_peaks_with_masks(
                            pred,
                            mask=mask,
                            return_visualization=True,
                            original_image=img
                        )
                        
                        vis_samples.append({
                            'image': img,
                            'mask': mask,
                            'pred': pred,
                            'visualization': result['visualization'],
                            'num_spots': result['num_spots'],
                            'coordinates': result['coordinates'],
                            'spot_masks': result['spot_masks']
                        })
        
        # Calculate average validation loss
        val_loss /= len(val_loader)
        history['val_loss'].append(val_loss)
        
        # Print epoch results
        print(f"Epoch {epoch+1}/{num_epochs} - Train Loss: {train_loss:.4f}, Val Loss: {val_loss:.4f}")
        
        # Save best model
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            torch.save(model.state_dict(), best_model_path)
            print(f"Saved best model with validation loss: {val_loss:.4f}")
        
        # Visualize samples at the end of each epoch
        if vis_samples:
            # Create directory for visualizations
            vis_dir = os.path.join('training_visualizations')
            os.makedirs(vis_dir, exist_ok=True)
            
            # Save visualization for this epoch
            fig, axes = plt.subplots(len(vis_samples), 4, figsize=(16, 4*len(vis_samples)))
            
            for i, sample in enumerate(vis_samples):
                # Original image
                if len(vis_samples) == 1:
                    axes[0].imshow(sample['image'], cmap='gray')
                    axes[0].set_title('Original Image')
                    axes[0].axis('off')
                    
                    # Ground truth mask
                    axes[1].imshow(sample['mask'], cmap='hot')
                    axes[1].set_title('Ground Truth')
                    axes[1].axis('off')
                    
                    # Prediction heatmap
                    axes[2].imshow(sample['pred'], cmap='hot')
                    axes[2].set_title('Prediction Heatmap')
                    axes[2].axis('off')
                    
                    # Peak detection visualization
                    axes[3].imshow(sample['visualization'])
                    axes[3].set_title(f'Enhanced Detection: {sample["num_spots"]} spots')
                    axes[3].axis('off')
                else:
                    axes[i, 0].imshow(sample['image'], cmap='gray')
                    axes[i, 0].set_title('Original Image')
                    axes[i, 0].axis('off')
                    
                    # Ground truth mask
                    axes[i, 1].imshow(sample['mask'], cmap='hot')
                    axes[i, 1].set_title('Ground Truth')
                    axes[i, 1].axis('off')
                    
                    # Prediction heatmap
                    axes[i, 2].imshow(sample['pred'], cmap='hot')
                    axes[i, 2].set_title('Prediction Heatmap')
                    axes[i, 2].axis('off')
                    
                    # Peak detection visualization
                    axes[i, 3].imshow(sample['visualization'])
                    axes[i, 3].set_title(f'Enhanced Detection: {sample["num_spots"]} spots')
                    axes[i, 3].axis('off')
            
            plt.tight_layout()
            plt.savefig(os.path.join(vis_dir, f'epoch_{epoch+1:03d}.png'))
            
            # Save a separate visualization showing just the spots
            fig, axes = plt.subplots(len(vis_samples), 1, figsize=(8, 6*len(vis_samples)))
            
            for i, sample in enumerate(vis_samples):
                if len(vis_samples) == 1:
                    axes.imshow(sample['visualization'])
                    axes.set_title(f'Enhanced Detection: {sample["num_spots"]} spots')
                    axes.axis('off')
                else:
                    axes[i].imshow(sample['visualization'])
                    axes[i].set_title(f'Enhanced Detection: {sample["num_spots"]} spots')
                    axes[i].axis('off')
            
            plt.tight_layout()
            plt.savefig(os.path.join(vis_dir, f'epoch_{epoch+1:03d}_spots.png'))
            plt.close('all')
    
    # Plot training history
    plt.figure(figsize=(10, 5))
    plt.plot(history['train_loss'], label='Train Loss')
    plt.plot(history['val_loss'], label='Val Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()
    plt.title('Training and Validation Loss')
    plt.grid(True, alpha=0.3)
    plt.savefig(os.path.join(run_dir, 'training_curves.png'))
    plt.close()
    
    # Return best model path
    return best_model_path

def evaluate_with_enhanced_peak_detection(model, dataloader, device, num_samples=5):
    """
    Evaluate model with enhanced peak detection
    
    This function evaluates the model using enhanced peak detection that combines
    peak detection for centroids with actual masks for more accurate spot representation.
    """
    model.eval()
    
    # Initialize peak detector
    peak_detector = EnhancedPeakDetector(
        min_distance=5,
        min_intensity=0.1,
        device=device
    )
    
    # Get samples for visualization
    vis_samples = []
    
    with torch.no_grad():
        for batch in dataloader:
            # Get data
            images = batch['image'].to(device)
            masks = batch['mask'].to(device)
            
            # Forward pass
            outputs = model(images)
            preds = torch.sigmoid(outputs)
            
            # Process each sample in batch
            for j in range(images.shape[0]):
                # Get single sample
                img = images[j, 0].cpu().numpy()
                mask = masks[j, 0].cpu().numpy()
                pred = preds[j, 0].cpu().numpy()
                
                # Detect peaks with masks
                result = peak_detector.detect_peaks_with_masks(
                    pred,
                    mask=mask,
                    return_visualization=True,
                    original_image=img
                )
                
                # Store for visualization
                vis_samples.append({
                    'image': img,
                    'mask': mask,
                    'pred': pred,
                    'visualization': result['visualization'],
                    'num_spots': result['num_spots'],
                    'coordinates': result['coordinates'],
                    'spot_masks': result['spot_masks']
                })
                
                # Break if we have enough samples
                if len(vis_samples) >= num_samples:
                    break
            
            # Break if we have enough samples
            if len(vis_samples) >= num_samples:
                break
    
    # Visualize samples
    fig, axes = plt.subplots(len(vis_samples), 4, figsize=(16, 4*len(vis_samples)))
    
    for i, sample in enumerate(vis_samples):
        # Original image
        axes[i, 0].imshow(sample['image'], cmap='gray')
        axes[i, 0].set_title('Original Image')
        axes[i, 0].axis('off')
        
        # Ground truth mask
        axes[i, 1].imshow(sample['mask'], cmap='hot')
        axes[i, 1].set_title('Ground Truth')
        axes[i, 1].axis('off')
        
        # Prediction heatmap
        axes[i, 2].imshow(sample['pred'], cmap='hot')
        axes[i, 2].set_title('Prediction Heatmap')
        axes[i, 2].axis('off')
        
        # Peak detection visualization
        axes[i, 3].imshow(sample['visualization'])
        axes[i, 3].set_title(f'Enhanced Detection: {sample["num_spots"]} spots')
        axes[i, 3].axis('off')
    
    plt.tight_layout()
    plt.show()
    
    return vis_samples
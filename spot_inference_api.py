import torch
import numpy as np
from pathlib import Path
from typing import Dict, List, Union, Optional
import tifffile
from enhanced_inference import enhanced_inference
from OptimizedSpotDetection_model import OptimizedSpotDetectionModel

class SpotDetector:
    def __init__(self, 
                 model_path: Optional[str] = None,
                 device: str = 'auto',
                 **model_kwargs):
        self.device = 'cuda' if torch.cuda.is_available() and device == 'auto' else device
        
        default_params = {
            'in_channels': 1,
            'base_filters': 32,
            'num_experts': 4,
            'dropout': 0.1
        }
        default_params.update(model_kwargs)
        
        self.model = OptimizedSpotDetectionModel(**default_params)
        
        if model_path and Path(model_path).exists():
            self.load_model(model_path)
        
        self.model.to(self.device)
        self.model.eval()
    
    def load_model(self, model_path: str):
        checkpoint = torch.load(model_path, map_location=self.device)
        
        if 'model_state_dict' in checkpoint:
            self.model.load_state_dict(checkpoint['model_state_dict'])
        else:
            self.model.load_state_dict(checkpoint)
        
        print(f"Loaded model weights from {model_path}")
    
    def detect_spots(self,
                    image: Union[str, np.ndarray],
                    patch_size: int = 256,
                    overlap: int = 32,
                    threshold: float = 0.5,
                    min_distance: int = 3,
                    min_size: int = 4,
                    use_nms: bool = False,
                    nms_threshold: float = 0.3,
                    use_flow: bool = True,
                    save_path: Optional[str] = None) -> Dict:
        if isinstance(image, (str, Path)):
            image = self._load_image(str(image))
        
        return enhanced_inference(
            model=self.model,
            image=image,
            patch_size=patch_size,
            overlap=overlap,
            threshold=threshold,
            min_distance=min_distance,
            min_size=min_size,
            use_nms=use_nms,
            nms_threshold=nms_threshold,
            use_flow=use_flow,
            device=self.device,
            save_path=save_path
        )
    
    def detect_spots_batch(self,
                          images: List[Union[str, np.ndarray]],
                          patch_size: int = 256,
                          overlap: int = 32,
                          threshold: float = 0.5,
                          min_distance: int = 3,
                          min_size: int = 4,
                          use_nms: bool = False,
                          nms_threshold: float = 0.3,
                          use_flow: bool = True,
                          save_path: Optional[str] = None,
                          **kwargs) -> List[Dict]:
        results = []
        for i, image in enumerate(images):
            print(f"Processing image {i+1}/{len(images)}")
            result = self.detect_spots(
                image, 
                patch_size=patch_size,
                overlap=overlap,
                threshold=threshold,
                min_distance=min_distance,
                min_size=min_size,
                use_nms=use_nms,
                nms_threshold=nms_threshold,
                use_flow=use_flow,
                save_path=save_path
            )
            results.append(result)
        return results
    
    def _load_image(self, image_path: str) -> np.ndarray:
        try:
            image = tifffile.imread(image_path)
        except:
            import cv2
            image = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)
        
        if image is None:
            raise ValueError(f"Could not load image from {image_path}")
        
        return image.astype(np.float32)

def quick_detect(image_path: str,
                model_path: Optional[str] = None,
                threshold: float = 0.5,
                min_distance: int = 3,
                min_size: int = 4,
                use_nms: bool = False,
                nms_threshold: float = 0.3,
                use_flow: bool = True,
                save_path: Optional[str] = None) -> Dict:
    detector = SpotDetector(model_path=model_path)
    return detector.detect_spots(
        image_path, 
        threshold=threshold, 
        min_distance=min_distance,
        min_size=min_size,
        use_nms=use_nms,
        nms_threshold=nms_threshold,
        use_flow=use_flow,
        save_path=save_path
    )
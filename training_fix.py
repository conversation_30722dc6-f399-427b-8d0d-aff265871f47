# Fix the plot_training_progress call by adding pred_boundary:

# Replace this line in your training code:
plot_training_progress(
    image=img0,
    pred_semantic=sem,
    pred_centroid=cmap,
    pred_flow=flow,
    detected_spots=np.array(spots),
    epoch=epoch,
    save_dir=vis_dir
)

# With this:
bnd = torch.sigmoid(outputs['boundary'])[0, 0].cpu().numpy()

plot_training_progress(
    image=img0,
    pred_semantic=sem,
    pred_centroid=cmap,
    pred_flow=flow,
    pred_boundary=bnd,  # Add this line
    detected_spots=np.array(spots),
    epoch=epoch,
    save_dir=vis_dir
)